/**
 * DL-Engine 媒体处理服务
 * 
 * 核心功能：
 * - 图像处理 (格式转换、尺寸调整、质量压缩、水印添加)
 * - 视频处理 (转码、切片、缩略图、质量优化)
 * - 音频处理 (格式转换、降噪、音量调节、音效处理)
 * - 3D模型处理 (格式转换、LOD生成、纹理优化、压缩算法)
 */

import { Injectable, BadRequestException } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { Repository } from 'typeorm'
import { InjectQueue } from '@nestjs/bull'
import { Queue } from 'bull'
import { ConfigService } from '@nestjs/config'
import * as sharp from 'sharp'
import * as ffmpeg from 'fluent-ffmpeg'
import * as path from 'path'
import * as fs from 'fs/promises'

import { MediaFile } from '../entities/media-file.entity'
import { ProcessingJob } from '../entities/processing-job.entity'
import { ProcessImageDto, ProcessVideoDto, ProcessAudioDto, Process3DModelDto } from './dto/processing.dto'

export interface ProcessingOptions {
  priority?: number
  delay?: number
  attempts?: number
  removeOnComplete?: number
  removeOnFail?: number
}

@Injectable()
export class ProcessingService {
  constructor(
    @InjectRepository(MediaFile)
    private readonly mediaFileRepository: Repository<MediaFile>,
    @InjectRepository(ProcessingJob)
    private readonly processingJobRepository: Repository<ProcessingJob>,
    @InjectQueue('media-processing')
    private readonly processingQueue: Queue,
    private readonly configService: ConfigService
  ) {
    this.initializeFFmpeg()
  }

  /**
   * 处理图像
   */
  async processImage(fileId: string, dto: ProcessImageDto, options: ProcessingOptions = {}) {
    const mediaFile = await this.getMediaFile(fileId)
    this.validateImageFile(mediaFile)

    const job = await this.createProcessingJob(fileId, 'image', dto)

    // 添加到处理队列
    const queueJob = await this.processingQueue.add('process-image', {
      jobId: job.id,
      fileId,
      filePath: mediaFile.url,
      options: dto
    }, {
      priority: options.priority || 5,
      delay: options.delay || 0,
      attempts: options.attempts || 3,
      removeOnComplete: options.removeOnComplete || 10,
      removeOnFail: options.removeOnFail || 5
    })

    await this.updateProcessingJob(job.id, { queueJobId: queueJob.id.toString() })

    return {
      jobId: job.id,
      queueJobId: queueJob.id,
      status: 'queued',
      estimatedTime: this.estimateImageProcessingTime(dto)
    }
  }

  /**
   * 处理视频
   */
  async processVideo(fileId: string, dto: ProcessVideoDto, options: ProcessingOptions = {}) {
    const mediaFile = await this.getMediaFile(fileId)
    this.validateVideoFile(mediaFile)

    const job = await this.createProcessingJob(fileId, 'video', dto)

    const queueJob = await this.processingQueue.add('process-video', {
      jobId: job.id,
      fileId,
      filePath: mediaFile.url,
      options: dto
    }, {
      priority: options.priority || 3, // 视频处理优先级较低
      delay: options.delay || 0,
      attempts: options.attempts || 2,
      removeOnComplete: options.removeOnComplete || 5,
      removeOnFail: options.removeOnFail || 3
    })

    await this.updateProcessingJob(job.id, { queueJobId: queueJob.id.toString() })

    return {
      jobId: job.id,
      queueJobId: queueJob.id,
      status: 'queued',
      estimatedTime: this.estimateVideoProcessingTime(dto, mediaFile.size)
    }
  }

  /**
   * 处理音频
   */
  async processAudio(fileId: string, dto: ProcessAudioDto, options: ProcessingOptions = {}) {
    const mediaFile = await this.getMediaFile(fileId)
    this.validateAudioFile(mediaFile)

    const job = await this.createProcessingJob(fileId, 'audio', dto)

    const queueJob = await this.processingQueue.add('process-audio', {
      jobId: job.id,
      fileId,
      filePath: mediaFile.url,
      options: dto
    }, {
      priority: options.priority || 4,
      delay: options.delay || 0,
      attempts: options.attempts || 3,
      removeOnComplete: options.removeOnComplete || 10,
      removeOnFail: options.removeOnFail || 5
    })

    await this.updateProcessingJob(job.id, { queueJobId: queueJob.id.toString() })

    return {
      jobId: job.id,
      queueJobId: queueJob.id,
      status: 'queued',
      estimatedTime: this.estimateAudioProcessingTime(dto, mediaFile.size)
    }
  }

  /**
   * 处理3D模型
   */
  async process3DModel(fileId: string, dto: Process3DModelDto, options: ProcessingOptions = {}) {
    const mediaFile = await this.getMediaFile(fileId)
    this.validate3DModelFile(mediaFile)

    const job = await this.createProcessingJob(fileId, '3d_model', dto)

    const queueJob = await this.processingQueue.add('process-3d-model', {
      jobId: job.id,
      fileId,
      filePath: mediaFile.url,
      options: dto
    }, {
      priority: options.priority || 2, // 3D模型处理优先级最低
      delay: options.delay || 0,
      attempts: options.attempts || 2,
      removeOnComplete: options.removeOnComplete || 5,
      removeOnFail: options.removeOnFail || 3
    })

    await this.updateProcessingJob(job.id, { queueJobId: queueJob.id.toString() })

    return {
      jobId: job.id,
      queueJobId: queueJob.id,
      status: 'queued',
      estimatedTime: this.estimate3DModelProcessingTime(dto, mediaFile.size)
    }
  }

  /**
   * 获取处理任务状态
   */
  async getJobStatus(jobId: string) {
    const job = await this.processingJobRepository.findOne({
      where: { id: jobId },
      relations: ['mediaFile']
    })

    if (!job) {
      throw new BadRequestException('处理任务不存在')
    }

    // 获取队列任务状态
    let queueStatus = null
    if (job.queueJobId) {
      const queueJob = await this.processingQueue.getJob(job.queueJobId)
      if (queueJob) {
        queueStatus = {
          state: await queueJob.getState(),
          progress: queueJob.progress(),
          processedOn: queueJob.processedOn,
          finishedOn: queueJob.finishedOn,
          failedReason: queueJob.failedReason
        }
      }
    }

    return {
      jobId: job.id,
      fileId: job.mediaFile.id,
      fileName: job.mediaFile.originalName,
      type: job.type,
      status: job.status,
      progress: job.progress,
      result: job.result,
      error: job.error,
      createdAt: job.createdAt,
      startedAt: job.startedAt,
      completedAt: job.completedAt,
      queueStatus
    }
  }

  /**
   * 取消处理任务
   */
  async cancelJob(jobId: string) {
    const job = await this.processingJobRepository.findOne({
      where: { id: jobId }
    })

    if (!job) {
      throw new BadRequestException('处理任务不存在')
    }

    if (job.status === 'completed' || job.status === 'failed') {
      throw new BadRequestException('任务已完成，无法取消')
    }

    // 取消队列任务
    if (job.queueJobId) {
      const queueJob = await this.processingQueue.getJob(job.queueJobId)
      if (queueJob) {
        await queueJob.remove()
      }
    }

    // 更新任务状态
    await this.updateProcessingJob(jobId, {
      status: 'cancelled',
      completedAt: new Date()
    })

    return { cancelled: true }
  }

  /**
   * 获取处理队列统计
   */
  async getQueueStats() {
    const [waiting, active, completed, failed, delayed] = await Promise.all([
      this.processingQueue.getWaiting(),
      this.processingQueue.getActive(),
      this.processingQueue.getCompleted(),
      this.processingQueue.getFailed(),
      this.processingQueue.getDelayed()
    ])

    return {
      waiting: waiting.length,
      active: active.length,
      completed: completed.length,
      failed: failed.length,
      delayed: delayed.length,
      total: waiting.length + active.length + completed.length + failed.length + delayed.length
    }
  }

  // 私有方法

  private async getMediaFile(fileId: string): Promise<MediaFile> {
    const mediaFile = await this.mediaFileRepository.findOne({
      where: { id: fileId }
    })

    if (!mediaFile) {
      throw new BadRequestException('媒体文件不存在')
    }

    return mediaFile
  }

  private validateImageFile(mediaFile: MediaFile) {
    const imageTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
    if (!imageTypes.includes(mediaFile.mimeType)) {
      throw new BadRequestException('不是有效的图像文件')
    }
  }

  private validateVideoFile(mediaFile: MediaFile) {
    const videoTypes = ['video/mp4', 'video/webm', 'video/avi', 'video/mov']
    if (!videoTypes.includes(mediaFile.mimeType)) {
      throw new BadRequestException('不是有效的视频文件')
    }
  }

  private validateAudioFile(mediaFile: MediaFile) {
    const audioTypes = ['audio/mpeg', 'audio/wav', 'audio/ogg', 'audio/aac']
    if (!audioTypes.includes(mediaFile.mimeType)) {
      throw new BadRequestException('不是有效的音频文件')
    }
  }

  private validate3DModelFile(mediaFile: MediaFile) {
    const modelTypes = ['model/gltf+json', 'model/gltf-binary', 'application/octet-stream']
    if (!modelTypes.includes(mediaFile.mimeType)) {
      throw new BadRequestException('不是有效的3D模型文件')
    }
  }

  private async createProcessingJob(fileId: string, type: string, options: any): Promise<ProcessingJob> {
    const job = this.processingJobRepository.create({
      mediaFile: { id: fileId } as MediaFile,
      type,
      status: 'pending',
      progress: 0,
      options: JSON.stringify(options),
      createdAt: new Date()
    })

    return this.processingJobRepository.save(job)
  }

  private async updateProcessingJob(jobId: string, updates: Partial<ProcessingJob>) {
    await this.processingJobRepository.update(jobId, updates)
  }

  private estimateImageProcessingTime(dto: ProcessImageDto): number {
    // 基于处理选项估算时间（秒）
    let time = 5 // 基础时间

    if (dto.resize) time += 2
    if (dto.format) time += 3
    if (dto.quality && dto.quality < 80) time += 2
    if (dto.watermark) time += 5
    if (dto.effects && dto.effects.length > 0) time += dto.effects.length * 2

    return time
  }

  private estimateVideoProcessingTime(dto: ProcessVideoDto, fileSize: number): number {
    // 基于文件大小和处理选项估算时间（秒）
    const sizeFactor = Math.ceil(fileSize / (1024 * 1024)) // MB
    let time = sizeFactor * 10 // 每MB约10秒

    if (dto.resolution && dto.resolution !== 'original') time *= 1.5
    if (dto.bitrate && dto.bitrate < 2000) time *= 1.2
    if (dto.format && dto.format !== 'mp4') time *= 1.3
    if (dto.thumbnail) time += 5

    return Math.min(time, 3600) // 最大1小时
  }

  private estimateAudioProcessingTime(dto: ProcessAudioDto, fileSize: number): number {
    const sizeFactor = Math.ceil(fileSize / (1024 * 1024))
    let time = sizeFactor * 2 // 每MB约2秒

    if (dto.format && dto.format !== 'mp3') time *= 1.2
    if (dto.bitrate && dto.bitrate < 128) time *= 1.1
    if (dto.effects && dto.effects.length > 0) time += dto.effects.length * 3

    return Math.min(time, 600) // 最大10分钟
  }

  private estimate3DModelProcessingTime(dto: Process3DModelDto, fileSize: number): number {
    const sizeFactor = Math.ceil(fileSize / (1024 * 1024))
    let time = sizeFactor * 30 // 每MB约30秒

    if (dto.format && dto.format !== 'gltf') time *= 1.5
    if (dto.compression) time *= 1.3
    if (dto.lodLevels && dto.lodLevels > 1) time += dto.lodLevels * 60
    if (dto.textureOptimization) time *= 1.4

    return Math.min(time, 7200) // 最大2小时
  }

  private initializeFFmpeg() {
    const ffmpegPath = this.configService.get('FFMPEG_PATH')
    const ffprobePath = this.configService.get('FFPROBE_PATH')

    if (ffmpegPath) {
      ffmpeg.setFfmpegPath(ffmpegPath)
    }
    if (ffprobePath) {
      ffmpeg.setFfprobePath(ffprobePath)
    }
  }
}
