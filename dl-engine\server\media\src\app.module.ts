import { Module } from '@nestjs/common'
import { ConfigModule } from '@nestjs/config'
import { TypeOrmModule } from '@nestjs/typeorm'
import { BullModule } from '@nestjs/bull'

import { UploadModule } from './upload/upload.module'
import { ProcessingModule } from './processing/processing.module'
import { StreamingModule } from './streaming/streaming.module'
import { CdnModule } from './cdn/cdn.module'
import { HealthModule } from './health/health.module'

import { MediaFile } from './entities/media-file.entity'
import { ProcessingJob } from './entities/processing-job.entity'
import { StreamingSession } from './entities/streaming-session.entity'

/**
 * DL-Engine 媒体处理服务主模块
 * 
 * 功能包括：
 * - 文件上传服务 (分片上传、断点续传)
 * - 媒体处理引擎 (图像、视频、音频、3D模型)
 * - 流媒体服务 (直播推流、点播服务)
 * - CDN集成 (内容分发、缓存管理)
 */
@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env']
    }),
    
    TypeOrmModule.forRoot({
      type: 'mysql',
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT || '3306'),
      username: process.env.DB_USERNAME || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_DATABASE || 'dl_engine_media',
      entities: [MediaFile, ProcessingJob, StreamingSession],
      synchronize: process.env.NODE_ENV === 'development',
      logging: process.env.NODE_ENV === 'development'
    }),

    BullModule.forRoot({
      redis: {
        host: process.env.REDIS_HOST || 'localhost',
        port: parseInt(process.env.REDIS_PORT || '6379'),
        password: process.env.REDIS_PASSWORD || undefined
      }
    }),

    UploadModule,
    ProcessingModule,
    StreamingModule,
    CdnModule,
    HealthModule
  ]
})
export class AppModule {}
