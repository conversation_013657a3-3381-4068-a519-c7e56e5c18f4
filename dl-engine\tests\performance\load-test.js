/**
 * DL-Engine 性能负载测试
 * 使用K6进行压力测试和性能基准测试
 */

import http from 'k6/http'
import ws from 'k6/ws'
import { check, group, sleep } from 'k6'
import { Rate, Trend, Counter } from 'k6/metrics'

// 自定义指标
const errorRate = new Rate('error_rate')
const responseTime = new Trend('response_time')
const wsConnections = new Counter('websocket_connections')
const wsMessages = new Counter('websocket_messages')

// 测试配置
export const options = {
  stages: [
    // 预热阶段
    { duration: '2m', target: 10 },
    // 负载增长阶段
    { duration: '5m', target: 50 },
    // 高负载维持阶段
    { duration: '10m', target: 100 },
    // 峰值负载测试
    { duration: '2m', target: 200 },
    // 负载下降阶段
    { duration: '5m', target: 50 },
    // 冷却阶段
    { duration: '2m', target: 0 }
  ],
  thresholds: {
    // HTTP请求成功率应大于95%
    http_req_failed: ['rate<0.05'],
    // 95%的请求响应时间应小于2秒
    http_req_duration: ['p(95)<2000'],
    // 错误率应小于5%
    error_rate: ['rate<0.05'],
    // WebSocket连接成功率应大于90%
    'websocket_connections': ['count>0']
  },
  ext: {
    loadimpact: {
      projectID: 3595341,
      name: 'DL-Engine Performance Test'
    }
  }
}

// 测试环境配置
const BASE_URL = __ENV.BASE_URL || 'http://localhost:3030'
const AUTH_URL = __ENV.AUTH_URL || 'http://localhost:3031'
const WS_URL = __ENV.WS_URL || 'ws://localhost:8080'

// 测试数据
const TEST_USERS = [
  { username: 'teacher1', password: 'password123', role: 'teacher' },
  { username: 'teacher2', password: 'password123', role: 'teacher' },
  { username: 'student1', password: 'password123', role: 'student' },
  { username: 'student2', password: 'password123', role: 'student' },
  { username: 'student3', password: 'password123', role: 'student' }
]

// 获取随机用户
function getRandomUser() {
  return TEST_USERS[Math.floor(Math.random() * TEST_USERS.length)]
}

// 用户认证
function authenticate(user) {
  const loginResponse = http.post(`${AUTH_URL}/login`, JSON.stringify(user), {
    headers: { 'Content-Type': 'application/json' }
  })
  
  const success = check(loginResponse, {
    '登录状态码为200': (r) => r.status === 200,
    '返回token': (r) => r.json('data.token') !== undefined
  })
  
  errorRate.add(!success)
  
  if (success) {
    return loginResponse.json('data.token')
  }
  return null
}

// 主测试函数
export default function() {
  const user = getRandomUser()
  const token = authenticate(user)
  
  if (!token) {
    console.error('认证失败，跳过测试')
    return
  }

  const headers = {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  }

  // API性能测试
  group('API Performance Tests', () => {
    // 获取课程列表
    group('获取课程列表', () => {
      const startTime = Date.now()
      const response = http.get(`${BASE_URL}/api/courses`, { headers })
      const duration = Date.now() - startTime
      
      const success = check(response, {
        '状态码为200': (r) => r.status === 200,
        '响应时间小于2秒': () => duration < 2000,
        '返回数据格式正确': (r) => Array.isArray(r.json('data'))
      })
      
      responseTime.add(duration)
      errorRate.add(!success)
    })

    // 创建课程（仅教师）
    if (user.role === 'teacher') {
      group('创建课程', () => {
        const courseData = {
          title: `测试课程 ${Date.now()}`,
          description: '这是一个性能测试课程',
          category: 'technology',
          level: 'beginner',
          duration: 60
        }
        
        const startTime = Date.now()
        const response = http.post(
          `${BASE_URL}/api/courses`,
          JSON.stringify(courseData),
          { headers }
        )
        const duration = Date.now() - startTime
        
        const success = check(response, {
          '创建成功': (r) => r.status === 201,
          '响应时间小于3秒': () => duration < 3000,
          '返回课程ID': (r) => r.json('data.id') !== undefined
        })
        
        responseTime.add(duration)
        errorRate.add(!success)
      })
    }

    // 获取用户信息
    group('获取用户信息', () => {
      const startTime = Date.now()
      const response = http.get(`${BASE_URL}/api/user/profile`, { headers })
      const duration = Date.now() - startTime
      
      const success = check(response, {
        '状态码为200': (r) => r.status === 200,
        '响应时间小于1秒': () => duration < 1000,
        '返回用户数据': (r) => r.json('data.username') !== undefined
      })
      
      responseTime.add(duration)
      errorRate.add(!success)
    })

    // 文件上传测试
    group('文件上传', () => {
      const fileData = 'x'.repeat(1024 * 100) // 100KB测试文件
      const formData = {
        file: http.file(fileData, 'test.txt', 'text/plain')
      }
      
      const startTime = Date.now()
      const response = http.post(`${BASE_URL}/api/upload`, formData, { headers })
      const duration = Date.now() - startTime
      
      const success = check(response, {
        '上传成功': (r) => r.status === 200,
        '响应时间小于5秒': () => duration < 5000
      })
      
      responseTime.add(duration)
      errorRate.add(!success)
    })
  })

  // WebSocket性能测试
  group('WebSocket Performance Tests', () => {
    const wsUrl = `${WS_URL}?token=${token}`
    
    const response = ws.connect(wsUrl, {}, (socket) => {
      wsConnections.add(1)
      
      socket.on('open', () => {
        console.log('WebSocket连接已建立')
        
        // 发送测试消息
        for (let i = 0; i < 10; i++) {
          socket.send(JSON.stringify({
            type: 'test:message',
            data: { message: `测试消息 ${i}`, timestamp: Date.now() }
          }))
          wsMessages.add(1)
          sleep(0.1)
        }
      })
      
      socket.on('message', (data) => {
        const message = JSON.parse(data)
        check(message, {
          '消息格式正确': (m) => m.type !== undefined,
          '包含时间戳': (m) => m.timestamp !== undefined
        })
      })
      
      socket.on('error', (e) => {
        console.error('WebSocket错误:', e)
        errorRate.add(1)
      })
      
      // 保持连接5秒
      sleep(5)
    })
    
    check(response, {
      'WebSocket连接成功': (r) => r && r.status === 101
    })
  })

  // 3D场景性能测试
  group('3D Scene Performance Tests', () => {
    // 获取场景列表
    group('获取场景列表', () => {
      const startTime = Date.now()
      const response = http.get(`${BASE_URL}/api/scenes`, { headers })
      const duration = Date.now() - startTime
      
      const success = check(response, {
        '状态码为200': (r) => r.status === 200,
        '响应时间小于2秒': () => duration < 2000
      })
      
      responseTime.add(duration)
      errorRate.add(!success)
    })

    // 创建场景（仅教师）
    if (user.role === 'teacher') {
      group('创建3D场景', () => {
        const sceneData = {
          name: `测试场景 ${Date.now()}`,
          description: '性能测试场景',
          template: 'basic_classroom',
          settings: {
            maxParticipants: 20,
            enablePhysics: true
          }
        }
        
        const startTime = Date.now()
        const response = http.post(
          `${BASE_URL}/api/scenes`,
          JSON.stringify(sceneData),
          { headers }
        )
        const duration = Date.now() - startTime
        
        const success = check(response, {
          '创建成功': (r) => r.status === 201,
          '响应时间小于3秒': () => duration < 3000
        })
        
        responseTime.add(duration)
        errorRate.add(!success)
      })
    }
  })

  // 数据库性能测试
  group('Database Performance Tests', () => {
    // 复杂查询测试
    group('复杂查询', () => {
      const queryParams = {
        page: Math.floor(Math.random() * 10) + 1,
        limit: 20,
        sort: 'createdAt',
        order: 'desc',
        search: 'test'
      }
      
      const url = `${BASE_URL}/api/courses?${Object.entries(queryParams)
        .map(([key, value]) => `${key}=${value}`)
        .join('&')}`
      
      const startTime = Date.now()
      const response = http.get(url, { headers })
      const duration = Date.now() - startTime
      
      const success = check(response, {
        '查询成功': (r) => r.status === 200,
        '响应时间小于3秒': () => duration < 3000,
        '包含分页信息': (r) => r.json('meta') !== undefined
      })
      
      responseTime.add(duration)
      errorRate.add(!success)
    })
  })

  // 缓存性能测试
  group('Cache Performance Tests', () => {
    // 重复请求测试缓存效果
    const cacheTestUrl = `${BASE_URL}/api/courses/popular`
    
    // 第一次请求
    const firstResponse = http.get(cacheTestUrl, { headers })
    const firstDuration = firstResponse.timings.duration
    
    sleep(0.1)
    
    // 第二次请求（应该从缓存返回）
    const secondResponse = http.get(cacheTestUrl, { headers })
    const secondDuration = secondResponse.timings.duration
    
    check(null, {
      '缓存生效': () => secondDuration < firstDuration,
      '两次请求都成功': () => firstResponse.status === 200 && secondResponse.status === 200
    })
  })

  // 随机等待，模拟真实用户行为
  sleep(Math.random() * 3 + 1)
}

// 测试结束后的清理工作
export function teardown(data) {
  console.log('性能测试完成，开始清理...')
  
  // 这里可以添加测试数据清理逻辑
  // 例如删除测试期间创建的课程、场景等
}

// 测试开始前的准备工作
export function setup() {
  console.log('开始DL-Engine性能测试...')
  
  // 预热请求
  const warmupResponse = http.get(`${BASE_URL}/health`)
  if (warmupResponse.status !== 200) {
    throw new Error('服务未就绪，无法开始测试')
  }
  
  console.log('服务预热完成，开始正式测试')
  return { startTime: Date.now() }
}
