# DL-Engine 实例服务 Docker镜像
# 支持Agones游戏服务器的实时多人协作服务

# 多阶段构建 - 构建阶段
FROM node:22-alpine AS builder

# 设置工作目录
WORKDIR /app

# 安装pnpm
RUN npm install -g pnpm

# 复制package文件
COPY package.json pnpm-lock.yaml pnpm-workspace.yaml ./
COPY dl-engine/package.json ./dl-engine/
COPY dl-engine/server/package.json ./dl-engine/server/
COPY dl-engine/server/instance/package.json ./dl-engine/server/instance/
COPY dl-engine/engine/package.json ./dl-engine/engine/
COPY dl-engine/shared/package.json ./dl-engine/shared/

# 安装依赖
RUN pnpm install --frozen-lockfile

# 复制源代码
COPY dl-engine/shared ./dl-engine/shared
COPY dl-engine/engine ./dl-engine/engine
COPY dl-engine/server/instance ./dl-engine/server/instance
COPY tsconfig.json ./
COPY dl-engine/tsconfig.json ./dl-engine/

# 构建应用
RUN pnpm --filter @dl-engine/instance build

# 生产阶段
FROM node:22-alpine AS production

# 创建非root用户
RUN addgroup -g 1001 -S nodejs && \
    adduser -S dlengine -u 1001

# 安装必要的系统包和Agones SDK
RUN apk add --no-cache \
    dumb-init \
    curl \
    ca-certificates \
    wget \
    unzip

# 下载并安装Agones SDK
RUN wget -O /tmp/agones-sdk.zip https://github.com/googleforgames/agones/releases/download/v1.26.0/agones-sdk-1.26.0-linux-amd64.zip && \
    unzip /tmp/agones-sdk.zip -d /tmp && \
    mv /tmp/agones-sdk-1.26.0-linux-amd64/sdk-server.linux.amd64 /usr/local/bin/agones-sdk && \
    chmod +x /usr/local/bin/agones-sdk && \
    rm -rf /tmp/agones-sdk.zip /tmp/agones-sdk-*

# 设置工作目录
WORKDIR /app

# 安装pnpm
RUN npm install -g pnpm

# 复制package文件
COPY --chown=dlengine:nodejs package.json pnpm-lock.yaml pnpm-workspace.yaml ./
COPY --chown=dlengine:nodejs dl-engine/package.json ./dl-engine/
COPY --chown=dlengine:nodejs dl-engine/server/package.json ./dl-engine/server/
COPY --chown=dlengine:nodejs dl-engine/server/instance/package.json ./dl-engine/server/instance/
COPY --chown=dlengine:nodejs dl-engine/engine/package.json ./dl-engine/engine/
COPY --chown=dlengine:nodejs dl-engine/shared/package.json ./dl-engine/shared/

# 安装生产依赖
RUN pnpm install --frozen-lockfile --prod

# 复制构建产物
COPY --from=builder --chown=dlengine:nodejs /app/dl-engine/server/instance/dist ./dl-engine/server/instance/dist
COPY --from=builder --chown=dlengine:nodejs /app/dl-engine/engine/dist ./dl-engine/engine/dist
COPY --from=builder --chown=dlengine:nodejs /app/dl-engine/shared/dist ./dl-engine/shared/dist

# 复制配置文件
COPY --chown=dlengine:nodejs dl-engine/server/instance/config ./dl-engine/server/instance/config

# 创建必要的目录
RUN mkdir -p /app/logs /app/tmp /app/data && \
    chown -R dlengine:nodejs /app/logs /app/tmp /app/data

# 设置环境变量
ENV NODE_ENV=production
ENV PORT=7777
ENV WEBSOCKET_PORT=8080
ENV WEBRTC_PORT=8081
ENV LOG_LEVEL=info
ENV MAX_PLAYERS=50
ENV SESSION_TIMEOUT=300000
ENV PHYSICS_TICK_RATE=60
ENV NETWORK_TICK_RATE=20
ENV ENABLE_VOICE_CHAT=true
ENV ENABLE_VIDEO_CHAT=true
ENV ENABLE_SCREEN_SHARE=true
ENV AGONES_SDK_LOG_LEVEL=info

# 暴露端口
EXPOSE 7777/udp 8080 8081

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=10s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# 切换到非root用户
USER dlengine

# 启动脚本
COPY --chown=dlengine:nodejs dl-engine/server/deployment/docker/scripts/start-instance.sh ./start-instance.sh
RUN chmod +x ./start-instance.sh

# 启动应用
ENTRYPOINT ["dumb-init", "--"]
CMD ["./start-instance.sh"]

# 标签信息
LABEL maintainer="DL-Engine Team <<EMAIL>>"
LABEL version="1.0.0"
LABEL description="DL-Engine Instance Service with Agones Support"
LABEL org.opencontainers.image.title="DL-Engine Instance"
LABEL org.opencontainers.image.description="Digital Learning Engine Instance Service for Real-time Collaboration"
LABEL org.opencontainers.image.version="1.0.0"
LABEL org.opencontainers.image.vendor="DL-Engine"
LABEL org.opencontainers.image.licenses="MIT"
LABEL org.opencontainers.image.source="https://github.com/dl-engine/dl-engine"
