/**
 * DL-Engine 文件上传服务
 * 
 * 核心功能：
 * - 分片上传和断点续传
 * - 文件校验和去重
 * - 权限控制和安全检查
 * - 多种存储后端支持
 */

import { Injectable, BadRequestException, UnauthorizedException, NotFoundException } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { Repository } from 'typeorm'
import { ConfigService } from '@nestjs/config'
import * as crypto from 'crypto'
import * as path from 'path'
import * as fs from 'fs/promises'
import { Client as MinioClient } from 'minio'

import { MediaFile } from '../entities/media-file.entity'
import { UploadChunkDto, InitiateUploadDto, CompleteUploadDto } from './dto/upload.dto'

export interface UploadSession {
  uploadId: string
  fileName: string
  fileSize: number
  chunkSize: number
  totalChunks: number
  uploadedChunks: Set<number>
  filePath: string
  hash: string
  userId: string
  createdAt: Date
  expiresAt: Date
}

@Injectable()
export class UploadService {
  private uploadSessions = new Map<string, UploadSession>()
  private minioClient: MinioClient

  constructor(
    @InjectRepository(MediaFile)
    private readonly mediaFileRepository: Repository<MediaFile>,
    private readonly configService: ConfigService
  ) {
    this.initializeMinioClient()
    this.startCleanupTask()
  }

  /**
   * 初始化分片上传
   */
  async initiateUpload(userId: string, dto: InitiateUploadDto) {
    const { fileName, fileSize, chunkSize = 1024 * 1024 * 5, contentType } = dto // 默认5MB分片

    // 验证文件类型
    this.validateFileType(fileName, contentType)

    // 验证文件大小
    this.validateFileSize(fileSize)

    // 生成上传ID
    const uploadId = this.generateUploadId()
    const totalChunks = Math.ceil(fileSize / chunkSize)
    const filePath = this.generateFilePath(uploadId, fileName)
    const hash = crypto.createHash('sha256').update(`${uploadId}${fileName}${fileSize}`).digest('hex')

    // 创建上传会话
    const session: UploadSession = {
      uploadId,
      fileName,
      fileSize,
      chunkSize,
      totalChunks,
      uploadedChunks: new Set(),
      filePath,
      hash,
      userId,
      createdAt: new Date(),
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24小时过期
    }

    this.uploadSessions.set(uploadId, session)

    // 创建临时目录
    await this.ensureDirectoryExists(path.dirname(filePath))

    return {
      uploadId,
      chunkSize,
      totalChunks,
      expiresAt: session.expiresAt
    }
  }

  /**
   * 上传文件分片
   */
  async uploadChunk(uploadId: string, chunkIndex: number, chunkData: Buffer) {
    const session = this.uploadSessions.get(uploadId)
    if (!session) {
      throw new NotFoundException('上传会话不存在或已过期')
    }

    if (session.expiresAt < new Date()) {
      this.uploadSessions.delete(uploadId)
      throw new BadRequestException('上传会话已过期')
    }

    if (chunkIndex < 0 || chunkIndex >= session.totalChunks) {
      throw new BadRequestException('分片索引无效')
    }

    // 验证分片大小
    const expectedSize = chunkIndex === session.totalChunks - 1 
      ? session.fileSize - (chunkIndex * session.chunkSize)
      : session.chunkSize

    if (chunkData.length !== expectedSize) {
      throw new BadRequestException('分片大小不匹配')
    }

    // 保存分片
    const chunkPath = `${session.filePath}.chunk.${chunkIndex}`
    await fs.writeFile(chunkPath, chunkData)

    // 记录已上传分片
    session.uploadedChunks.add(chunkIndex)

    return {
      chunkIndex,
      uploaded: true,
      progress: session.uploadedChunks.size / session.totalChunks
    }
  }

  /**
   * 完成文件上传
   */
  async completeUpload(uploadId: string, dto: CompleteUploadDto) {
    const session = this.uploadSessions.get(uploadId)
    if (!session) {
      throw new NotFoundException('上传会话不存在或已过期')
    }

    // 检查所有分片是否已上传
    if (session.uploadedChunks.size !== session.totalChunks) {
      throw new BadRequestException('文件上传未完成')
    }

    try {
      // 合并分片
      const finalPath = await this.mergeChunks(session)

      // 计算文件哈希
      const fileHash = await this.calculateFileHash(finalPath)

      // 验证文件完整性
      if (dto.expectedHash && dto.expectedHash !== fileHash) {
        throw new BadRequestException('文件完整性校验失败')
      }

      // 检查文件是否已存在（去重）
      const existingFile = await this.mediaFileRepository.findOne({
        where: { hash: fileHash }
      })

      if (existingFile) {
        // 文件已存在，删除临时文件
        await this.cleanupTempFiles(session)
        this.uploadSessions.delete(uploadId)

        return {
          fileId: existingFile.id,
          fileName: existingFile.originalName,
          fileSize: existingFile.size,
          hash: existingFile.hash,
          url: existingFile.url,
          duplicate: true
        }
      }

      // 上传到对象存储
      const objectKey = this.generateObjectKey(fileHash, session.fileName)
      const url = await this.uploadToStorage(finalPath, objectKey)

      // 保存文件记录
      const mediaFile = this.mediaFileRepository.create({
        originalName: session.fileName,
        fileName: objectKey,
        size: session.fileSize,
        mimeType: dto.contentType || this.getMimeType(session.fileName),
        hash: fileHash,
        url,
        uploadedBy: session.userId,
        uploadedAt: new Date(),
        status: 'completed'
      })

      const savedFile = await this.mediaFileRepository.save(mediaFile)

      // 清理临时文件和会话
      await this.cleanupTempFiles(session)
      this.uploadSessions.delete(uploadId)

      return {
        fileId: savedFile.id,
        fileName: savedFile.originalName,
        fileSize: savedFile.size,
        hash: savedFile.hash,
        url: savedFile.url,
        duplicate: false
      }

    } catch (error) {
      // 清理临时文件
      await this.cleanupTempFiles(session)
      this.uploadSessions.delete(uploadId)
      throw error
    }
  }

  /**
   * 获取上传进度
   */
  async getUploadProgress(uploadId: string) {
    const session = this.uploadSessions.get(uploadId)
    if (!session) {
      throw new NotFoundException('上传会话不存在')
    }

    return {
      uploadId,
      fileName: session.fileName,
      fileSize: session.fileSize,
      totalChunks: session.totalChunks,
      uploadedChunks: session.uploadedChunks.size,
      progress: session.uploadedChunks.size / session.totalChunks,
      expiresAt: session.expiresAt
    }
  }

  /**
   * 取消上传
   */
  async cancelUpload(uploadId: string) {
    const session = this.uploadSessions.get(uploadId)
    if (!session) {
      throw new NotFoundException('上传会话不存在')
    }

    // 清理临时文件
    await this.cleanupTempFiles(session)
    this.uploadSessions.delete(uploadId)

    return { cancelled: true }
  }

  // 私有方法

  private initializeMinioClient() {
    this.minioClient = new MinioClient({
      endPoint: this.configService.get('MINIO_ENDPOINT', 'localhost'),
      port: parseInt(this.configService.get('MINIO_PORT', '9000')),
      useSSL: this.configService.get('MINIO_USE_SSL', 'false') === 'true',
      accessKey: this.configService.get('MINIO_ACCESS_KEY', 'minioadmin'),
      secretKey: this.configService.get('MINIO_SECRET_KEY', 'minioadmin')
    })
  }

  private validateFileType(fileName: string, contentType?: string) {
    const allowedExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.mp4', '.webm', '.mp3', '.wav', '.ogg', '.gltf', '.glb', '.fbx', '.obj']
    const ext = path.extname(fileName).toLowerCase()
    
    if (!allowedExtensions.includes(ext)) {
      throw new BadRequestException(`不支持的文件类型: ${ext}`)
    }
  }

  private validateFileSize(fileSize: number) {
    const maxSize = this.configService.get('MAX_FILE_SIZE', 100 * 1024 * 1024) // 默认100MB
    if (fileSize > maxSize) {
      throw new BadRequestException(`文件大小超过限制: ${maxSize / 1024 / 1024}MB`)
    }
  }

  private generateUploadId(): string {
    return crypto.randomBytes(16).toString('hex')
  }

  private generateFilePath(uploadId: string, fileName: string): string {
    const tempDir = this.configService.get('TEMP_UPLOAD_DIR', '/tmp/uploads')
    return path.join(tempDir, uploadId, fileName)
  }

  private async ensureDirectoryExists(dirPath: string) {
    try {
      await fs.access(dirPath)
    } catch {
      await fs.mkdir(dirPath, { recursive: true })
    }
  }

  private async mergeChunks(session: UploadSession): Promise<string> {
    const finalPath = session.filePath
    const writeStream = await fs.open(finalPath, 'w')

    try {
      for (let i = 0; i < session.totalChunks; i++) {
        const chunkPath = `${session.filePath}.chunk.${i}`
        const chunkData = await fs.readFile(chunkPath)
        await writeStream.write(chunkData, 0, chunkData.length, i * session.chunkSize)
        await fs.unlink(chunkPath) // 删除分片文件
      }
    } finally {
      await writeStream.close()
    }

    return finalPath
  }

  private async calculateFileHash(filePath: string): Promise<string> {
    const data = await fs.readFile(filePath)
    return crypto.createHash('sha256').update(data).digest('hex')
  }

  private generateObjectKey(hash: string, fileName: string): string {
    const ext = path.extname(fileName)
    const date = new Date().toISOString().split('T')[0]
    return `media/${date}/${hash}${ext}`
  }

  private async uploadToStorage(filePath: string, objectKey: string): Promise<string> {
    const bucketName = this.configService.get('MINIO_BUCKET', 'dl-engine-media')
    
    // 确保存储桶存在
    const bucketExists = await this.minioClient.bucketExists(bucketName)
    if (!bucketExists) {
      await this.minioClient.makeBucket(bucketName)
    }

    // 上传文件
    await this.minioClient.fPutObject(bucketName, objectKey, filePath)

    // 返回访问URL
    return `${this.configService.get('MINIO_PUBLIC_URL', 'http://localhost:9000')}/${bucketName}/${objectKey}`
  }

  private getMimeType(fileName: string): string {
    const ext = path.extname(fileName).toLowerCase()
    const mimeTypes: Record<string, string> = {
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.png': 'image/png',
      '.gif': 'image/gif',
      '.webp': 'image/webp',
      '.mp4': 'video/mp4',
      '.webm': 'video/webm',
      '.mp3': 'audio/mpeg',
      '.wav': 'audio/wav',
      '.ogg': 'audio/ogg',
      '.gltf': 'model/gltf+json',
      '.glb': 'model/gltf-binary',
      '.fbx': 'application/octet-stream',
      '.obj': 'application/octet-stream'
    }
    return mimeTypes[ext] || 'application/octet-stream'
  }

  private async cleanupTempFiles(session: UploadSession) {
    try {
      // 删除主文件
      await fs.unlink(session.filePath).catch(() => {})
      
      // 删除分片文件
      for (let i = 0; i < session.totalChunks; i++) {
        const chunkPath = `${session.filePath}.chunk.${i}`
        await fs.unlink(chunkPath).catch(() => {})
      }

      // 删除目录
      const dir = path.dirname(session.filePath)
      await fs.rmdir(dir).catch(() => {})
    } catch (error) {
      console.error('清理临时文件失败:', error)
    }
  }

  private startCleanupTask() {
    // 每小时清理过期的上传会话
    setInterval(() => {
      const now = new Date()
      for (const [uploadId, session] of this.uploadSessions.entries()) {
        if (session.expiresAt < now) {
          this.cleanupTempFiles(session)
          this.uploadSessions.delete(uploadId)
        }
      }
    }, 60 * 60 * 1000) // 1小时
  }
}
