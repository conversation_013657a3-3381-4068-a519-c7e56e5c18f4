import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
  Index,
} from 'typeorm';
import { UserEntity } from './user.entity';
import { SceneEntity } from './scene.entity';
import { AssetEntity } from './asset.entity';

export enum ProjectType {
  SCENE_3D = 'scene_3d',
  VR_EXPERIENCE = 'vr_experience',
  AR_EXPERIENCE = 'ar_experience',
  EDUCATIONAL_CONTENT = 'educational_content',
  INTERACTIVE_LESSON = 'interactive_lesson',
}

export enum ProjectStatus {
  DRAFT = 'draft',
  PUBLISHED = 'published',
  ARCHIVED = 'archived',
  DELETED = 'deleted',
}

export enum ProjectVisibility {
  PRIVATE = 'private',
  PUBLIC = 'public',
  SHARED = 'shared',
  ORGANIZATION = 'organization',
}

@Entity('projects')
@Index(['ownerId'])
@Index(['type'])
@Index(['status'])
@Index(['visibility'])
export class ProjectEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 200 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'enum', enum: ProjectType })
  type: ProjectType;

  @Column({ type: 'enum', enum: ProjectStatus, default: ProjectStatus.DRAFT })
  status: ProjectStatus;

  @Column({ type: 'enum', enum: ProjectVisibility, default: ProjectVisibility.PRIVATE })
  visibility: ProjectVisibility;

  @Column({ type: 'varchar', length: 500, nullable: true })
  thumbnail: string;

  @Column({ type: 'json', nullable: true })
  metadata: {
    version: string;
    engineVersion: string;
    tags: string[];
    category: string;
    difficulty: 'beginner' | 'intermediate' | 'advanced';
    estimatedTime: number; // 分钟
    targetAudience: string[];
    learningObjectives: string[];
  };

  @Column({ type: 'json', nullable: true })
  settings: {
    physics: {
      enabled: boolean;
      gravity: number[];
      timeStep: number;
    };
    rendering: {
      quality: 'low' | 'medium' | 'high' | 'ultra';
      shadows: boolean;
      postProcessing: boolean;
    };
    audio: {
      enabled: boolean;
      spatialAudio: boolean;
      volume: number;
    };
    vr: {
      enabled: boolean;
      roomScale: boolean;
      handTracking: boolean;
    };
    ar: {
      enabled: boolean;
      planeDetection: boolean;
      lightEstimation: boolean;
    };
  };

  @Column({ type: 'json', nullable: true })
  collaborators: {
    userId: string;
    role: 'viewer' | 'editor' | 'admin';
    permissions: string[];
    addedAt: Date;
  }[];

  @Column({ type: 'bigint', default: 0 })
  fileSize: number;

  @Column({ type: 'int', default: 0 })
  viewCount: number;

  @Column({ type: 'int', default: 0 })
  downloadCount: number;

  @Column({ type: 'int', default: 0 })
  likeCount: number;

  @Column({ type: 'datetime', nullable: true })
  publishedAt: Date;

  @Column({ type: 'datetime', nullable: true })
  lastAccessedAt: Date;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // 关联关系
  @Column({ type: 'uuid' })
  ownerId: string;

  @ManyToOne(() => UserEntity, (user) => user.projects)
  @JoinColumn({ name: 'ownerId' })
  owner: UserEntity;

  @OneToMany(() => SceneEntity, (scene) => scene.project)
  scenes: SceneEntity[];

  @OneToMany(() => AssetEntity, (asset) => asset.project)
  assets: AssetEntity[];

  // 虚拟字段
  get isPublished(): boolean {
    return this.status === ProjectStatus.PUBLISHED;
  }

  get isPublic(): boolean {
    return this.visibility === ProjectVisibility.PUBLIC;
  }

  get canEdit(): boolean {
    return this.status !== ProjectStatus.ARCHIVED && this.status !== ProjectStatus.DELETED;
  }
}
