-- DL-Engine 数据库性能优化脚本
-- 包括索引优化、查询优化和性能监控

-- =============================================
-- 索引优化
-- =============================================

-- 用户表索引
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_phone ON users(phone);
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at);
CREATE INDEX IF NOT EXISTS idx_users_status ON users(status);

-- 课程表索引
CREATE INDEX IF NOT EXISTS idx_courses_instructor_id ON courses(instructor_id);
CREATE INDEX IF NOT EXISTS idx_courses_category ON courses(category);
CREATE INDEX IF NOT EXISTS idx_courses_level ON courses(level);
CREATE INDEX IF NOT EXISTS idx_courses_status ON courses(status);
CREATE INDEX IF NOT EXISTS idx_courses_created_at ON courses(created_at);
CREATE INDEX IF NOT EXISTS idx_courses_published_at ON courses(published_at);
CREATE INDEX IF NOT EXISTS idx_courses_rating ON courses(rating);

-- 课程标签复合索引
CREATE INDEX IF NOT EXISTS idx_courses_category_level ON courses(category, level);
CREATE INDEX IF NOT EXISTS idx_courses_status_published ON courses(status, published_at);

-- 课程报名表索引
CREATE INDEX IF NOT EXISTS idx_enrollments_user_id ON enrollments(user_id);
CREATE INDEX IF NOT EXISTS idx_enrollments_course_id ON enrollments(course_id);
CREATE INDEX IF NOT EXISTS idx_enrollments_status ON enrollments(status);
CREATE INDEX IF NOT EXISTS idx_enrollments_enrolled_at ON enrollments(enrolled_at);

-- 复合索引用于常见查询
CREATE INDEX IF NOT EXISTS idx_enrollments_user_status ON enrollments(user_id, status);
CREATE INDEX IF NOT EXISTS idx_enrollments_course_status ON enrollments(course_id, status);

-- 学习进度表索引
CREATE INDEX IF NOT EXISTS idx_learning_progress_user_id ON learning_progress(user_id);
CREATE INDEX IF NOT EXISTS idx_learning_progress_course_id ON learning_progress(course_id);
CREATE INDEX IF NOT EXISTS idx_learning_progress_lesson_id ON learning_progress(lesson_id);
CREATE INDEX IF NOT EXISTS idx_learning_progress_status ON learning_progress(status);
CREATE INDEX IF NOT EXISTS idx_learning_progress_updated_at ON learning_progress(updated_at);

-- 复合索引用于进度查询
CREATE INDEX IF NOT EXISTS idx_learning_progress_user_course ON learning_progress(user_id, course_id);
CREATE INDEX IF NOT EXISTS idx_learning_progress_user_lesson ON learning_progress(user_id, lesson_id);

-- 场景表索引
CREATE INDEX IF NOT EXISTS idx_scenes_creator_id ON scenes(creator_id);
CREATE INDEX IF NOT EXISTS idx_scenes_template_id ON scenes(template_id);
CREATE INDEX IF NOT EXISTS idx_scenes_status ON scenes(status);
CREATE INDEX IF NOT EXISTS idx_scenes_created_at ON scenes(created_at);
CREATE INDEX IF NOT EXISTS idx_scenes_is_public ON scenes(is_public);

-- 场景对象表索引
CREATE INDEX IF NOT EXISTS idx_scene_objects_scene_id ON scene_objects(scene_id);
CREATE INDEX IF NOT EXISTS idx_scene_objects_type ON scene_objects(type);
CREATE INDEX IF NOT EXISTS idx_scene_objects_created_at ON scene_objects(created_at);

-- 协作会话表索引
CREATE INDEX IF NOT EXISTS idx_collaboration_sessions_host_id ON collaboration_sessions(host_id);
CREATE INDEX IF NOT EXISTS idx_collaboration_sessions_scene_id ON collaboration_sessions(scene_id);
CREATE INDEX IF NOT EXISTS idx_collaboration_sessions_status ON collaboration_sessions(status);
CREATE INDEX IF NOT EXISTS idx_collaboration_sessions_created_at ON collaboration_sessions(created_at);
CREATE INDEX IF NOT EXISTS idx_collaboration_sessions_start_time ON collaboration_sessions(start_time);

-- 会话参与者表索引
CREATE INDEX IF NOT EXISTS idx_session_participants_session_id ON session_participants(session_id);
CREATE INDEX IF NOT EXISTS idx_session_participants_user_id ON session_participants(user_id);
CREATE INDEX IF NOT EXISTS idx_session_participants_role ON session_participants(role);
CREATE INDEX IF NOT EXISTS idx_session_participants_joined_at ON session_participants(joined_at);

-- 文件表索引
CREATE INDEX IF NOT EXISTS idx_files_uploader_id ON files(uploader_id);
CREATE INDEX IF NOT EXISTS idx_files_type ON files(type);
CREATE INDEX IF NOT EXISTS idx_files_status ON files(status);
CREATE INDEX IF NOT EXISTS idx_files_created_at ON files(created_at);
CREATE INDEX IF NOT EXISTS idx_files_size ON files(size);

-- 日志表索引（按时间分区）
CREATE INDEX IF NOT EXISTS idx_logs_level ON logs(level);
CREATE INDEX IF NOT EXISTS idx_logs_service ON logs(service);
CREATE INDEX IF NOT EXISTS idx_logs_user_id ON logs(user_id);
CREATE INDEX IF NOT EXISTS idx_logs_created_at ON logs(created_at);
CREATE INDEX IF NOT EXISTS idx_logs_request_id ON logs(request_id);

-- =============================================
-- 查询优化视图
-- =============================================

-- 课程统计视图
CREATE OR REPLACE VIEW course_stats AS
SELECT 
    c.id,
    c.title,
    c.instructor_id,
    c.category,
    c.level,
    COUNT(DISTINCT e.user_id) as enrollment_count,
    AVG(r.rating) as avg_rating,
    COUNT(DISTINCT r.id) as review_count,
    c.created_at,
    c.updated_at
FROM courses c
LEFT JOIN enrollments e ON c.id = e.course_id AND e.status = 'active'
LEFT JOIN course_reviews r ON c.id = r.course_id
WHERE c.status = 'published'
GROUP BY c.id, c.title, c.instructor_id, c.category, c.level, c.created_at, c.updated_at;

-- 用户学习统计视图
CREATE OR REPLACE VIEW user_learning_stats AS
SELECT 
    u.id as user_id,
    u.username,
    COUNT(DISTINCT e.course_id) as enrolled_courses,
    COUNT(DISTINCT CASE WHEN lp.status = 'completed' THEN lp.course_id END) as completed_courses,
    AVG(CASE WHEN lp.status = 'completed' THEN lp.progress END) as avg_progress,
    SUM(lp.time_spent) as total_time_spent,
    MAX(lp.updated_at) as last_activity
FROM users u
LEFT JOIN enrollments e ON u.id = e.user_id AND e.status = 'active'
LEFT JOIN learning_progress lp ON u.id = lp.user_id
WHERE u.status = 'active'
GROUP BY u.id, u.username;

-- 热门课程视图
CREATE OR REPLACE VIEW popular_courses AS
SELECT 
    c.*,
    cs.enrollment_count,
    cs.avg_rating,
    cs.review_count
FROM courses c
JOIN course_stats cs ON c.id = cs.id
WHERE c.status = 'published'
ORDER BY cs.enrollment_count DESC, cs.avg_rating DESC
LIMIT 50;

-- =============================================
-- 性能监控存储过程
-- =============================================

DELIMITER //

-- 获取慢查询统计
CREATE PROCEDURE GetSlowQueryStats()
BEGIN
    SELECT 
        sql_text,
        exec_count,
        avg_timer_wait/1000000000 as avg_duration_seconds,
        max_timer_wait/1000000000 as max_duration_seconds,
        sum_timer_wait/1000000000 as total_duration_seconds
    FROM performance_schema.events_statements_summary_by_digest
    WHERE avg_timer_wait > 1000000000  -- 大于1秒的查询
    ORDER BY avg_timer_wait DESC
    LIMIT 20;
END //

-- 获取表空间使用情况
CREATE PROCEDURE GetTableSpaceUsage()
BEGIN
    SELECT 
        table_schema,
        table_name,
        ROUND(((data_length + index_length) / 1024 / 1024), 2) as size_mb,
        table_rows,
        ROUND((data_length / 1024 / 1024), 2) as data_size_mb,
        ROUND((index_length / 1024 / 1024), 2) as index_size_mb
    FROM information_schema.tables
    WHERE table_schema = DATABASE()
    ORDER BY (data_length + index_length) DESC;
END //

-- 获取索引使用情况
CREATE PROCEDURE GetIndexUsage()
BEGIN
    SELECT 
        object_schema,
        object_name,
        index_name,
        count_read,
        count_write,
        count_fetch,
        count_insert,
        count_update,
        count_delete
    FROM performance_schema.table_io_waits_summary_by_index_usage
    WHERE object_schema = DATABASE()
    ORDER BY count_read DESC;
END //

DELIMITER ;

-- =============================================
-- 数据清理和归档
-- =============================================

-- 清理过期日志（保留30天）
CREATE EVENT IF NOT EXISTS cleanup_old_logs
ON SCHEDULE EVERY 1 DAY
STARTS CURRENT_TIMESTAMP
DO
    DELETE FROM logs 
    WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY);

-- 清理过期会话数据（保留7天）
CREATE EVENT IF NOT EXISTS cleanup_old_sessions
ON SCHEDULE EVERY 1 DAY
STARTS CURRENT_TIMESTAMP
DO
    DELETE FROM collaboration_sessions 
    WHERE status = 'ended' 
    AND end_time < DATE_SUB(NOW(), INTERVAL 7 DAY);

-- 清理临时文件记录（保留3天）
CREATE EVENT IF NOT EXISTS cleanup_temp_files
ON SCHEDULE EVERY 6 HOUR
STARTS CURRENT_TIMESTAMP
DO
    DELETE FROM files 
    WHERE type = 'temp' 
    AND created_at < DATE_SUB(NOW(), INTERVAL 3 DAY);

-- =============================================
-- 数据库配置优化建议
-- =============================================

/*
-- MySQL配置优化建议（my.cnf）

[mysqld]
# 基础配置
default-storage-engine = InnoDB
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci

# 内存配置
innodb_buffer_pool_size = 2G  # 设置为可用内存的70-80%
innodb_log_file_size = 256M
innodb_log_buffer_size = 64M
key_buffer_size = 256M
max_connections = 500
thread_cache_size = 50

# 性能配置
innodb_flush_log_at_trx_commit = 2
innodb_flush_method = O_DIRECT
innodb_file_per_table = 1
innodb_read_io_threads = 8
innodb_write_io_threads = 8

# 查询缓存
query_cache_type = 1
query_cache_size = 256M
query_cache_limit = 2M

# 慢查询日志
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2

# 二进制日志
log-bin = mysql-bin
binlog_format = ROW
expire_logs_days = 7

# 性能监控
performance_schema = ON
*/

-- =============================================
-- 分区表示例（大数据表优化）
-- =============================================

-- 日志表按月分区
ALTER TABLE logs PARTITION BY RANGE (YEAR(created_at) * 100 + MONTH(created_at)) (
    PARTITION p202401 VALUES LESS THAN (202402),
    PARTITION p202402 VALUES LESS THAN (202403),
    PARTITION p202403 VALUES LESS THAN (202404),
    PARTITION p202404 VALUES LESS THAN (202405),
    PARTITION p202405 VALUES LESS THAN (202406),
    PARTITION p202406 VALUES LESS THAN (202407),
    PARTITION p202407 VALUES LESS THAN (202408),
    PARTITION p202408 VALUES LESS THAN (202409),
    PARTITION p202409 VALUES LESS THAN (202410),
    PARTITION p202410 VALUES LESS THAN (202411),
    PARTITION p202411 VALUES LESS THAN (202412),
    PARTITION p202412 VALUES LESS THAN (202501),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);

-- =============================================
-- 数据库健康检查脚本
-- =============================================

-- 检查表碎片
SELECT 
    table_schema,
    table_name,
    ROUND(data_length/1024/1024,2) as data_size_mb,
    ROUND(data_free/1024/1024,2) as free_space_mb,
    ROUND(data_free/(data_length+index_length+data_free)*100,2) as fragmentation_percent
FROM information_schema.tables 
WHERE table_schema = DATABASE()
AND data_free > 0
ORDER BY fragmentation_percent DESC;

-- 检查未使用的索引
SELECT 
    object_schema,
    object_name,
    index_name
FROM performance_schema.table_io_waits_summary_by_index_usage 
WHERE object_schema = DATABASE()
AND index_name IS NOT NULL 
AND index_name != 'PRIMARY'
AND count_read = 0 
AND count_write = 0 
AND count_fetch = 0 
AND count_insert = 0 
AND count_update = 0 
AND count_delete = 0;

-- 检查重复索引
SELECT 
    a.table_schema,
    a.table_name,
    a.index_name as index1,
    b.index_name as index2,
    a.column_name
FROM information_schema.statistics a
JOIN information_schema.statistics b ON 
    a.table_schema = b.table_schema AND
    a.table_name = b.table_name AND
    a.column_name = b.column_name AND
    a.index_name != b.index_name
WHERE a.table_schema = DATABASE()
ORDER BY a.table_name, a.column_name;
