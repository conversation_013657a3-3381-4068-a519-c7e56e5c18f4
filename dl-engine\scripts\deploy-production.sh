#!/bin/bash

# DL-Engine 生产环境部署脚本
# 自动化部署完整的DL-Engine系统到Kubernetes集群

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
DEPLOYMENT_DIR="$PROJECT_ROOT/server/deployment"

# 默认配置
ENVIRONMENT=${ENVIRONMENT:-"production"}
NAMESPACE=${NAMESPACE:-"dl-engine"}
DOMAIN=${DOMAIN:-"dl-engine.org"}
CLUSTER_NAME=${CLUSTER_NAME:-"dl-engine-prod"}
REGION=${REGION:-"us-west-2"}
NODE_COUNT=${NODE_COUNT:-"5"}
NODE_TYPE=${NODE_TYPE:-"m5.xlarge"}

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

log_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

log_success() {
    echo -e "${CYAN}[SUCCESS]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
DL-Engine 生产环境部署脚本

用法: $0 [选项]

选项:
    -e, --environment ENV       部署环境 (默认: production)
    -n, --namespace NAMESPACE   Kubernetes命名空间 (默认: dl-engine)
    -d, --domain DOMAIN         域名 (默认: dl-engine.org)
    -c, --cluster CLUSTER       集群名称 (默认: dl-engine-prod)
    -r, --region REGION         AWS区域 (默认: us-west-2)
    --node-count COUNT          节点数量 (默认: 5)
    --node-type TYPE            节点类型 (默认: m5.xlarge)
    --skip-cluster              跳过集群创建
    --skip-database             跳过数据库初始化
    --skip-monitoring           跳过监控部署
    --dry-run                   仅显示将要执行的命令
    -h, --help                  显示帮助信息

示例:
    $0                          # 使用默认配置部署
    $0 -d mycompany.com         # 使用自定义域名部署
    $0 --skip-cluster           # 跳过集群创建，仅部署应用
    $0 --dry-run                # 预览部署命令

EOF
}

# 解析命令行参数
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -e|--environment)
                ENVIRONMENT="$2"
                shift 2
                ;;
            -n|--namespace)
                NAMESPACE="$2"
                shift 2
                ;;
            -d|--domain)
                DOMAIN="$2"
                shift 2
                ;;
            -c|--cluster)
                CLUSTER_NAME="$2"
                shift 2
                ;;
            -r|--region)
                REGION="$2"
                shift 2
                ;;
            --node-count)
                NODE_COUNT="$2"
                shift 2
                ;;
            --node-type)
                NODE_TYPE="$2"
                shift 2
                ;;
            --skip-cluster)
                SKIP_CLUSTER=true
                shift
                ;;
            --skip-database)
                SKIP_DATABASE=true
                shift
                ;;
            --skip-monitoring)
                SKIP_MONITORING=true
                shift
                ;;
            --dry-run)
                DRY_RUN=true
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
}

# 执行命令（支持dry-run）
execute_cmd() {
    local cmd="$1"
    local description="$2"
    
    if [ "$DRY_RUN" = true ]; then
        log_debug "[DRY-RUN] $description"
        log_debug "[DRY-RUN] Command: $cmd"
        return 0
    fi
    
    log_debug "执行: $description"
    log_debug "命令: $cmd"
    
    if eval "$cmd"; then
        log_success "$description 完成"
        return 0
    else
        log_error "$description 失败"
        return 1
    fi
}

# 检查依赖
check_dependencies() {
    log_step "检查依赖工具..."
    
    local deps=("kubectl" "helm" "aws" "docker" "jq" "yq")
    local missing_deps=()
    
    for dep in "${deps[@]}"; do
        if ! command -v "$dep" &> /dev/null; then
            missing_deps+=("$dep")
        fi
    done
    
    if [ ${#missing_deps[@]} -ne 0 ]; then
        log_error "缺少依赖工具: ${missing_deps[*]}"
        log_info "请安装缺少的工具后重试"
        exit 1
    fi
    
    log_success "所有依赖工具已安装"
}

# 验证AWS凭证
verify_aws_credentials() {
    log_step "验证AWS凭证..."
    
    if ! aws sts get-caller-identity &> /dev/null; then
        log_error "AWS凭证验证失败"
        log_info "请运行 'aws configure' 配置AWS凭证"
        exit 1
    fi
    
    local account_id=$(aws sts get-caller-identity --query Account --output text)
    local user_arn=$(aws sts get-caller-identity --query Arn --output text)
    
    log_success "AWS凭证验证成功"
    log_info "账户ID: $account_id"
    log_info "用户ARN: $user_arn"
}

# 创建EKS集群
create_eks_cluster() {
    if [ "$SKIP_CLUSTER" = true ]; then
        log_info "跳过集群创建"
        return 0
    fi
    
    log_step "创建EKS集群..."
    
    # 检查集群是否已存在
    if aws eks describe-cluster --name "$CLUSTER_NAME" --region "$REGION" &> /dev/null; then
        log_warn "集群 $CLUSTER_NAME 已存在，跳过创建"
        return 0
    fi
    
    # 创建集群配置文件
    cat > /tmp/cluster-config.yaml << EOF
apiVersion: eksctl.io/v1alpha5
kind: ClusterConfig

metadata:
  name: $CLUSTER_NAME
  region: $REGION
  version: "1.28"

iam:
  withOIDC: true

addons:
  - name: vpc-cni
    version: latest
  - name: coredns
    version: latest
  - name: kube-proxy
    version: latest
  - name: aws-ebs-csi-driver
    version: latest

nodeGroups:
  - name: dl-engine-workers
    instanceType: $NODE_TYPE
    desiredCapacity: $NODE_COUNT
    minSize: 2
    maxSize: 10
    volumeSize: 100
    volumeType: gp3
    amiFamily: AmazonLinux2
    iam:
      attachPolicyARNs:
        - arn:aws:iam::aws:policy/AmazonEKSWorkerNodePolicy
        - arn:aws:iam::aws:policy/AmazonEKS_CNI_Policy
        - arn:aws:iam::aws:policy/AmazonEC2ContainerRegistryReadOnly
        - arn:aws:iam::aws:policy/AmazonEBSCSIDriverPolicy
    labels:
      role: worker
      environment: $ENVIRONMENT
    tags:
      Environment: $ENVIRONMENT
      Project: dl-engine
      ManagedBy: eksctl

cloudWatch:
  clusterLogging:
    enable: ["api", "audit", "authenticator", "controllerManager", "scheduler"]
EOF

    execute_cmd "eksctl create cluster -f /tmp/cluster-config.yaml" "创建EKS集群"
    
    # 更新kubeconfig
    execute_cmd "aws eks update-kubeconfig --region $REGION --name $CLUSTER_NAME" "更新kubeconfig"
    
    # 验证集群连接
    execute_cmd "kubectl cluster-info" "验证集群连接"
    
    log_success "EKS集群创建完成"
}

# 安装必要的Kubernetes组件
install_k8s_components() {
    log_step "安装Kubernetes组件..."
    
    # 安装Nginx Ingress Controller
    execute_cmd "helm repo add ingress-nginx https://kubernetes.github.io/ingress-nginx" "添加Nginx Ingress仓库"
    execute_cmd "helm repo update" "更新Helm仓库"
    execute_cmd "helm upgrade --install ingress-nginx ingress-nginx/ingress-nginx \
        --namespace ingress-nginx \
        --create-namespace \
        --set controller.service.type=LoadBalancer \
        --set controller.service.annotations.'service\.beta\.kubernetes\.io/aws-load-balancer-type'=nlb" "安装Nginx Ingress"
    
    # 安装Cert-Manager
    execute_cmd "helm repo add jetstack https://charts.jetstack.io" "添加Cert-Manager仓库"
    execute_cmd "helm upgrade --install cert-manager jetstack/cert-manager \
        --namespace cert-manager \
        --create-namespace \
        --set installCRDs=true" "安装Cert-Manager"
    
    # 安装Agones
    execute_cmd "helm repo add agones https://agones.dev/chart/stable" "添加Agones仓库"
    execute_cmd "helm upgrade --install agones agones/agones \
        --namespace agones-system \
        --create-namespace" "安装Agones"
    
    # 安装AWS Load Balancer Controller
    execute_cmd "helm repo add eks https://aws.github.io/eks-charts" "添加EKS Charts仓库"
    
    local account_id=$(aws sts get-caller-identity --query Account --output text)
    execute_cmd "helm upgrade --install aws-load-balancer-controller eks/aws-load-balancer-controller \
        --namespace kube-system \
        --set clusterName=$CLUSTER_NAME \
        --set serviceAccount.create=false \
        --set serviceAccount.name=aws-load-balancer-controller \
        --set region=$REGION \
        --set vpcId=\$(aws eks describe-cluster --name $CLUSTER_NAME --region $REGION --query 'cluster.resourcesVpcConfig.vpcId' --output text)" "安装AWS Load Balancer Controller"
    
    log_success "Kubernetes组件安装完成"
}

# 创建命名空间和RBAC
setup_namespaces() {
    log_step "创建命名空间和RBAC..."
    
    # 创建命名空间
    execute_cmd "kubectl create namespace $NAMESPACE --dry-run=client -o yaml | kubectl apply -f -" "创建主命名空间"
    execute_cmd "kubectl create namespace ${NAMESPACE}-data --dry-run=client -o yaml | kubectl apply -f -" "创建数据命名空间"
    execute_cmd "kubectl create namespace ${NAMESPACE}-monitoring --dry-run=client -o yaml | kubectl apply -f -" "创建监控命名空间"
    execute_cmd "kubectl create namespace ${NAMESPACE}-edge --dry-run=client -o yaml | kubectl apply -f -" "创建边缘命名空间"
    
    # 应用RBAC配置
    execute_cmd "kubectl apply -f $DEPLOYMENT_DIR/security/security-hardening.yaml" "应用安全配置"
    
    log_success "命名空间和RBAC配置完成"
}

# 部署数据库
deploy_databases() {
    if [ "$SKIP_DATABASE" = true ]; then
        log_info "跳过数据库部署"
        return 0
    fi
    
    log_step "部署数据库..."
    
    # 部署MySQL
    execute_cmd "helm repo add bitnami https://charts.bitnami.com/bitnami" "添加Bitnami仓库"
    execute_cmd "helm upgrade --install mysql bitnami/mysql \
        --namespace ${NAMESPACE}-data \
        --set auth.rootPassword=\$(openssl rand -base64 32) \
        --set auth.database=dl_engine \
        --set auth.username=dl_engine \
        --set auth.password=\$(openssl rand -base64 32) \
        --set primary.persistence.size=100Gi \
        --set primary.resources.requests.memory=2Gi \
        --set primary.resources.requests.cpu=1000m" "部署MySQL"
    
    # 部署Redis
    execute_cmd "helm upgrade --install redis bitnami/redis \
        --namespace ${NAMESPACE}-data \
        --set auth.password=\$(openssl rand -base64 32) \
        --set master.persistence.size=20Gi \
        --set master.resources.requests.memory=1Gi \
        --set master.resources.requests.cpu=500m" "部署Redis"
    
    # 部署PostgreSQL (用于向量数据库)
    execute_cmd "helm upgrade --install postgresql bitnami/postgresql \
        --namespace ${NAMESPACE}-data \
        --set auth.postgresPassword=\$(openssl rand -base64 32) \
        --set auth.database=dl_engine_vector \
        --set auth.username=dl_engine \
        --set auth.password=\$(openssl rand -base64 32) \
        --set primary.persistence.size=50Gi \
        --set primary.resources.requests.memory=2Gi \
        --set primary.resources.requests.cpu=1000m" "部署PostgreSQL"
    
    # 部署MinIO
    execute_cmd "helm upgrade --install minio bitnami/minio \
        --namespace ${NAMESPACE}-data \
        --set auth.rootUser=dl-engine \
        --set auth.rootPassword=\$(openssl rand -base64 32) \
        --set persistence.size=500Gi \
        --set resources.requests.memory=2Gi \
        --set resources.requests.cpu=1000m" "部署MinIO"
    
    log_success "数据库部署完成"
}

# 部署DL-Engine应用
deploy_dl_engine() {
    log_step "部署DL-Engine应用..."
    
    # 构建和推送镜像
    execute_cmd "cd $PROJECT_ROOT && ./server/deployment/docker/registry/build-and-push.sh --push all" "构建和推送镜像"
    
    # 部署Helm Chart
    execute_cmd "helm upgrade --install dl-engine $DEPLOYMENT_DIR/kubernetes/helm/dl-engine-chart \
        --namespace $NAMESPACE \
        --set global.domain=$DOMAIN \
        --set global.environment=$ENVIRONMENT \
        --set ingress.hosts[0].host=$DOMAIN \
        --set ingress.tls[0].hosts[0]=$DOMAIN \
        --wait --timeout=10m" "部署DL-Engine应用"
    
    log_success "DL-Engine应用部署完成"
}

# 部署监控系统
deploy_monitoring() {
    if [ "$SKIP_MONITORING" = true ]; then
        log_info "跳过监控部署"
        return 0
    fi
    
    log_step "部署监控系统..."
    
    # 部署Prometheus
    execute_cmd "helm repo add prometheus-community https://prometheus-community.github.io/helm-charts" "添加Prometheus仓库"
    execute_cmd "helm upgrade --install prometheus prometheus-community/kube-prometheus-stack \
        --namespace ${NAMESPACE}-monitoring \
        --set prometheus.prometheusSpec.retention=30d \
        --set prometheus.prometheusSpec.storageSpec.volumeClaimTemplate.spec.resources.requests.storage=100Gi \
        --set grafana.adminPassword=\$(openssl rand -base64 32)" "部署Prometheus Stack"
    
    # 部署ELK Stack
    execute_cmd "helm repo add elastic https://helm.elastic.co" "添加Elastic仓库"
    execute_cmd "helm upgrade --install elasticsearch elastic/elasticsearch \
        --namespace ${NAMESPACE}-monitoring \
        --set replicas=3 \
        --set volumeClaimTemplate.resources.requests.storage=100Gi" "部署Elasticsearch"
    
    execute_cmd "helm upgrade --install kibana elastic/kibana \
        --namespace ${NAMESPACE}-monitoring" "部署Kibana"
    
    execute_cmd "helm upgrade --install logstash elastic/logstash \
        --namespace ${NAMESPACE}-monitoring" "部署Logstash"
    
    log_success "监控系统部署完成"
}

# 配置SSL证书
setup_ssl() {
    log_step "配置SSL证书..."
    
    # 创建ClusterIssuer
    cat > /tmp/cluster-issuer.yaml << EOF
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-prod
spec:
  acme:
    server: https://acme-v02.api.letsencrypt.org/directory
    email: admin@$DOMAIN
    privateKeySecretRef:
      name: letsencrypt-prod
    solvers:
    - http01:
        ingress:
          class: nginx
EOF
    
    execute_cmd "kubectl apply -f /tmp/cluster-issuer.yaml" "创建SSL证书颁发者"
    
    log_success "SSL证书配置完成"
}

# 验证部署
verify_deployment() {
    log_step "验证部署..."
    
    # 等待所有Pod就绪
    execute_cmd "kubectl wait --for=condition=ready pod --all -n $NAMESPACE --timeout=300s" "等待Pod就绪"
    
    # 检查服务状态
    execute_cmd "kubectl get pods,svc,ingress -n $NAMESPACE" "检查服务状态"
    
    # 获取Ingress地址
    local ingress_ip=$(kubectl get ingress -n $NAMESPACE -o jsonpath='{.items[0].status.loadBalancer.ingress[0].hostname}')
    if [ -n "$ingress_ip" ]; then
        log_success "部署验证完成"
        log_info "应用访问地址: https://$DOMAIN"
        log_info "Ingress地址: $ingress_ip"
    else
        log_warn "Ingress地址尚未分配，请稍后检查"
    fi
}

# 显示部署信息
show_deployment_info() {
    log_step "部署信息汇总..."
    
    echo -e "${CYAN}================================${NC}"
    echo -e "${CYAN}  DL-Engine 生产环境部署完成  ${NC}"
    echo -e "${CYAN}================================${NC}"
    echo ""
    echo -e "${GREEN}集群信息:${NC}"
    echo -e "  集群名称: $CLUSTER_NAME"
    echo -e "  区域: $REGION"
    echo -e "  命名空间: $NAMESPACE"
    echo -e "  域名: $DOMAIN"
    echo ""
    echo -e "${GREEN}访问地址:${NC}"
    echo -e "  应用: https://$DOMAIN"
    echo -e "  Grafana: https://grafana.$DOMAIN"
    echo -e "  Kibana: https://kibana.$DOMAIN"
    echo ""
    echo -e "${GREEN}管理命令:${NC}"
    echo -e "  查看Pod状态: kubectl get pods -n $NAMESPACE"
    echo -e "  查看日志: kubectl logs -f deployment/dl-engine-gateway -n $NAMESPACE"
    echo -e "  扩容服务: kubectl scale deployment dl-engine-api --replicas=5 -n $NAMESPACE"
    echo ""
    echo -e "${YELLOW}注意事项:${NC}"
    echo -e "  1. 请保存好数据库密码和其他敏感信息"
    echo -e "  2. 定期备份数据库和配置文件"
    echo -e "  3. 监控系统资源使用情况"
    echo -e "  4. 及时更新安全补丁"
    echo ""
}

# 主函数
main() {
    echo -e "${PURPLE}🚀 DL-Engine 生产环境部署脚本${NC}"
    echo -e "${PURPLE}================================${NC}"
    echo ""
    
    # 解析参数
    parse_args "$@"
    
    # 显示配置信息
    log_info "部署配置:"
    log_info "  环境: $ENVIRONMENT"
    log_info "  命名空间: $NAMESPACE"
    log_info "  域名: $DOMAIN"
    log_info "  集群: $CLUSTER_NAME"
    log_info "  区域: $REGION"
    echo ""
    
    # 确认部署
    if [ "$DRY_RUN" != true ]; then
        read -p "确认开始部署? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "部署已取消"
            exit 0
        fi
    fi
    
    # 执行部署步骤
    check_dependencies
    verify_aws_credentials
    create_eks_cluster
    install_k8s_components
    setup_namespaces
    deploy_databases
    deploy_dl_engine
    deploy_monitoring
    setup_ssl
    verify_deployment
    show_deployment_info
    
    log_success "🎉 DL-Engine 生产环境部署完成!"
}

# 错误处理
trap 'log_error "部署过程中发生错误，请检查日志"; exit 1' ERR

# 执行主函数
main "$@"
