import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@nestjs/typeorm'
import { ConfigModule } from '@nestjs/config'

import { Friendship } from './entities/friendship.entity'
import { Group } from './entities/group.entity'
import { GroupMember } from './entities/group-member.entity'
import { Message } from './entities/message.entity'
import { Conversation } from './entities/conversation.entity'
import { ConversationParticipant } from './entities/conversation-participant.entity'
import { Post } from './entities/post.entity'
import { Comment } from './entities/comment.entity'
import { Like } from './entities/like.entity'
import { Follow } from './entities/follow.entity'
import { Notification } from './entities/notification.entity'
import { Report } from './entities/report.entity'

import { FriendshipsController } from './controllers/friendships.controller'
import { GroupsController } from './controllers/groups.controller'
import { GroupMembersController } from './controllers/group-members.controller'
import { MessagesController } from './controllers/messages.controller'
import { ConversationsController } from './controllers/conversations.controller'
import { PostsController } from './controllers/posts.controller'
import { CommentsController } from './controllers/comments.controller'
import { LikesController } from './controllers/likes.controller'
import { FollowsController } from './controllers/follows.controller'
import { NotificationsController } from './controllers/notifications.controller'
import { ReportsController } from './controllers/reports.controller'

import { FriendshipsService } from './services/friendships.service'
import { GroupsService } from './services/groups.service'
import { GroupMembersService } from './services/group-members.service'
import { MessagesService } from './services/messages.service'
import { ConversationsService } from './services/conversations.service'
import { PostsService } from './services/posts.service'
import { CommentsService } from './services/comments.service'
import { LikesService } from './services/likes.service'
import { FollowsService } from './services/follows.service'
import { NotificationsService } from './services/notifications.service'
import { ReportsService } from './services/reports.service'
import { SocialValidationService } from './services/social-validation.service'
import { ContentModerationService } from './services/content-moderation.service'

import { UsersModule } from '../users/users.module'
import { NotificationModule } from '../notifications/notification.module'

/**
 * 社交功能模块
 * 
 * 适配教育场景的社交功能，包括：
 * - 好友系统和关注功能
 * - 群组管理和成员管理
 * - 消息通信和对话管理
 * - 内容发布和评论系统
 * - 点赞和互动功能
 * - 通知系统
 * - 内容审核和举报
 */
@Module({
  imports: [
    ConfigModule,
    TypeOrmModule.forFeature([
      Friendship,
      Group,
      GroupMember,
      Message,
      Conversation,
      ConversationParticipant,
      Post,
      Comment,
      Like,
      Follow,
      Notification,
      Report
    ]),
    UsersModule,
    NotificationModule
  ],
  controllers: [
    FriendshipsController,
    GroupsController,
    GroupMembersController,
    MessagesController,
    ConversationsController,
    PostsController,
    CommentsController,
    LikesController,
    FollowsController,
    NotificationsController,
    ReportsController
  ],
  providers: [
    FriendshipsService,
    GroupsService,
    GroupMembersService,
    MessagesService,
    ConversationsService,
    PostsService,
    CommentsService,
    LikesService,
    FollowsService,
    NotificationsService,
    ReportsService,
    SocialValidationService,
    ContentModerationService
  ],
  exports: [
    FriendshipsService,
    GroupsService,
    GroupMembersService,
    MessagesService,
    ConversationsService,
    PostsService,
    CommentsService,
    LikesService,
    FollowsService,
    NotificationsService,
    ReportsService,
    ContentModerationService
  ]
})
export class SocialModule {}
