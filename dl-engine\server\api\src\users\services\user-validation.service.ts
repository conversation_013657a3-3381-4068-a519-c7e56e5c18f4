import { Injectable, BadRequestException } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'

import { CreateUserDto } from '../dto/create-user.dto'
import { UpdateUserDto } from '../dto/update-user.dto'
import { User } from '../entities/user.entity'

/**
 * 用户验证服务
 * 
 * 提供用户数据的验证逻辑
 */
@Injectable()
export class UserValidationService {
  constructor(private readonly configService: ConfigService) {}

  /**
   * 验证创建用户的数据
   */
  async validateCreateUser(createUserDto: CreateUserDto): Promise<void> {
    const errors: string[] = []

    // 验证显示名称
    if (!createUserDto.displayName || createUserDto.displayName.trim().length === 0) {
      errors.push('显示名称不能为空')
    } else if (createUserDto.displayName.length > 100) {
      errors.push('显示名称不能超过100个字符')
    }

    // 验证手机号
    if (createUserDto.phone) {
      if (!this.isValidPhone(createUserDto.phone)) {
        errors.push('手机号格式不正确')
      }
    }

    // 验证邮箱
    if (createUserDto.email) {
      if (!this.isValidEmail(createUserDto.email)) {
        errors.push('邮箱格式不正确')
      }
    }

    // 至少需要手机号或邮箱之一
    if (!createUserDto.phone && !createUserDto.email) {
      errors.push('必须提供手机号或邮箱')
    }

    // 验证用户名
    if (createUserDto.username) {
      if (!this.isValidUsername(createUserDto.username)) {
        errors.push('用户名格式不正确，只能包含字母、数字、下划线和连字符，长度3-50个字符')
      }
    }

    // 验证国家代码
    if (createUserDto.countryCode && !this.isValidCountryCode(createUserDto.countryCode)) {
      errors.push('国家代码格式不正确')
    }

    // 验证服务条款
    if (!createUserDto.termsAccepted) {
      errors.push('必须接受服务条款')
    }

    if (errors.length > 0) {
      throw new BadRequestException({
        message: '用户数据验证失败',
        errors
      })
    }
  }

  /**
   * 验证更新用户的数据
   */
  async validateUpdateUser(updateUserDto: UpdateUserDto, existingUser: User): Promise<void> {
    const errors: string[] = []

    // 验证显示名称
    if (updateUserDto.displayName !== undefined) {
      if (!updateUserDto.displayName || updateUserDto.displayName.trim().length === 0) {
        errors.push('显示名称不能为空')
      } else if (updateUserDto.displayName.length > 100) {
        errors.push('显示名称不能超过100个字符')
      }
    }

    // 验证手机号
    if (updateUserDto.phone !== undefined) {
      if (updateUserDto.phone && !this.isValidPhone(updateUserDto.phone)) {
        errors.push('手机号格式不正确')
      }
    }

    // 验证邮箱
    if (updateUserDto.email !== undefined) {
      if (updateUserDto.email && !this.isValidEmail(updateUserDto.email)) {
        errors.push('邮箱格式不正确')
      }
    }

    // 验证用户名
    if (updateUserDto.username !== undefined) {
      if (updateUserDto.username && !this.isValidUsername(updateUserDto.username)) {
        errors.push('用户名格式不正确，只能包含字母、数字、下划线和连字符，长度3-50个字符')
      }
    }

    // 验证国家代码
    if (updateUserDto.countryCode && !this.isValidCountryCode(updateUserDto.countryCode)) {
      errors.push('国家代码格式不正确')
    }

    // 确保至少有一种联系方式
    const finalPhone = updateUserDto.phone !== undefined ? updateUserDto.phone : existingUser.phone
    const finalEmail = updateUserDto.email !== undefined ? updateUserDto.email : existingUser.email
    
    if (!finalPhone && !finalEmail) {
      errors.push('必须保留至少一种联系方式（手机号或邮箱）')
    }

    if (errors.length > 0) {
      throw new BadRequestException({
        message: '用户数据验证失败',
        errors
      })
    }
  }

  /**
   * 验证手机号格式
   */
  private isValidPhone(phone: string): boolean {
    // 中国大陆手机号格式：11位数字，以1开头
    const chinaPhoneRegex = /^1[3-9]\d{9}$/
    
    // 国际手机号格式：7-15位数字
    const internationalPhoneRegex = /^\d{7,15}$/
    
    return chinaPhoneRegex.test(phone) || internationalPhoneRegex.test(phone)
  }

  /**
   * 验证邮箱格式
   */
  private isValidEmail(email: string): boolean {
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
    return emailRegex.test(email) && email.length <= 255
  }

  /**
   * 验证用户名格式
   */
  private isValidUsername(username: string): boolean {
    // 用户名：3-50个字符，只能包含字母、数字、下划线和连字符
    const usernameRegex = /^[a-zA-Z0-9_-]{3,50}$/
    return usernameRegex.test(username)
  }

  /**
   * 验证国家代码格式
   */
  private isValidCountryCode(countryCode: string): boolean {
    // 国家代码：+号开头，后跟1-4位数字
    const countryCodeRegex = /^\+\d{1,4}$/
    return countryCodeRegex.test(countryCode)
  }

  /**
   * 验证密码强度（如果需要）
   */
  validatePasswordStrength(password: string): {
    isValid: boolean
    score: number
    feedback: string[]
  } {
    const feedback: string[] = []
    let score = 0

    // 长度检查
    if (password.length >= 8) {
      score += 1
    } else {
      feedback.push('密码长度至少8个字符')
    }

    // 包含小写字母
    if (/[a-z]/.test(password)) {
      score += 1
    } else {
      feedback.push('密码应包含小写字母')
    }

    // 包含大写字母
    if (/[A-Z]/.test(password)) {
      score += 1
    } else {
      feedback.push('密码应包含大写字母')
    }

    // 包含数字
    if (/\d/.test(password)) {
      score += 1
    } else {
      feedback.push('密码应包含数字')
    }

    // 包含特殊字符
    if (/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
      score += 1
    } else {
      feedback.push('密码应包含特殊字符')
    }

    // 长度奖励
    if (password.length >= 12) {
      score += 1
    }

    return {
      isValid: score >= 4,
      score,
      feedback
    }
  }

  /**
   * 验证显示名称
   */
  validateDisplayName(displayName: string): boolean {
    if (!displayName || displayName.trim().length === 0) {
      return false
    }

    // 检查长度
    if (displayName.length > 100) {
      return false
    }

    // 检查是否包含不当内容（简单检查）
    const inappropriateWords = ['admin', 'root', 'system', 'test']
    const lowerDisplayName = displayName.toLowerCase()
    
    return !inappropriateWords.some(word => lowerDisplayName.includes(word))
  }

  /**
   * 验证年龄
   */
  validateAge(birthDate: Date): boolean {
    const today = new Date()
    const birth = new Date(birthDate)
    const age = today.getFullYear() - birth.getFullYear()
    const monthDiff = today.getMonth() - birth.getMonth()
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--
    }

    // 年龄限制：6-120岁
    return age >= 6 && age <= 120
  }

  /**
   * 验证URL格式
   */
  validateUrl(url: string): boolean {
    try {
      new URL(url)
      return true
    } catch {
      return false
    }
  }

  /**
   * 清理和标准化手机号
   */
  normalizePhone(phone: string): string {
    // 移除所有非数字字符
    return phone.replace(/\D/g, '')
  }

  /**
   * 清理和标准化邮箱
   */
  normalizeEmail(email: string): string {
    return email.toLowerCase().trim()
  }

  /**
   * 清理和标准化用户名
   */
  normalizeUsername(username: string): string {
    return username.toLowerCase().trim()
  }
}
