apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "dl-engine.fullname" . }}-config
  labels:
    {{- include "dl-engine.labels" . | nindent 4 }}
data:
  # 应用配置
  NODE_ENV: {{ .Values.configMaps.app.NODE_ENV | quote }}
  LOG_LEVEL: {{ .Values.configMaps.app.LOG_LEVEL | quote }}
  
  # 数据库配置
  MYSQL_HOST: {{ .Values.configMaps.app.MYSQL_HOST | quote }}
  MYSQL_PORT: {{ .Values.configMaps.app.MYSQL_PORT | quote }}
  MYSQL_DATABASE: {{ .Values.configMaps.app.MYSQL_DATABASE | quote }}
  
  # Redis配置
  REDIS_HOST: {{ .Values.configMaps.app.REDIS_HOST | quote }}
  REDIS_PORT: {{ .Values.configMaps.app.REDIS_PORT | quote }}
  
  # PostgreSQL配置
  POSTGRESQL_HOST: {{ .Values.configMaps.app.POSTGRESQL_HOST | quote }}
  POSTGRESQL_PORT: {{ .Values.configMaps.app.POSTGRESQL_PORT | quote }}
  
  # Minio配置
  MINIO_HOST: {{ .Values.configMaps.app.MINIO_HOST | quote }}
  MINIO_PORT: {{ .Values.configMaps.app.MINIO_PORT | quote }}
  
  # AI服务配置
  AI_SERVICE_URL: {{ .Values.configMaps.app.AI_SERVICE_URL | quote }}
  
  # 中文界面配置
  ENABLE_CHINESE_UI: {{ .Values.configMaps.app.ENABLE_CHINESE_UI | quote }}
  DEFAULT_LOCALE: {{ .Values.configMaps.app.DEFAULT_LOCALE | quote }}
  
  # 服务发现配置
  GATEWAY_SERVICE_URL: "http://{{ include "dl-engine.fullname" . }}-gateway:{{ .Values.services.gateway.service.port }}"
  AUTH_SERVICE_URL: "http://{{ include "dl-engine.fullname" . }}-auth:{{ .Values.services.auth.service.port }}"
  API_SERVICE_URL: "http://{{ include "dl-engine.fullname" . }}-api:{{ .Values.services.api.service.port }}"
  INSTANCE_SERVICE_URL: "http://{{ include "dl-engine.fullname" . }}-instance:{{ .Values.services.instance.service.port }}"
  MEDIA_SERVICE_URL: "http://{{ include "dl-engine.fullname" . }}-media:{{ .Values.services.media.service.port }}"
  AI_SERVICE_URL: "http://{{ include "dl-engine.fullname" . }}-ai:{{ .Values.services.ai.service.port }}"
  TASK_SERVICE_URL: "http://{{ include "dl-engine.fullname" . }}-task:{{ .Values.services.task.service.port }}"
  
  # 健康检查配置
  HEALTH_CHECK_INTERVAL: "30000"
  HEALTH_CHECK_TIMEOUT: "5000"
  
  # 日志配置
  LOG_FORMAT: "json"
  LOG_TIMESTAMP: "true"
  
  # 性能配置
  MAX_CONCURRENT_REQUESTS: "1000"
  REQUEST_TIMEOUT: "30000"
  
  # 安全配置
  CORS_ORIGIN: "*"
  RATE_LIMIT_WINDOW: "900000"
  RATE_LIMIT_MAX: "100"
  
  # 教育功能配置
  ENABLE_EDUCATION_MODE: "true"
  MAX_STUDENTS_PER_CLASS: "50"
  MAX_CONCURRENT_SESSIONS: "100"
  
  # 3D渲染配置
  MAX_TEXTURE_SIZE: "2048"
  MAX_GEOMETRY_VERTICES: "100000"
  ENABLE_PHYSICS_SIMULATION: "true"
  
  # VR/AR配置
  ENABLE_WEBXR: "true"
  ENABLE_HAND_TRACKING: "true"
  ENABLE_EYE_TRACKING: "false"
  
  # 网络配置
  WEBSOCKET_HEARTBEAT_INTERVAL: "30000"
  WEBRTC_ICE_SERVERS: '[{"urls": "stun:stun.l.google.com:19302"}]'
  
  # 缓存配置
  CACHE_TTL: "3600"
  CACHE_MAX_SIZE: "1000"
  
  # 文件上传配置
  MAX_FILE_SIZE: "100MB"
  ALLOWED_FILE_TYPES: "jpg,jpeg,png,gif,mp4,mp3,wav,gltf,glb,fbx"
  
  # AI配置
  OLLAMA_HOST: "http://ollama:11434"
  ENABLE_AI_RECOMMENDATIONS: "true"
  ENABLE_LEARNING_ANALYTICS: "true"
