import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { MySQLService } from './mysql.service';
import { MyS<PERSON>Controller } from './mysql.controller';
import { UserEntity } from './entities/user.entity';
import { ProjectEntity } from './entities/project.entity';
import { SceneEntity } from './entities/scene.entity';
import { AssetEntity } from './entities/asset.entity';
import { CourseEntity } from './entities/course.entity';
import { AssignmentEntity } from './entities/assignment.entity';
import { AssessmentEntity } from './entities/assessment.entity';
import { TransactionService } from './transaction.service';
import { DataMigrationService } from './migration.service';

@Module({
  imports: [
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        type: 'mysql',
        host: configService.get('MYSQL_HOST', 'localhost'),
        port: configService.get('MYSQL_PORT', 3306),
        username: configService.get('MYSQL_USERNAME', 'root'),
        password: configService.get('MYSQL_PASSWORD', ''),
        database: configService.get('MYSQL_DATABASE', 'dl_engine'),
        entities: [
          UserEntity,
          ProjectEntity,
          SceneEntity,
          AssetEntity,
          CourseEntity,
          AssignmentEntity,
          AssessmentEntity,
        ],
        synchronize: configService.get('NODE_ENV') === 'development',
        logging: configService.get('NODE_ENV') === 'development',
        charset: 'utf8mb4',
        timezone: '+08:00',
        extra: {
          connectionLimit: 100,
          acquireTimeout: 60000,
          timeout: 60000,
        },
      }),
      inject: [ConfigService],
    }),
    TypeOrmModule.forFeature([
      UserEntity,
      ProjectEntity,
      SceneEntity,
      AssetEntity,
      CourseEntity,
      AssignmentEntity,
      AssessmentEntity,
    ]),
  ],
  providers: [MySQLService, TransactionService, DataMigrationService],
  controllers: [MySQLController],
  exports: [MySQLService, TransactionService, DataMigrationService],
})
export class MySQLModule {}
