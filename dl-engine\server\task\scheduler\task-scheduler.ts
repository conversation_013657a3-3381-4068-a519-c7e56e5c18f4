/*
DL-Engine Task Scheduler Service
数字化学习引擎 - 任务调度器服务

实现定时任务、优先级队列和任务分发功能
*/

import { Application } from '@feathersjs/feathers'
import { EventEmitter } from 'events'
import logger from '@ir-engine/server-core/src/ServerLogger'
import { v4 as uuidv4 } from 'uuid'

/**
 * 任务类型枚举
 */
export enum TaskType {
  IMMEDIATE = 'immediate',        // 立即执行
  SCHEDULED = 'scheduled',        // 定时执行
  RECURRING = 'recurring',        // 循环执行
  DELAYED = 'delayed',           // 延迟执行
  CONDITIONAL = 'conditional'     // 条件执行
}

/**
 * 任务状态枚举
 */
export enum TaskStatus {
  PENDING = 'pending',           // 等待中
  RUNNING = 'running',           // 执行中
  COMPLETED = 'completed',       // 已完成
  FAILED = 'failed',            // 失败
  CANCELLED = 'cancelled',       // 已取消
  RETRYING = 'retrying'         // 重试中
}

/**
 * 任务优先级枚举
 */
export enum TaskPriority {
  CRITICAL = 1,    // 关键任务
  HIGH = 2,        // 高优先级
  NORMAL = 3,      // 普通优先级
  LOW = 4,         // 低优先级
  BACKGROUND = 5   // 后台任务
}

/**
 * 任务定义接口
 */
export interface TaskDefinition {
  id: string
  name: string
  type: TaskType
  priority: TaskPriority
  handler: string                    // 处理器名称
  payload: Record<string, any>       // 任务参数
  schedule?: ScheduleConfig          // 调度配置
  retry?: RetryConfig               // 重试配置
  timeout?: number                  // 超时时间 (ms)
  dependencies?: string[]           // 依赖任务ID
  tags?: string[]                   // 任务标签
  metadata?: Record<string, any>    // 元数据
  createdAt: Date
  updatedAt: Date
}

/**
 * 调度配置接口
 */
export interface ScheduleConfig {
  startTime?: Date                  // 开始时间
  endTime?: Date                   // 结束时间
  interval?: number                // 间隔时间 (ms)
  cron?: string                    // Cron 表达式
  timezone?: string                // 时区
  maxRuns?: number                 // 最大运行次数
}

/**
 * 重试配置接口
 */
export interface RetryConfig {
  maxAttempts: number              // 最大重试次数
  backoffStrategy: 'fixed' | 'exponential' | 'linear'
  initialDelay: number             // 初始延迟 (ms)
  maxDelay: number                 // 最大延迟 (ms)
  multiplier?: number              // 指数退避倍数
}

/**
 * 任务执行结果接口
 */
export interface TaskResult {
  taskId: string
  status: TaskStatus
  result?: any
  error?: Error
  startTime: Date
  endTime?: Date
  duration?: number
  attempts: number
  nextRetryAt?: Date
}

/**
 * 任务调度器类
 */
export class TaskSchedulerService extends EventEmitter {
  private app: Application
  private tasks: Map<string, TaskDefinition> = new Map()
  private runningTasks: Map<string, TaskExecution> = new Map()
  private priorityQueues: Map<TaskPriority, TaskDefinition[]> = new Map()
  private scheduledTasks: Map<string, NodeJS.Timeout> = new Map()
  private taskHandlers: Map<string, TaskHandler> = new Map()
  private isRunning: boolean = false
  private maxConcurrentTasks: number = 10
  private processingInterval: NodeJS.Timeout | null = null

  constructor(app: Application) {
    super()
    this.app = app
    this.initializePriorityQueues()
    this.initializeTaskScheduler()
  }

  /**
   * 注册任务处理器
   */
  registerHandler(name: string, handler: TaskHandler): void {
    this.taskHandlers.set(name, handler)
    logger.info('任务处理器已注册', { name })
  }

  /**
   * 创建任务
   */
  async createTask(taskDef: Omit<TaskDefinition, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    try {
      const task: TaskDefinition = {
        ...taskDef,
        id: uuidv4(),
        createdAt: new Date(),
        updatedAt: new Date()
      }

      // 验证任务定义
      this.validateTaskDefinition(task)

      // 检查处理器是否存在
      if (!this.taskHandlers.has(task.handler)) {
        throw new Error(`任务处理器不存在: ${task.handler}`)
      }

      // 保存任务
      this.tasks.set(task.id, task)

      // 根据任务类型进行调度
      await this.scheduleTask(task)

      logger.info('任务创建成功', { taskId: task.id, name: task.name, type: task.type })
      
      this.emit('task-created', task)
      return task.id

    } catch (error) {
      logger.error('任务创建失败', { error, taskDef })
      throw error
    }
  }

  /**
   * 取消任务
   */
  async cancelTask(taskId: string): Promise<void> {
    try {
      const task = this.tasks.get(taskId)
      if (!task) {
        throw new Error('任务不存在')
      }

      // 如果任务正在运行，停止执行
      const execution = this.runningTasks.get(taskId)
      if (execution) {
        execution.cancelled = true
        this.runningTasks.delete(taskId)
      }

      // 取消定时器
      const timer = this.scheduledTasks.get(taskId)
      if (timer) {
        clearTimeout(timer)
        this.scheduledTasks.delete(taskId)
      }

      // 从优先级队列中移除
      this.removeFromPriorityQueue(task)

      // 更新任务状态
      task.updatedAt = new Date()
      
      logger.info('任务已取消', { taskId, name: task.name })
      
      this.emit('task-cancelled', task)

    } catch (error) {
      logger.error('任务取消失败', { error, taskId })
      throw error
    }
  }

  /**
   * 获取任务信息
   */
  getTask(taskId: string): TaskDefinition | null {
    return this.tasks.get(taskId) || null
  }

  /**
   * 获取任务状态
   */
  getTaskStatus(taskId: string): TaskStatus | null {
    const execution = this.runningTasks.get(taskId)
    if (execution) {
      return execution.status
    }

    const task = this.tasks.get(taskId)
    if (task) {
      // 从数据库或缓存中获取状态
      return TaskStatus.PENDING
    }

    return null
  }

  /**
   * 列出任务
   */
  listTasks(filter?: {
    type?: TaskType
    status?: TaskStatus
    priority?: TaskPriority
    tags?: string[]
  }): TaskDefinition[] {
    let tasks = Array.from(this.tasks.values())

    if (filter) {
      if (filter.type) {
        tasks = tasks.filter(task => task.type === filter.type)
      }
      if (filter.priority) {
        tasks = tasks.filter(task => task.priority === filter.priority)
      }
      if (filter.tags) {
        tasks = tasks.filter(task => 
          task.tags && filter.tags!.some(tag => task.tags!.includes(tag))
        )
      }
    }

    return tasks
  }

  /**
   * 启动调度器
   */
  start(): void {
    if (this.isRunning) return

    this.isRunning = true
    
    // 启动任务处理循环
    this.processingInterval = setInterval(() => {
      this.processTaskQueue()
    }, 1000) // 每秒处理一次

    logger.info('任务调度器已启动')
    this.emit('scheduler-started')
  }

  /**
   * 停止调度器
   */
  stop(): void {
    if (!this.isRunning) return

    this.isRunning = false

    // 停止处理循环
    if (this.processingInterval) {
      clearInterval(this.processingInterval)
      this.processingInterval = null
    }

    // 取消所有定时任务
    for (const timer of this.scheduledTasks.values()) {
      clearTimeout(timer)
    }
    this.scheduledTasks.clear()

    // 等待正在运行的任务完成
    this.waitForRunningTasks()

    logger.info('任务调度器已停止')
    this.emit('scheduler-stopped')
  }

  /**
   * 获取调度器统计信息
   */
  getStats(): any {
    const queueSizes = new Map()
    for (const [priority, queue] of this.priorityQueues) {
      queueSizes.set(priority, queue.length)
    }

    return {
      totalTasks: this.tasks.size,
      runningTasks: this.runningTasks.size,
      scheduledTasks: this.scheduledTasks.size,
      queueSizes: Object.fromEntries(queueSizes),
      registeredHandlers: this.taskHandlers.size,
      isRunning: this.isRunning
    }
  }

  /**
   * 调度任务
   */
  private async scheduleTask(task: TaskDefinition): Promise<void> {
    switch (task.type) {
      case TaskType.IMMEDIATE:
        this.addToPriorityQueue(task)
        break

      case TaskType.SCHEDULED:
        if (task.schedule?.startTime) {
          const delay = task.schedule.startTime.getTime() - Date.now()
          if (delay > 0) {
            const timer = setTimeout(() => {
              this.addToPriorityQueue(task)
              this.scheduledTasks.delete(task.id)
            }, delay)
            this.scheduledTasks.set(task.id, timer)
          } else {
            this.addToPriorityQueue(task)
          }
        }
        break

      case TaskType.RECURRING:
        this.scheduleRecurringTask(task)
        break

      case TaskType.DELAYED:
        if (task.schedule?.interval) {
          const timer = setTimeout(() => {
            this.addToPriorityQueue(task)
            this.scheduledTasks.delete(task.id)
          }, task.schedule.interval)
          this.scheduledTasks.set(task.id, timer)
        }
        break

      case TaskType.CONDITIONAL:
        // 条件任务需要外部触发
        break
    }
  }

  /**
   * 调度循环任务
   */
  private scheduleRecurringTask(task: TaskDefinition): void {
    if (!task.schedule) return

    const scheduleNext = () => {
      if (task.schedule!.cron) {
        // 使用 Cron 表达式计算下次执行时间
        const nextTime = this.calculateNextCronTime(task.schedule!.cron)
        const delay = nextTime.getTime() - Date.now()
        
        const timer = setTimeout(() => {
          this.addToPriorityQueue(task)
          scheduleNext() // 调度下次执行
        }, delay)
        
        this.scheduledTasks.set(task.id, timer)
      } else if (task.schedule!.interval) {
        // 使用固定间隔
        const timer = setTimeout(() => {
          this.addToPriorityQueue(task)
          scheduleNext() // 调度下次执行
        }, task.schedule!.interval)
        
        this.scheduledTasks.set(task.id, timer)
      }
    }

    scheduleNext()
  }

  /**
   * 处理任务队列
   */
  private async processTaskQueue(): Promise<void> {
    if (!this.isRunning || this.runningTasks.size >= this.maxConcurrentTasks) {
      return
    }

    // 按优先级处理任务
    for (const priority of [TaskPriority.CRITICAL, TaskPriority.HIGH, TaskPriority.NORMAL, TaskPriority.LOW, TaskPriority.BACKGROUND]) {
      const queue = this.priorityQueues.get(priority)
      if (!queue || queue.length === 0) continue

      const task = queue.shift()!
      
      // 检查依赖任务
      if (await this.checkDependencies(task)) {
        await this.executeTask(task)
        break // 一次只执行一个任务
      } else {
        // 依赖未满足，重新加入队列末尾
        queue.push(task)
      }
    }
  }

  /**
   * 执行任务
   */
  private async executeTask(task: TaskDefinition): Promise<void> {
    try {
      const execution: TaskExecution = {
        taskId: task.id,
        status: TaskStatus.RUNNING,
        startTime: new Date(),
        attempts: 1,
        cancelled: false
      }

      this.runningTasks.set(task.id, execution)
      
      logger.info('开始执行任务', { taskId: task.id, name: task.name })
      this.emit('task-started', task)

      // 获取处理器
      const handler = this.taskHandlers.get(task.handler)
      if (!handler) {
        throw new Error(`任务处理器不存在: ${task.handler}`)
      }

      // 设置超时
      const timeoutPromise = task.timeout ? 
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('任务执行超时')), task.timeout)
        ) : null

      // 执行任务
      const executePromise = handler.execute(task.payload, {
        taskId: task.id,
        app: this.app
      })

      const result = timeoutPromise ? 
        await Promise.race([executePromise, timeoutPromise]) :
        await executePromise

      // 任务完成
      execution.status = TaskStatus.COMPLETED
      execution.endTime = new Date()
      execution.duration = execution.endTime.getTime() - execution.startTime.getTime()
      execution.result = result

      logger.info('任务执行完成', { 
        taskId: task.id, 
        name: task.name, 
        duration: execution.duration 
      })

      this.emit('task-completed', task, result)

    } catch (error) {
      logger.error('任务执行失败', { error, taskId: task.id, name: task.name })
      await this.handleTaskFailure(task, error as Error)
    } finally {
      this.runningTasks.delete(task.id)
    }
  }

  /**
   * 处理任务失败
   */
  private async handleTaskFailure(task: TaskDefinition, error: Error): Promise<void> {
    const execution = this.runningTasks.get(task.id)
    if (!execution) return

    execution.status = TaskStatus.FAILED
    execution.error = error
    execution.endTime = new Date()

    // 检查是否需要重试
    if (task.retry && execution.attempts < task.retry.maxAttempts) {
      execution.status = TaskStatus.RETRYING
      execution.attempts++

      // 计算重试延迟
      const delay = this.calculateRetryDelay(task.retry, execution.attempts)
      execution.nextRetryAt = new Date(Date.now() + delay)

      logger.info('任务将重试', { 
        taskId: task.id, 
        attempt: execution.attempts, 
        delay 
      })

      // 调度重试
      setTimeout(() => {
        this.addToPriorityQueue(task)
      }, delay)

      this.emit('task-retry', task, execution.attempts)
    } else {
      logger.error('任务最终失败', { taskId: task.id, name: task.name, error })
      this.emit('task-failed', task, error)
    }
  }

  /**
   * 计算重试延迟
   */
  private calculateRetryDelay(retryConfig: RetryConfig, attempt: number): number {
    let delay = retryConfig.initialDelay

    switch (retryConfig.backoffStrategy) {
      case 'exponential':
        delay = retryConfig.initialDelay * Math.pow(retryConfig.multiplier || 2, attempt - 1)
        break
      case 'linear':
        delay = retryConfig.initialDelay * attempt
        break
      case 'fixed':
      default:
        delay = retryConfig.initialDelay
        break
    }

    return Math.min(delay, retryConfig.maxDelay)
  }

  /**
   * 检查任务依赖
   */
  private async checkDependencies(task: TaskDefinition): Promise<boolean> {
    if (!task.dependencies || task.dependencies.length === 0) {
      return true
    }

    // 检查所有依赖任务是否已完成
    for (const depId of task.dependencies) {
      const depTask = this.tasks.get(depId)
      if (!depTask) {
        logger.warn('依赖任务不存在', { taskId: task.id, dependencyId: depId })
        return false
      }

      const status = this.getTaskStatus(depId)
      if (status !== TaskStatus.COMPLETED) {
        return false
      }
    }

    return true
  }

  /**
   * 添加到优先级队列
   */
  private addToPriorityQueue(task: TaskDefinition): void {
    const queue = this.priorityQueues.get(task.priority)
    if (queue) {
      queue.push(task)
    }
  }

  /**
   * 从优先级队列移除
   */
  private removeFromPriorityQueue(task: TaskDefinition): void {
    const queue = this.priorityQueues.get(task.priority)
    if (queue) {
      const index = queue.findIndex(t => t.id === task.id)
      if (index !== -1) {
        queue.splice(index, 1)
      }
    }
  }

  /**
   * 初始化优先级队列
   */
  private initializePriorityQueues(): void {
    for (const priority of Object.values(TaskPriority)) {
      if (typeof priority === 'number') {
        this.priorityQueues.set(priority, [])
      }
    }
  }

  /**
   * 验证任务定义
   */
  private validateTaskDefinition(task: TaskDefinition): void {
    if (!task.name || !task.handler) {
      throw new Error('任务名称和处理器不能为空')
    }

    if (task.type === TaskType.SCHEDULED && !task.schedule?.startTime) {
      throw new Error('定时任务必须指定开始时间')
    }

    if (task.type === TaskType.RECURRING && !task.schedule?.interval && !task.schedule?.cron) {
      throw new Error('循环任务必须指定间隔或 Cron 表达式')
    }
  }

  /**
   * 计算下次 Cron 执行时间
   */
  private calculateNextCronTime(cron: string): Date {
    // 这里应该使用 Cron 解析库，如 node-cron
    // 简化实现，返回1小时后
    return new Date(Date.now() + 60 * 60 * 1000)
  }

  /**
   * 等待正在运行的任务完成
   */
  private async waitForRunningTasks(): Promise<void> {
    const timeout = 30000 // 30秒超时
    const startTime = Date.now()

    while (this.runningTasks.size > 0 && Date.now() - startTime < timeout) {
      await new Promise(resolve => setTimeout(resolve, 100))
    }

    if (this.runningTasks.size > 0) {
      logger.warn('强制停止调度器，仍有任务在运行', { 
        runningTasks: this.runningTasks.size 
      })
    }
  }

  /**
   * 初始化任务调度器
   */
  private initializeTaskScheduler(): void {
    logger.info('任务调度器初始化完成')
  }
}

/**
 * 任务执行状态接口
 */
interface TaskExecution {
  taskId: string
  status: TaskStatus
  startTime: Date
  endTime?: Date
  duration?: number
  attempts: number
  result?: any
  error?: Error
  nextRetryAt?: Date
  cancelled: boolean
}

/**
 * 任务处理器接口
 */
export interface TaskHandler {
  execute(payload: any, context: TaskContext): Promise<any>
}

/**
 * 任务执行上下文接口
 */
export interface TaskContext {
  taskId: string
  app: Application
}

// 导出服务
export default (app: Application): void => {
  const taskScheduler = new TaskSchedulerService(app)
  app.set('taskScheduler', taskScheduler)
}
