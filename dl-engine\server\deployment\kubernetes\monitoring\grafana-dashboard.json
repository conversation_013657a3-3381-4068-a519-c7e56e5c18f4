{"dashboard": {"id": null, "title": "DL-Engine 数字化学习引擎监控仪表板", "tags": ["dl-engine", "education", "monitoring"], "style": "dark", "timezone": "Asia/Shanghai", "refresh": "30s", "time": {"from": "now-1h", "to": "now"}, "panels": [{"id": 1, "title": "系统概览", "type": "stat", "gridPos": {"h": 8, "w": 24, "x": 0, "y": 0}, "targets": [{"expr": "sum(up{job=~\"dl-engine-.*\"})", "legendFormat": "在线服务数", "refId": "A"}, {"expr": "sum(agones_gameservers{status=\"Ready\"})", "legendFormat": "可用游戏服务器", "refId": "B"}, {"expr": "sum(rate(http_requests_total[5m]))", "legendFormat": "每秒请求数", "refId": "C"}, {"expr": "sum(dl_engine_active_users)", "legendFormat": "在线用户数", "refId": "D"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"displayMode": "list", "orientation": "horizontal"}, "mappings": [], "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}}}, {"id": 2, "title": "服务响应时间", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "targets": [{"expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{job=~\"dl-engine-.*\"}[5m]))", "legendFormat": "{{job}} - 95th percentile", "refId": "A"}, {"expr": "histogram_quantile(0.50, rate(http_request_duration_seconds_bucket{job=~\"dl-engine-.*\"}[5m]))", "legendFormat": "{{job}} - 50th percentile", "refId": "B"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"drawStyle": "line", "lineInterpolation": "linear", "barAlignment": 0, "lineWidth": 1, "fillOpacity": 0, "gradientMode": "none", "spanNulls": false, "insertNulls": false, "showPoints": "auto", "pointSize": 5, "stacking": {"mode": "none", "group": "A"}, "axisPlacement": "auto", "axisLabel": "", "axisColorMode": "text", "scaleDistribution": {"type": "linear"}, "axisCenteredZero": false, "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}}}, {"id": 3, "title": "错误率", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "targets": [{"expr": "rate(http_requests_total{status=~\"5..\",job=~\"dl-engine-.*\"}[5m]) / rate(http_requests_total{job=~\"dl-engine-.*\"}[5m]) * 100", "legendFormat": "{{job}} - 错误率", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"drawStyle": "line", "lineInterpolation": "linear", "barAlignment": 0, "lineWidth": 1, "fillOpacity": 10, "gradientMode": "none", "spanNulls": false, "insertNulls": false, "showPoints": "auto", "pointSize": 5, "stacking": {"mode": "none", "group": "A"}, "axisPlacement": "auto", "axisLabel": "", "axisColorMode": "text", "scaleDistribution": {"type": "linear"}, "axisCenteredZero": false, "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 1}, {"color": "red", "value": 5}]}, "unit": "percent"}}}, {"id": 4, "title": "资源使用情况", "type": "timeseries", "gridPos": {"h": 8, "w": 24, "x": 0, "y": 16}, "targets": [{"expr": "rate(container_cpu_usage_seconds_total{pod=~\"dl-engine-.*\"}[5m]) * 100", "legendFormat": "{{pod}} - CPU使用率", "refId": "A"}, {"expr": "container_memory_usage_bytes{pod=~\"dl-engine-.*\"} / container_spec_memory_limit_bytes{pod=~\"dl-engine-.*\"} * 100", "legendFormat": "{{pod}} - 内存使用率", "refId": "B"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"drawStyle": "line", "lineInterpolation": "linear", "barAlignment": 0, "lineWidth": 1, "fillOpacity": 0, "gradientMode": "none", "spanNulls": false, "insertNulls": false, "showPoints": "auto", "pointSize": 5, "stacking": {"mode": "none", "group": "A"}, "axisPlacement": "auto", "axisLabel": "", "axisColorMode": "text", "scaleDistribution": {"type": "linear"}, "axisCenteredZero": false, "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 70}, {"color": "red", "value": 90}]}, "unit": "percent"}}}, {"id": 5, "title": "游戏服务器状态", "type": "stat", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 24}, "targets": [{"expr": "agones_gameservers{status=\"Ready\"}", "legendFormat": "就绪", "refId": "A"}, {"expr": "agones_gameservers{status=\"Allocated\"}", "legendFormat": "已分配", "refId": "B"}, {"expr": "agones_gameservers{status=\"Creating\"}", "legendFormat": "创建中", "refId": "C"}, {"expr": "agones_gameservers{status=\"Error\"}", "legendFormat": "错误", "refId": "D"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"displayMode": "list", "orientation": "horizontal"}, "mappings": [], "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}}}, {"id": 6, "title": "教育业务指标", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 24}, "targets": [{"expr": "dl_engine_active_sessions", "legendFormat": "活跃会话数", "refId": "A"}, {"expr": "dl_engine_concurrent_users", "legendFormat": "并发用户数", "refId": "B"}, {"expr": "rate(dl_engine_scene_loads_total[5m])", "legendFormat": "场景加载速率", "refId": "C"}, {"expr": "rate(dl_engine_user_interactions_total[5m])", "legendFormat": "用户交互速率", "refId": "D"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"drawStyle": "line", "lineInterpolation": "linear", "barAlignment": 0, "lineWidth": 1, "fillOpacity": 0, "gradientMode": "none", "spanNulls": false, "insertNulls": false, "showPoints": "auto", "pointSize": 5, "stacking": {"mode": "none", "group": "A"}, "axisPlacement": "auto", "axisLabel": "", "axisColorMode": "text", "scaleDistribution": {"type": "linear"}, "axisCenteredZero": false, "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}}}, {"id": 7, "title": "数据库性能", "type": "timeseries", "gridPos": {"h": 8, "w": 24, "x": 0, "y": 32}, "targets": [{"expr": "rate(mysql_global_status_queries[5m])", "legendFormat": "MySQL - 查询速率", "refId": "A"}, {"expr": "mysql_global_status_threads_connected", "legendFormat": "MySQL - 连接数", "refId": "B"}, {"expr": "rate(redis_commands_processed_total[5m])", "legendFormat": "Redis - 命令速率", "refId": "C"}, {"expr": "redis_connected_clients", "legendFormat": "Redis - 连接数", "refId": "D"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"drawStyle": "line", "lineInterpolation": "linear", "barAlignment": 0, "lineWidth": 1, "fillOpacity": 0, "gradientMode": "none", "spanNulls": false, "insertNulls": false, "showPoints": "auto", "pointSize": 5, "stacking": {"mode": "none", "group": "A"}, "axisPlacement": "auto", "axisLabel": "", "axisColorMode": "text", "scaleDistribution": {"type": "linear"}, "axisCenteredZero": false, "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}}}], "templating": {"list": [{"name": "namespace", "type": "query", "query": "label_values(up, kubernetes_namespace)", "current": {"selected": false, "text": "dl-engine", "value": "dl-engine"}, "options": [], "refresh": 1, "includeAll": false, "multi": false, "allValue": null, "regex": "", "datasource": "Prometheus", "definition": "label_values(up, kubernetes_namespace)", "hide": 0, "label": "命名空间", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tagsQuery": "", "useTags": false}, {"name": "service", "type": "query", "query": "label_values(up{kubernetes_namespace=\"$namespace\"}, job)", "current": {"selected": true, "text": "All", "value": "$__all"}, "options": [], "refresh": 1, "includeAll": true, "multi": true, "allValue": null, "regex": "", "datasource": "Prometheus", "definition": "label_values(up{kubernetes_namespace=\"$namespace\"}, job)", "hide": 0, "label": "服务", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tagsQuery": "", "useTags": false}]}, "annotations": {"list": [{"name": "部署事件", "datasource": "Prometheus", "enable": true, "expr": "changes(up[1m]) > 0", "iconColor": "rgba(0, 211, 255, 1)", "titleFormat": "服务状态变更", "textFormat": "{{instance}} 状态变更"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "links": [], "liveNow": false, "schemaVersion": 27, "version": 1, "weekStart": ""}}