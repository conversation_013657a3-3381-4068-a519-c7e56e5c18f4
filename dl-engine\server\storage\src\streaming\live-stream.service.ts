import { Injectable, Logger } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { ConfigService } from '@nestjs/config';
import { RTMPService } from './rtmp.service';
import { TranscodingService } from './transcoding.service';
import { StreamConfig, StreamStats } from './streaming.service';
import * as ffmpeg from 'fluent-ffmpeg';
import * as fs from 'fs/promises';
import * as path from 'path';

export interface LiveStreamSession {
  streamId: string;
  sessionId: string;
  clientIp: string;
  userAgent: string;
  startTime: Date;
  lastActivity: Date;
  status: 'connecting' | 'connected' | 'streaming' | 'disconnected';
  inputStats: {
    bitrate: number;
    framerate: number;
    resolution: string;
    codec: string;
    audioCodec: string;
  };
  outputStats: {
    viewers: number;
    bandwidth: number;
    segments: number;
  };
  metadata?: Record<string, any>;
}

export interface LiveStreamEvent {
  streamId: string;
  sessionId?: string;
  type: 'stream_start' | 'stream_stop' | 'viewer_join' | 'viewer_leave' | 'quality_change' | 'error';
  timestamp: Date;
  data: any;
}

@Injectable()
export class LiveStreamService {
  private readonly logger = new Logger(LiveStreamService.name);
  private readonly sessions = new Map<string, LiveStreamSession>();
  private readonly streamProcesses = new Map<string, any>();
  private readonly hlsDir: string;

  constructor(
    private configService: ConfigService,
    private rtmpService: RTMPService,
    private transcodingService: TranscodingService,
    @InjectQueue('live-transcoding') private transcodingQueue: Queue,
  ) {
    this.hlsDir = this.configService.get('HLS_OUTPUT_DIR', './hls');
    this.ensureHLSDirectory();
  }

  /**
   * 初始化直播流
   */
  async initializeStream(config: StreamConfig): Promise<void> {
    try {
      // 创建HLS输出目录
      const streamDir = path.join(this.hlsDir, config.id);
      await fs.mkdir(streamDir, { recursive: true });

      // 配置RTMP接入点
      if (config.input.type === 'rtmp') {
        await this.rtmpService.createEndpoint(config.id, config.input.url);
      }

      // 准备输出配置
      for (const output of config.outputs) {
        if (output.type === 'hls') {
          const outputDir = path.join(streamDir, output.quality);
          await fs.mkdir(outputDir, { recursive: true });
        }
      }

      this.logger.log(`Live stream initialized: ${config.id}`);
    } catch (error) {
      this.logger.error(`Failed to initialize live stream ${config.id}:`, error);
      throw error;
    }
  }

  /**
   * 启动直播流
   */
  async startStream(streamId: string): Promise<void> {
    try {
      // 启动RTMP服务器监听
      await this.rtmpService.startListening(streamId);

      // 等待推流连接
      this.logger.log(`Live stream started, waiting for input: ${streamId}`);
    } catch (error) {
      this.logger.error(`Failed to start live stream ${streamId}:`, error);
      throw error;
    }
  }

  /**
   * 停止直播流
   */
  async stopStream(streamId: string): Promise<void> {
    try {
      // 停止所有会话
      const sessions = Array.from(this.sessions.values()).filter(s => s.streamId === streamId);
      for (const session of sessions) {
        await this.disconnectSession(session.sessionId);
      }

      // 停止RTMP监听
      await this.rtmpService.stopListening(streamId);

      // 停止FFmpeg进程
      const process = this.streamProcesses.get(streamId);
      if (process) {
        process.kill('SIGTERM');
        this.streamProcesses.delete(streamId);
      }

      this.logger.log(`Live stream stopped: ${streamId}`);
    } catch (error) {
      this.logger.error(`Failed to stop live stream ${streamId}:`, error);
      throw error;
    }
  }

  /**
   * 处理推流连接
   */
  async handleStreamConnection(
    streamId: string,
    sessionId: string,
    clientInfo: {
      ip: string;
      userAgent: string;
      metadata?: Record<string, any>;
    }
  ): Promise<void> {
    try {
      const session: LiveStreamSession = {
        streamId,
        sessionId,
        clientIp: clientInfo.ip,
        userAgent: clientInfo.userAgent,
        startTime: new Date(),
        lastActivity: new Date(),
        status: 'connecting',
        inputStats: {
          bitrate: 0,
          framerate: 0,
          resolution: '',
          codec: '',
          audioCodec: '',
        },
        outputStats: {
          viewers: 0,
          bandwidth: 0,
          segments: 0,
        },
        metadata: clientInfo.metadata,
      };

      this.sessions.set(sessionId, session);

      // 启动转码和分发
      await this.startStreamProcessing(streamId, sessionId);

      session.status = 'connected';
      this.logger.log(`Stream connection established: ${streamId}/${sessionId}`);

      // 发送事件
      await this.emitStreamEvent({
        streamId,
        sessionId,
        type: 'stream_start',
        timestamp: new Date(),
        data: { clientInfo },
      });
    } catch (error) {
      this.logger.error(`Failed to handle stream connection ${streamId}/${sessionId}:`, error);
      throw error;
    }
  }

  /**
   * 处理推流断开
   */
  async handleStreamDisconnection(sessionId: string): Promise<void> {
    const session = this.sessions.get(sessionId);
    if (!session) {
      return;
    }

    try {
      session.status = 'disconnected';

      // 停止流处理
      await this.stopStreamProcessing(session.streamId, sessionId);

      // 清理会话
      this.sessions.delete(sessionId);

      this.logger.log(`Stream disconnected: ${session.streamId}/${sessionId}`);

      // 发送事件
      await this.emitStreamEvent({
        streamId: session.streamId,
        sessionId,
        type: 'stream_stop',
        timestamp: new Date(),
        data: { duration: Date.now() - session.startTime.getTime() },
      });
    } catch (error) {
      this.logger.error(`Failed to handle stream disconnection ${sessionId}:`, error);
    }
  }

  /**
   * 获取直播流统计
   */
  async getStreamStats(streamId: string): Promise<StreamStats | null> {
    const sessions = Array.from(this.sessions.values()).filter(s => s.streamId === streamId);
    
    if (sessions.length === 0) {
      return null;
    }

    const activeSession = sessions.find(s => s.status === 'streaming') || sessions[0];
    
    // 获取观众数量
    const viewers = await this.getViewerCount(streamId);
    
    // 计算总带宽
    const totalBandwidth = sessions.reduce((sum, session) => sum + session.outputStats.bandwidth, 0);

    return {
      streamId,
      status: activeSession.status,
      viewers: {
        current: viewers.current,
        peak: viewers.peak,
        total: viewers.total,
      },
      bandwidth: {
        input: activeSession.inputStats.bitrate,
        output: totalBandwidth,
        total: activeSession.inputStats.bitrate + totalBandwidth,
      },
      quality: {
        bitrate: activeSession.inputStats.bitrate,
        framerate: activeSession.inputStats.framerate,
        resolution: activeSession.inputStats.resolution,
        dropRate: 0, // 需要实际计算
      },
      duration: Date.now() - activeSession.startTime.getTime(),
      startTime: activeSession.startTime,
      lastUpdate: new Date(),
    };
  }

  /**
   * 获取活跃会话
   */
  getActiveSessions(streamId?: string): LiveStreamSession[] {
    let sessions = Array.from(this.sessions.values());
    
    if (streamId) {
      sessions = sessions.filter(s => s.streamId === streamId);
    }
    
    return sessions.filter(s => s.status === 'streaming' || s.status === 'connected');
  }

  /**
   * 断开会话
   */
  async disconnectSession(sessionId: string): Promise<void> {
    const session = this.sessions.get(sessionId);
    if (!session) {
      return;
    }

    try {
      // 停止流处理
      await this.stopStreamProcessing(session.streamId, sessionId);

      // 更新状态
      session.status = 'disconnected';

      this.logger.log(`Session disconnected: ${sessionId}`);
    } catch (error) {
      this.logger.error(`Failed to disconnect session ${sessionId}:`, error);
    }
  }

  /**
   * 清理流资源
   */
  async cleanupStream(streamId: string): Promise<void> {
    try {
      // 清理所有会话
      const sessions = Array.from(this.sessions.values()).filter(s => s.streamId === streamId);
      for (const session of sessions) {
        await this.disconnectSession(session.sessionId);
      }

      // 清理RTMP端点
      await this.rtmpService.removeEndpoint(streamId);

      // 清理HLS文件
      const streamDir = path.join(this.hlsDir, streamId);
      try {
        await fs.rmdir(streamDir, { recursive: true });
      } catch (error) {
        this.logger.warn(`Failed to cleanup HLS directory for ${streamId}:`, error);
      }

      this.logger.log(`Live stream cleaned up: ${streamId}`);
    } catch (error) {
      this.logger.error(`Failed to cleanup live stream ${streamId}:`, error);
    }
  }

  private async startStreamProcessing(streamId: string, sessionId: string): Promise<void> {
    const session = this.sessions.get(sessionId);
    if (!session) {
      throw new Error(`Session not found: ${sessionId}`);
    }

    try {
      // 启动FFmpeg进程进行转码和分发
      const inputUrl = await this.rtmpService.getStreamUrl(streamId, sessionId);
      const outputDir = path.join(this.hlsDir, streamId);

      const command = ffmpeg(inputUrl)
        .inputOptions([
          '-fflags', '+genpts',
          '-avoid_negative_ts', 'make_zero',
        ])
        .outputOptions([
          '-c:v', 'libx264',
          '-c:a', 'aac',
          '-preset', 'veryfast',
          '-g', '50',
          '-sc_threshold', '0',
          '-f', 'hls',
          '-hls_time', '6',
          '-hls_list_size', '10',
          '-hls_flags', 'delete_segments',
          '-hls_segment_filename', path.join(outputDir, 'segment_%03d.ts'),
        ])
        .output(path.join(outputDir, 'playlist.m3u8'))
        .on('start', (commandLine) => {
          this.logger.debug(`FFmpeg started: ${commandLine}`);
          session.status = 'streaming';
        })
        .on('progress', (progress) => {
          // 更新统计信息
          session.lastActivity = new Date();
          session.outputStats.segments++;
        })
        .on('error', (error) => {
          this.logger.error(`FFmpeg error for ${streamId}/${sessionId}:`, error);
          session.status = 'disconnected';
        })
        .on('end', () => {
          this.logger.log(`FFmpeg ended for ${streamId}/${sessionId}`);
          session.status = 'disconnected';
        });

      command.run();
      this.streamProcesses.set(`${streamId}:${sessionId}`, command);

      this.logger.log(`Stream processing started: ${streamId}/${sessionId}`);
    } catch (error) {
      this.logger.error(`Failed to start stream processing ${streamId}/${sessionId}:`, error);
      throw error;
    }
  }

  private async stopStreamProcessing(streamId: string, sessionId: string): Promise<void> {
    const processKey = `${streamId}:${sessionId}`;
    const process = this.streamProcesses.get(processKey);
    
    if (process) {
      try {
        process.kill('SIGTERM');
        this.streamProcesses.delete(processKey);
        this.logger.log(`Stream processing stopped: ${streamId}/${sessionId}`);
      } catch (error) {
        this.logger.error(`Failed to stop stream processing ${streamId}/${sessionId}:`, error);
      }
    }
  }

  private async getViewerCount(streamId: string): Promise<{
    current: number;
    peak: number;
    total: number;
  }> {
    // 这里应该从Redis或其他存储获取观众统计
    // 临时返回模拟数据
    return {
      current: Math.floor(Math.random() * 100),
      peak: Math.floor(Math.random() * 200),
      total: Math.floor(Math.random() * 1000),
    };
  }

  private async emitStreamEvent(event: LiveStreamEvent): Promise<void> {
    // 发送流事件到消息队列或WebSocket
    this.logger.debug(`Stream event: ${event.type} for ${event.streamId}`);
    
    // 这里可以集成WebSocket或消息队列来实时通知客户端
  }

  private async ensureHLSDirectory(): Promise<void> {
    try {
      await fs.access(this.hlsDir);
    } catch {
      await fs.mkdir(this.hlsDir, { recursive: true });
    }
  }
}
