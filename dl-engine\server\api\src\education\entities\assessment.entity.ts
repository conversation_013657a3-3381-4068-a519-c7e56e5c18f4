import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, OneToMany, JoinColumn, Index } from 'typeorm'
import { Course } from './course.entity'
import { Grade } from './grade.entity'

@Entity('assessments')
@Index(['courseId'])
@Index(['type'])
@Index(['status'])
export class Assessment {
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Column({ length: 200 })
  title: string

  @Column({ type: 'text', nullable: true })
  description: string

  @Column({ 
    type: 'enum',
    enum: ['quiz', 'exam', 'project', 'presentation', 'practical'],
    default: 'quiz'
  })
  type: string

  @Column({ 
    type: 'enum',
    enum: ['draft', 'published', 'active', 'closed', 'archived'],
    default: 'draft'
  })
  status: string

  @Column({ type: 'json' })
  questions: any // 题目列表

  @Column({ type: 'int', default: 60 })
  timeLimit: number // 时间限制（分钟）

  @Column({ type: 'int', default: 1 })
  maxAttempts: number

  @Column({ type: 'decimal', precision: 5, scale: 2, default: 60.0 })
  passingScore: number

  @Column({ type: 'boolean', default: false })
  randomizeQuestions: boolean

  @Column({ type: 'boolean', default: false })
  showCorrectAnswers: boolean

  @Column({ type: 'datetime', nullable: true })
  availableFrom: Date

  @Column({ type: 'datetime', nullable: true })
  availableUntil: Date

  @Column({ type: 'json', nullable: true })
  settings: any // 其他设置

  // 关联关系
  @Column({ name: 'course_id' })
  courseId: string

  @ManyToOne(() => Course, course => course.assessments)
  @JoinColumn({ name: 'course_id' })
  course: Course

  @OneToMany(() => Grade, grade => grade.assessment)
  grades: Grade[]

  @CreateDateColumn()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date
}
