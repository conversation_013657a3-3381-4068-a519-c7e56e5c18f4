import { Controller, Get, Post, Put, Delete, Body, Param, Query, UseGuards, Request } from '@nestjs/common'
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger'
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard'
import { RolesGuard } from '../../auth/guards/roles.guard'
import { Roles } from '../../auth/decorators/roles.decorator'
import { AssessmentService } from '../services/assessment.service'
import { CreateAssessmentDto, UpdateAssessmentDto, AssessmentQueryDto, TakeAssessmentDto } from '../dto/assessment.dto'

@ApiTags('教育-评估管理')
@Controller('education/assessments')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class AssessmentController {
  constructor(private readonly assessmentService: AssessmentService) {}

  @Post()
  @Roles('teacher', 'admin')
  @ApiOperation({ summary: '创建评估' })
  @ApiResponse({ status: 201, description: '评估创建成功' })
  async create(@Body() createAssessmentDto: CreateAssessmentDto, @Request() req) {
    return this.assessmentService.create(createAssessmentDto, req.user.id)
  }

  @Get()
  @ApiOperation({ summary: '获取评估列表' })
  @ApiResponse({ status: 200, description: '评估列表获取成功' })
  async findAll(@Query() query: AssessmentQueryDto, @Request() req) {
    return this.assessmentService.findAll(query, req.user)
  }

  @Get(':id')
  @ApiOperation({ summary: '获取评估详情' })
  @ApiResponse({ status: 200, description: '评估详情获取成功' })
  async findOne(@Param('id') id: string, @Request() req) {
    return this.assessmentService.findOne(id, req.user)
  }

  @Put(':id')
  @Roles('teacher', 'admin')
  @ApiOperation({ summary: '更新评估' })
  @ApiResponse({ status: 200, description: '评估更新成功' })
  async update(@Param('id') id: string, @Body() updateAssessmentDto: UpdateAssessmentDto, @Request() req) {
    return this.assessmentService.update(id, updateAssessmentDto, req.user.id)
  }

  @Delete(':id')
  @Roles('teacher', 'admin')
  @ApiOperation({ summary: '删除评估' })
  @ApiResponse({ status: 200, description: '评估删除成功' })
  async remove(@Param('id') id: string, @Request() req) {
    return this.assessmentService.remove(id, req.user.id)
  }

  @Post(':id/publish')
  @Roles('teacher', 'admin')
  @ApiOperation({ summary: '发布评估' })
  @ApiResponse({ status: 200, description: '评估发布成功' })
  async publish(@Param('id') id: string, @Request() req) {
    return this.assessmentService.publish(id, req.user.id)
  }

  @Post(':id/take')
  @Roles('student')
  @ApiOperation({ summary: '开始评估' })
  @ApiResponse({ status: 200, description: '评估开始成功' })
  async startAssessment(@Param('id') id: string, @Request() req) {
    return this.assessmentService.startAssessment(id, req.user.id)
  }

  @Post(':id/submit')
  @Roles('student')
  @ApiOperation({ summary: '提交评估答案' })
  @ApiResponse({ status: 200, description: '评估提交成功' })
  async submitAssessment(@Param('id') id: string, @Body() takeAssessmentDto: TakeAssessmentDto, @Request() req) {
    return this.assessmentService.submitAssessment(id, takeAssessmentDto, req.user.id)
  }

  @Get(':id/results')
  @ApiOperation({ summary: '获取评估结果' })
  @ApiResponse({ status: 200, description: '评估结果获取成功' })
  async getResults(@Param('id') id: string, @Request() req) {
    return this.assessmentService.getResults(id, req.user)
  }

  @Get(':id/analytics')
  @Roles('teacher', 'admin')
  @ApiOperation({ summary: '获取评估分析' })
  @ApiResponse({ status: 200, description: '评估分析获取成功' })
  async getAnalytics(@Param('id') id: string, @Request() req) {
    return this.assessmentService.getAnalytics(id, req.user.id)
  }
}
