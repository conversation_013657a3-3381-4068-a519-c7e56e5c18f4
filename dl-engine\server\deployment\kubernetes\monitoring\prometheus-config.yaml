# DL-Engine Prometheus监控配置
# 支持教育场景的性能监控和业务指标收集

apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-config
  namespace: dl-engine-monitoring
  labels:
    app: prometheus
    component: config
data:
  prometheus.yml: |
    global:
      scrape_interval: 15s
      evaluation_interval: 15s
      external_labels:
        cluster: 'dl-engine'
        environment: 'production'

    # 告警规则文件
    rule_files:
      - "/etc/prometheus/rules/*.yml"

    # 告警管理器配置
    alerting:
      alertmanagers:
        - static_configs:
            - targets:
              - alertmanager:9093

    # 抓取配置
    scrape_configs:
      # Prometheus自身监控
      - job_name: 'prometheus'
        static_configs:
          - targets: ['localhost:9090']

      # Kubernetes API服务器
      - job_name: 'kubernetes-apiservers'
        kubernetes_sd_configs:
          - role: endpoints
        scheme: https
        tls_config:
          ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
        bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
        relabel_configs:
          - source_labels: [__meta_kubernetes_namespace, __meta_kubernetes_service_name, __meta_kubernetes_endpoint_port_name]
            action: keep
            regex: default;kubernetes;https

      # Kubernetes节点监控
      - job_name: 'kubernetes-nodes'
        kubernetes_sd_configs:
          - role: node
        scheme: https
        tls_config:
          ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
        bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
        relabel_configs:
          - action: labelmap
            regex: __meta_kubernetes_node_label_(.+)
          - target_label: __address__
            replacement: kubernetes.default.svc:443
          - source_labels: [__meta_kubernetes_node_name]
            regex: (.+)
            target_label: __metrics_path__
            replacement: /api/v1/nodes/${1}/proxy/metrics

      # Kubernetes Pod监控
      - job_name: 'kubernetes-pods'
        kubernetes_sd_configs:
          - role: pod
        relabel_configs:
          - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
            action: keep
            regex: true
          - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
            action: replace
            target_label: __metrics_path__
            regex: (.+)
          - source_labels: [__address__, __meta_kubernetes_pod_annotation_prometheus_io_port]
            action: replace
            regex: ([^:]+)(?::\d+)?;(\d+)
            replacement: $1:$2
            target_label: __address__
          - action: labelmap
            regex: __meta_kubernetes_pod_label_(.+)
          - source_labels: [__meta_kubernetes_namespace]
            action: replace
            target_label: kubernetes_namespace
          - source_labels: [__meta_kubernetes_pod_name]
            action: replace
            target_label: kubernetes_pod_name

      # DL-Engine服务监控
      - job_name: 'dl-engine-gateway'
        kubernetes_sd_configs:
          - role: endpoints
            namespaces:
              names:
                - dl-engine
        relabel_configs:
          - source_labels: [__meta_kubernetes_service_name]
            action: keep
            regex: dl-engine-gateway
          - source_labels: [__meta_kubernetes_endpoint_port_name]
            action: keep
            regex: http
        metrics_path: /metrics
        scrape_interval: 10s

      - job_name: 'dl-engine-auth'
        kubernetes_sd_configs:
          - role: endpoints
            namespaces:
              names:
                - dl-engine
        relabel_configs:
          - source_labels: [__meta_kubernetes_service_name]
            action: keep
            regex: dl-engine-auth
          - source_labels: [__meta_kubernetes_endpoint_port_name]
            action: keep
            regex: http
        metrics_path: /metrics
        scrape_interval: 10s

      - job_name: 'dl-engine-api'
        kubernetes_sd_configs:
          - role: endpoints
            namespaces:
              names:
                - dl-engine
        relabel_configs:
          - source_labels: [__meta_kubernetes_service_name]
            action: keep
            regex: dl-engine-api
          - source_labels: [__meta_kubernetes_endpoint_port_name]
            action: keep
            regex: http
        metrics_path: /metrics
        scrape_interval: 10s

      - job_name: 'dl-engine-instance'
        kubernetes_sd_configs:
          - role: endpoints
            namespaces:
              names:
                - dl-engine
        relabel_configs:
          - source_labels: [__meta_kubernetes_service_name]
            action: keep
            regex: dl-engine-instance
          - source_labels: [__meta_kubernetes_endpoint_port_name]
            action: keep
            regex: http
        metrics_path: /metrics
        scrape_interval: 5s

      - job_name: 'dl-engine-media'
        kubernetes_sd_configs:
          - role: endpoints
            namespaces:
              names:
                - dl-engine
        relabel_configs:
          - source_labels: [__meta_kubernetes_service_name]
            action: keep
            regex: dl-engine-media
          - source_labels: [__meta_kubernetes_endpoint_port_name]
            action: keep
            regex: http
        metrics_path: /metrics
        scrape_interval: 10s

      - job_name: 'dl-engine-ai'
        kubernetes_sd_configs:
          - role: endpoints
            namespaces:
              names:
                - dl-engine
        relabel_configs:
          - source_labels: [__meta_kubernetes_service_name]
            action: keep
            regex: dl-engine-ai
          - source_labels: [__meta_kubernetes_endpoint_port_name]
            action: keep
            regex: http
        metrics_path: /metrics
        scrape_interval: 15s

      # Agones GameServer监控
      - job_name: 'agones-gameservers'
        kubernetes_sd_configs:
          - role: pod
            namespaces:
              names:
                - dl-engine-gameservers
        relabel_configs:
          - source_labels: [__meta_kubernetes_pod_label_agones_dev_role]
            action: keep
            regex: gameserver
          - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
            action: keep
            regex: true
          - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
            action: replace
            target_label: __metrics_path__
            regex: (.+)
          - source_labels: [__address__, __meta_kubernetes_pod_annotation_prometheus_io_port]
            action: replace
            regex: ([^:]+)(?::\d+)?;(\d+)
            replacement: $1:$2
            target_label: __address__
        metrics_path: /metrics
        scrape_interval: 5s

      # 数据库监控
      - job_name: 'mysql-exporter'
        static_configs:
          - targets: ['mysql-exporter:9104']
        scrape_interval: 10s

      - job_name: 'redis-exporter'
        static_configs:
          - targets: ['redis-exporter:9121']
        scrape_interval: 10s

      - job_name: 'postgres-exporter'
        static_configs:
          - targets: ['postgres-exporter:9187']
        scrape_interval: 10s

      # 基础设施监控
      - job_name: 'node-exporter'
        kubernetes_sd_configs:
          - role: endpoints
        relabel_configs:
          - source_labels: [__meta_kubernetes_endpoints_name]
            regex: 'node-exporter'
            action: keep
        scrape_interval: 10s

      - job_name: 'kube-state-metrics'
        static_configs:
          - targets: ['kube-state-metrics:8080']
        scrape_interval: 10s

      # 网络监控
      - job_name: 'blackbox'
        metrics_path: /probe
        params:
          module: [http_2xx]
        static_configs:
          - targets:
            - http://dl-engine-gateway:3030/health
            - http://dl-engine-auth:3031/health
            - http://dl-engine-api:3032/health
            - http://dl-engine-client:3000/health
        relabel_configs:
          - source_labels: [__address__]
            target_label: __param_target
          - source_labels: [__param_target]
            target_label: instance
          - target_label: __address__
            replacement: blackbox-exporter:9115

  # 告警规则配置
  alert_rules.yml: |
    groups:
      - name: dl-engine.rules
        rules:
          # 服务可用性告警
          - alert: ServiceDown
            expr: up == 0
            for: 1m
            labels:
              severity: critical
            annotations:
              summary: "Service {{ $labels.instance }} is down"
              description: "{{ $labels.instance }} of job {{ $labels.job }} has been down for more than 1 minute."

          # 高错误率告警
          - alert: HighErrorRate
            expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
            for: 5m
            labels:
              severity: warning
            annotations:
              summary: "High error rate on {{ $labels.instance }}"
              description: "Error rate is {{ $value }} errors per second on {{ $labels.instance }}."

          # 高响应时间告警
          - alert: HighResponseTime
            expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 2
            for: 5m
            labels:
              severity: warning
            annotations:
              summary: "High response time on {{ $labels.instance }}"
              description: "95th percentile response time is {{ $value }}s on {{ $labels.instance }}."

          # 高CPU使用率告警
          - alert: HighCPUUsage
            expr: 100 - (avg by(instance) (rate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
            for: 5m
            labels:
              severity: warning
            annotations:
              summary: "High CPU usage on {{ $labels.instance }}"
              description: "CPU usage is {{ $value }}% on {{ $labels.instance }}."

          # 高内存使用率告警
          - alert: HighMemoryUsage
            expr: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100 > 80
            for: 5m
            labels:
              severity: warning
            annotations:
              summary: "High memory usage on {{ $labels.instance }}"
              description: "Memory usage is {{ $value }}% on {{ $labels.instance }}."

          # GameServer告警
          - alert: GameServerDown
            expr: agones_gameservers{status="Ready"} == 0
            for: 2m
            labels:
              severity: critical
            annotations:
              summary: "No ready GameServers available"
              description: "There are no ready GameServers available for allocation."

          # 数据库连接告警
          - alert: DatabaseConnectionHigh
            expr: mysql_global_status_threads_connected / mysql_global_variables_max_connections > 0.8
            for: 5m
            labels:
              severity: warning
            annotations:
              summary: "High database connections"
              description: "Database connection usage is {{ $value }}%."
