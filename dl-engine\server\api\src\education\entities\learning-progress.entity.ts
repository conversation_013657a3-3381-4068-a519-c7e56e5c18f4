import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn, Index } from 'typeorm'
import { LearningPath } from './learning-path.entity'

@Entity('learning_progress')
@Index(['learningPathId'])
@Index(['studentId'])
@Index(['status'])
export class LearningProgress {
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Column({ type: 'decimal', precision: 5, scale: 2, default: 0 })
  completionPercentage: number

  @Column({ 
    type: 'enum',
    enum: ['not_started', 'in_progress', 'completed', 'paused'],
    default: 'not_started'
  })
  status: string

  @Column({ type: 'json', nullable: true })
  completedCourses: any // 已完成课程列表

  @Column({ type: 'json', nullable: true })
  currentCourse: any // 当前学习课程

  @Column({ type: 'int', default: 0 })
  totalTimeSpent: number // 总学习时间（分钟）

  @Column({ type: 'json', nullable: true })
  achievements: any // 获得的成就

  @Column({ type: 'json', nullable: true })
  milestones: any // 里程碑记录

  @Column({ type: 'datetime', nullable: true })
  startedAt: Date

  @Column({ type: 'datetime', nullable: true })
  lastAccessedAt: Date

  @Column({ type: 'datetime', nullable: true })
  completedAt: Date

  @Column({ type: 'json', nullable: true })
  analytics: any // 学习分析数据

  // 关联关系
  @Column({ name: 'learning_path_id' })
  learningPathId: string

  @Column({ name: 'student_id' })
  studentId: string

  @ManyToOne(() => LearningPath, learningPath => learningPath.progresses)
  @JoinColumn({ name: 'learning_path_id' })
  learningPath: LearningPath

  @CreateDateColumn()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date
}
