# DL-Engine Agones游戏服务器配置
# 支持实时多人协作的教育场景

apiVersion: agones.dev/v1
kind: GameServer
metadata:
  name: dl-engine-gameserver
  namespace: dl-engine-gameservers
  labels:
    app: dl-engine
    component: gameserver
    version: "1.0.0"
spec:
  # 容器配置
  container: dl-engine-instance
  
  # 端口配置
  ports:
    - name: default
      portPolicy: Dynamic
      containerPort: 7777
      protocol: UDP
    - name: websocket
      portPolicy: Dynamic
      containerPort: 8080
      protocol: TCP
    - name: webrtc
      portPolicy: Dynamic
      containerPort: 8081
      protocol: TCP
  
  # 健康检查
  health:
    disabled: false
    initialDelaySeconds: 5
    periodSeconds: 5
    failureThreshold: 3
  
  # 模板配置
  template:
    metadata:
      labels:
        app: dl-engine
        component: gameserver
        version: "1.0.0"
    spec:
      containers:
        - name: dl-engine-instance
          image: dl-engine/instance:1.0.0
          imagePullPolicy: IfNotPresent
          
          # 环境变量
          env:
            - name: NODE_ENV
              value: "production"
            - name: LOG_LEVEL
              value: "info"
            - name: AGONES_SDK_LOG_LEVEL
              value: "info"
            - name: GAMESERVER_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: GAMESERVER_NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
            - name: POD_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.podIP
            - name: MAX_PLAYERS
              value: "50"
            - name: SESSION_TIMEOUT
              value: "300000"
            - name: PHYSICS_TICK_RATE
              value: "60"
            - name: NETWORK_TICK_RATE
              value: "20"
            - name: ENABLE_VOICE_CHAT
              value: "true"
            - name: ENABLE_VIDEO_CHAT
              value: "true"
            - name: ENABLE_SCREEN_SHARE
              value: "true"
            - name: MYSQL_HOST
              value: "dl-engine-mysql"
            - name: MYSQL_PORT
              value: "3306"
            - name: MYSQL_DATABASE
              value: "dl_engine"
            - name: MYSQL_USER
              value: "dl_engine"
            - name: MYSQL_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: dl-engine-secret
                  key: MYSQL_PASSWORD
            - name: REDIS_HOST
              value: "dl-engine-redis-master"
            - name: REDIS_PORT
              value: "6379"
            - name: REDIS_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: dl-engine-secret
                  key: REDIS_PASSWORD
          
          # 端口配置
          ports:
            - containerPort: 7777
              protocol: UDP
              name: game-port
            - containerPort: 8080
              protocol: TCP
              name: websocket
            - containerPort: 8081
              protocol: TCP
              name: webrtc
          
          # 资源限制
          resources:
            limits:
              cpu: 2000m
              memory: 2Gi
            requests:
              cpu: 1000m
              memory: 1Gi
          
          # 健康检查
          livenessProbe:
            httpGet:
              path: /health
              port: 8080
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3
          
          readinessProbe:
            httpGet:
              path: /ready
              port: 8080
            initialDelaySeconds: 5
            periodSeconds: 5
            timeoutSeconds: 3
            failureThreshold: 3
          
          # 卷挂载
          volumeMounts:
            - name: tmp
              mountPath: /tmp
            - name: agones-sdk
              mountPath: /agones-sdk
      
      # 卷配置
      volumes:
        - name: tmp
          emptyDir: {}
        - name: agones-sdk
          emptyDir: {}
      
      # 服务账户
      serviceAccountName: dl-engine-gameserver
      
      # 安全上下文
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        fsGroup: 2000
      
      # 节点选择器
      nodeSelector:
        agones.dev/agones-system: "true"
      
      # 容忍度
      tolerations:
        - key: "agones.dev/agones-system"
          operator: "Equal"
          value: "true"
          effect: "NoSchedule"

---
# GameServer Fleet配置
apiVersion: agones.dev/v1
kind: Fleet
metadata:
  name: dl-engine-fleet
  namespace: dl-engine-gameservers
  labels:
    app: dl-engine
    component: fleet
spec:
  # 副本数量
  replicas: 3
  
  # 分配策略
  allocationOverflow:
    labels:
      stable: "true"
    annotations:
      cluster-autoscaler.kubernetes.io/safe-to-evict: "false"
  
  # 调度策略
  scheduling: Packed
  
  # 策略配置
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
  
  # GameServer模板
  template:
    metadata:
      labels:
        app: dl-engine
        component: gameserver
        fleet: dl-engine-fleet
    spec:
      container: dl-engine-instance
      ports:
        - name: default
          portPolicy: Dynamic
          containerPort: 7777
          protocol: UDP
        - name: websocket
          portPolicy: Dynamic
          containerPort: 8080
          protocol: TCP
        - name: webrtc
          portPolicy: Dynamic
          containerPort: 8081
          protocol: TCP
      health:
        disabled: false
        initialDelaySeconds: 5
        periodSeconds: 5
        failureThreshold: 3
      template:
        metadata:
          labels:
            app: dl-engine
            component: gameserver
            fleet: dl-engine-fleet
        spec:
          containers:
            - name: dl-engine-instance
              image: dl-engine/instance:1.0.0
              imagePullPolicy: IfNotPresent
              env:
                - name: NODE_ENV
                  value: "production"
                - name: LOG_LEVEL
                  value: "info"
                - name: MAX_PLAYERS
                  value: "50"
                - name: SESSION_TIMEOUT
                  value: "300000"
                - name: PHYSICS_TICK_RATE
                  value: "60"
                - name: NETWORK_TICK_RATE
                  value: "20"
              ports:
                - containerPort: 7777
                  protocol: UDP
                  name: game-port
                - containerPort: 8080
                  protocol: TCP
                  name: websocket
                - containerPort: 8081
                  protocol: TCP
                  name: webrtc
              resources:
                limits:
                  cpu: 2000m
                  memory: 2Gi
                requests:
                  cpu: 1000m
                  memory: 1Gi

---
# Fleet Autoscaler配置
apiVersion: autoscaling.agones.dev/v1
kind: FleetAutoscaler
metadata:
  name: dl-engine-fleet-autoscaler
  namespace: dl-engine-gameservers
  labels:
    app: dl-engine
    component: autoscaler
spec:
  fleetName: dl-engine-fleet
  policy:
    type: Buffer
    buffer:
      bufferSize: 2
      minReplicas: 2
      maxReplicas: 20
  sync:
    type: FixedInterval
    fixedInterval:
      seconds: 30
