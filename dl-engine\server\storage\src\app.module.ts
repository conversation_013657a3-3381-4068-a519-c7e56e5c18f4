import { Module } from '@nestjs/common'
import { ConfigModule } from '@nestjs/config'
import { TypeOrmModule } from '@nestjs/typeorm'
import { ScheduleModule } from '@nestjs/schedule'

import { DatabaseModule } from './database/database.module'
import { MinioModule } from './minio/minio.module'
import { BackupModule } from './backup/backup.module'
import { MigrationModule } from './migration/migration.module'
import { HealthModule } from './health/health.module'

/**
 * DL-Engine 存储服务主模块
 * 
 * 功能包括：
 * - 数据库服务 (MySQL主库、Redis缓存、PostgreSQL向量库)
 * - Minio对象存储 (文件上传、存储管理、访问控制)
 * - 备份与迁移 (数据备份、数据迁移、一致性检查)
 */
@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env']
    }),

    ScheduleModule.forRoot(),

    // MySQL 主数据库
    TypeOrmModule.forRoot({
      name: 'mysql',
      type: 'mysql',
      host: process.env.MYSQL_HOST || 'localhost',
      port: parseInt(process.env.MYSQL_PORT || '3306'),
      username: process.env.MYSQL_USERNAME || 'root',
      password: process.env.MYSQL_PASSWORD || '',
      database: process.env.MYSQL_DATABASE || 'dl_engine',
      entities: ['dist/**/*.entity{.ts,.js}'],
      synchronize: process.env.NODE_ENV === 'development',
      logging: process.env.NODE_ENV === 'development',
      charset: 'utf8mb4',
      timezone: '+08:00'
    }),

    // PostgreSQL 向量数据库
    TypeOrmModule.forRoot({
      name: 'postgresql',
      type: 'postgres',
      host: process.env.POSTGRES_HOST || 'localhost',
      port: parseInt(process.env.POSTGRES_PORT || '5432'),
      username: process.env.POSTGRES_USERNAME || 'postgres',
      password: process.env.POSTGRES_PASSWORD || '',
      database: process.env.POSTGRES_DATABASE || 'dl_engine_vectors',
      entities: ['dist/**/*.vector.entity{.ts,.js}'],
      synchronize: process.env.NODE_ENV === 'development',
      logging: process.env.NODE_ENV === 'development'
    }),

    DatabaseModule,
    MinioModule,
    BackupModule,
    MigrationModule,
    HealthModule
  ]
})
export class AppModule {}
