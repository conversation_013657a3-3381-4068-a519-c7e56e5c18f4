import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { BullModule } from '@nestjs/bull';
import { MediaController } from './media.controller';
import { MediaService } from './media.service';
import { ImageProcessingService } from './image-processing.service';
import { VideoProcessingService } from './video-processing.service';
import { AudioProcessingService } from './audio-processing.service';
import { ModelProcessingService } from './model-processing.service';
import { StreamingService } from './streaming.service';
import { TranscodingService } from './transcoding.service';
import { ThumbnailService } from './thumbnail.service';
import { MetadataService } from './metadata.service';
import { MinioModule } from '../minio/minio.module';

@Module({
  imports: [
    ConfigModule,
    MinioModule,
    BullModule.registerQueue(
      { name: 'image-processing' },
      { name: 'video-processing' },
      { name: 'audio-processing' },
      { name: 'model-processing' },
      { name: 'transcoding' },
      { name: 'thumbnail-generation' },
    ),
  ],
  controllers: [MediaController],
  providers: [
    MediaService,
    ImageProcessingService,
    VideoProcessingService,
    AudioProcessingService,
    ModelProcessingService,
    StreamingService,
    TranscodingService,
    ThumbnailService,
    MetadataService,
  ],
  exports: [
    MediaService,
    ImageProcessingService,
    VideoProcessingService,
    AudioProcessingService,
    ModelProcessingService,
    StreamingService,
    TranscodingService,
    ThumbnailService,
    MetadataService,
  ],
})
export class MediaModule {}
