import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { BullModule } from '@nestjs/bull';
import { AIController } from './ai.controller';
import { AIService } from './ai.service';
import { OllamaService } from './ollama.service';
import { EmbeddingService } from './embedding.service';
import { VectorSearchService } from './vector-search.service';
import { KnowledgeGraphService } from './knowledge-graph.service';
import { ContentAnalysisService } from './content-analysis.service';
import { RecommendationService } from './recommendation.service';
import { LearningPathService } from './learning-path.service';
import { ChatbotService } from './chatbot.service';
import { PostgreSQLModule } from '../database/postgresql/postgresql.module';
import { RedisModule } from '../database/redis/redis.module';
import { MinioModule } from '../minio/minio.module';

@Module({
  imports: [
    ConfigModule,
    PostgreSQLModule,
    RedisModule,
    MinioModule,
    BullModule.registerQueue(
      { name: 'ai-processing' },
      { name: 'embedding-generation' },
      { name: 'content-analysis' },
      { name: 'recommendation-update' },
    ),
  ],
  controllers: [AIController],
  providers: [
    AIService,
    OllamaService,
    EmbeddingService,
    VectorSearchService,
    KnowledgeGraphService,
    ContentAnalysisService,
    RecommendationService,
    LearningPathService,
    ChatbotService,
  ],
  exports: [
    AIService,
    OllamaService,
    EmbeddingService,
    VectorSearchService,
    KnowledgeGraphService,
    ContentAnalysisService,
    RecommendationService,
    LearningPathService,
    ChatbotService,
  ],
})
export class AIModule {}
