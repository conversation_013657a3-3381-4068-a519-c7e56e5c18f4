import { 
  Controller, 
  Get, 
  Post, 
  Put, 
  Delete, 
  Body, 
  Param, 
  Query, 
  UploadedFile, 
  UseInterceptors,
  Logger,
  Headers,
  Req,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { MinioService } from './minio.service';
import { UploadService } from './upload.service';
import { StorageService } from './storage.service';
import { AccessControlService } from './access-control.service';
import { CDNService } from './cdn.service';

@Controller('minio')
export class MinioController {
  private readonly logger = new Logger(MinioController.name);

  constructor(
    private minioService: MinioService,
    private uploadService: UploadService,
    private storageService: StorageService,
    private accessControlService: AccessControlService,
    private cdnService: CDNService,
  ) {}

  // 健康检查
  @Get('health')
  async healthCheck() {
    return await this.minioService.healthCheck();
  }

  // 存储桶管理
  @Post('buckets')
  async createBucket(@Body() { bucketName, region, policy }: any) {
    if (policy) {
      await this.storageService.createBucketWithPolicy(bucketName, policy, region);
    } else {
      await this.minioService.createBucket(bucketName, region);
    }
    return { success: true };
  }

  @Get('buckets')
  async listBuckets() {
    return await this.minioService.listBuckets();
  }

  @Delete('buckets/:bucketName')
  async deleteBucket(@Param('bucketName') bucketName: string) {
    await this.minioService.deleteBucket(bucketName);
    return { success: true };
  }

  @Get('buckets/:bucketName/exists')
  async bucketExists(@Param('bucketName') bucketName: string) {
    const exists = await this.minioService.bucketExists(bucketName);
    return { exists };
  }

  @Get('buckets/:bucketName/policy')
  async getBucketPolicy(@Param('bucketName') bucketName: string) {
    return await this.storageService.getBucketPolicy(bucketName);
  }

  @Put('buckets/:bucketName/policy')
  async setBucketPolicy(@Param('bucketName') bucketName: string, @Body() policy: any) {
    await this.storageService.setBucketPolicy(bucketName, policy);
    return { success: true };
  }

  @Get('buckets/:bucketName/usage')
  async getBucketUsage(@Param('bucketName') bucketName: string) {
    return await this.storageService.getBucketUsage(bucketName);
  }

  // 对象管理
  @Get('buckets/:bucketName/objects')
  async listObjects(
    @Param('bucketName') bucketName: string,
    @Query('prefix') prefix?: string,
    @Query('recursive') recursive?: boolean
  ) {
    return await this.minioService.listObjects(bucketName, prefix, recursive === true);
  }

  @Get('buckets/:bucketName/objects/:objectName/stat')
  async statObject(
    @Param('bucketName') bucketName: string,
    @Param('objectName') objectName: string
  ) {
    return await this.minioService.statObject(bucketName, objectName);
  }

  @Delete('buckets/:bucketName/objects/:objectName')
  async removeObject(
    @Param('bucketName') bucketName: string,
    @Param('objectName') objectName: string
  ) {
    await this.minioService.removeObject(bucketName, objectName);
    return { success: true };
  }

  @Post('buckets/:bucketName/objects/batch-delete')
  async removeObjects(
    @Param('bucketName') bucketName: string,
    @Body() { objectNames }: { objectNames: string[] }
  ) {
    await this.minioService.removeObjects(bucketName, objectNames);
    return { success: true };
  }

  // 文件上传
  @Post('upload')
  @UseInterceptors(FileInterceptor('file'))
  async uploadFile(
    @UploadedFile() file: Express.Multer.File,
    @Body() options: any,
    @Headers('user-id') userId?: string
  ) {
    const uploadOptions = {
      bucketName: options.bucketName,
      objectName: options.objectName,
      contentType: file.mimetype,
      metadata: options.metadata ? JSON.parse(options.metadata) : undefined,
      maxFileSize: options.maxFileSize ? parseInt(options.maxFileSize) : undefined,
      allowedMimeTypes: options.allowedMimeTypes ? JSON.parse(options.allowedMimeTypes) : undefined,
    };

    return await this.uploadService.uploadFile(file.buffer, uploadOptions, userId);
  }

  @Post('upload/chunk/init')
  async initiateChunkUpload(
    @Body() options: any,
    @Headers('user-id') userId?: string
  ) {
    return await this.uploadService.initiateChunkUpload(options, userId);
  }

  @Post('upload/chunk/:sessionId/:chunkNumber')
  @UseInterceptors(FileInterceptor('chunk'))
  async uploadChunk(
    @Param('sessionId') sessionId: string,
    @Param('chunkNumber') chunkNumber: string,
    @UploadedFile() chunk: Express.Multer.File,
    @Headers('user-id') userId?: string
  ) {
    return await this.uploadService.uploadChunk(
      sessionId,
      parseInt(chunkNumber),
      chunk.buffer,
      userId
    );
  }

  @Post('upload/chunk/:sessionId/complete')
  async completeChunkUpload(
    @Param('sessionId') sessionId: string,
    @Headers('user-id') userId?: string
  ) {
    return await this.uploadService.completeChunkUpload(sessionId, userId);
  }

  @Delete('upload/chunk/:sessionId')
  async abortChunkUpload(@Param('sessionId') sessionId: string) {
    await this.uploadService.abortChunkUpload(sessionId);
    return { success: true };
  }

  @Get('upload/chunk/:sessionId/progress')
  async getUploadProgress(@Param('sessionId') sessionId: string) {
    return this.uploadService.getUploadProgress(sessionId);
  }

  @Get('upload/chunk/:sessionId/chunks')
  async getUploadedChunks(@Param('sessionId') sessionId: string) {
    const chunks = this.uploadService.getUploadedChunks(sessionId);
    return { chunks };
  }

  // 预签名URL
  @Post('presigned-url')
  async getPresignedUrl(@Body() { method, bucketName, objectName, expires, userId }: any, @Req() req: any) {
    if (userId) {
      const context = {
        ip: req.ip,
        userAgent: req.headers['user-agent'],
        referer: req.headers.referer,
      };

      return {
        url: await this.accessControlService.generateSecurePresignedUrl(
          userId,
          method,
          bucketName,
          objectName,
          expires,
          context
        ),
      };
    } else {
      return {
        url: await this.minioService.getPresignedUrl(method, bucketName, objectName, { expires }),
      };
    }
  }

  @Post('presigned-post-policy')
  async getPresignedPostPolicy(@Body() { bucketName, objectName, expires, conditions }: any) {
    const expiresDate = new Date(Date.now() + (expires || 3600) * 1000);
    return await this.minioService.getPresignedPostPolicy(bucketName, objectName, expiresDate, conditions);
  }

  // 访问控制
  @Post('permissions')
  async setUserPermission(@Body() permission: any) {
    this.accessControlService.setUserPermission(permission);
    return { success: true };
  }

  @Get('permissions/:userId')
  async getUserPermissions(@Param('userId') userId: string) {
    return this.accessControlService.getUserPermissions(userId);
  }

  @Delete('permissions/:userId/:bucketName')
  async removeUserPermission(
    @Param('userId') userId: string,
    @Param('bucketName') bucketName: string,
    @Query('objectName') objectName?: string
  ) {
    const removed = this.accessControlService.removeUserPermission(userId, bucketName, objectName);
    return { removed };
  }

  @Post('temporary-access')
  async createTemporaryAccess(@Body() { userId, bucketName, permissions, expiresIn, maxUsage, objectName, metadata }: any) {
    const token = this.accessControlService.createTemporaryAccess(
      userId,
      bucketName,
      permissions,
      expiresIn,
      maxUsage,
      objectName,
      metadata
    );
    return { token };
  }

  @Delete('temporary-access/:token')
  async revokeTemporaryAccess(@Param('token') token: string) {
    const revoked = this.accessControlService.revokeTemporaryAccess(token);
    return { revoked };
  }

  @Get('access-logs')
  async getAccessLogs(
    @Query('userId') userId?: string,
    @Query('bucketName') bucketName?: string,
    @Query('limit') limit?: string
  ) {
    return this.accessControlService.getAccessLogs(
      userId,
      bucketName,
      limit ? parseInt(limit) : undefined
    );
  }

  @Get('access-stats')
  async getAccessStats(@Query('start') start?: string, @Query('end') end?: string) {
    const timeRange = start && end ? {
      start: new Date(start),
      end: new Date(end),
    } : undefined;

    return this.accessControlService.getAccessStats(timeRange);
  }

  // 存储管理
  @Get('storage/stats')
  async getStorageStats() {
    return await this.storageService.getStorageStats();
  }

  @Post('storage/quota')
  async setUserQuota(@Body() { userId, quota }: any) {
    this.storageService.setUserQuota(userId, quota);
    return { success: true };
  }

  @Get('storage/quota/:userId')
  async checkUserQuota(@Param('userId') userId: string, @Query('additionalSize') additionalSize?: string) {
    return await this.storageService.checkUserQuota(
      userId,
      additionalSize ? parseInt(additionalSize) : 0
    );
  }

  @Post('storage/cleanup/:bucketName')
  async cleanupExpiredObjects(
    @Param('bucketName') bucketName: string,
    @Body() { retentionDays }: { retentionDays: number }
  ) {
    const cleaned = await this.storageService.cleanupExpiredObjects(bucketName, retentionDays);
    return { cleaned };
  }

  @Post('storage/backup')
  async backupBucket(@Body() { sourceBucket, targetBucket }: any) {
    const copied = await this.storageService.backupBucket(sourceBucket, targetBucket);
    return { copied };
  }

  @Post('storage/sync')
  async syncBuckets(@Body() { sourceBucket, targetBucket }: any) {
    return await this.storageService.syncBuckets(sourceBucket, targetBucket);
  }

  // CDN管理
  @Get('cdn/health')
  async cdnHealthCheck() {
    return await this.cdnService.healthCheck();
  }

  @Post('cdn/url')
  async generateCDNUrl(@Body() { bucketName, objectName, options }: any) {
    const url = this.cdnService.generateCDNUrl(bucketName, objectName, options);
    return { url };
  }

  @Post('cdn/urls/batch')
  async generateBatchCDNUrls(@Body() { objects, options }: any) {
    return this.cdnService.generateBatchCDNUrls(objects, options);
  }

  @Post('cdn/warmup')
  async warmupCache(@Body() { urls }: { urls: string[] }) {
    return await this.cdnService.warmupCache(urls);
  }

  @Post('cdn/purge')
  async purgeCache(@Body() { urls }: { urls: string[] }) {
    return await this.cdnService.purgeCache(urls);
  }

  @Get('cdn/stats')
  async getCDNStats(@Query('start') start?: string, @Query('end') end?: string) {
    const timeRange = start && end ? {
      start: new Date(start),
      end: new Date(end),
    } : undefined;

    return await this.cdnService.getCDNStats(timeRange);
  }

  @Post('cdn/cache-rule')
  async setCacheRule(@Body() rule: any) {
    this.cdnService.setCacheRule(rule);
    return { success: true };
  }

  // 文件验证
  @Post('verify/:bucketName/:objectName')
  async verifyFileIntegrity(
    @Param('bucketName') bucketName: string,
    @Param('objectName') objectName: string,
    @Body() { expectedHash }: { expectedHash: string }
  ) {
    const isValid = await this.uploadService.verifyFileIntegrity(bucketName, objectName, expectedHash);
    return { isValid };
  }
}
