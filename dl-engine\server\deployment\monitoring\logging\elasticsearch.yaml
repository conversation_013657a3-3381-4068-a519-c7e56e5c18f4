# DL-Engine Elasticsearch 日志系统配置
# 支持分布式日志收集、存储和分析

apiVersion: v1
kind: Namespace
metadata:
  name: dl-engine-logging
  labels:
    name: dl-engine-logging
    app.kubernetes.io/name: dl-engine
    app.kubernetes.io/component: logging

---
# Elasticsearch配置
apiVersion: v1
kind: ConfigMap
metadata:
  name: elasticsearch-config
  namespace: dl-engine-logging
  labels:
    app.kubernetes.io/name: elasticsearch
    app.kubernetes.io/component: config
data:
  elasticsearch.yml: |
    cluster.name: dl-engine-logs
    node.name: ${HOSTNAME}
    node.data: true
    node.master: true
    node.ingest: true
    
    # 网络配置
    network.host: 0.0.0.0
    http.port: 9200
    transport.port: 9300
    
    # 发现配置
    discovery.seed_hosts: ["elasticsearch-0.elasticsearch-headless.dl-engine-logging.svc.cluster.local:9300"]
    cluster.initial_master_nodes: ["elasticsearch-0"]
    
    # 路径配置
    path.data: /usr/share/elasticsearch/data
    path.logs: /usr/share/elasticsearch/logs
    
    # 内存配置
    bootstrap.memory_lock: false
    
    # 安全配置
    xpack.security.enabled: false
    xpack.monitoring.collection.enabled: true
    
    # 索引配置
    action.auto_create_index: true
    action.destructive_requires_name: true
    
    # 日志配置
    logger.level: INFO
    logger.org.elasticsearch.discovery: DEBUG

  jvm.options: |
    -Xms1g
    -Xmx1g
    -XX:+UseG1GC
    -XX:G1HeapRegionSize=16m
    -XX:+UseG1GC
    -XX:+UnlockExperimentalVMOptions
    -XX:+UseCGroupMemoryLimitForHeap
    -Djava.awt.headless=true
    -Dfile.encoding=UTF-8
    -Djna.nosys=true
    -Djdk.io.permissionsUseCanonicalPath=true
    -Dio.netty.noUnsafe=true
    -Dio.netty.noKeySetOptimization=true
    -Dio.netty.recycler.maxCapacityPerThread=0
    -Dlog4j.shutdownHookEnabled=false
    -Dlog4j2.disable.jmx=true
    -Dlog4j.skipJansi=true

---
# Elasticsearch StatefulSet
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: elasticsearch
  namespace: dl-engine-logging
  labels:
    app.kubernetes.io/name: elasticsearch
    app.kubernetes.io/component: database
spec:
  serviceName: elasticsearch-headless
  replicas: 3
  selector:
    matchLabels:
      app.kubernetes.io/name: elasticsearch
      app.kubernetes.io/component: database
  template:
    metadata:
      labels:
        app.kubernetes.io/name: elasticsearch
        app.kubernetes.io/component: database
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9200"
        prometheus.io/path: "/_prometheus/metrics"
    spec:
      securityContext:
        fsGroup: 1000
      initContainers:
        - name: configure-sysctl
          image: busybox:1.35
          command: ["sysctl", "-w", "vm.max_map_count=262144"]
          securityContext:
            privileged: true
      containers:
        - name: elasticsearch
          image: elasticsearch:8.5.0
          ports:
            - containerPort: 9200
              name: http
              protocol: TCP
            - containerPort: 9300
              name: transport
              protocol: TCP
          env:
            - name: HOSTNAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: ES_JAVA_OPTS
              value: "-Xms1g -Xmx1g"
            - name: ELASTIC_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: elasticsearch-secret
                  key: password
          volumeMounts:
            - name: elasticsearch-config
              mountPath: /usr/share/elasticsearch/config/elasticsearch.yml
              subPath: elasticsearch.yml
            - name: elasticsearch-config
              mountPath: /usr/share/elasticsearch/config/jvm.options
              subPath: jvm.options
            - name: elasticsearch-data
              mountPath: /usr/share/elasticsearch/data
          resources:
            limits:
              cpu: 2000m
              memory: 2Gi
            requests:
              cpu: 1000m
              memory: 1Gi
          livenessProbe:
            httpGet:
              path: /_cluster/health
              port: 9200
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3
          readinessProbe:
            httpGet:
              path: /_cluster/health?wait_for_status=yellow
              port: 9200
            initialDelaySeconds: 10
            periodSeconds: 5
            timeoutSeconds: 3
            failureThreshold: 3
      volumes:
        - name: elasticsearch-config
          configMap:
            name: elasticsearch-config
  volumeClaimTemplates:
    - metadata:
        name: elasticsearch-data
      spec:
        accessModes: ["ReadWriteOnce"]
        resources:
          requests:
            storage: 50Gi

---
# Elasticsearch Headless Service
apiVersion: v1
kind: Service
metadata:
  name: elasticsearch-headless
  namespace: dl-engine-logging
  labels:
    app.kubernetes.io/name: elasticsearch
    app.kubernetes.io/component: headless-service
spec:
  clusterIP: None
  ports:
    - port: 9200
      targetPort: 9200
      protocol: TCP
      name: http
    - port: 9300
      targetPort: 9300
      protocol: TCP
      name: transport
  selector:
    app.kubernetes.io/name: elasticsearch
    app.kubernetes.io/component: database

---
# Elasticsearch Service
apiVersion: v1
kind: Service
metadata:
  name: elasticsearch
  namespace: dl-engine-logging
  labels:
    app.kubernetes.io/name: elasticsearch
    app.kubernetes.io/component: service
spec:
  type: ClusterIP
  ports:
    - port: 9200
      targetPort: 9200
      protocol: TCP
      name: http
  selector:
    app.kubernetes.io/name: elasticsearch
    app.kubernetes.io/component: database

---
# Elasticsearch Secret
apiVersion: v1
kind: Secret
metadata:
  name: elasticsearch-secret
  namespace: dl-engine-logging
  labels:
    app.kubernetes.io/name: elasticsearch
    app.kubernetes.io/component: secret
type: Opaque
data:
  password: ZGwtZW5naW5lLWVsYXN0aWMtcGFzc3dvcmQ= # dl-engine-elastic-password

---
# Kibana配置
apiVersion: v1
kind: ConfigMap
metadata:
  name: kibana-config
  namespace: dl-engine-logging
  labels:
    app.kubernetes.io/name: kibana
    app.kubernetes.io/component: config
data:
  kibana.yml: |
    server.name: kibana
    server.host: 0.0.0.0
    server.port: 5601
    
    # Elasticsearch配置
    elasticsearch.hosts: ["http://elasticsearch:9200"]
    elasticsearch.username: "elastic"
    elasticsearch.password: "${ELASTIC_PASSWORD}"
    
    # 监控配置
    monitoring.ui.container.elasticsearch.enabled: true
    
    # 安全配置
    xpack.security.enabled: false
    xpack.encryptedSavedObjects.encryptionKey: "dl-engine-kibana-encryption-key-32-chars"
    
    # 中文支持
    i18n.locale: "zh-CN"
    
    # 日志配置
    logging.level: info
    logging.appenders:
      default:
        type: console
        layout:
          type: json

---
# Kibana Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: kibana
  namespace: dl-engine-logging
  labels:
    app.kubernetes.io/name: kibana
    app.kubernetes.io/component: visualization
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: kibana
      app.kubernetes.io/component: visualization
  template:
    metadata:
      labels:
        app.kubernetes.io/name: kibana
        app.kubernetes.io/component: visualization
    spec:
      containers:
        - name: kibana
          image: kibana:8.5.0
          ports:
            - containerPort: 5601
              name: http
              protocol: TCP
          env:
            - name: ELASTIC_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: elasticsearch-secret
                  key: password
          volumeMounts:
            - name: kibana-config
              mountPath: /usr/share/kibana/config/kibana.yml
              subPath: kibana.yml
          resources:
            limits:
              cpu: 1000m
              memory: 1Gi
            requests:
              cpu: 500m
              memory: 512Mi
          livenessProbe:
            httpGet:
              path: /api/status
              port: 5601
            initialDelaySeconds: 60
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3
          readinessProbe:
            httpGet:
              path: /api/status
              port: 5601
            initialDelaySeconds: 30
            periodSeconds: 5
            timeoutSeconds: 3
            failureThreshold: 3
      volumes:
        - name: kibana-config
          configMap:
            name: kibana-config
      depends_on:
        - elasticsearch

---
# Kibana Service
apiVersion: v1
kind: Service
metadata:
  name: kibana
  namespace: dl-engine-logging
  labels:
    app.kubernetes.io/name: kibana
    app.kubernetes.io/component: service
spec:
  type: ClusterIP
  ports:
    - port: 5601
      targetPort: 5601
      protocol: TCP
      name: http
  selector:
    app.kubernetes.io/name: kibana
    app.kubernetes.io/component: visualization

---
# Logstash配置
apiVersion: v1
kind: ConfigMap
metadata:
  name: logstash-config
  namespace: dl-engine-logging
  labels:
    app.kubernetes.io/name: logstash
    app.kubernetes.io/component: config
data:
  logstash.yml: |
    http.host: "0.0.0.0"
    path.config: /usr/share/logstash/pipeline
    xpack.monitoring.elasticsearch.hosts: ["http://elasticsearch:9200"]
    xpack.monitoring.enabled: true

  pipelines.yml: |
    - pipeline.id: dl-engine-logs
      path.config: "/usr/share/logstash/pipeline/dl-engine.conf"

  dl-engine.conf: |
    input {
      beats {
        port => 5044
      }
      
      http {
        port => 8080
        codec => json
      }
    }
    
    filter {
      # 解析DL-Engine日志格式
      if [fields][service] {
        mutate {
          add_field => { "service_name" => "%{[fields][service]}" }
        }
      }
      
      # 解析JSON日志
      if [message] =~ /^\{.*\}$/ {
        json {
          source => "message"
        }
      }
      
      # 添加时间戳
      date {
        match => [ "timestamp", "ISO8601" ]
      }
      
      # 地理位置信息
      if [client_ip] {
        geoip {
          source => "client_ip"
          target => "geoip"
        }
      }
      
      # 用户代理解析
      if [user_agent] {
        useragent {
          source => "user_agent"
          target => "ua"
        }
      }
    }
    
    output {
      elasticsearch {
        hosts => ["elasticsearch:9200"]
        index => "dl-engine-logs-%{+YYYY.MM.dd}"
        template_name => "dl-engine-logs"
        template_pattern => "dl-engine-logs-*"
        template => "/usr/share/logstash/templates/dl-engine-template.json"
      }
      
      # 调试输出
      stdout {
        codec => rubydebug
      }
    }

  dl-engine-template.json: |
    {
      "index_patterns": ["dl-engine-logs-*"],
      "settings": {
        "number_of_shards": 1,
        "number_of_replicas": 1,
        "index.refresh_interval": "5s"
      },
      "mappings": {
        "properties": {
          "@timestamp": {
            "type": "date"
          },
          "level": {
            "type": "keyword"
          },
          "message": {
            "type": "text",
            "analyzer": "standard"
          },
          "service_name": {
            "type": "keyword"
          },
          "user_id": {
            "type": "keyword"
          },
          "session_id": {
            "type": "keyword"
          },
          "request_id": {
            "type": "keyword"
          },
          "client_ip": {
            "type": "ip"
          },
          "geoip": {
            "properties": {
              "location": {
                "type": "geo_point"
              },
              "country_name": {
                "type": "keyword"
              },
              "city_name": {
                "type": "keyword"
              }
            }
          }
        }
      }
    }
