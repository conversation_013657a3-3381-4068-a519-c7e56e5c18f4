/**
 * DL-Engine Ollama集成服务
 * 
 * 核心功能：
 * - 模型管理 (模型加载、版本管理、性能监控、资源调度)
 * - 嵌入服务 (文本嵌入、图像嵌入、向量生成、相似度计算)
 * - 推理服务 (模型推理、批量处理、结果缓存、API接口)
 */

import { Injectable, OnModuleInit, BadRequestException, ServiceUnavailableException } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import { InjectRepository } from '@nestjs/typeorm'
import { Repository } from 'typeorm'
import { HttpService } from '@nestjs/axios'
import { firstValueFrom } from 'rxjs'
import { Ollama } from 'ollama'

import { AIModel } from '../entities/ai-model.entity'
import { GenerateTextDto, GenerateEmbeddingDto, ChatDto } from './dto/ollama.dto'

export interface ModelInfo {
  name: string
  size: number
  digest: string
  details: {
    format: string
    family: string
    families: string[]
    parameter_size: string
    quantization_level: string
  }
  modified_at: string
}

export interface ModelPerformance {
  modelName: string
  averageResponseTime: number
  totalRequests: number
  successRate: number
  lastUsed: Date
  memoryUsage: number
}

@Injectable()
export class OllamaService implements OnModuleInit {
  private ollama: Ollama
  private modelPerformance = new Map<string, ModelPerformance>()
  private isHealthy = false

  constructor(
    @InjectRepository(AIModel)
    private readonly aiModelRepository: Repository<AIModel>,
    private readonly configService: ConfigService,
    private readonly httpService: HttpService
  ) {
    this.initializeOllama()
  }

  async onModuleInit() {
    await this.checkOllamaHealth()
    await this.syncModels()
    this.startPerformanceMonitoring()
  }

  /**
   * 生成文本
   */
  async generateText(dto: GenerateTextDto) {
    const startTime = Date.now()
    
    try {
      await this.ensureModelLoaded(dto.model)
      
      const response = await this.ollama.generate({
        model: dto.model,
        prompt: dto.prompt,
        system: dto.system,
        options: {
          temperature: dto.temperature || 0.7,
          top_p: dto.topP || 0.9,
          top_k: dto.topK || 40,
          num_predict: dto.maxTokens || 2048,
          stop: dto.stop
        },
        stream: false
      })

      const responseTime = Date.now() - startTime
      this.updateModelPerformance(dto.model, responseTime, true)

      return {
        text: response.response,
        model: dto.model,
        responseTime,
        tokens: {
          prompt: response.prompt_eval_count || 0,
          completion: response.eval_count || 0,
          total: (response.prompt_eval_count || 0) + (response.eval_count || 0)
        },
        performance: {
          promptEvalTime: response.prompt_eval_duration || 0,
          evalTime: response.eval_duration || 0,
          totalTime: response.total_duration || 0
        }
      }

    } catch (error) {
      const responseTime = Date.now() - startTime
      this.updateModelPerformance(dto.model, responseTime, false)
      
      console.error('文本生成失败:', error)
      throw new BadRequestException('文本生成失败: ' + error.message)
    }
  }

  /**
   * 生成嵌入向量
   */
  async generateEmbedding(dto: GenerateEmbeddingDto) {
    const startTime = Date.now()
    
    try {
      await this.ensureModelLoaded(dto.model)
      
      const response = await this.ollama.embeddings({
        model: dto.model,
        prompt: dto.text
      })

      const responseTime = Date.now() - startTime
      this.updateModelPerformance(dto.model, responseTime, true)

      return {
        embedding: response.embedding,
        model: dto.model,
        dimensions: response.embedding.length,
        responseTime,
        text: dto.text
      }

    } catch (error) {
      const responseTime = Date.now() - startTime
      this.updateModelPerformance(dto.model, responseTime, false)
      
      console.error('嵌入生成失败:', error)
      throw new BadRequestException('嵌入生成失败: ' + error.message)
    }
  }

  /**
   * 对话聊天
   */
  async chat(dto: ChatDto) {
    const startTime = Date.now()
    
    try {
      await this.ensureModelLoaded(dto.model)
      
      const response = await this.ollama.chat({
        model: dto.model,
        messages: dto.messages,
        options: {
          temperature: dto.temperature || 0.7,
          top_p: dto.topP || 0.9,
          top_k: dto.topK || 40,
          num_predict: dto.maxTokens || 2048
        },
        stream: false
      })

      const responseTime = Date.now() - startTime
      this.updateModelPerformance(dto.model, responseTime, true)

      return {
        message: response.message,
        model: dto.model,
        responseTime,
        tokens: {
          prompt: response.prompt_eval_count || 0,
          completion: response.eval_count || 0,
          total: (response.prompt_eval_count || 0) + (response.eval_count || 0)
        },
        performance: {
          promptEvalTime: response.prompt_eval_duration || 0,
          evalTime: response.eval_duration || 0,
          totalTime: response.total_duration || 0
        }
      }

    } catch (error) {
      const responseTime = Date.now() - startTime
      this.updateModelPerformance(dto.model, responseTime, false)
      
      console.error('对话失败:', error)
      throw new BadRequestException('对话失败: ' + error.message)
    }
  }

  /**
   * 获取可用模型列表
   */
  async getModels() {
    try {
      const response = await this.ollama.list()
      return response.models.map(model => ({
        name: model.name,
        size: model.size,
        digest: model.digest,
        modified_at: model.modified_at,
        details: model.details
      }))
    } catch (error) {
      console.error('获取模型列表失败:', error)
      throw new ServiceUnavailableException('无法获取模型列表')
    }
  }

  /**
   * 拉取模型
   */
  async pullModel(modelName: string) {
    try {
      const response = await this.ollama.pull({
        model: modelName,
        stream: false
      })

      // 更新数据库中的模型信息
      await this.updateModelInDatabase(modelName)

      return {
        model: modelName,
        status: 'pulled',
        digest: response.digest
      }

    } catch (error) {
      console.error('拉取模型失败:', error)
      throw new BadRequestException('拉取模型失败: ' + error.message)
    }
  }

  /**
   * 删除模型
   */
  async deleteModel(modelName: string) {
    try {
      await this.ollama.delete({ model: modelName })
      
      // 从数据库中删除模型信息
      await this.aiModelRepository.delete({ name: modelName })
      
      // 清除性能统计
      this.modelPerformance.delete(modelName)

      return {
        model: modelName,
        status: 'deleted'
      }

    } catch (error) {
      console.error('删除模型失败:', error)
      throw new BadRequestException('删除模型失败: ' + error.message)
    }
  }

  /**
   * 获取模型性能统计
   */
  getModelPerformance(modelName?: string) {
    if (modelName) {
      return this.modelPerformance.get(modelName) || null
    }
    
    return Array.from(this.modelPerformance.entries()).map(([name, perf]) => ({
      modelName: name,
      ...perf
    }))
  }

  /**
   * 获取服务健康状态
   */
  async getHealthStatus() {
    try {
      await this.ollama.list()
      this.isHealthy = true
      
      return {
        healthy: true,
        ollamaVersion: await this.getOllamaVersion(),
        modelsCount: this.modelPerformance.size,
        totalRequests: Array.from(this.modelPerformance.values())
          .reduce((sum, perf) => sum + perf.totalRequests, 0)
      }
    } catch (error) {
      this.isHealthy = false
      return {
        healthy: false,
        error: error.message
      }
    }
  }

  /**
   * 批量生成嵌入
   */
  async batchGenerateEmbeddings(model: string, texts: string[]) {
    const results = []
    const batchSize = 10 // 每批处理10个文本
    
    for (let i = 0; i < texts.length; i += batchSize) {
      const batch = texts.slice(i, i + batchSize)
      const batchPromises = batch.map(text => 
        this.generateEmbedding({ model, text })
      )
      
      const batchResults = await Promise.allSettled(batchPromises)
      results.push(...batchResults)
    }

    return results.map((result, index) => ({
      index,
      text: texts[index],
      success: result.status === 'fulfilled',
      embedding: result.status === 'fulfilled' ? result.value.embedding : null,
      error: result.status === 'rejected' ? result.reason.message : null
    }))
  }

  // 私有方法

  private initializeOllama() {
    const host = this.configService.get('OLLAMA_HOST', 'localhost')
    const port = this.configService.get('OLLAMA_PORT', '11434')
    
    this.ollama = new Ollama({
      host: `http://${host}:${port}`
    })
  }

  private async checkOllamaHealth() {
    try {
      await this.ollama.list()
      this.isHealthy = true
      console.log('Ollama服务连接成功')
    } catch (error) {
      this.isHealthy = false
      console.error('Ollama服务连接失败:', error)
      throw new ServiceUnavailableException('Ollama服务不可用')
    }
  }

  private async syncModels() {
    try {
      const models = await this.getModels()
      
      for (const model of models) {
        await this.updateModelInDatabase(model.name, model)
      }
      
      console.log(`同步了${models.length}个模型`)
    } catch (error) {
      console.error('同步模型失败:', error)
    }
  }

  private async updateModelInDatabase(modelName: string, modelInfo?: any) {
    const existingModel = await this.aiModelRepository.findOne({
      where: { name: modelName }
    })

    if (existingModel) {
      existingModel.lastUsed = new Date()
      if (modelInfo) {
        existingModel.size = modelInfo.size
        existingModel.digest = modelInfo.digest
        existingModel.details = JSON.stringify(modelInfo.details)
      }
      await this.aiModelRepository.save(existingModel)
    } else {
      const newModel = this.aiModelRepository.create({
        name: modelName,
        type: 'ollama',
        status: 'available',
        size: modelInfo?.size || 0,
        digest: modelInfo?.digest || '',
        details: modelInfo ? JSON.stringify(modelInfo.details) : '{}',
        createdAt: new Date(),
        lastUsed: new Date()
      })
      await this.aiModelRepository.save(newModel)
    }
  }

  private async ensureModelLoaded(modelName: string) {
    if (!this.isHealthy) {
      throw new ServiceUnavailableException('Ollama服务不可用')
    }

    // 检查模型是否存在
    const models = await this.getModels()
    const modelExists = models.some(model => model.name === modelName)
    
    if (!modelExists) {
      throw new BadRequestException(`模型 ${modelName} 不存在`)
    }
  }

  private updateModelPerformance(modelName: string, responseTime: number, success: boolean) {
    const existing = this.modelPerformance.get(modelName) || {
      modelName,
      averageResponseTime: 0,
      totalRequests: 0,
      successRate: 0,
      lastUsed: new Date(),
      memoryUsage: 0
    }

    existing.totalRequests++
    existing.averageResponseTime = (existing.averageResponseTime * (existing.totalRequests - 1) + responseTime) / existing.totalRequests
    existing.successRate = success 
      ? (existing.successRate * (existing.totalRequests - 1) + 1) / existing.totalRequests
      : (existing.successRate * (existing.totalRequests - 1)) / existing.totalRequests
    existing.lastUsed = new Date()

    this.modelPerformance.set(modelName, existing)
  }

  private startPerformanceMonitoring() {
    // 每5分钟更新一次性能统计
    setInterval(() => {
      this.savePerformanceStats()
    }, 5 * 60 * 1000)
  }

  private async savePerformanceStats() {
    for (const [modelName, perf] of this.modelPerformance.entries()) {
      await this.updateModelInDatabase(modelName)
    }
  }

  private async getOllamaVersion(): Promise<string> {
    try {
      const response = await firstValueFrom(
        this.httpService.get(`${this.ollama.host}/api/version`)
      )
      return response.data.version || 'unknown'
    } catch {
      return 'unknown'
    }
  }
}
