import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn, Index } from 'typeorm'
import { Assignment } from './assignment.entity'

@Entity('submissions')
@Index(['assignmentId'])
@Index(['studentId'])
@Index(['status'])
@Index(['submittedAt'])
export class Submission {
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Column({ type: 'text', nullable: true })
  content: string

  @Column({ type: 'json', nullable: true })
  attachments: any // 附件列表

  @Column({ 
    type: 'enum',
    enum: ['draft', 'submitted', 'graded', 'returned'],
    default: 'draft'
  })
  status: string

  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: true })
  score: number

  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: true })
  maxScore: number

  @Column({ type: 'text', nullable: true })
  feedback: string

  @Column({ type: 'json', nullable: true })
  gradingRubric: any // 评分标准

  @Column({ type: 'int', default: 0 })
  attemptNumber: number

  @Column({ type: 'datetime', nullable: true })
  submittedAt: Date

  @Column({ type: 'datetime', nullable: true })
  gradedAt: Date

  @Column({ length: 36, nullable: true })
  gradedBy: string // 评分教师ID

  // 关联关系
  @Column({ name: 'assignment_id' })
  assignmentId: string

  @Column({ name: 'student_id' })
  studentId: string

  @ManyToOne(() => Assignment, assignment => assignment.submissions)
  @JoinColumn({ name: 'assignment_id' })
  assignment: Assignment

  @CreateDateColumn()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date
}
