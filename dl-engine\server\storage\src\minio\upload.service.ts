import { Injectable, Logger } from '@nestjs/common';
import { MinioService } from './minio.service';
import { AccessControlService } from './access-control.service';
import * as crypto from 'crypto';

export interface UploadOptions {
  bucketName: string;
  objectName?: string;
  contentType?: string;
  metadata?: Record<string, string>;
  maxFileSize?: number;
  allowedMimeTypes?: string[];
  enableChunking?: boolean;
  chunkSize?: number;
  enableResumable?: boolean;
}

export interface UploadResult {
  objectName: string;
  etag: string;
  size: number;
  contentType: string;
  url: string;
  metadata?: Record<string, string>;
}

export interface ChunkUploadSession {
  sessionId: string;
  bucketName: string;
  objectName: string;
  uploadId: string;
  totalSize: number;
  chunkSize: number;
  uploadedChunks: Array<{
    partNumber: number;
    etag: string;
    size: number;
  }>;
  createdAt: Date;
  expiresAt: Date;
}

@Injectable()
export class UploadService {
  private readonly logger = new Logger(UploadService.name);
  private readonly uploadSessions = new Map<string, ChunkUploadSession>();
  private readonly defaultChunkSize = 5 * 1024 * 1024; // 5MB
  private readonly maxFileSize = 100 * 1024 * 1024; // 100MB

  constructor(
    private minioService: MinioService,
    private accessControlService: AccessControlService,
  ) {
    // 定期清理过期的上传会话
    setInterval(() => this.cleanupExpiredSessions(), 60000); // 每分钟清理一次
  }

  /**
   * 单文件上传
   */
  async uploadFile(
    file: Buffer,
    options: UploadOptions,
    userId?: string
  ): Promise<UploadResult> {
    try {
      // 验证文件大小
      if (options.maxFileSize && file.length > options.maxFileSize) {
        throw new Error(`File size exceeds limit: ${options.maxFileSize} bytes`);
      }

      // 验证MIME类型
      if (options.allowedMimeTypes && options.contentType) {
        if (!options.allowedMimeTypes.includes(options.contentType)) {
          throw new Error(`File type not allowed: ${options.contentType}`);
        }
      }

      // 检查访问权限
      if (userId) {
        const hasPermission = await this.accessControlService.checkUploadPermission(
          userId,
          options.bucketName,
          'write'
        );
        if (!hasPermission) {
          throw new Error('Insufficient permissions for upload');
        }
      }

      // 生成对象名称
      const objectName = options.objectName || await this.generateObjectName();

      // 计算文件哈希
      const fileHash = crypto.createHash('md5').update(file).digest('hex');
      const metadata = {
        ...options.metadata,
        'file-hash': fileHash,
        'upload-time': new Date().toISOString(),
        'uploaded-by': userId || 'anonymous',
      };

      // 上传文件
      const result = await this.minioService.putObject(
        options.bucketName,
        objectName,
        file,
        metadata
      );

      // 生成访问URL
      const url = await this.minioService.getPresignedUrl(
        'GET',
        options.bucketName,
        objectName,
        { expires: 3600 }
      );

      this.logger.log(`File uploaded successfully: ${options.bucketName}/${objectName}`);

      return {
        objectName,
        etag: result.etag,
        size: file.length,
        contentType: options.contentType || 'application/octet-stream',
        url,
        metadata,
      };
    } catch (error) {
      this.logger.error('File upload failed:', error);
      throw error;
    }
  }

  /**
   * 初始化分片上传
   */
  async initiateChunkUpload(
    options: UploadOptions & { totalSize: number },
    userId?: string
  ): Promise<{ sessionId: string; chunkSize: number }> {
    try {
      // 检查权限
      if (userId) {
        const hasPermission = await this.accessControlService.checkUploadPermission(
          userId,
          options.bucketName,
          'write'
        );
        if (!hasPermission) {
          throw new Error('Insufficient permissions for upload');
        }
      }

      // 验证文件大小
      if (options.maxFileSize && options.totalSize > options.maxFileSize) {
        throw new Error(`File size exceeds limit: ${options.maxFileSize} bytes`);
      }

      const objectName = options.objectName || await this.generateObjectName();
      const chunkSize = options.chunkSize || this.defaultChunkSize;

      // 初始化多部分上传
      const uploadId = await this.minioService.initiateMultipartUpload(
        options.bucketName,
        objectName,
        options.metadata
      );

      // 创建上传会话
      const sessionId = this.generateSessionId();
      const session: ChunkUploadSession = {
        sessionId,
        bucketName: options.bucketName,
        objectName,
        uploadId,
        totalSize: options.totalSize,
        chunkSize,
        uploadedChunks: [],
        createdAt: new Date(),
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24小时后过期
      };

      this.uploadSessions.set(sessionId, session);

      this.logger.log(`Chunk upload initiated: ${sessionId} for ${options.bucketName}/${objectName}`);

      return { sessionId, chunkSize };
    } catch (error) {
      this.logger.error('Failed to initiate chunk upload:', error);
      throw error;
    }
  }

  /**
   * 上传文件块
   */
  async uploadChunk(
    sessionId: string,
    chunkNumber: number,
    chunkData: Buffer,
    userId?: string
  ): Promise<{ success: boolean; etag: string }> {
    try {
      const session = this.uploadSessions.get(sessionId);
      if (!session) {
        throw new Error('Upload session not found or expired');
      }

      // 检查会话是否过期
      if (new Date() > session.expiresAt) {
        await this.abortChunkUpload(sessionId);
        throw new Error('Upload session expired');
      }

      // 检查权限
      if (userId) {
        const hasPermission = await this.accessControlService.checkUploadPermission(
          userId,
          session.bucketName,
          'write'
        );
        if (!hasPermission) {
          throw new Error('Insufficient permissions for upload');
        }
      }

      // 验证块大小
      const expectedSize = Math.min(session.chunkSize, session.totalSize - (chunkNumber - 1) * session.chunkSize);
      if (chunkData.length !== expectedSize) {
        throw new Error(`Invalid chunk size: expected ${expectedSize}, got ${chunkData.length}`);
      }

      // 上传块
      const result = await this.minioService.uploadPart(
        session.bucketName,
        session.objectName,
        session.uploadId,
        chunkNumber,
        chunkData
      );

      // 记录已上传的块
      session.uploadedChunks.push({
        partNumber: chunkNumber,
        etag: result.etag,
        size: chunkData.length,
      });

      // 按部分号排序
      session.uploadedChunks.sort((a, b) => a.partNumber - b.partNumber);

      this.logger.debug(`Chunk uploaded: ${sessionId} part ${chunkNumber}`);

      return { success: true, etag: result.etag };
    } catch (error) {
      this.logger.error(`Failed to upload chunk ${chunkNumber} for session ${sessionId}:`, error);
      throw error;
    }
  }

  /**
   * 完成分片上传
   */
  async completeChunkUpload(sessionId: string, userId?: string): Promise<UploadResult> {
    try {
      const session = this.uploadSessions.get(sessionId);
      if (!session) {
        throw new Error('Upload session not found or expired');
      }

      // 检查权限
      if (userId) {
        const hasPermission = await this.accessControlService.checkUploadPermission(
          userId,
          session.bucketName,
          'write'
        );
        if (!hasPermission) {
          throw new Error('Insufficient permissions for upload');
        }
      }

      // 验证所有块都已上传
      const expectedChunks = Math.ceil(session.totalSize / session.chunkSize);
      if (session.uploadedChunks.length !== expectedChunks) {
        throw new Error(`Missing chunks: expected ${expectedChunks}, got ${session.uploadedChunks.length}`);
      }

      // 完成多部分上传
      const result = await this.minioService.completeMultipartUpload(
        session.bucketName,
        session.objectName,
        session.uploadId,
        session.uploadedChunks
      );

      // 生成访问URL
      const url = await this.minioService.getPresignedUrl(
        'GET',
        session.bucketName,
        session.objectName,
        { expires: 3600 }
      );

      // 清理会话
      this.uploadSessions.delete(sessionId);

      this.logger.log(`Chunk upload completed: ${sessionId} for ${session.bucketName}/${session.objectName}`);

      return {
        objectName: session.objectName,
        etag: result.etag,
        size: session.totalSize,
        contentType: 'application/octet-stream',
        url,
      };
    } catch (error) {
      this.logger.error(`Failed to complete chunk upload for session ${sessionId}:`, error);
      throw error;
    }
  }

  /**
   * 中止分片上传
   */
  async abortChunkUpload(sessionId: string): Promise<void> {
    try {
      const session = this.uploadSessions.get(sessionId);
      if (!session) {
        return; // 会话不存在，可能已经清理
      }

      // 中止多部分上传
      await this.minioService.abortMultipartUpload(
        session.bucketName,
        session.objectName,
        session.uploadId
      );

      // 清理会话
      this.uploadSessions.delete(sessionId);

      this.logger.log(`Chunk upload aborted: ${sessionId}`);
    } catch (error) {
      this.logger.error(`Failed to abort chunk upload for session ${sessionId}:`, error);
      throw error;
    }
  }

  /**
   * 获取上传进度
   */
  getUploadProgress(sessionId: string): {
    totalSize: number;
    uploadedSize: number;
    progress: number;
    uploadedChunks: number;
    totalChunks: number;
  } | null {
    const session = this.uploadSessions.get(sessionId);
    if (!session) {
      return null;
    }

    const uploadedSize = session.uploadedChunks.reduce((sum, chunk) => sum + chunk.size, 0);
    const progress = (uploadedSize / session.totalSize) * 100;
    const totalChunks = Math.ceil(session.totalSize / session.chunkSize);

    return {
      totalSize: session.totalSize,
      uploadedSize,
      progress: Math.round(progress * 100) / 100,
      uploadedChunks: session.uploadedChunks.length,
      totalChunks,
    };
  }

  /**
   * 断点续传 - 获取已上传的块列表
   */
  getUploadedChunks(sessionId: string): number[] {
    const session = this.uploadSessions.get(sessionId);
    if (!session) {
      return [];
    }

    return session.uploadedChunks.map(chunk => chunk.partNumber);
  }

  /**
   * 批量上传文件
   */
  async uploadMultipleFiles(
    files: Array<{ data: Buffer; options: UploadOptions }>,
    userId?: string
  ): Promise<UploadResult[]> {
    const results: UploadResult[] = [];
    const errors: Error[] = [];

    for (const { data, options } of files) {
      try {
        const result = await this.uploadFile(data, options, userId);
        results.push(result);
      } catch (error) {
        errors.push(error);
        this.logger.error(`Failed to upload file in batch:`, error);
      }
    }

    if (errors.length > 0) {
      this.logger.warn(`Batch upload completed with ${errors.length} errors out of ${files.length} files`);
    }

    return results;
  }

  /**
   * 验证文件完整性
   */
  async verifyFileIntegrity(
    bucketName: string,
    objectName: string,
    expectedHash: string
  ): Promise<boolean> {
    try {
      return await this.minioService.validateObjectIntegrity(bucketName, objectName, expectedHash);
    } catch (error) {
      this.logger.error(`Failed to verify file integrity for ${bucketName}/${objectName}:`, error);
      return false;
    }
  }

  private generateObjectName(): string {
    const timestamp = Date.now();
    const random = crypto.randomBytes(8).toString('hex');
    return `uploads/${timestamp}_${random}`;
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${crypto.randomBytes(16).toString('hex')}`;
  }

  private cleanupExpiredSessions(): void {
    const now = new Date();
    const expiredSessions: string[] = [];

    for (const [sessionId, session] of this.uploadSessions.entries()) {
      if (now > session.expiresAt) {
        expiredSessions.push(sessionId);
      }
    }

    for (const sessionId of expiredSessions) {
      this.abortChunkUpload(sessionId).catch(error => {
        this.logger.error(`Failed to cleanup expired session ${sessionId}:`, error);
      });
    }

    if (expiredSessions.length > 0) {
      this.logger.log(`Cleaned up ${expiredSessions.length} expired upload sessions`);
    }
  }
}
