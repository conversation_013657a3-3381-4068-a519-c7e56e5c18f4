-- DL-Engine 数据库初始化脚本

-- 创建数据库
CREATE DATABASE IF NOT EXISTS `dl_engine` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS `dl_engine_auth` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS `dl_engine_media` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS `dl_engine_ai` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER IF NOT EXISTS 'dl_engine'@'%' IDENTIFIED BY 'dl_engine_password';

-- 授权
GRANT ALL PRIVILEGES ON `dl_engine`.* TO 'dl_engine'@'%';
GRANT ALL PRIVILEGES ON `dl_engine_auth`.* TO 'dl_engine'@'%';
GRANT ALL PRIVILEGES ON `dl_engine_media`.* TO 'dl_engine'@'%';
GRANT ALL PRIVILEGES ON `dl_engine_ai`.* TO 'dl_engine'@'%';

-- 刷新权限
FLUSH PRIVILEGES;

-- 使用主数据库
USE `dl_engine`;

-- 创建基础表结构（如果TypeORM同步未启用）

-- 用户表
CREATE TABLE IF NOT EXISTS `users` (
  `id` varchar(36) NOT NULL,
  `phone` varchar(20) NOT NULL,
  `country_code` varchar(10) NOT NULL DEFAULT '+86',
  `username` varchar(50) NULL,
  `email` varchar(100) NULL,
  `avatar_url` text NULL,
  `nickname` varchar(100) NULL,
  `bio` text NULL,
  `status` enum('active','inactive','suspended') NOT NULL DEFAULT 'active',
  `last_login_at` datetime NULL,
  `created_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `updated_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`id`),
  UNIQUE KEY `IDX_users_phone_country` (`phone`, `country_code`),
  KEY `IDX_users_status` (`status`),
  KEY `IDX_users_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 项目表
CREATE TABLE IF NOT EXISTS `projects` (
  `id` varchar(36) NOT NULL,
  `name` varchar(200) NOT NULL,
  `description` text NULL,
  `type` enum('3d_scene','vr_experience','ar_app','game') NOT NULL DEFAULT '3d_scene',
  `status` enum('draft','published','archived') NOT NULL DEFAULT 'draft',
  `visibility` enum('private','public','shared') NOT NULL DEFAULT 'private',
  `thumbnail_url` text NULL,
  `settings` json NULL,
  `metadata` json NULL,
  `owner_id` varchar(36) NOT NULL,
  `created_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `updated_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`id`),
  KEY `IDX_projects_owner_id` (`owner_id`),
  KEY `IDX_projects_status` (`status`),
  KEY `IDX_projects_type` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 课程表
CREATE TABLE IF NOT EXISTS `courses` (
  `id` varchar(36) NOT NULL,
  `title` varchar(200) NOT NULL,
  `description` text NULL,
  `category` varchar(100) NULL,
  `difficulty` enum('beginner','intermediate','advanced') NOT NULL DEFAULT 'beginner',
  `status` enum('draft','published','archived') NOT NULL DEFAULT 'draft',
  `thumbnail_url` text NULL,
  `estimated_hours` int NOT NULL DEFAULT 0,
  `max_students` int NOT NULL DEFAULT 50,
  `current_students` int NOT NULL DEFAULT 0,
  `price` decimal(10,2) NOT NULL DEFAULT 0.00,
  `objectives` json NULL,
  `prerequisites` json NULL,
  `metadata` json NULL,
  `instructor_id` varchar(36) NOT NULL,
  `created_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `updated_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`id`),
  KEY `IDX_courses_instructor_id` (`instructor_id`),
  KEY `IDX_courses_category` (`category`),
  KEY `IDX_courses_difficulty` (`difficulty`),
  KEY `IDX_courses_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 插入示例数据
INSERT IGNORE INTO `users` (`id`, `phone`, `country_code`, `username`, `nickname`, `status`) VALUES
('admin-user-id', '13800138000', '+86', 'admin', '系统管理员', 'active'),
('teacher-user-id', '13800138001', '+86', 'teacher', '示例教师', 'active'),
('student-user-id', '13800138002', '+86', 'student', '示例学生', 'active');

INSERT IGNORE INTO `courses` (`id`, `title`, `description`, `category`, `difficulty`, `status`, `instructor_id`) VALUES
('sample-course-id', '3D建模基础教程', '学习使用3D建模工具创建基础模型', '3D设计', 'beginner', 'published', 'teacher-user-id');

-- 设置字符集
ALTER DATABASE `dl_engine` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER DATABASE `dl_engine_auth` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER DATABASE `dl_engine_media` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER DATABASE `dl_engine_ai` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
