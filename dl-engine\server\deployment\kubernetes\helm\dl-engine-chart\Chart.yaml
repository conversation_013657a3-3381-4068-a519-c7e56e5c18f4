apiVersion: v2
name: dl-engine
description: Digital Learning Engine - 数字化学习引擎Helm Chart
type: application
version: 1.0.0
appVersion: "1.0.0"
home: https://github.com/dl-engine/dl-engine
sources:
  - https://github.com/dl-engine/dl-engine
maintainers:
  - name: DL-Engine Team
    email: <EMAIL>
keywords:
  - education
  - 3d
  - vr
  - ar
  - learning
  - engine
  - kubernetes
annotations:
  category: Education
  licenses: MIT
dependencies:
  - name: mysql
    version: 9.4.6
    repository: https://charts.bitnami.com/bitnami
    condition: mysql.enabled
  - name: redis
    version: 17.3.7
    repository: https://charts.bitnami.com/bitnami
    condition: redis.enabled
  - name: postgresql
    version: 12.1.2
    repository: https://charts.bitnami.com/bitnami
    condition: postgresql.enabled
  - name: minio
    version: 12.1.3
    repository: https://charts.bitnami.com/bitnami
    condition: minio.enabled
  - name: prometheus
    version: 15.18.0
    repository: https://prometheus-community.github.io/helm-charts
    condition: monitoring.prometheus.enabled
  - name: grafana
    version: 6.50.7
    repository: https://grafana.github.io/helm-charts
    condition: monitoring.grafana.enabled
  - name: agones
    version: 1.26.0
    repository: https://agones.dev/chart/stable
    condition: agones.enabled
