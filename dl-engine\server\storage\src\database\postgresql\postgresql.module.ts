import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { PostgreSQLService } from './postgresql.service';
import { PostgreSQLController } from './postgresql.controller';
import { VectorService } from './vector.service';
import { EmbeddingService } from './embedding.service';
import { SimilarityService } from './similarity.service';
import { VectorEntity } from './entities/vector.entity';
import { EmbeddingEntity } from './entities/embedding.entity';
import { DocumentEntity } from './entities/document.entity';
import { KnowledgeGraphEntity } from './entities/knowledge-graph.entity';

@Module({
  imports: [
    TypeOrmModule.forRootAsync({
      name: 'postgresql',
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        type: 'postgres',
        host: configService.get('POSTGRESQL_HOST', 'localhost'),
        port: configService.get('POSTGRESQL_PORT', 5432),
        username: configService.get('POSTGRESQL_USERNAME', 'postgres'),
        password: configService.get('POSTGRESQL_PASSWORD', ''),
        database: configService.get('POSTGRESQL_DATABASE', 'dl_engine_vectors'),
        entities: [
          VectorEntity,
          EmbeddingEntity,
          DocumentEntity,
          KnowledgeGraphEntity,
        ],
        synchronize: configService.get('NODE_ENV') === 'development',
        logging: configService.get('NODE_ENV') === 'development',
        ssl: configService.get('POSTGRESQL_SSL', false) ? {
          rejectUnauthorized: false,
        } : false,
        extra: {
          max: 20,
          idleTimeoutMillis: 30000,
          connectionTimeoutMillis: 2000,
        },
        // 启用向量扩展
        migrations: ['dist/database/postgresql/migrations/*.js'],
        migrationsRun: true,
      }),
      inject: [ConfigService],
    }),
    TypeOrmModule.forFeature([
      VectorEntity,
      EmbeddingEntity,
      DocumentEntity,
      KnowledgeGraphEntity,
    ], 'postgresql'),
  ],
  providers: [
    PostgreSQLService,
    VectorService,
    EmbeddingService,
    SimilarityService,
  ],
  controllers: [PostgreSQLController],
  exports: [
    PostgreSQLService,
    VectorService,
    EmbeddingService,
    SimilarityService,
  ],
})
export class PostgreSQLModule {}
