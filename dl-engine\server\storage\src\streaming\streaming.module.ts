import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { BullModule } from '@nestjs/bull';
import { StreamingController } from './streaming.controller';
import { StreamingService } from './streaming.service';
import { LiveStreamService } from './live-stream.service';
import { VodService } from './vod.service';
import { RTMPService } from './rtmp.service';
import { TranscodingService } from './transcoding.service';
import { RecordingService } from './recording.service';
import { CDNService } from './cdn.service';
import { StreamAnalyticsService } from './stream-analytics.service';
import { MinioModule } from '../minio/minio.module';
import { RedisModule } from '../database/redis/redis.module';

@Module({
  imports: [
    ConfigModule,
    MinioModule,
    RedisModule,
    BullModule.registerQueue(
      { name: 'live-transcoding' },
      { name: 'vod-processing' },
      { name: 'recording' },
      { name: 'stream-analytics' },
    ),
  ],
  controllers: [StreamingController],
  providers: [
    StreamingService,
    LiveStreamService,
    VodService,
    RTMPService,
    TranscodingService,
    RecordingService,
    CDNService,
    StreamAnalyticsService,
  ],
  exports: [
    StreamingService,
    LiveStreamService,
    VodService,
    RTMPService,
    TranscodingService,
    RecordingService,
    CDNService,
    StreamAnalyticsService,
  ],
})
export class StreamingModule {}
