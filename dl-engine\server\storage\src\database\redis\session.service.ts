import { Injectable, Logger } from '@nestjs/common';
import { RedisService } from './redis.service';

export interface SessionData {
  userId: string;
  username: string;
  role: string;
  permissions: string[];
  loginTime: Date;
  lastActivity: Date;
  ip: string;
  userAgent: string;
  deviceInfo?: {
    type: 'desktop' | 'mobile' | 'tablet';
    os: string;
    browser: string;
  };
  metadata?: Record<string, any>;
}

export interface SessionOptions {
  ttl?: number; // 会话过期时间（秒）
  slidingExpiration?: boolean; // 是否滑动过期
  maxSessions?: number; // 用户最大会话数
}

@Injectable()
export class SessionService {
  private readonly logger = new Logger(SessionService.name);
  private readonly sessionPrefix = 'dl:session:';
  private readonly userSessionsPrefix = 'dl:user_sessions:';
  private readonly defaultTTL = 24 * 60 * 60; // 24小时

  constructor(private redisService: RedisService) {}

  /**
   * 创建会话
   */
  async createSession(
    sessionId: string,
    sessionData: SessionData,
    options: SessionOptions = {}
  ): Promise<void> {
    const {
      ttl = this.defaultTTL,
      maxSessions = 5,
    } = options;

    try {
      const sessionKey = `${this.sessionPrefix}${sessionId}`;
      const userSessionsKey = `${this.userSessionsPrefix}${sessionData.userId}`;

      // 检查用户会话数限制
      await this.enforceMaxSessions(sessionData.userId, maxSessions);

      // 设置会话数据
      await this.redisService.set(sessionKey, sessionData, { ttl });

      // 添加到用户会话列表
      await this.redisService.zadd(userSessionsKey, Date.now(), sessionId);
      await this.redisService.expire(userSessionsKey, ttl);

      this.logger.debug(`Session created: ${sessionId} for user ${sessionData.userId}`);
    } catch (error) {
      this.logger.error(`Failed to create session ${sessionId}:`, error);
      throw error;
    }
  }

  /**
   * 获取会话
   */
  async getSession(sessionId: string): Promise<SessionData | null> {
    try {
      const sessionKey = `${this.sessionPrefix}${sessionId}`;
      const sessionData = await this.redisService.get<SessionData>(sessionKey);

      if (!sessionData) {
        return null;
      }

      // 更新最后活动时间
      sessionData.lastActivity = new Date();
      await this.redisService.set(sessionKey, sessionData);

      return sessionData;
    } catch (error) {
      this.logger.error(`Failed to get session ${sessionId}:`, error);
      return null;
    }
  }

  /**
   * 更新会话
   */
  async updateSession(
    sessionId: string,
    updates: Partial<SessionData>,
    options: SessionOptions = {}
  ): Promise<boolean> {
    try {
      const sessionKey = `${this.sessionPrefix}${sessionId}`;
      const existingData = await this.redisService.get<SessionData>(sessionKey);

      if (!existingData) {
        return false;
      }

      const updatedData = {
        ...existingData,
        ...updates,
        lastActivity: new Date(),
      };

      await this.redisService.set(sessionKey, updatedData);

      // 滑动过期
      if (options.slidingExpiration && options.ttl) {
        await this.redisService.expire(sessionKey, options.ttl);
      }

      this.logger.debug(`Session updated: ${sessionId}`);
      return true;
    } catch (error) {
      this.logger.error(`Failed to update session ${sessionId}:`, error);
      return false;
    }
  }

  /**
   * 删除会话
   */
  async deleteSession(sessionId: string): Promise<boolean> {
    try {
      const sessionKey = `${this.sessionPrefix}${sessionId}`;
      
      // 获取会话数据以获取用户ID
      const sessionData = await this.redisService.get<SessionData>(sessionKey);
      
      if (sessionData) {
        const userSessionsKey = `${this.userSessionsPrefix}${sessionData.userId}`;
        await this.redisService.zrem(userSessionsKey, sessionId);
      }

      const deleted = await this.redisService.del(sessionKey);
      
      if (deleted > 0) {
        this.logger.debug(`Session deleted: ${sessionId}`);
        return true;
      }
      
      return false;
    } catch (error) {
      this.logger.error(`Failed to delete session ${sessionId}:`, error);
      return false;
    }
  }

  /**
   * 获取用户所有会话
   */
  async getUserSessions(userId: string): Promise<SessionData[]> {
    try {
      const userSessionsKey = `${this.userSessionsPrefix}${userId}`;
      const sessionIds = await this.redisService.zrange<string>(userSessionsKey, 0, -1);

      const sessions: SessionData[] = [];
      
      for (const sessionId of sessionIds) {
        const sessionData = await this.getSession(sessionId);
        if (sessionData) {
          sessions.push(sessionData);
        }
      }

      return sessions.sort((a, b) => 
        new Date(b.lastActivity).getTime() - new Date(a.lastActivity).getTime()
      );
    } catch (error) {
      this.logger.error(`Failed to get user sessions for ${userId}:`, error);
      return [];
    }
  }

  /**
   * 删除用户所有会话
   */
  async deleteUserSessions(userId: string, excludeSessionId?: string): Promise<number> {
    try {
      const userSessionsKey = `${this.userSessionsPrefix}${userId}`;
      const sessionIds = await this.redisService.zrange<string>(userSessionsKey, 0, -1);

      let deletedCount = 0;

      for (const sessionId of sessionIds) {
        if (excludeSessionId && sessionId === excludeSessionId) {
          continue;
        }

        const deleted = await this.deleteSession(sessionId);
        if (deleted) {
          deletedCount++;
        }
      }

      this.logger.debug(`Deleted ${deletedCount} sessions for user ${userId}`);
      return deletedCount;
    } catch (error) {
      this.logger.error(`Failed to delete user sessions for ${userId}:`, error);
      return 0;
    }
  }

  /**
   * 验证会话
   */
  async validateSession(sessionId: string): Promise<{
    isValid: boolean;
    sessionData?: SessionData;
    reason?: string;
  }> {
    try {
      const sessionData = await this.getSession(sessionId);

      if (!sessionData) {
        return {
          isValid: false,
          reason: 'Session not found',
        };
      }

      // 检查会话是否过期
      const ttl = await this.redisService.ttl(`${this.sessionPrefix}${sessionId}`);
      if (ttl <= 0) {
        await this.deleteSession(sessionId);
        return {
          isValid: false,
          reason: 'Session expired',
        };
      }

      // 检查最后活动时间
      const lastActivity = new Date(sessionData.lastActivity);
      const now = new Date();
      const inactiveTime = (now.getTime() - lastActivity.getTime()) / 1000;
      
      if (inactiveTime > this.defaultTTL) {
        await this.deleteSession(sessionId);
        return {
          isValid: false,
          reason: 'Session inactive too long',
        };
      }

      return {
        isValid: true,
        sessionData,
      };
    } catch (error) {
      this.logger.error(`Failed to validate session ${sessionId}:`, error);
      return {
        isValid: false,
        reason: 'Validation error',
      };
    }
  }

  /**
   * 刷新会话
   */
  async refreshSession(sessionId: string, ttl?: number): Promise<boolean> {
    try {
      const sessionKey = `${this.sessionPrefix}${sessionId}`;
      const sessionData = await this.redisService.get<SessionData>(sessionKey);

      if (!sessionData) {
        return false;
      }

      sessionData.lastActivity = new Date();
      await this.redisService.set(sessionKey, sessionData, { 
        ttl: ttl || this.defaultTTL 
      });

      return true;
    } catch (error) {
      this.logger.error(`Failed to refresh session ${sessionId}:`, error);
      return false;
    }
  }

  /**
   * 获取活跃会话统计
   */
  async getActiveSessionStats(): Promise<{
    totalSessions: number;
    userCount: number;
    sessionsByRole: Record<string, number>;
    sessionsByDevice: Record<string, number>;
  }> {
    try {
      const sessionKeys = await this.redisService.keys(`${this.sessionPrefix}*`);
      const totalSessions = sessionKeys.length;

      const users = new Set<string>();
      const roleStats: Record<string, number> = {};
      const deviceStats: Record<string, number> = {};

      for (const sessionKey of sessionKeys) {
        const sessionData = await this.redisService.get<SessionData>(sessionKey);
        if (sessionData) {
          users.add(sessionData.userId);
          
          roleStats[sessionData.role] = (roleStats[sessionData.role] || 0) + 1;
          
          const deviceType = sessionData.deviceInfo?.type || 'unknown';
          deviceStats[deviceType] = (deviceStats[deviceType] || 0) + 1;
        }
      }

      return {
        totalSessions,
        userCount: users.size,
        sessionsByRole: roleStats,
        sessionsByDevice: deviceStats,
      };
    } catch (error) {
      this.logger.error('Failed to get session stats:', error);
      return {
        totalSessions: 0,
        userCount: 0,
        sessionsByRole: {},
        sessionsByDevice: {},
      };
    }
  }

  /**
   * 清理过期会话
   */
  async cleanupExpiredSessions(): Promise<number> {
    try {
      const sessionKeys = await this.redisService.keys(`${this.sessionPrefix}*`);
      let cleanedCount = 0;

      for (const sessionKey of sessionKeys) {
        const ttl = await this.redisService.ttl(sessionKey);
        if (ttl <= 0) {
          const sessionId = sessionKey.replace(this.sessionPrefix, '');
          await this.deleteSession(sessionId);
          cleanedCount++;
        }
      }

      if (cleanedCount > 0) {
        this.logger.log(`Cleaned up ${cleanedCount} expired sessions`);
      }

      return cleanedCount;
    } catch (error) {
      this.logger.error('Failed to cleanup expired sessions:', error);
      return 0;
    }
  }

  private async enforceMaxSessions(userId: string, maxSessions: number): Promise<void> {
    const userSessionsKey = `${this.userSessionsPrefix}${userId}`;
    const sessionCount = await this.redisService.zcard(userSessionsKey);

    if (sessionCount >= maxSessions) {
      // 删除最旧的会话
      const oldestSessions = await this.redisService.zrange<string>(
        userSessionsKey, 
        0, 
        sessionCount - maxSessions
      );

      for (const sessionId of oldestSessions) {
        await this.deleteSession(sessionId);
      }
    }
  }

  private async zcard(key: string): Promise<number> {
    try {
      return await this.redisService.getClient().zCard(key);
    } catch (error) {
      this.logger.error(`Failed to get zcard for ${key}:`, error);
      return 0;
    }
  }

  private async zrem(key: string, member: any): Promise<number> {
    try {
      return await this.redisService.getClient().zRem(key, member);
    } catch (error) {
      this.logger.error(`Failed to zrem from ${key}:`, error);
      return 0;
    }
  }
}
