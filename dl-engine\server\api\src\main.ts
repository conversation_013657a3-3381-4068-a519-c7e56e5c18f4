import { NestFactory } from '@nestjs/core'
import { ValidationPipe } from '@nestjs/common'
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger'
import { ConfigService } from '@nestjs/config'
import { AppModule } from './app.module'

async function bootstrap() {
  const app = await NestFactory.create(AppModule)
  const configService = app.get(ConfigService)

  // 全局验证管道
  app.useGlobalPipes(new ValidationPipe({
    whitelist: true,
    forbidNonWhitelisted: true,
    transform: true,
    transformOptions: {
      enableImplicitConversion: true
    }
  }))

  // CORS配置
  app.enableCors({
    origin: configService.get('CORS_ORIGIN', '*'),
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'Accept', 'X-Requested-With']
  })

  // API前缀
  app.setGlobalPrefix('api/v1')

  // Swagger文档配置
  if (configService.get('NODE_ENV') !== 'production') {
    const config = new DocumentBuilder()
      .setTitle('DL-Engine API')
      .setDescription('DL-Engine 数字化学习平台 API 文档')
      .setVersion('1.0')
      .addBearerAuth()
      .addTag('用户管理', '用户注册、登录、资料管理')
      .addTag('项目管理', '项目创建、编辑、分享')
      .addTag('场景管理', '3D场景管理')
      .addTag('资产管理', '媒体资产管理')
      .addTag('教育-课程管理', '课程创建、管理')
      .addTag('教育-作业管理', '作业发布、提交、批改')
      .addTag('教育-评估管理', '测验、考试管理')
      .addTag('教育-学习路径', '学习路径规划')
      .addTag('教育-课时管理', '课时内容管理')
      .addTag('社交功能', '好友、群组、消息')
      .addTag('协作功能', '实时协作')
      .addTag('通知系统', '消息推送')
      .addTag('监控系统', '系统监控')
      .build()

    const document = SwaggerModule.createDocument(app, config)
    SwaggerModule.setup('api/docs', app, document, {
      swaggerOptions: {
        persistAuthorization: true,
        tagsSorter: 'alpha',
        operationsSorter: 'alpha'
      }
    })

    console.log(`📚 API文档地址: http://localhost:${configService.get('PORT', 3030)}/api/docs`)
  }

  // 启动服务
  const port = configService.get('PORT', 3030)
  await app.listen(port)

  console.log(`🚀 DL-Engine API服务已启动`)
  console.log(`🌐 服务地址: http://localhost:${port}`)
  console.log(`📊 健康检查: http://localhost:${port}/api/v1/health`)
  console.log(`🔧 环境: ${configService.get('NODE_ENV', 'development')}`)
}

bootstrap().catch(error => {
  console.error('❌ 服务启动失败:', error)
  process.exit(1)
})
