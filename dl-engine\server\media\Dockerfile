# 多阶段构建 - 构建阶段
FROM node:18-alpine AS builder

WORKDIR /app

# 复制package文件
COPY package*.json ./
COPY tsconfig*.json ./

# 安装依赖
RUN npm ci --only=production && npm cache clean --force

# 复制源代码
COPY src/ ./src/

# 构建应用
RUN npm run build

# 生产阶段
FROM node:18-alpine AS production

# 安装FFmpeg和图像处理工具
RUN apk add --no-cache \
    ffmpeg \
    imagemagick \
    vips-dev \
    python3 \
    make \
    g++

# 创建应用用户
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nestjs -u 1001

WORKDIR /app

# 复制package文件
COPY package*.json ./

# 只安装生产依赖
RUN npm ci --only=production && npm cache clean --force

# 从构建阶段复制构建结果
COPY --from=builder --chown=nestjs:nodejs /app/dist ./dist

# 切换到非root用户
USER nestjs

# 暴露端口
EXPOSE 3003

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3003/media/health || exit 1

# 启动应用
CMD ["node", "dist/main.js"]
