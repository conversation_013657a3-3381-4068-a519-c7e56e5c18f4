import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { MinioService } from './minio.service';
import { MinioController } from './minio.controller';
import { UploadService } from './upload.service';
import { StorageService } from './storage.service';
import { AccessControlService } from './access-control.service';
import { CDNService } from './cdn.service';

@Module({
  imports: [ConfigModule],
  providers: [
    {
      provide: 'MINIO_CLIENT',
      useFactory: (configService: ConfigService) => {
        const Minio = require('minio');
        
        return new Minio.Client({
          endPoint: configService.get('MINIO_ENDPOINT', 'localhost'),
          port: configService.get('MINIO_PORT', 9000),
          useSSL: configService.get('MINIO_USE_SSL', false),
          accessKey: configService.get('MINIO_ACCESS_KEY', 'minioadmin'),
          secretKey: configService.get('MINIO_SECRET_KEY', 'minioadmin'),
          region: configService.get('MINIO_REGION', 'us-east-1'),
        });
      },
      inject: [ConfigService],
    },
    MinioService,
    UploadService,
    StorageService,
    AccessControlService,
    CDNService,
  ],
  controllers: [MinioController],
  exports: [
    'MINIO_CLIENT',
    MinioService,
    UploadService,
    StorageService,
    AccessControlService,
    CDNService,
  ],
})
export class MinioModule {}
