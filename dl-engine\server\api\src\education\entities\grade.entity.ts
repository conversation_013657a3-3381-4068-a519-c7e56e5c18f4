import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn, Index } from 'typeorm'
import { Assessment } from './assessment.entity'

@Entity('grades')
@Index(['assessmentId'])
@Index(['studentId'])
@Index(['status'])
export class Grade {
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Column({ type: 'decimal', precision: 5, scale: 2 })
  score: number

  @Column({ type: 'decimal', precision: 5, scale: 2 })
  maxScore: number

  @Column({ type: 'decimal', precision: 5, scale: 2 })
  percentage: number

  @Column({ length: 10, nullable: true })
  letterGrade: string // A, B, C, D, F

  @Column({ 
    type: 'enum',
    enum: ['pending', 'graded', 'reviewed', 'final'],
    default: 'pending'
  })
  status: string

  @Column({ type: 'text', nullable: true })
  feedback: string

  @Column({ type: 'json', nullable: true })
  details: any // 详细评分信息

  @Column({ type: 'int', default: 1 })
  attemptNumber: number

  @Column({ type: 'int', default: 0 })
  timeSpent: number // 用时（秒）

  @Column({ type: 'datetime', nullable: true })
  startedAt: Date

  @Column({ type: 'datetime', nullable: true })
  completedAt: Date

  @Column({ type: 'datetime', nullable: true })
  gradedAt: Date

  @Column({ length: 36, nullable: true })
  gradedBy: string // 评分教师ID

  // 关联关系
  @Column({ name: 'assessment_id' })
  assessmentId: string

  @Column({ name: 'student_id' })
  studentId: string

  @ManyToOne(() => Assessment, assessment => assessment.grades)
  @JoinColumn({ name: 'assessment_id' })
  assessment: Assessment

  @CreateDateColumn()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date
}
