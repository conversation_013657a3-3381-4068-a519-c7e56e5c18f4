import { Injectable, Logger } from '@nestjs/common';
import { DataSource, QueryRunner, EntityManager } from 'typeorm';
import { MySQLService } from './mysql.service';

export interface TransactionContext {
  queryRunner: QueryRunner;
  manager: <PERSON>ti<PERSON><PERSON>anager;
}

export interface TransactionOptions {
  isolationLevel?: 'READ UNCOMMITTED' | 'READ COMMITTED' | 'REPEATABLE READ' | 'SERIALIZABLE';
  timeout?: number; // 毫秒
  retryAttempts?: number;
  retryDelay?: number; // 毫秒
}

@Injectable()
export class TransactionService {
  private readonly logger = new Logger(TransactionService.name);

  constructor(
    private dataSource: DataSource,
    private mysqlService: MySQLService,
  ) {}

  /**
   * 执行事务
   */
  async executeTransaction<T>(
    operation: (context: TransactionContext) => Promise<T>,
    options: TransactionOptions = {},
  ): Promise<T> {
    const {
      isolationLevel = 'READ COMMITTED',
      timeout = 30000,
      retryAttempts = 3,
      retryDelay = 1000,
    } = options;

    let attempt = 0;
    let lastError: Error;

    while (attempt < retryAttempts) {
      const queryRunner = await this.mysqlService.getQueryRunner();
      
      try {
        await queryRunner.connect();
        await queryRunner.startTransaction(isolationLevel);

        // 设置事务超时
        if (timeout > 0) {
          await queryRunner.query(`SET SESSION innodb_lock_wait_timeout = ${Math.ceil(timeout / 1000)}`);
        }

        const context: TransactionContext = {
          queryRunner,
          manager: queryRunner.manager,
        };

        const result = await Promise.race([
          operation(context),
          this.createTimeoutPromise(timeout),
        ]);

        await queryRunner.commitTransaction();
        
        this.logger.debug(`Transaction completed successfully on attempt ${attempt + 1}`);
        return result;

      } catch (error) {
        await queryRunner.rollbackTransaction();
        lastError = error;
        attempt++;

        this.logger.warn(`Transaction failed on attempt ${attempt}:`, error.message);

        // 检查是否是可重试的错误
        if (!this.isRetryableError(error) || attempt >= retryAttempts) {
          break;
        }

        // 等待后重试
        if (retryDelay > 0) {
          await this.delay(retryDelay * attempt);
        }

      } finally {
        await queryRunner.release();
      }
    }

    this.logger.error(`Transaction failed after ${attempt} attempts:`, lastError);
    throw lastError;
  }

  /**
   * 批量操作事务
   */
  async executeBatchTransaction<T>(
    operations: Array<(context: TransactionContext) => Promise<T>>,
    options: TransactionOptions = {},
  ): Promise<T[]> {
    return this.executeTransaction(async (context) => {
      const results: T[] = [];
      
      for (const operation of operations) {
        const result = await operation(context);
        results.push(result);
      }
      
      return results;
    }, options);
  }

  /**
   * 分布式事务（Saga模式）
   */
  async executeSagaTransaction<T>(
    steps: Array<{
      execute: (context: TransactionContext) => Promise<T>;
      compensate: (context: TransactionContext, result?: T) => Promise<void>;
    }>,
    options: TransactionOptions = {},
  ): Promise<T[]> {
    const results: T[] = [];
    const executedSteps: Array<{ step: any; result: T }> = [];

    try {
      return await this.executeTransaction(async (context) => {
        for (const step of steps) {
          const result = await step.execute(context);
          results.push(result);
          executedSteps.push({ step, result });
        }
        return results;
      }, options);

    } catch (error) {
      // 执行补偿操作
      this.logger.warn('Saga transaction failed, executing compensation...');
      await this.executeCompensation(executedSteps);
      throw error;
    }
  }

  /**
   * 读写分离事务
   */
  async executeReadWriteTransaction<T>(
    readOperations: Array<(context: TransactionContext) => Promise<any>>,
    writeOperations: Array<(context: TransactionContext) => Promise<any>>,
    options: TransactionOptions = {},
  ): Promise<{ reads: any[]; writes: any[] }> {
    return this.executeTransaction(async (context) => {
      // 先执行读操作
      const reads = [];
      for (const readOp of readOperations) {
        const result = await readOp(context);
        reads.push(result);
      }

      // 再执行写操作
      const writes = [];
      for (const writeOp of writeOperations) {
        const result = await writeOp(context);
        writes.push(result);
      }

      return { reads, writes };
    }, options);
  }

  /**
   * 嵌套事务（保存点）
   */
  async executeNestedTransaction<T>(
    operation: (context: TransactionContext) => Promise<T>,
    parentContext: TransactionContext,
    savepointName: string = `sp_${Date.now()}`,
  ): Promise<T> {
    const { queryRunner } = parentContext;

    try {
      // 创建保存点
      await queryRunner.query(`SAVEPOINT ${savepointName}`);
      
      const result = await operation(parentContext);
      
      // 释放保存点
      await queryRunner.query(`RELEASE SAVEPOINT ${savepointName}`);
      
      return result;

    } catch (error) {
      // 回滚到保存点
      await queryRunner.query(`ROLLBACK TO SAVEPOINT ${savepointName}`);
      throw error;
    }
  }

  /**
   * 事务统计信息
   */
  async getTransactionStats(): Promise<any> {
    try {
      const [innodb] = await this.dataSource.query(`
        SHOW ENGINE INNODB STATUS
      `);

      const [transactions] = await this.dataSource.query(`
        SELECT 
          COUNT(*) as active_transactions,
          MAX(TIME) as longest_transaction_time
        FROM INFORMATION_SCHEMA.INNODB_TRX
      `);

      const [locks] = await this.dataSource.query(`
        SELECT COUNT(*) as waiting_locks
        FROM INFORMATION_SCHEMA.INNODB_LOCK_WAITS
      `);

      return {
        activeTransactions: transactions.active_transactions || 0,
        longestTransactionTime: transactions.longest_transaction_time || 0,
        waitingLocks: locks.waiting_locks || 0,
        innodbStatus: innodb.Status,
        timestamp: new Date().toISOString(),
      };

    } catch (error) {
      this.logger.error('Failed to get transaction stats:', error);
      return {
        error: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }

  private async executeCompensation(executedSteps: Array<{ step: any; result: any }>): Promise<void> {
    // 逆序执行补偿操作
    for (let i = executedSteps.length - 1; i >= 0; i--) {
      const { step, result } = executedSteps[i];
      
      try {
        await this.executeTransaction(async (context) => {
          await step.compensate(context, result);
        });
        
        this.logger.debug(`Compensation executed for step ${i}`);
        
      } catch (error) {
        this.logger.error(`Compensation failed for step ${i}:`, error);
        // 继续执行其他补偿操作
      }
    }
  }

  private isRetryableError(error: Error): boolean {
    const retryableErrors = [
      'ER_LOCK_WAIT_TIMEOUT',
      'ER_LOCK_DEADLOCK',
      'ER_LOCK_TABLE_FULL',
      'ECONNRESET',
      'ENOTFOUND',
      'ETIMEDOUT',
    ];

    return retryableErrors.some(code => 
      error.message.includes(code) || 
      (error as any).code === code
    );
  }

  private createTimeoutPromise<T>(timeout: number): Promise<T> {
    return new Promise((_, reject) => {
      setTimeout(() => {
        reject(new Error(`Transaction timeout after ${timeout}ms`));
      }, timeout);
    });
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
