/**
 * DL-Engine 第四批次功能验证脚本
 * 验证存储与AI智能服务的完整功能
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

// 服务端点配置
const SERVICES = {
  storage: 'http://localhost:3001',
  ai: 'http://localhost:3002',
  media: 'http://localhost:3003'
};

// 测试结果收集
const testResults = {
  storage: { passed: 0, failed: 0, tests: [] },
  ai: { passed: 0, failed: 0, tests: [] },
  media: { passed: 0, failed: 0, tests: [] }
};

/**
 * 执行测试并记录结果
 */
async function runTest(service, testName, testFn) {
  try {
    console.log(`🧪 测试 [${service}] ${testName}...`);
    await testFn();
    testResults[service].passed++;
    testResults[service].tests.push({ name: testName, status: 'PASSED' });
    console.log(`✅ [${service}] ${testName} - 通过`);
  } catch (error) {
    testResults[service].failed++;
    testResults[service].tests.push({ 
      name: testName, 
      status: 'FAILED', 
      error: error.message 
    });
    console.log(`❌ [${service}] ${testName} - 失败: ${error.message}`);
  }
}

/**
 * 存储服务测试
 */
async function testStorageService() {
  console.log('\n📦 开始测试存储服务...\n');

  // 健康检查
  await runTest('storage', '健康检查', async () => {
    const response = await axios.get(`${SERVICES.storage}/storage/health`);
    if (response.status !== 200) {
      throw new Error(`健康检查失败: ${response.status}`);
    }
  });

  // MySQL连接测试
  await runTest('storage', 'MySQL连接', async () => {
    const response = await axios.get(`${SERVICES.storage}/mysql/health`);
    if (response.data.status !== 'healthy') {
      throw new Error('MySQL连接失败');
    }
  });

  // Redis连接测试
  await runTest('storage', 'Redis连接', async () => {
    const response = await axios.get(`${SERVICES.storage}/redis/ping`);
    if (response.data.status !== 'ok') {
      throw new Error('Redis连接失败');
    }
  });

  // PostgreSQL连接测试
  await runTest('storage', 'PostgreSQL连接', async () => {
    const response = await axios.get(`${SERVICES.storage}/postgresql/health`);
    if (response.data.status !== 'healthy') {
      throw new Error('PostgreSQL连接失败');
    }
  });

  // 存储指标获取
  await runTest('storage', '存储指标', async () => {
    const response = await axios.get(`${SERVICES.storage}/storage/metrics`);
    if (!response.data.databases || !response.data.storage) {
      throw new Error('存储指标数据不完整');
    }
  });

  // 文件上传测试
  await runTest('storage', '文件上传', async () => {
    const testFile = Buffer.from('测试文件内容');
    const formData = new FormData();
    formData.append('file', new Blob([testFile]), 'test.txt');
    
    const response = await axios.post(`${SERVICES.storage}/minio/upload`, formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    });
    
    if (!response.data.fileId) {
      throw new Error('文件上传失败');
    }
  });
}

/**
 * AI智能服务测试
 */
async function testAIService() {
  console.log('\n🤖 开始测试AI智能服务...\n');

  // 健康检查
  await runTest('ai', '健康检查', async () => {
    const response = await axios.get(`${SERVICES.ai}/ai/health`);
    if (response.status !== 200) {
      throw new Error(`健康检查失败: ${response.status}`);
    }
  });

  // AI能力查询
  await runTest('ai', 'AI能力查询', async () => {
    const response = await axios.get(`${SERVICES.ai}/ai/capabilities`);
    if (!response.data.features || !response.data.models) {
      throw new Error('AI能力数据不完整');
    }
  });

  // 文本嵌入生成
  await runTest('ai', '文本嵌入生成', async () => {
    const response = await axios.post(`${SERVICES.ai}/ai/embeddings`, {
      text: '这是一个测试文本',
      model: 'mxbai-embed-large'
    });
    
    if (!response.data.embedding || !Array.isArray(response.data.embedding)) {
      throw new Error('文本嵌入生成失败');
    }
  });

  // 向量搜索
  await runTest('ai', '向量搜索', async () => {
    const response = await axios.post(`${SERVICES.ai}/ai/search`, {
      query: '学习路径',
      limit: 5
    });
    
    if (!Array.isArray(response.data.results)) {
      throw new Error('向量搜索失败');
    }
  });

  // 内容分析
  await runTest('ai', '内容分析', async () => {
    const response = await axios.post(`${SERVICES.ai}/ai/analyze`, {
      text: '这是一个关于数学学习的教育内容，包含三角函数和微积分的知识点。',
      analysisType: 'keywords'
    });
    
    if (!response.data.keywords || !Array.isArray(response.data.keywords)) {
      throw new Error('内容分析失败');
    }
  });

  // 推荐生成
  await runTest('ai', '推荐生成', async () => {
    const response = await axios.post(`${SERVICES.ai}/ai/recommend`, {
      userId: 'test_user_001',
      contentType: 'course',
      limit: 3
    });
    
    if (!Array.isArray(response.data.recommendations)) {
      throw new Error('推荐生成失败');
    }
  });
}

/**
 * 媒体处理服务测试
 */
async function testMediaService() {
  console.log('\n🎬 开始测试媒体处理服务...\n');

  // 健康检查
  await runTest('media', '健康检查', async () => {
    const response = await axios.get(`${SERVICES.media}/media/health`);
    if (response.status !== 200) {
      throw new Error(`健康检查失败: ${response.status}`);
    }
  });

  // 媒体文件列表
  await runTest('media', '媒体文件列表', async () => {
    const response = await axios.get(`${SERVICES.media}/media/files`);
    if (!Array.isArray(response.data.files)) {
      throw new Error('媒体文件列表获取失败');
    }
  });

  // 图像处理能力
  await runTest('media', '图像处理能力', async () => {
    const response = await axios.get(`${SERVICES.media}/media/capabilities/image`);
    if (!response.data.formats || !response.data.operations) {
      throw new Error('图像处理能力查询失败');
    }
  });

  // 视频处理能力
  await runTest('media', '视频处理能力', async () => {
    const response = await axios.get(`${SERVICES.media}/media/capabilities/video`);
    if (!response.data.formats || !response.data.codecs) {
      throw new Error('视频处理能力查询失败');
    }
  });

  // 音频处理能力
  await runTest('media', '音频处理能力', async () => {
    const response = await axios.get(`${SERVICES.media}/media/capabilities/audio`);
    if (!response.data.formats || !response.data.effects) {
      throw new Error('音频处理能力查询失败');
    }
  });
}

/**
 * 生成测试报告
 */
function generateReport() {
  console.log('\n📊 测试报告\n');
  console.log('=' .repeat(60));
  
  let totalPassed = 0;
  let totalFailed = 0;
  
  Object.keys(testResults).forEach(service => {
    const result = testResults[service];
    totalPassed += result.passed;
    totalFailed += result.failed;
    
    console.log(`\n${service.toUpperCase()} 服务:`);
    console.log(`  ✅ 通过: ${result.passed}`);
    console.log(`  ❌ 失败: ${result.failed}`);
    console.log(`  📈 成功率: ${((result.passed / (result.passed + result.failed)) * 100).toFixed(1)}%`);
    
    if (result.failed > 0) {
      console.log('  失败的测试:');
      result.tests.filter(t => t.status === 'FAILED').forEach(test => {
        console.log(`    - ${test.name}: ${test.error}`);
      });
    }
  });
  
  console.log('\n' + '=' .repeat(60));
  console.log(`总计: ${totalPassed + totalFailed} 个测试`);
  console.log(`✅ 通过: ${totalPassed}`);
  console.log(`❌ 失败: ${totalFailed}`);
  console.log(`📈 总成功率: ${((totalPassed / (totalPassed + totalFailed)) * 100).toFixed(1)}%`);
  
  // 保存详细报告
  const reportData = {
    timestamp: new Date().toISOString(),
    summary: {
      total: totalPassed + totalFailed,
      passed: totalPassed,
      failed: totalFailed,
      successRate: ((totalPassed / (totalPassed + totalFailed)) * 100).toFixed(1)
    },
    services: testResults
  };
  
  fs.writeFileSync(
    path.join(__dirname, 'fourth-batch-test-report.json'),
    JSON.stringify(reportData, null, 2)
  );
  
  console.log('\n📄 详细报告已保存到: fourth-batch-test-report.json');
}

/**
 * 主测试函数
 */
async function runAllTests() {
  console.log('🚀 DL-Engine 第四批次功能验证开始\n');
  console.log('测试范围: 存储与AI智能服务');
  console.log('包含服务: 存储服务、AI智能服务、媒体处理服务\n');
  
  try {
    await testStorageService();
    await testAIService();
    await testMediaService();
  } catch (error) {
    console.error('测试执行出错:', error);
  }
  
  generateReport();
  
  const totalTests = Object.values(testResults).reduce((sum, result) => sum + result.passed + result.failed, 0);
  const totalPassed = Object.values(testResults).reduce((sum, result) => sum + result.passed, 0);
  
  if (totalPassed === totalTests) {
    console.log('\n🎉 所有测试通过！第四批次功能验证成功！');
    process.exit(0);
  } else {
    console.log('\n⚠️  部分测试失败，请检查服务状态和配置。');
    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = {
  runAllTests,
  testStorageService,
  testAIService,
  testMediaService
};
