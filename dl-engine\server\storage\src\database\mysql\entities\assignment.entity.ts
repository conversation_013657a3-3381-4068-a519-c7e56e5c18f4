import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { UserEntity } from './user.entity';
import { CourseEntity } from './course.entity';

export enum AssignmentType {
  PROJECT = 'project',
  QUIZ = 'quiz',
  ESSAY = 'essay',
  PRACTICAL = 'practical',
  PRESENTATION = 'presentation',
  PEER_REVIEW = 'peer_review',
}

export enum AssignmentStatus {
  NOT_STARTED = 'not_started',
  IN_PROGRESS = 'in_progress',
  SUBMITTED = 'submitted',
  GRADED = 'graded',
  RETURNED = 'returned',
  OVERDUE = 'overdue',
}

@Entity('assignments')
@Index(['courseId'])
@Index(['studentId'])
@Index(['status'])
@Index(['dueDate'])
export class AssignmentEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 200 })
  title: string;

  @Column({ type: 'text' })
  description: string;

  @Column({ type: 'enum', enum: AssignmentType })
  type: AssignmentType;

  @Column({ type: 'enum', enum: AssignmentStatus, default: AssignmentStatus.NOT_STARTED })
  status: AssignmentStatus;

  @Column({ type: 'json' })
  instructions: {
    objectives: string[];
    requirements: string[];
    deliverables: string[];
    resources: {
      title: string;
      url: string;
      type: 'document' | 'video' | 'link' | 'scene' | 'project';
    }[];
    rubric: {
      criteria: string;
      excellent: string;
      good: string;
      satisfactory: string;
      needsImprovement: string;
      points: number;
    }[];
  };

  @Column({ type: 'json', nullable: true })
  submission: {
    submittedAt: Date;
    files: {
      name: string;
      url: string;
      type: string;
      size: number;
    }[];
    projectId?: string;
    sceneId?: string;
    text?: string;
    metadata: Record<string, any>;
  };

  @Column({ type: 'json', nullable: true })
  grading: {
    score: number;
    maxScore: number;
    percentage: number;
    grade: string;
    feedback: string;
    rubricScores: {
      criteria: string;
      score: number;
      maxScore: number;
      feedback: string;
    }[];
    gradedAt: Date;
    gradedBy: string;
  };

  @Column({ type: 'int', default: 100 })
  maxScore: number;

  @Column({ type: 'datetime' })
  dueDate: Date;

  @Column({ type: 'datetime', nullable: true })
  startDate: Date;

  @Column({ type: 'boolean', default: true })
  allowLateSubmission: boolean;

  @Column({ type: 'int', default: 0 })
  latePenaltyPercent: number;

  @Column({ type: 'int', default: 1 })
  maxAttempts: number;

  @Column({ type: 'int', default: 0 })
  attemptCount: number;

  @Column({ type: 'json', nullable: true })
  attempts: {
    attemptNumber: number;
    submittedAt: Date;
    score?: number;
    feedback?: string;
    files: any[];
  }[];

  @Column({ type: 'boolean', default: false })
  isGroupAssignment: boolean;

  @Column({ type: 'json', nullable: true })
  groupMembers: string[];

  @Column({ type: 'json', nullable: true })
  settings: {
    autoGrade: boolean;
    showScoreImmediately: boolean;
    allowPeerReview: boolean;
    requirePeerReview: boolean;
    anonymousGrading: boolean;
    plagiarismCheck: boolean;
  };

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // 关联关系
  @Column({ type: 'uuid' })
  courseId: string;

  @Column({ type: 'uuid' })
  studentId: string;

  @ManyToOne(() => CourseEntity, (course) => course.assignments)
  @JoinColumn({ name: 'courseId' })
  course: CourseEntity;

  @ManyToOne(() => UserEntity, (user) => user.assignments)
  @JoinColumn({ name: 'studentId' })
  student: UserEntity;

  // 虚拟字段
  get isOverdue(): boolean {
    return new Date() > this.dueDate && this.status !== AssignmentStatus.SUBMITTED && this.status !== AssignmentStatus.GRADED;
  }

  get isSubmitted(): boolean {
    return this.status === AssignmentStatus.SUBMITTED || this.status === AssignmentStatus.GRADED || this.status === AssignmentStatus.RETURNED;
  }

  get isGraded(): boolean {
    return this.status === AssignmentStatus.GRADED || this.status === AssignmentStatus.RETURNED;
  }

  get canSubmit(): boolean {
    if (this.isOverdue && !this.allowLateSubmission) return false;
    if (this.attemptCount >= this.maxAttempts) return false;
    return this.status === AssignmentStatus.NOT_STARTED || this.status === AssignmentStatus.IN_PROGRESS || this.status === AssignmentStatus.RETURNED;
  }

  get finalScore(): number | null {
    if (!this.grading) return null;
    
    let score = this.grading.score;
    
    // 应用迟交惩罚
    if (this.submission && this.isOverdue && this.latePenaltyPercent > 0) {
      score = score * (1 - this.latePenaltyPercent / 100);
    }
    
    return Math.max(0, score);
  }
}
