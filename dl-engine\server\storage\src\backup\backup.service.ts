import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Cron, CronExpression } from '@nestjs/schedule';
import { MySQLService } from '../database/mysql/mysql.service';
import { RedisService } from '../database/redis/redis.service';
import { PostgreSQLService } from '../database/postgresql/postgresql.service';
import { MinioService } from '../minio/minio.service';
import * as fs from 'fs/promises';
import * as path from 'path';
import * as crypto from 'crypto';
import * as zlib from 'zlib';
import { promisify } from 'util';

const gzip = promisify(zlib.gzip);
const gunzip = promisify(zlib.gunzip);

export interface BackupConfig {
  enabled: boolean;
  schedule: string; // cron表达式
  retention: {
    daily: number; // 保留天数
    weekly: number; // 保留周数
    monthly: number; // 保留月数
  };
  compression: boolean;
  encryption: boolean;
  remoteStorage: {
    enabled: boolean;
    bucket: string;
    prefix: string;
  };
  databases: {
    mysql: boolean;
    redis: boolean;
    postgresql: boolean;
  };
  storage: {
    minio: boolean;
  };
}

export interface BackupJob {
  id: string;
  type: 'full' | 'incremental' | 'differential';
  status: 'pending' | 'running' | 'completed' | 'failed';
  startTime: Date;
  endTime?: Date;
  size: number;
  location: string;
  checksum: string;
  metadata: {
    databases: string[];
    buckets: string[];
    version: string;
    compression: boolean;
    encryption: boolean;
  };
  error?: string;
}

@Injectable()
export class BackupService {
  private readonly logger = new Logger(BackupService.name);
  private readonly backupJobs = new Map<string, BackupJob>();
  private readonly backupConfig: BackupConfig;
  private readonly backupDir: string;

  constructor(
    private configService: ConfigService,
    private mysqlService: MySQLService,
    private redisService: RedisService,
    private postgresqlService: PostgreSQLService,
    private minioService: MinioService,
  ) {
    this.backupConfig = {
      enabled: this.configService.get('BACKUP_ENABLED', true),
      schedule: this.configService.get('BACKUP_SCHEDULE', '0 2 * * *'), // 每天凌晨2点
      retention: {
        daily: this.configService.get('BACKUP_RETENTION_DAILY', 7),
        weekly: this.configService.get('BACKUP_RETENTION_WEEKLY', 4),
        monthly: this.configService.get('BACKUP_RETENTION_MONTHLY', 12),
      },
      compression: this.configService.get('BACKUP_COMPRESSION', true),
      encryption: this.configService.get('BACKUP_ENCRYPTION', false),
      remoteStorage: {
        enabled: this.configService.get('BACKUP_REMOTE_ENABLED', false),
        bucket: this.configService.get('BACKUP_REMOTE_BUCKET', 'backups'),
        prefix: this.configService.get('BACKUP_REMOTE_PREFIX', 'dl-engine'),
      },
      databases: {
        mysql: this.configService.get('BACKUP_MYSQL', true),
        redis: this.configService.get('BACKUP_REDIS', true),
        postgresql: this.configService.get('BACKUP_POSTGRESQL', true),
      },
      storage: {
        minio: this.configService.get('BACKUP_MINIO', true),
      },
    };

    this.backupDir = this.configService.get('BACKUP_DIR', './backups');
    this.ensureBackupDirectory();
  }

  /**
   * 定时备份任务
   */
  @Cron(CronExpression.EVERY_DAY_AT_2AM)
  async scheduledBackup(): Promise<void> {
    if (!this.backupConfig.enabled) {
      return;
    }

    try {
      await this.createFullBackup();
    } catch (error) {
      this.logger.error('Scheduled backup failed:', error);
    }
  }

  /**
   * 创建完整备份
   */
  async createFullBackup(): Promise<string> {
    const jobId = this.generateJobId();
    const job: BackupJob = {
      id: jobId,
      type: 'full',
      status: 'pending',
      startTime: new Date(),
      size: 0,
      location: '',
      checksum: '',
      metadata: {
        databases: [],
        buckets: [],
        version: '1.0.0',
        compression: this.backupConfig.compression,
        encryption: this.backupConfig.encryption,
      },
    };

    this.backupJobs.set(jobId, job);

    try {
      job.status = 'running';
      this.logger.log(`Starting full backup: ${jobId}`);

      const backupData = await this.collectBackupData();
      const backupPath = await this.saveBackupData(jobId, backupData);
      
      job.location = backupPath;
      job.size = await this.getFileSize(backupPath);
      job.checksum = await this.calculateChecksum(backupPath);
      job.endTime = new Date();
      job.status = 'completed';

      // 上传到远程存储
      if (this.backupConfig.remoteStorage.enabled) {
        await this.uploadToRemoteStorage(jobId, backupPath);
      }

      // 清理旧备份
      await this.cleanupOldBackups();

      this.logger.log(`Full backup completed: ${jobId} (${this.formatSize(job.size)})`);
      return jobId;
    } catch (error) {
      job.status = 'failed';
      job.error = error.message;
      job.endTime = new Date();
      
      this.logger.error(`Full backup failed: ${jobId}`, error);
      throw error;
    }
  }

  /**
   * 创建增量备份
   */
  async createIncrementalBackup(lastBackupId?: string): Promise<string> {
    const jobId = this.generateJobId();
    const job: BackupJob = {
      id: jobId,
      type: 'incremental',
      status: 'pending',
      startTime: new Date(),
      size: 0,
      location: '',
      checksum: '',
      metadata: {
        databases: [],
        buckets: [],
        version: '1.0.0',
        compression: this.backupConfig.compression,
        encryption: this.backupConfig.encryption,
      },
    };

    this.backupJobs.set(jobId, job);

    try {
      job.status = 'running';
      this.logger.log(`Starting incremental backup: ${jobId}`);

      const lastBackup = lastBackupId ? this.backupJobs.get(lastBackupId) : this.getLastSuccessfulBackup();
      const sinceTime = lastBackup?.startTime || new Date(0);

      const backupData = await this.collectIncrementalBackupData(sinceTime);
      const backupPath = await this.saveBackupData(jobId, backupData);
      
      job.location = backupPath;
      job.size = await this.getFileSize(backupPath);
      job.checksum = await this.calculateChecksum(backupPath);
      job.endTime = new Date();
      job.status = 'completed';

      if (this.backupConfig.remoteStorage.enabled) {
        await this.uploadToRemoteStorage(jobId, backupPath);
      }

      this.logger.log(`Incremental backup completed: ${jobId} (${this.formatSize(job.size)})`);
      return jobId;
    } catch (error) {
      job.status = 'failed';
      job.error = error.message;
      job.endTime = new Date();
      
      this.logger.error(`Incremental backup failed: ${jobId}`, error);
      throw error;
    }
  }

  /**
   * 恢复备份
   */
  async restoreBackup(
    jobId: string,
    options: {
      databases?: string[];
      buckets?: string[];
      overwrite?: boolean;
      targetPrefix?: string;
    } = {}
  ): Promise<void> {
    const job = this.backupJobs.get(jobId);
    if (!job || job.status !== 'completed') {
      throw new Error(`Backup job not found or not completed: ${jobId}`);
    }

    try {
      this.logger.log(`Starting restore from backup: ${jobId}`);

      let backupPath = job.location;
      
      // 如果备份在远程存储，先下载
      if (this.backupConfig.remoteStorage.enabled && !await this.fileExists(backupPath)) {
        backupPath = await this.downloadFromRemoteStorage(jobId);
      }

      // 验证备份完整性
      const isValid = await this.verifyBackupIntegrity(backupPath, job.checksum);
      if (!isValid) {
        throw new Error('Backup integrity check failed');
      }

      // 加载备份数据
      const backupData = await this.loadBackupData(backupPath);

      // 恢复数据库
      if (options.databases) {
        for (const dbName of options.databases) {
          if (backupData.databases[dbName]) {
            await this.restoreDatabase(dbName, backupData.databases[dbName], options.overwrite);
          }
        }
      }

      // 恢复存储桶
      if (options.buckets) {
        for (const bucketName of options.buckets) {
          if (backupData.buckets[bucketName]) {
            const targetBucket = options.targetPrefix ? `${options.targetPrefix}-${bucketName}` : bucketName;
            await this.restoreBucket(targetBucket, backupData.buckets[bucketName], options.overwrite);
          }
        }
      }

      this.logger.log(`Restore completed from backup: ${jobId}`);
    } catch (error) {
      this.logger.error(`Restore failed from backup: ${jobId}`, error);
      throw error;
    }
  }

  /**
   * 获取备份列表
   */
  getBackupJobs(limit: number = 50): BackupJob[] {
    return Array.from(this.backupJobs.values())
      .sort((a, b) => b.startTime.getTime() - a.startTime.getTime())
      .slice(0, limit);
  }

  /**
   * 获取备份详情
   */
  getBackupJob(jobId: string): BackupJob | null {
    return this.backupJobs.get(jobId) || null;
  }

  /**
   * 删除备份
   */
  async deleteBackup(jobId: string): Promise<void> {
    const job = this.backupJobs.get(jobId);
    if (!job) {
      throw new Error(`Backup job not found: ${jobId}`);
    }

    try {
      // 删除本地文件
      if (await this.fileExists(job.location)) {
        await fs.unlink(job.location);
      }

      // 删除远程文件
      if (this.backupConfig.remoteStorage.enabled) {
        await this.deleteFromRemoteStorage(jobId);
      }

      // 删除任务记录
      this.backupJobs.delete(jobId);

      this.logger.log(`Backup deleted: ${jobId}`);
    } catch (error) {
      this.logger.error(`Failed to delete backup: ${jobId}`, error);
      throw error;
    }
  }

  /**
   * 验证备份完整性
   */
  async verifyBackupIntegrity(backupPath: string, expectedChecksum: string): Promise<boolean> {
    try {
      const actualChecksum = await this.calculateChecksum(backupPath);
      return actualChecksum === expectedChecksum;
    } catch (error) {
      this.logger.error(`Failed to verify backup integrity: ${backupPath}`, error);
      return false;
    }
  }

  /**
   * 获取备份统计信息
   */
  async getBackupStats(): Promise<{
    totalBackups: number;
    totalSize: number;
    successRate: number;
    lastBackup?: Date;
    nextBackup?: Date;
    retentionPolicy: any;
  }> {
    const jobs = Array.from(this.backupJobs.values());
    const completedJobs = jobs.filter(job => job.status === 'completed');
    const totalSize = completedJobs.reduce((sum, job) => sum + job.size, 0);
    const successRate = jobs.length > 0 ? (completedJobs.length / jobs.length) * 100 : 0;
    
    const lastBackup = completedJobs.length > 0 
      ? new Date(Math.max(...completedJobs.map(job => job.startTime.getTime())))
      : undefined;

    return {
      totalBackups: jobs.length,
      totalSize,
      successRate: Math.round(successRate * 100) / 100,
      lastBackup,
      nextBackup: this.getNextScheduledBackup(),
      retentionPolicy: this.backupConfig.retention,
    };
  }

  private async collectBackupData(): Promise<any> {
    const backupData: any = {
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      databases: {},
      buckets: {},
    };

    // 备份MySQL
    if (this.backupConfig.databases.mysql) {
      try {
        backupData.databases.mysql = await this.backupMySQL();
        this.logger.debug('MySQL backup collected');
      } catch (error) {
        this.logger.error('Failed to backup MySQL:', error);
      }
    }

    // 备份Redis
    if (this.backupConfig.databases.redis) {
      try {
        backupData.databases.redis = await this.backupRedis();
        this.logger.debug('Redis backup collected');
      } catch (error) {
        this.logger.error('Failed to backup Redis:', error);
      }
    }

    // 备份PostgreSQL
    if (this.backupConfig.databases.postgresql) {
      try {
        backupData.databases.postgresql = await this.backupPostgreSQL();
        this.logger.debug('PostgreSQL backup collected');
      } catch (error) {
        this.logger.error('Failed to backup PostgreSQL:', error);
      }
    }

    // 备份Minio
    if (this.backupConfig.storage.minio) {
      try {
        backupData.buckets = await this.backupMinio();
        this.logger.debug('Minio backup collected');
      } catch (error) {
        this.logger.error('Failed to backup Minio:', error);
      }
    }

    return backupData;
  }

  private async collectIncrementalBackupData(sinceTime: Date): Promise<any> {
    // 增量备份逻辑 - 只备份自指定时间以来的变更
    const backupData: any = {
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      type: 'incremental',
      since: sinceTime.toISOString(),
      databases: {},
      buckets: {},
    };

    // 实现增量备份逻辑...
    // 这里需要根据具体数据库的增量备份能力来实现

    return backupData;
  }

  private async saveBackupData(jobId: string, data: any): Promise<string> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `backup_${jobId}_${timestamp}.json`;
    const backupPath = path.join(this.backupDir, filename);

    let content = JSON.stringify(data, null, 2);

    // 压缩
    if (this.backupConfig.compression) {
      content = (await gzip(Buffer.from(content))).toString('base64');
    }

    // 加密
    if (this.backupConfig.encryption) {
      content = this.encrypt(content);
    }

    await fs.writeFile(backupPath, content);
    return backupPath;
  }

  private async loadBackupData(backupPath: string): Promise<any> {
    let content = await fs.readFile(backupPath, 'utf8');

    // 解密
    if (this.backupConfig.encryption) {
      content = this.decrypt(content);
    }

    // 解压缩
    if (this.backupConfig.compression) {
      const buffer = await gunzip(Buffer.from(content, 'base64'));
      content = buffer.toString('utf8');
    }

    return JSON.parse(content);
  }

  private async backupMySQL(): Promise<any> {
    // 实现MySQL备份逻辑
    return { tables: [], data: [] };
  }

  private async backupRedis(): Promise<any> {
    // 实现Redis备份逻辑
    return { keys: [], data: [] };
  }

  private async backupPostgreSQL(): Promise<any> {
    // 实现PostgreSQL备份逻辑
    return { tables: [], data: [] };
  }

  private async backupMinio(): Promise<any> {
    // 实现Minio备份逻辑
    const buckets = await this.minioService.listBuckets();
    const backupData: any = {};

    for (const bucket of buckets) {
      const objects = await this.minioService.listObjects(bucket.name, '', true);
      backupData[bucket.name] = {
        metadata: bucket,
        objects: objects.map(obj => ({
          name: obj.name,
          size: obj.size,
          etag: obj.etag,
          lastModified: obj.lastModified,
        })),
      };
    }

    return backupData;
  }

  private async restoreDatabase(dbName: string, data: any, overwrite: boolean): Promise<void> {
    // 实现数据库恢复逻辑
    this.logger.debug(`Restoring database: ${dbName}`);
  }

  private async restoreBucket(bucketName: string, data: any, overwrite: boolean): Promise<void> {
    // 实现存储桶恢复逻辑
    this.logger.debug(`Restoring bucket: ${bucketName}`);
  }

  private async uploadToRemoteStorage(jobId: string, backupPath: string): Promise<void> {
    const objectName = `${this.backupConfig.remoteStorage.prefix}/${jobId}/${path.basename(backupPath)}`;
    const fileData = await fs.readFile(backupPath);
    
    await this.minioService.putObject(
      this.backupConfig.remoteStorage.bucket,
      objectName,
      fileData
    );
  }

  private async downloadFromRemoteStorage(jobId: string): Promise<string> {
    // 实现从远程存储下载备份
    const localPath = path.join(this.backupDir, `temp_${jobId}.json`);
    // 下载逻辑...
    return localPath;
  }

  private async deleteFromRemoteStorage(jobId: string): Promise<void> {
    // 实现从远程存储删除备份
  }

  private async cleanupOldBackups(): Promise<void> {
    // 实现备份清理逻辑
    const jobs = Array.from(this.backupJobs.values())
      .filter(job => job.status === 'completed')
      .sort((a, b) => b.startTime.getTime() - a.startTime.getTime());

    const now = new Date();
    const toDelete: string[] = [];

    // 应用保留策略
    for (const job of jobs) {
      const age = now.getTime() - job.startTime.getTime();
      const days = age / (1000 * 60 * 60 * 24);

      if (days > this.backupConfig.retention.daily) {
        toDelete.push(job.id);
      }
    }

    // 删除过期备份
    for (const jobId of toDelete) {
      try {
        await this.deleteBackup(jobId);
      } catch (error) {
        this.logger.error(`Failed to cleanup backup ${jobId}:`, error);
      }
    }
  }

  private getLastSuccessfulBackup(): BackupJob | null {
    const completedJobs = Array.from(this.backupJobs.values())
      .filter(job => job.status === 'completed')
      .sort((a, b) => b.startTime.getTime() - a.startTime.getTime());

    return completedJobs[0] || null;
  }

  private getNextScheduledBackup(): Date | undefined {
    // 根据cron表达式计算下次备份时间
    // 这里需要使用cron解析库
    return undefined;
  }

  private async ensureBackupDirectory(): Promise<void> {
    try {
      await fs.access(this.backupDir);
    } catch {
      await fs.mkdir(this.backupDir, { recursive: true });
    }
  }

  private generateJobId(): string {
    return `backup_${Date.now()}_${crypto.randomBytes(8).toString('hex')}`;
  }

  private async getFileSize(filePath: string): Promise<number> {
    const stats = await fs.stat(filePath);
    return stats.size;
  }

  private async calculateChecksum(filePath: string): Promise<string> {
    const data = await fs.readFile(filePath);
    return crypto.createHash('sha256').update(data).digest('hex');
  }

  private async fileExists(filePath: string): Promise<boolean> {
    try {
      await fs.access(filePath);
      return true;
    } catch {
      return false;
    }
  }

  private formatSize(bytes: number): string {
    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
    let size = bytes;
    let unitIndex = 0;
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }
    
    return `${size.toFixed(2)} ${units[unitIndex]}`;
  }

  private encrypt(data: string): string {
    // 实现加密逻辑
    return data;
  }

  private decrypt(data: string): string {
    // 实现解密逻辑
    return data;
  }
}
