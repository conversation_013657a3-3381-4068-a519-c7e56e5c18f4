# DL-Engine Alertmanager 告警系统配置
# 支持多渠道告警通知和智能告警分组

apiVersion: v1
kind: ConfigMap
metadata:
  name: alertmanager-config
  namespace: dl-engine-monitoring
  labels:
    app.kubernetes.io/name: alertmanager
    app.kubernetes.io/component: config
data:
  alertmanager.yml: |
    global:
      # SMTP配置
      smtp_smarthost: 'smtp.example.com:587'
      smtp_from: '<EMAIL>'
      smtp_auth_username: '<EMAIL>'
      smtp_auth_password: 'your-smtp-password'
      smtp_require_tls: true
      
      # 微信企业号配置
      wechat_api_url: 'https://qyapi.weixin.qq.com/cgi-bin/'
      wechat_api_secret: 'your-wechat-secret'
      wechat_api_corp_id: 'your-corp-id'
      
      # 钉钉配置
      dingtalk_api_url: 'https://oapi.dingtalk.com/robot/send'
      
      # 默认配置
      resolve_timeout: 5m
      http_config:
        follow_redirects: true
    
    # 模板配置
    templates:
      - '/etc/alertmanager/templates/*.tmpl'
    
    # 路由配置
    route:
      group_by: ['alertname', 'cluster', 'service']
      group_wait: 10s
      group_interval: 10s
      repeat_interval: 1h
      receiver: 'default'
      routes:
        # 严重告警立即通知
        - match:
            severity: critical
          receiver: 'critical-alerts'
          group_wait: 0s
          repeat_interval: 5m
          
        # 服务不可用告警
        - match:
            alertname: ServiceDown
          receiver: 'service-down-alerts'
          group_wait: 0s
          repeat_interval: 2m
          
        # 数据库告警
        - match_re:
            service: '(mysql|redis|postgresql)'
          receiver: 'database-alerts'
          
        # 游戏服务器告警
        - match:
            service: gameserver
          receiver: 'gameserver-alerts'
          
        # 教育业务告警
        - match_re:
            alertname: '(HighStudentLoad|LearningSessionFailed|CourseAccessError)'
          receiver: 'education-alerts'
          
        # 性能告警
        - match_re:
            alertname: '(HighCPUUsage|HighMemoryUsage|HighResponseTime)'
          receiver: 'performance-alerts'
          
        # 安全告警
        - match_re:
            alertname: '(SecurityBreach|UnauthorizedAccess|SuspiciousActivity)'
          receiver: 'security-alerts'
          group_wait: 0s
          repeat_interval: 1m
    
    # 抑制规则
    inhibit_rules:
      # 如果服务完全不可用，抑制其他相关告警
      - source_match:
          alertname: ServiceDown
        target_match_re:
          alertname: '(HighResponseTime|HighErrorRate|HighCPUUsage)'
        equal: ['service']
        
      # 如果节点不可用，抑制该节点上的所有告警
      - source_match:
          alertname: NodeDown
        target_match_re:
          alertname: '.*'
        equal: ['instance']
    
    # 接收器配置
    receivers:
      # 默认接收器
      - name: 'default'
        email_configs:
          - to: '<EMAIL>'
            subject: '[DL-Engine] {{ .GroupLabels.alertname }} - {{ .Status | toUpper }}'
            body: |
              {{ range .Alerts }}
              告警名称: {{ .Annotations.summary }}
              告警详情: {{ .Annotations.description }}
              告警级别: {{ .Labels.severity }}
              服务名称: {{ .Labels.service }}
              实例地址: {{ .Labels.instance }}
              开始时间: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
              {{ if .EndsAt }}结束时间: {{ .EndsAt.Format "2006-01-02 15:04:05" }}{{ end }}
              {{ end }}
      
      # 严重告警接收器
      - name: 'critical-alerts'
        email_configs:
          - to: '<EMAIL>'
            subject: '[CRITICAL] DL-Engine 严重告警 - {{ .GroupLabels.alertname }}'
            body: |
              🚨 严重告警 🚨
              
              {{ range .Alerts }}
              告警名称: {{ .Annotations.summary }}
              告警详情: {{ .Annotations.description }}
              告警级别: {{ .Labels.severity }}
              服务名称: {{ .Labels.service }}
              实例地址: {{ .Labels.instance }}
              开始时间: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
              
              请立即处理！
              {{ end }}
        wechat_configs:
          - agent_id: 'your-agent-id'
            to_user: '@all'
            message: |
              🚨 DL-Engine严重告警
              {{ range .Alerts }}
              {{ .Annotations.summary }}
              服务: {{ .Labels.service }}
              时间: {{ .StartsAt.Format "15:04:05" }}
              {{ end }}
        webhook_configs:
          - url: 'https://hooks.dingtalk.com/services/your-webhook-url'
            send_resolved: true
            http_config:
              follow_redirects: true
            title: 'DL-Engine严重告警'
            text: |
              {{ range .Alerts }}
              **告警**: {{ .Annotations.summary }}
              **服务**: {{ .Labels.service }}
              **时间**: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
              {{ end }}
      
      # 服务不可用告警
      - name: 'service-down-alerts'
        email_configs:
          - to: '<EMAIL>,<EMAIL>'
            subject: '[SERVICE DOWN] {{ .GroupLabels.service }} 服务不可用'
            body: |
              ⚠️ 服务不可用告警 ⚠️
              
              {{ range .Alerts }}
              服务名称: {{ .Labels.service }}
              实例地址: {{ .Labels.instance }}
              告警详情: {{ .Annotations.description }}
              开始时间: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
              {{ end }}
              
              请立即检查服务状态！
        wechat_configs:
          - agent_id: 'your-agent-id'
            to_user: '@all'
            message: |
              ⚠️ 服务不可用
              {{ range .Alerts }}
              服务: {{ .Labels.service }}
              实例: {{ .Labels.instance }}
              时间: {{ .StartsAt.Format "15:04:05" }}
              {{ end }}
      
      # 数据库告警
      - name: 'database-alerts'
        email_configs:
          - to: '<EMAIL>,<EMAIL>'
            subject: '[DATABASE] {{ .GroupLabels.service }} 数据库告警'
            body: |
              💾 数据库告警 💾
              
              {{ range .Alerts }}
              数据库: {{ .Labels.service }}
              告警名称: {{ .Annotations.summary }}
              告警详情: {{ .Annotations.description }}
              实例地址: {{ .Labels.instance }}
              开始时间: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
              {{ end }}
      
      # 游戏服务器告警
      - name: 'gameserver-alerts'
        email_configs:
          - to: '<EMAIL>'
            subject: '[GAMESERVER] 游戏服务器告警'
            body: |
              🎮 游戏服务器告警 🎮
              
              {{ range .Alerts }}
              告警名称: {{ .Annotations.summary }}
              告警详情: {{ .Annotations.description }}
              GameServer: {{ .Labels.gameserver }}
              Fleet: {{ .Labels.fleet }}
              开始时间: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
              {{ end }}
      
      # 教育业务告警
      - name: 'education-alerts'
        email_configs:
          - to: '<EMAIL>,<EMAIL>'
            subject: '[EDUCATION] 教育业务告警'
            body: |
              📚 教育业务告警 📚
              
              {{ range .Alerts }}
              告警名称: {{ .Annotations.summary }}
              告警详情: {{ .Annotations.description }}
              课程ID: {{ .Labels.course_id }}
              用户数: {{ .Labels.user_count }}
              开始时间: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
              {{ end }}
      
      # 性能告警
      - name: 'performance-alerts'
        email_configs:
          - to: '<EMAIL>'
            subject: '[PERFORMANCE] 性能告警'
            body: |
              ⚡ 性能告警 ⚡
              
              {{ range .Alerts }}
              告警名称: {{ .Annotations.summary }}
              告警详情: {{ .Annotations.description }}
              服务名称: {{ .Labels.service }}
              实例地址: {{ .Labels.instance }}
              当前值: {{ .Annotations.value }}
              开始时间: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
              {{ end }}
      
      # 安全告警
      - name: 'security-alerts'
        email_configs:
          - to: '<EMAIL>,<EMAIL>'
            subject: '[SECURITY] 安全告警 - 立即处理'
            body: |
              🔒 安全告警 🔒
              
              {{ range .Alerts }}
              告警名称: {{ .Annotations.summary }}
              告警详情: {{ .Annotations.description }}
              源IP: {{ .Labels.source_ip }}
              目标服务: {{ .Labels.service }}
              开始时间: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
              {{ end }}
              
              请立即检查并采取安全措施！
        wechat_configs:
          - agent_id: 'your-agent-id'
            to_user: '@all'
            message: |
              🔒 安全告警
              {{ range .Alerts }}
              {{ .Annotations.summary }}
              源IP: {{ .Labels.source_ip }}
              时间: {{ .StartsAt.Format "15:04:05" }}
              {{ end }}

  # 告警模板
  alert-templates.tmpl: |
    {{ define "email.default.subject" }}
    [{{ .Status | toUpper }}{{ if eq .Status "firing" }}:{{ .Alerts.Firing | len }}{{ end }}] DL-Engine Alert
    {{ end }}
    
    {{ define "email.default.html" }}
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>DL-Engine Alert</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .alert { border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px; }
            .firing { border-left: 5px solid #d9534f; background-color: #f2dede; }
            .resolved { border-left: 5px solid #5cb85c; background-color: #dff0d8; }
            .label { display: inline-block; background-color: #f5f5f5; padding: 2px 6px; margin: 2px; border-radius: 3px; font-size: 12px; }
            .annotation { margin: 5px 0; }
            .time { color: #666; font-size: 12px; }
        </style>
    </head>
    <body>
        <h2>DL-Engine 告警通知</h2>
        {{ range .Alerts }}
        <div class="alert {{ if eq .Status "firing" }}firing{{ else }}resolved{{ end }}">
            <h3>{{ .Annotations.summary }}</h3>
            <div class="annotation">
                <strong>详情:</strong> {{ .Annotations.description }}
            </div>
            <div class="annotation">
                <strong>标签:</strong>
                {{ range .Labels.SortedPairs }}
                <span class="label">{{ .Name }}={{ .Value }}</span>
                {{ end }}
            </div>
            <div class="time">
                <strong>开始时间:</strong> {{ .StartsAt.Format "2006-01-02 15:04:05" }}
                {{ if .EndsAt }}
                <br><strong>结束时间:</strong> {{ .EndsAt.Format "2006-01-02 15:04:05" }}
                {{ end }}
            </div>
        </div>
        {{ end }}
        
        <hr>
        <p style="color: #666; font-size: 12px;">
            此邮件由 DL-Engine 监控系统自动发送，请勿回复。
        </p>
    </body>
    </html>
    {{ end }}
    
    {{ define "wechat.default.message" }}
    {{ if eq .Status "firing" }}🚨{{ else }}✅{{ end }} DL-Engine告警
    {{ range .Alerts }}
    {{ .Annotations.summary }}
    服务: {{ .Labels.service }}
    {{ if .Labels.instance }}实例: {{ .Labels.instance }}{{ end }}
    时间: {{ .StartsAt.Format "01-02 15:04" }}
    {{ end }}
    {{ end }}

---
# Alertmanager Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: alertmanager
  namespace: dl-engine-monitoring
  labels:
    app.kubernetes.io/name: alertmanager
    app.kubernetes.io/component: alerting
spec:
  replicas: 2
  selector:
    matchLabels:
      app.kubernetes.io/name: alertmanager
      app.kubernetes.io/component: alerting
  template:
    metadata:
      labels:
        app.kubernetes.io/name: alertmanager
        app.kubernetes.io/component: alerting
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9093"
        prometheus.io/path: "/metrics"
    spec:
      containers:
        - name: alertmanager
          image: prom/alertmanager:v0.25.0
          ports:
            - containerPort: 9093
              name: http
              protocol: TCP
          args:
            - '--config.file=/etc/alertmanager/alertmanager.yml'
            - '--storage.path=/alertmanager'
            - '--web.external-url=http://alertmanager.dl-engine.local'
            - '--web.route-prefix=/'
            - '--cluster.listen-address=0.0.0.0:9094'
            - '--cluster.peer=alertmanager-0.alertmanager-headless.dl-engine-monitoring.svc.cluster.local:9094'
            - '--cluster.peer=alertmanager-1.alertmanager-headless.dl-engine-monitoring.svc.cluster.local:9094'
          volumeMounts:
            - name: alertmanager-config
              mountPath: /etc/alertmanager
            - name: alertmanager-storage
              mountPath: /alertmanager
          resources:
            limits:
              cpu: 500m
              memory: 512Mi
            requests:
              cpu: 250m
              memory: 256Mi
          livenessProbe:
            httpGet:
              path: /-/healthy
              port: 9093
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3
          readinessProbe:
            httpGet:
              path: /-/ready
              port: 9093
            initialDelaySeconds: 5
            periodSeconds: 5
            timeoutSeconds: 3
            failureThreshold: 3
      volumes:
        - name: alertmanager-config
          configMap:
            name: alertmanager-config
        - name: alertmanager-storage
          emptyDir: {}
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 100
              podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: app.kubernetes.io/name
                      operator: In
                      values:
                        - alertmanager
                topologyKey: kubernetes.io/hostname

---
# Alertmanager Service
apiVersion: v1
kind: Service
metadata:
  name: alertmanager
  namespace: dl-engine-monitoring
  labels:
    app.kubernetes.io/name: alertmanager
    app.kubernetes.io/component: service
spec:
  type: ClusterIP
  ports:
    - port: 9093
      targetPort: 9093
      protocol: TCP
      name: http
  selector:
    app.kubernetes.io/name: alertmanager
    app.kubernetes.io/component: alerting

---
# Alertmanager Headless Service
apiVersion: v1
kind: Service
metadata:
  name: alertmanager-headless
  namespace: dl-engine-monitoring
  labels:
    app.kubernetes.io/name: alertmanager
    app.kubernetes.io/component: headless-service
spec:
  clusterIP: None
  ports:
    - port: 9093
      targetPort: 9093
      protocol: TCP
      name: http
    - port: 9094
      targetPort: 9094
      protocol: TCP
      name: cluster
  selector:
    app.kubernetes.io/name: alertmanager
    app.kubernetes.io/component: alerting
