import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  ManyToMany,
  JoinTable,
  JoinColumn,
  Index
} from 'typeorm'
import { User } from '../../users/entities/user.entity'
import { AssetCategory } from './asset-category.entity'
import { AssetTag } from './asset-tag.entity'
import { AssetVersion } from './asset-version.entity'
import { AssetUsage } from './asset-usage.entity'
import { AssetShare } from './asset-share.entity'
import { AssetComment } from './asset-comment.entity'
import { AssetRating } from './asset-rating.entity'

/**
 * 资产状态枚举
 */
export enum AssetStatus {
  UPLOADING = 'uploading',     // 上传中
  PROCESSING = 'processing',   // 处理中
  ACTIVE = 'active',           // 活跃
  ARCHIVED = 'archived',       // 已归档
  DELETED = 'deleted',         // 已删除
  FAILED = 'failed'            // 处理失败
}

/**
 * 资产类型枚举
 */
export enum AssetType {
  IMAGE = 'image',             // 图片
  VIDEO = 'video',             // 视频
  AUDIO = 'audio',             // 音频
  MODEL_3D = '3d_model',       // 3D模型
  TEXTURE = 'texture',         // 纹理
  MATERIAL = 'material',       // 材质
  ANIMATION = 'animation',     // 动画
  SCRIPT = 'script',           // 脚本
  DOCUMENT = 'document',       // 文档
  ARCHIVE = 'archive',         // 压缩包
  FONT = 'font',               // 字体
  OTHER = 'other'              // 其他
}

/**
 * 可见性枚举
 */
export enum AssetVisibility {
  PUBLIC = 'public',           // 公开
  PRIVATE = 'private',         // 私有
  UNLISTED = 'unlisted',       // 不公开列出
  ORGANIZATION = 'organization' // 组织内可见
}

/**
 * 资产实体
 * 
 * 存储各种类型的数字资产信息
 */
@Entity('assets')
@Index(['uploaderId', 'status'])
@Index(['type', 'status'])
@Index(['visibility', 'status'])
@Index(['categoryId'])
@Index(['createdAt'])
export class Asset {
  @PrimaryGeneratedColumn('uuid')
  id: string

  /**
   * 资产名称
   */
  @Column({ type: 'varchar', length: 255 })
  name: string

  /**
   * 资产描述
   */
  @Column({ type: 'text', nullable: true })
  description?: string

  /**
   * 资产类型
   */
  @Column({
    type: 'enum',
    enum: AssetType
  })
  type: AssetType

  /**
   * 资产状态
   */
  @Column({
    type: 'enum',
    enum: AssetStatus,
    default: AssetStatus.UPLOADING
  })
  status: AssetStatus

  /**
   * 可见性设置
   */
  @Column({
    type: 'enum',
    enum: AssetVisibility,
    default: AssetVisibility.PRIVATE
  })
  visibility: AssetVisibility

  /**
   * 上传者ID
   */
  @Column({ type: 'uuid' })
  uploaderId: string

  /**
   * 原始文件名
   */
  @Column({ type: 'varchar', length: 255 })
  originalName: string

  /**
   * 文件扩展名
   */
  @Column({ type: 'varchar', length: 20 })
  extension: string

  /**
   * MIME类型
   */
  @Column({ type: 'varchar', length: 100 })
  mimeType: string

  /**
   * 文件大小（字节）
   */
  @Column({ type: 'bigint' })
  size: number

  /**
   * 文件哈希值
   */
  @Column({ type: 'varchar', length: 64, nullable: true })
  hash?: string

  /**
   * 存储路径
   */
  @Column({ type: 'varchar', length: 500 })
  storagePath: string

  /**
   * 公开访问URL
   */
  @Column({ type: 'text', nullable: true })
  publicUrl?: string

  /**
   * 缩略图URL
   */
  @Column({ type: 'text', nullable: true })
  thumbnailUrl?: string

  /**
   * 预览URL列表
   */
  @Column({ type: 'json', nullable: true })
  previewUrls?: string[]

  /**
   * 资产版本号
   */
  @Column({ type: 'varchar', length: 50, default: '1.0.0' })
  version: string

  /**
   * 许可证信息
   */
  @Column({ type: 'json', nullable: true })
  license?: {
    type: string // MIT, GPL, CC, Commercial, etc.
    name: string
    url?: string
    attribution?: string
    commercial: boolean
    modification: boolean
    distribution: boolean
  }

  /**
   * 文件元数据
   */
  @Column({ type: 'json', nullable: true })
  metadata?: {
    // 图片元数据
    image?: {
      width: number
      height: number
      format: string
      colorSpace: string
      hasAlpha: boolean
      dpi?: number
    }
    // 视频元数据
    video?: {
      width: number
      height: number
      duration: number
      frameRate: number
      bitrate: number
      codec: string
      hasAudio: boolean
    }
    // 音频元数据
    audio?: {
      duration: number
      bitrate: number
      sampleRate: number
      channels: number
      codec: string
    }
    // 3D模型元数据
    model3d?: {
      vertices: number
      triangles: number
      materials: number
      textures: number
      animations: number
      format: string
      units: string
      bounds: {
        min: { x: number; y: number; z: number }
        max: { x: number; y: number; z: number }
      }
    }
    // 通用元数据
    general?: {
      author?: string
      software?: string
      version?: string
      created?: Date
      modified?: Date
      keywords?: string[]
      [key: string]: any
    }
  }

  /**
   * 处理信息
   */
  @Column({ type: 'json', nullable: true })
  processing?: {
    status: 'pending' | 'processing' | 'completed' | 'failed'
    progress: number // 0-100
    startedAt?: Date
    completedAt?: Date
    error?: string
    tasks: Array<{
      name: string
      status: 'pending' | 'processing' | 'completed' | 'failed'
      progress: number
      result?: any
      error?: string
    }>
  }

  /**
   * 统计信息
   */
  @Column({ type: 'json', nullable: true })
  statistics?: {
    downloads: number
    views: number
    uses: number
    shares: number
    likes: number
    comments: number
    avgRating: number
    totalRatings: number
    lastUsed: Date
  }

  /**
   * 教育相关信息
   */
  @Column({ type: 'json', nullable: true })
  educationInfo?: {
    subject: string[]
    gradeLevel: string[]
    difficulty: 'beginner' | 'intermediate' | 'advanced'
    learningObjectives: string[]
    prerequisites: string[]
    usageInstructions: string
    educationalValue: string
    ageAppropriate: boolean
  }

  /**
   * 分类ID
   */
  @Column({ type: 'uuid', nullable: true })
  categoryId?: string

  /**
   * 最后访问时间
   */
  @Column({ type: 'timestamp', nullable: true })
  lastAccessedAt?: Date

  /**
   * 创建时间
   */
  @CreateDateColumn()
  createdAt: Date

  /**
   * 更新时间
   */
  @UpdateDateColumn()
  updatedAt: Date

  // 关联关系
  @ManyToOne(() => User)
  @JoinColumn({ name: 'uploaderId' })
  uploader: User

  @ManyToOne(() => AssetCategory, { nullable: true })
  @JoinColumn({ name: 'categoryId' })
  category?: AssetCategory

  @ManyToMany(() => AssetTag)
  @JoinTable({
    name: 'asset_tag_relations',
    joinColumn: { name: 'assetId', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'tagId', referencedColumnName: 'id' }
  })
  tags: AssetTag[]

  @OneToMany(() => AssetVersion, version => version.asset, { cascade: true })
  versions: AssetVersion[]

  @OneToMany(() => AssetUsage, usage => usage.asset, { cascade: true })
  usages: AssetUsage[]

  @OneToMany(() => AssetShare, share => share.asset, { cascade: true })
  shares: AssetShare[]

  @OneToMany(() => AssetComment, comment => comment.asset, { cascade: true })
  comments: AssetComment[]

  @OneToMany(() => AssetRating, rating => rating.asset, { cascade: true })
  ratings: AssetRating[]

  /**
   * 检查资产是否公开
   */
  isPublic(): boolean {
    return this.visibility === AssetVisibility.PUBLIC
  }

  /**
   * 检查资产是否活跃
   */
  isActive(): boolean {
    return this.status === AssetStatus.ACTIVE
  }

  /**
   * 检查资产是否可用
   */
  isAvailable(): boolean {
    return this.status === AssetStatus.ACTIVE || this.status === AssetStatus.ARCHIVED
  }

  /**
   * 检查是否为图片类型
   */
  isImage(): boolean {
    return this.type === AssetType.IMAGE || this.type === AssetType.TEXTURE
  }

  /**
   * 检查是否为视频类型
   */
  isVideo(): boolean {
    return this.type === AssetType.VIDEO
  }

  /**
   * 检查是否为音频类型
   */
  isAudio(): boolean {
    return this.type === AssetType.AUDIO
  }

  /**
   * 检查是否为3D模型
   */
  is3DModel(): boolean {
    return this.type === AssetType.MODEL_3D
  }

  /**
   * 获取文件大小的人类可读格式
   */
  getFormattedSize(): string {
    const units = ['B', 'KB', 'MB', 'GB', 'TB']
    let size = this.size
    let unitIndex = 0

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024
      unitIndex++
    }

    return `${size.toFixed(1)} ${units[unitIndex]}`
  }

  /**
   * 获取资产的维度信息
   */
  getDimensions(): string | null {
    if (this.metadata?.image) {
      return `${this.metadata.image.width} × ${this.metadata.image.height}`
    }
    if (this.metadata?.video) {
      return `${this.metadata.video.width} × ${this.metadata.video.height}`
    }
    return null
  }

  /**
   * 获取资产的持续时间
   */
  getDuration(): number | null {
    if (this.metadata?.video) {
      return this.metadata.video.duration
    }
    if (this.metadata?.audio) {
      return this.metadata.audio.duration
    }
    return null
  }

  /**
   * 更新统计信息
   */
  updateStatistics(field: keyof Asset['statistics'], increment = 1): void {
    if (!this.statistics) {
      this.statistics = {
        downloads: 0,
        views: 0,
        uses: 0,
        shares: 0,
        likes: 0,
        comments: 0,
        avgRating: 0,
        totalRatings: 0,
        lastUsed: new Date()
      }
    }

    if (typeof this.statistics[field] === 'number') {
      this.statistics[field] += increment
    }
    this.statistics.lastUsed = new Date()
  }

  /**
   * 检查是否支持预览
   */
  supportsPreview(): boolean {
    return this.isImage() || this.isVideo() || this.type === AssetType.DOCUMENT
  }

  /**
   * 检查是否需要处理
   */
  needsProcessing(): boolean {
    return this.status === AssetStatus.UPLOADING || this.status === AssetStatus.PROCESSING
  }

  /**
   * 获取资产URL友好的slug
   */
  getSlug(): string {
    return this.name
      .toLowerCase()
      .replace(/[^a-z0-9\u4e00-\u9fa5]/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '')
  }
}
