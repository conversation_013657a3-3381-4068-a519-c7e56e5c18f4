version: '3.8'

services:
  # API网关
  gateway:
    build:
      context: ./gateway
      dockerfile: Dockerfile
    ports:
      - "8080:8080"
    environment:
      - NODE_ENV=production
      - PORT=8080
      - API_SERVICE_URL=http://api:3030
      - AUTH_SERVICE_URL=http://auth:3031
      - INSTANCE_SERVICE_URL=http://instance:3032
      - TASK_SERVICE_URL=http://task:3033
    depends_on:
      - api
      - auth
      - instance
      - task
    networks:
      - dl-engine-network
    restart: unless-stopped

  # 核心API服务
  api:
    build:
      context: ./api
      dockerfile: Dockerfile
    ports:
      - "3030:3030"
    environment:
      - NODE_ENV=production
      - PORT=3030
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USERNAME=dl_engine
      - DB_PASSWORD=dl_engine_password
      - DB_DATABASE=dl_engine
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    depends_on:
      - mysql
      - redis
    networks:
      - dl-engine-network
    restart: unless-stopped

  # 认证服务
  auth:
    build:
      context: ./auth
      dockerfile: Dockerfile
    ports:
      - "3031:3031"
    environment:
      - NODE_ENV=production
      - PORT=3031
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USERNAME=dl_engine
      - DB_PASSWORD=dl_engine_password
      - DB_DATABASE=dl_engine_auth
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - JWT_SECRET=your-super-secret-jwt-key
      - SMS_PROVIDER=aliyun
      - SMS_ACCESS_KEY=your-sms-access-key
      - SMS_SECRET_KEY=your-sms-secret-key
    depends_on:
      - mysql
      - redis
    networks:
      - dl-engine-network
    restart: unless-stopped

  # 实例服务
  instance:
    build:
      context: ./instance
      dockerfile: Dockerfile
    ports:
      - "3032:3032"
    environment:
      - NODE_ENV=production
      - PORT=3032
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - MAX_INSTANCES=100
      - INSTANCE_TIMEOUT=3600
    depends_on:
      - redis
    networks:
      - dl-engine-network
    restart: unless-stopped

  # 任务服务
  task:
    build:
      context: ./task
      dockerfile: Dockerfile
    ports:
      - "3033:3033"
    environment:
      - NODE_ENV=production
      - PORT=3033
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - QUEUE_CONCURRENCY=10
    depends_on:
      - redis
    networks:
      - dl-engine-network
    restart: unless-stopped

  # MySQL数据库
  mysql:
    image: mysql:8.0
    ports:
      - "3306:3306"
    environment:
      - MYSQL_ROOT_PASSWORD=root_password
      - MYSQL_DATABASE=dl_engine
      - MYSQL_USER=dl_engine
      - MYSQL_PASSWORD=dl_engine_password
    volumes:
      - mysql_data:/var/lib/mysql
      - ./scripts/mysql-init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - dl-engine-network
    restart: unless-stopped

  # Redis缓存
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --requirepass redis_password
    volumes:
      - redis_data:/data
    networks:
      - dl-engine-network
    restart: unless-stopped

  # 存储服务
  storage:
    build:
      context: ./storage
      dockerfile: Dockerfile
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - PORT=3001
      - MYSQL_HOST=mysql
      - MYSQL_PORT=3306
      - MYSQL_USERNAME=dl_engine
      - MYSQL_PASSWORD=dl_engine_password
      - MYSQL_DATABASE=dl_engine
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - POSTGRESQL_HOST=postgresql
      - POSTGRESQL_PORT=5432
      - POSTGRESQL_USERNAME=postgres
      - POSTGRESQL_PASSWORD=postgres_password
      - POSTGRESQL_DATABASE=dl_engine_vectors
      - MINIO_ENDPOINT=minio
      - MINIO_PORT=9000
      - MINIO_ACCESS_KEY=minioadmin
      - MINIO_SECRET_KEY=minioadmin
    depends_on:
      - mysql
      - redis
      - postgresql
      - minio
    networks:
      - dl-engine-network
    restart: unless-stopped

  # AI智能服务
  ai:
    build:
      context: ./ai
      dockerfile: Dockerfile
    ports:
      - "3002:3002"
    environment:
      - NODE_ENV=production
      - PORT=3002
      - MYSQL_HOST=mysql
      - MYSQL_PORT=3306
      - MYSQL_USERNAME=dl_engine
      - MYSQL_PASSWORD=dl_engine_password
      - MYSQL_DATABASE=dl_engine_ai
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - POSTGRESQL_HOST=postgresql
      - POSTGRESQL_PORT=5432
      - POSTGRESQL_USERNAME=postgres
      - POSTGRESQL_PASSWORD=postgres_password
      - POSTGRESQL_DATABASE=dl_engine_vectors
      - OLLAMA_HOST=ollama
      - OLLAMA_PORT=11434
    depends_on:
      - mysql
      - redis
      - postgresql
      - ollama
    networks:
      - dl-engine-network
    restart: unless-stopped

  # 媒体处理服务
  media:
    build:
      context: ./media
      dockerfile: Dockerfile
    ports:
      - "3003:3003"
    environment:
      - NODE_ENV=production
      - PORT=3003
      - MYSQL_HOST=mysql
      - MYSQL_PORT=3306
      - MYSQL_USERNAME=dl_engine
      - MYSQL_PASSWORD=dl_engine_password
      - MYSQL_DATABASE=dl_engine_media
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - MINIO_ENDPOINT=minio
      - MINIO_PORT=9000
      - MINIO_ACCESS_KEY=minioadmin
      - MINIO_SECRET_KEY=minioadmin
    depends_on:
      - mysql
      - redis
      - minio
    networks:
      - dl-engine-network
    restart: unless-stopped

  # PostgreSQL向量数据库
  postgresql:
    image: pgvector/pgvector:pg16
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres_password
      - POSTGRES_DB=dl_engine_vectors
    volumes:
      - postgresql_data:/var/lib/postgresql/data
      - ./scripts/postgresql-init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - dl-engine-network
    restart: unless-stopped

  # Minio对象存储
  minio:
    image: minio/minio:latest
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      - MINIO_ROOT_USER=minioadmin
      - MINIO_ROOT_PASSWORD=minioadmin
    volumes:
      - minio_data:/data
    command: server /data --console-address ":9001"
    networks:
      - dl-engine-network
    restart: unless-stopped

  # Ollama AI模型服务
  ollama:
    image: ollama/ollama:latest
    ports:
      - "11434:11434"
    environment:
      - OLLAMA_KEEP_ALIVE=24h
    volumes:
      - ollama_data:/root/.ollama
    entrypoint:
      - /bin/bash
      - -c
      - |
        ollama serve &
        sleep 10
        ollama pull llama2
        ollama pull mxbai-embed-large
        wait
    networks:
      - dl-engine-network
    restart: unless-stopped

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - gateway
      - storage
      - ai
      - media
    networks:
      - dl-engine-network
    restart: unless-stopped

volumes:
  mysql_data:
  redis_data:
  postgresql_data:
  minio_data:
  ollama_data:

networks:
  dl-engine-network:
    driver: bridge
