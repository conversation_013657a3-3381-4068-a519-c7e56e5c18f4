import { Module } from '@nestjs/common'
import { ConfigModule, ConfigService } from '@nestjs/config'
import { TypeOrmModule } from '@nestjs/typeorm'
import { ThrottlerModule } from '@nestjs/throttler'
import { ScheduleModule } from '@nestjs/schedule'
import { CacheModule } from '@nestjs/cache-manager'
import { EventEmitterModule } from '@nestjs/event-emitter'

// 核心模块
import { UsersModule } from './users/users.module'
import { ProjectsModule } from './projects/projects.module'
import { ScenesModule } from './scenes/scenes.module'
import { AssetsModule } from './assets/assets.module'
import { EducationModule } from './education/education.module'
import { SocialModule } from './social/social.module'
import { CollaborationModule } from './collaboration/collaboration.module'
import { NotificationModule } from './notifications/notifications.module'
import { MonitoringModule } from './monitoring/monitoring.module'

// 基础设施模块
import { AuthModule } from './auth/auth.module'
import { StorageModule } from './storage/storage.module'
import { SearchModule } from './search/search.module'
import { AnalyticsModule } from './analytics/analytics.module'

// 配置
import databaseConfig from './config/database.config'
import redisConfig from './config/redis.config'
import storageConfig from './config/storage.config'
import authConfig from './config/auth.config'
import notificationConfig from './config/notification.config'

/**
 * DL-Engine API 主应用模块
 * 
 * 整合所有功能模块，提供完整的数字化学习平台API服务
 */
@Module({
  imports: [
    // 配置模块
    ConfigModule.forRoot({
      isGlobal: true,
      load: [
        databaseConfig,
        redisConfig,
        storageConfig,
        authConfig,
        notificationConfig
      ],
      envFilePath: ['.env.local', '.env'],
    }),

    // 数据库模块
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        type: 'postgres',
        host: configService.get('database.host'),
        port: configService.get('database.port'),
        username: configService.get('database.username'),
        password: configService.get('database.password'),
        database: configService.get('database.name'),
        entities: [__dirname + '/**/*.entity{.ts,.js}'],
        synchronize: configService.get('database.synchronize'),
        logging: configService.get('database.logging'),
        ssl: configService.get('database.ssl'),
        extra: {
          max: configService.get('database.maxConnections'),
          connectionTimeoutMillis: configService.get('database.connectionTimeout'),
          idleTimeoutMillis: configService.get('database.idleTimeout'),
        },
      }),
      inject: [ConfigService],
    }),

    // 缓存模块
    CacheModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        store: 'redis',
        host: configService.get('redis.host'),
        port: configService.get('redis.port'),
        password: configService.get('redis.password'),
        db: configService.get('redis.db'),
        ttl: configService.get('redis.ttl'),
      }),
      inject: [ConfigService],
      isGlobal: true,
    }),

    // 限流模块
    ThrottlerModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        ttl: configService.get('throttle.ttl', 60),
        limit: configService.get('throttle.limit', 100),
      }),
      inject: [ConfigService],
    }),

    // 定时任务模块
    ScheduleModule.forRoot(),

    // 事件模块
    EventEmitterModule.forRoot(),

    // 认证授权模块
    AuthModule,

    // 核心业务模块
    UsersModule,
    ProjectsModule,
    ScenesModule,
    AssetsModule,
    EducationModule,
    SocialModule,
    CollaborationModule,
    NotificationModule,
    MonitoringModule,

    // 基础设施模块
    StorageModule,
    SearchModule,
    AnalyticsModule,
  ],
  controllers: [],
  providers: [],
})
export class AppModule {}
