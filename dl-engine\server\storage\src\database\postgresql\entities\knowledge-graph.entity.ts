import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';

export enum NodeType {
  CONCEPT = 'concept',
  ENTITY = 'entity',
  TOPIC = 'topic',
  SKILL = 'skill',
  LEARNING_OBJECTIVE = 'learning_objective',
  PREREQUISITE = 'prerequisite',
  RESOURCE = 'resource',
  ASSESSMENT = 'assessment',
}

export enum RelationType {
  IS_A = 'is_a',
  PART_OF = 'part_of',
  RELATED_TO = 'related_to',
  DEPENDS_ON = 'depends_on',
  PREREQUISITE_FOR = 'prerequisite_for',
  SIMILAR_TO = 'similar_to',
  OPPOSITE_TO = 'opposite_to',
  CAUSES = 'causes',
  ENABLES = 'enables',
  TEACHES = 'teaches',
  ASSESSES = 'assesses',
}

@Entity('knowledge_graph_nodes')
@Index(['type'])
@Index(['namespace'])
@Index(['isActive'])
export class KnowledgeGraphEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 200 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'enum', enum: NodeType })
  type: NodeType;

  @Column({ type: 'varchar', length: 100, nullable: true })
  namespace: string;

  @Column({ type: 'vector', length: 1536, nullable: true })
  embedding: number[];

  @Column({ type: 'json', nullable: true })
  properties: {
    aliases?: string[];
    definitions?: string[];
    examples?: string[];
    difficulty?: 'beginner' | 'intermediate' | 'advanced' | 'expert';
    bloomLevel?: 'remember' | 'understand' | 'apply' | 'analyze' | 'evaluate' | 'create';
    cognitiveLoad?: number; // 1-10
    estimatedTime?: number; // 学习时间（分钟）
    prerequisites?: string[];
    learningOutcomes?: string[];
    assessmentCriteria?: string[];
    resources?: Array<{
      type: 'text' | 'video' | 'audio' | 'interactive' | 'simulation';
      url: string;
      title: string;
      description?: string;
    }>;
    metadata?: Record<string, any>;
  };

  @Column({ type: 'json', nullable: true })
  relationships: Array<{
    targetNodeId: string;
    relationType: RelationType;
    strength: number; // 0-1
    confidence: number; // 0-1
    bidirectional: boolean;
    metadata?: {
      source?: string;
      evidence?: string[];
      createdBy?: string;
      validatedBy?: string[];
      lastValidated?: Date;
    };
  }>;

  @Column({ type: 'json', nullable: true })
  learningAnalytics: {
    totalLearners?: number;
    averageCompletionTime?: number;
    averageScore?: number;
    difficultyRating?: number;
    engagementScore?: number;
    dropoutRate?: number;
    commonMistakes?: Array<{
      mistake: string;
      frequency: number;
      remediation: string;
    }>;
    learningPaths?: Array<{
      pathId: string;
      frequency: number;
      successRate: number;
    }>;
  };

  @Column({ type: 'json', nullable: true })
  tags: string[];

  @Column({ type: 'decimal', precision: 10, scale: 8, nullable: true })
  latitude: number;

  @Column({ type: 'decimal', precision: 11, scale: 8, nullable: true })
  longitude: number;

  @Column({ type: 'int', default: 0 })
  accessCount: number;

  @Column({ type: 'datetime', nullable: true })
  lastAccessedAt: Date;

  @Column({ type: 'boolean', default: true })
  isActive: boolean;

  @Column({ type: 'boolean', default: false })
  isValidated: boolean;

  @Column({ type: 'uuid', nullable: true })
  createdBy: string;

  @Column({ type: 'uuid', nullable: true })
  lastModifiedBy: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // 虚拟字段
  get relationshipCount(): number {
    return this.relationships?.length || 0;
  }

  get incomingRelationships(): Array<any> {
    // 这个需要通过查询其他节点来获取
    return [];
  }

  get outgoingRelationships(): Array<any> {
    return this.relationships || [];
  }

  get hasEmbedding(): boolean {
    return this.embedding !== null && this.embedding.length > 0;
  }

  get difficultyLevel(): string {
    return this.properties?.difficulty || 'unknown';
  }

  get estimatedLearningTime(): number {
    return this.properties?.estimatedTime || 0;
  }

  get cognitiveLoad(): number {
    return this.properties?.cognitiveLoad || 0;
  }

  get hasPrerequisites(): boolean {
    return (this.properties?.prerequisites?.length || 0) > 0;
  }

  get hasResources(): boolean {
    return (this.properties?.resources?.length || 0) > 0;
  }

  get averageRelationshipStrength(): number {
    if (!this.relationships || this.relationships.length === 0) return 0;
    
    const totalStrength = this.relationships.reduce((sum, rel) => sum + rel.strength, 0);
    return totalStrength / this.relationships.length;
  }

  get strongRelationships(): Array<any> {
    return (this.relationships || []).filter(rel => rel.strength >= 0.7);
  }

  get weakRelationships(): Array<any> {
    return (this.relationships || []).filter(rel => rel.strength < 0.3);
  }

  get isLearningResource(): boolean {
    return [NodeType.RESOURCE, NodeType.ASSESSMENT].includes(this.type);
  }

  get isLearningConcept(): boolean {
    return [
      NodeType.CONCEPT, 
      NodeType.TOPIC, 
      NodeType.SKILL, 
      NodeType.LEARNING_OBJECTIVE
    ].includes(this.type);
  }
}
