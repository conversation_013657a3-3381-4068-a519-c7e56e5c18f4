/**
 * DL-Engine 第四批次服务集成测试
 * 测试存储与AI智能服务的完整功能
 */

import { Test, TestingModule } from '@nestjs/testing'
import { INestApplication } from '@nestjs/common'
import { ConfigModule } from '@nestjs/config'
import { TypeOrmModule } from '@nestjs/typeorm'

// 媒体处理服务
import { UploadService } from '../media/src/upload/upload.service'
import { ProcessingService } from '../media/src/processing/processing.service'

// 存储服务
import { DatabaseService } from '../storage/src/database/database.service'

// AI服务
import { OllamaService } from '../ai/src/ollama/ollama.service'

describe('第四批次：存储与AI智能服务集成测试', () => {
  let app: INestApplication
  let uploadService: UploadService
  let processingService: ProcessingService
  let databaseService: DatabaseService
  let ollamaService: OllamaService

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          isGlobal: true,
          envFilePath: ['.env.test']
        }),
        TypeOrmModule.forRoot({
          type: 'sqlite',
          database: ':memory:',
          entities: ['src/**/*.entity{.ts,.js}'],
          synchronize: true
        })
      ],
      providers: [
        UploadService,
        ProcessingService,
        DatabaseService,
        OllamaService
      ]
    }).compile()

    app = moduleFixture.createNestApplication()
    await app.init()

    uploadService = moduleFixture.get<UploadService>(UploadService)
    processingService = moduleFixture.get<ProcessingService>(ProcessingService)
    databaseService = moduleFixture.get<DatabaseService>(DatabaseService)
    ollamaService = moduleFixture.get<OllamaService>(OllamaService)
  })

  afterAll(async () => {
    await app.close()
  })

  describe('媒体处理服务测试', () => {
    it('应该能够初始化分片上传', async () => {
      const uploadDto = {
        fileName: 'test-image.jpg',
        fileSize: 1024 * 1024 * 5, // 5MB
        chunkSize: 1024 * 1024, // 1MB
        contentType: 'image/jpeg'
      }

      const result = await uploadService.initiateUpload('test-user-id', uploadDto)

      expect(result).toHaveProperty('uploadId')
      expect(result).toHaveProperty('chunkSize', uploadDto.chunkSize)
      expect(result).toHaveProperty('totalChunks', 5)
      expect(result).toHaveProperty('expiresAt')
    })

    it('应该能够处理图像文件', async () => {
      // 模拟图像处理
      const processDto = {
        resize: { width: 800, height: 600 },
        format: 'webp',
        quality: 80,
        watermark: {
          text: 'DL-Engine',
          position: 'bottom-right'
        }
      }

      // 这里需要先创建一个测试媒体文件
      // const result = await processingService.processImage('test-file-id', processDto)
      
      // expect(result).toHaveProperty('jobId')
      // expect(result).toHaveProperty('status', 'queued')
      // expect(result).toHaveProperty('estimatedTime')
    })

    it('应该能够获取处理队列统计', async () => {
      const stats = await processingService.getQueueStats()

      expect(stats).toHaveProperty('waiting')
      expect(stats).toHaveProperty('active')
      expect(stats).toHaveProperty('completed')
      expect(stats).toHaveProperty('failed')
      expect(stats).toHaveProperty('total')
    })
  })

  describe('存储服务测试', () => {
    it('应该能够获取数据库统计信息', async () => {
      const stats = await databaseService.getDatabaseStats()

      expect(stats).toHaveProperty('mysql')
      expect(stats).toHaveProperty('redis')
      expect(stats).toHaveProperty('postgresql')
      
      expect(stats.mysql).toHaveProperty('connections')
      expect(stats.mysql).toHaveProperty('queries')
      expect(stats.mysql).toHaveProperty('uptime')
      
      expect(stats.redis).toHaveProperty('memory')
      expect(stats.redis).toHaveProperty('keys')
      expect(stats.redis).toHaveProperty('hits')
      
      expect(stats.postgresql).toHaveProperty('vectorCount')
      expect(stats.postgresql).toHaveProperty('indexSize')
    })

    it('应该能够执行Redis缓存操作', async () => {
      const key = 'test:cache:key'
      const value = 'test-value'
      const ttl = 60

      // 设置缓存
      await databaseService.redisSet(key, value, ttl)

      // 获取缓存
      const cachedValue = await databaseService.redisGet(key)
      expect(cachedValue).toBe(value)

      // 检查键是否存在
      const exists = await databaseService.redisExists(key)
      expect(exists).toBe(1)

      // 删除缓存
      const deleted = await databaseService.redisDel(key)
      expect(deleted).toBe(1)
    })

    it('应该能够执行向量操作', async () => {
      const table = 'test_vectors'
      const id = 'test-vector-1'
      const vector = [0.1, 0.2, 0.3, 0.4, 0.5]
      const metadata = { type: 'test', category: 'example' }

      // 插入向量
      await databaseService.insertVector(table, id, vector, metadata)

      // 搜索相似向量
      const queryVector = [0.1, 0.2, 0.3, 0.4, 0.6] // 略有不同
      const results = await databaseService.searchSimilarVectors(table, queryVector, 5, 0.8)

      expect(Array.isArray(results)).toBe(true)
      // expect(results.length).toBeGreaterThan(0)
    })

    it('应该能够使用分布式锁', async () => {
      const lockKey = 'test:lock:resource'
      const ttl = 30

      // 获取锁
      const acquired = await databaseService.acquireLock(lockKey, ttl)
      expect(acquired).toBe(true)

      // 尝试再次获取同一个锁（应该失败）
      const acquiredAgain = await databaseService.acquireLock(lockKey, ttl)
      expect(acquiredAgain).toBe(false)

      // 释放锁
      await databaseService.releaseLock(lockKey)

      // 再次获取锁（应该成功）
      const acquiredAfterRelease = await databaseService.acquireLock(lockKey, ttl)
      expect(acquiredAfterRelease).toBe(true)

      // 清理
      await databaseService.releaseLock(lockKey)
    })
  })

  describe('AI服务测试', () => {
    it('应该能够获取服务健康状态', async () => {
      const health = await ollamaService.getHealthStatus()

      expect(health).toHaveProperty('healthy')
      if (health.healthy) {
        expect(health).toHaveProperty('ollamaVersion')
        expect(health).toHaveProperty('modelsCount')
        expect(health).toHaveProperty('totalRequests')
      } else {
        expect(health).toHaveProperty('error')
      }
    })

    it('应该能够获取可用模型列表', async () => {
      try {
        const models = await ollamaService.getModels()
        expect(Array.isArray(models)).toBe(true)
        
        if (models.length > 0) {
          const model = models[0]
          expect(model).toHaveProperty('name')
          expect(model).toHaveProperty('size')
          expect(model).toHaveProperty('digest')
          expect(model).toHaveProperty('modified_at')
        }
      } catch (error) {
        // Ollama服务可能不可用，跳过测试
        console.warn('Ollama服务不可用，跳过模型列表测试')
      }
    })

    it('应该能够生成文本（如果有可用模型）', async () => {
      try {
        const models = await ollamaService.getModels()
        if (models.length === 0) {
          console.warn('没有可用模型，跳过文本生成测试')
          return
        }

        const generateDto = {
          model: models[0].name,
          prompt: '请用中文介绍一下数字化学习的优势',
          temperature: 0.7,
          maxTokens: 100
        }

        const result = await ollamaService.generateText(generateDto)

        expect(result).toHaveProperty('text')
        expect(result).toHaveProperty('model', generateDto.model)
        expect(result).toHaveProperty('responseTime')
        expect(result).toHaveProperty('tokens')
        expect(result.tokens).toHaveProperty('total')
      } catch (error) {
        console.warn('文本生成测试失败，可能是模型不可用:', error.message)
      }
    })

    it('应该能够生成嵌入向量（如果有可用模型）', async () => {
      try {
        const models = await ollamaService.getModels()
        if (models.length === 0) {
          console.warn('没有可用模型，跳过嵌入生成测试')
          return
        }

        const embeddingDto = {
          model: models[0].name,
          text: '这是一个测试文本'
        }

        const result = await ollamaService.generateEmbedding(embeddingDto)

        expect(result).toHaveProperty('embedding')
        expect(result).toHaveProperty('model', embeddingDto.model)
        expect(result).toHaveProperty('dimensions')
        expect(result).toHaveProperty('responseTime')
        expect(Array.isArray(result.embedding)).toBe(true)
        expect(result.embedding.length).toBeGreaterThan(0)
      } catch (error) {
        console.warn('嵌入生成测试失败，可能是模型不可用:', error.message)
      }
    })

    it('应该能够获取模型性能统计', async () => {
      const performance = ollamaService.getModelPerformance()
      expect(Array.isArray(performance)).toBe(true)
      
      // 如果有性能数据
      if (performance.length > 0) {
        const perf = performance[0]
        expect(perf).toHaveProperty('modelName')
        expect(perf).toHaveProperty('averageResponseTime')
        expect(perf).toHaveProperty('totalRequests')
        expect(perf).toHaveProperty('successRate')
        expect(perf).toHaveProperty('lastUsed')
      }
    })
  })

  describe('服务集成测试', () => {
    it('应该能够完成完整的媒体处理流程', async () => {
      // 1. 上传文件
      // 2. 处理文件
      // 3. 生成嵌入向量
      // 4. 存储到向量数据库
      // 5. 搜索相似内容
      
      console.log('完整媒体处理流程测试需要实际的文件和模型，在集成环境中测试')
    })

    it('应该能够处理AI增强的学习分析', async () => {
      // 1. 分析学习数据
      // 2. 生成个性化推荐
      // 3. 缓存推荐结果
      // 4. 记录分析日志
      
      console.log('AI学习分析测试需要实际的学习数据，在集成环境中测试')
    })
  })
})

// 性能测试
describe('第四批次服务性能测试', () => {
  it('应该能够处理并发上传请求', async () => {
    // 并发上传测试
  })

  it('应该能够处理大量向量搜索请求', async () => {
    // 向量搜索性能测试
  })

  it('应该能够处理批量AI推理请求', async () => {
    // AI推理性能测试
  })
})
