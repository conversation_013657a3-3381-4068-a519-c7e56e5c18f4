import { Controller, Get, Post, Put, Delete, Body, Param, Query, Logger } from '@nestjs/common';
import { MySQLService } from './mysql.service';
import { TransactionService } from './transaction.service';
import { DataMigrationService } from './migration.service';

@Controller('mysql')
export class MySQLController {
  private readonly logger = new Logger(MySQLController.name);

  constructor(
    private mysqlService: MySQLService,
    private transactionService: TransactionService,
    private migrationService: DataMigrationService,
  ) {}

  // 健康检查
  @Get('health')
  async healthCheck() {
    return await this.mysqlService.healthCheck();
  }

  // 事务统计
  @Get('stats/transactions')
  async getTransactionStats() {
    return await this.transactionService.getTransactionStats();
  }

  // 用户管理接口
  @Post('users')
  async createUser(@Body() userData: any) {
    return await this.mysqlService.createUser(userData);
  }

  @Get('users/:id')
  async getUserById(@Param('id') id: string) {
    return await this.mysqlService.findUserById(id);
  }

  @Get('users/phone/:phone')
  async getUserByPhone(@Param('phone') phone: string) {
    return await this.mysqlService.findUserByPhone(phone);
  }

  @Put('users/:id')
  async updateUser(@Param('id') id: string, @Body() updateData: any) {
    return await this.mysqlService.updateUser(id, updateData);
  }

  @Post('users/:id/login')
  async updateUserLogin(@Param('id') id: string, @Body() { ip }: { ip: string }) {
    await this.mysqlService.updateUserLoginInfo(id, ip);
    return { success: true };
  }

  // 项目管理接口
  @Post('projects')
  async createProject(@Body() projectData: any) {
    return await this.mysqlService.createProject(projectData);
  }

  @Get('projects/:id')
  async getProjectById(@Param('id') id: string) {
    return await this.mysqlService.findProjectById(id);
  }

  @Get('projects/owner/:ownerId')
  async getProjectsByOwner(@Param('ownerId') ownerId: string) {
    return await this.mysqlService.findProjectsByOwner(ownerId);
  }

  @Put('projects/:id')
  async updateProject(@Param('id') id: string, @Body() updateData: any) {
    return await this.mysqlService.updateProject(id, updateData);
  }

  @Post('projects/:id/access')
  async updateProjectAccess(@Param('id') id: string) {
    await this.mysqlService.updateProjectAccess(id);
    return { success: true };
  }

  // 场景管理接口
  @Post('scenes')
  async createScene(@Body() sceneData: any) {
    return await this.mysqlService.createScene(sceneData);
  }

  @Get('scenes/:id')
  async getSceneById(@Param('id') id: string) {
    return await this.mysqlService.findSceneById(id);
  }

  @Get('scenes/project/:projectId')
  async getScenesByProject(@Param('projectId') projectId: string) {
    return await this.mysqlService.findScenesByProject(projectId);
  }

  @Put('scenes/:id')
  async updateScene(@Param('id') id: string, @Body() updateData: any) {
    return await this.mysqlService.updateScene(id, updateData);
  }

  // 资产管理接口
  @Post('assets')
  async createAsset(@Body() assetData: any) {
    return await this.mysqlService.createAsset(assetData);
  }

  @Get('assets/:id')
  async getAssetById(@Param('id') id: string) {
    return await this.mysqlService.findAssetById(id);
  }

  @Get('assets/project/:projectId')
  async getAssetsByProject(@Param('projectId') projectId: string) {
    return await this.mysqlService.findAssetsByProject(projectId);
  }

  @Put('assets/:id')
  async updateAsset(@Param('id') id: string, @Body() updateData: any) {
    return await this.mysqlService.updateAsset(id, updateData);
  }

  @Post('assets/:id/access')
  async updateAssetAccess(@Param('id') id: string) {
    await this.mysqlService.updateAssetAccess(id);
    return { success: true };
  }

  // 课程管理接口
  @Post('courses')
  async createCourse(@Body() courseData: any) {
    return await this.mysqlService.createCourse(courseData);
  }

  @Get('courses/:id')
  async getCourseById(@Param('id') id: string) {
    return await this.mysqlService.findCourseById(id);
  }

  @Get('courses/teacher/:teacherId')
  async getCoursesByTeacher(@Param('teacherId') teacherId: string) {
    return await this.mysqlService.findCoursesByTeacher(teacherId);
  }

  @Put('courses/:id')
  async updateCourse(@Param('id') id: string, @Body() updateData: any) {
    return await this.mysqlService.updateCourse(id, updateData);
  }

  // 作业管理接口
  @Post('assignments')
  async createAssignment(@Body() assignmentData: any) {
    return await this.mysqlService.createAssignment(assignmentData);
  }

  @Get('assignments/:id')
  async getAssignmentById(@Param('id') id: string) {
    return await this.mysqlService.findAssignmentById(id);
  }

  @Get('assignments/student/:studentId')
  async getAssignmentsByStudent(@Param('studentId') studentId: string) {
    return await this.mysqlService.findAssignmentsByStudent(studentId);
  }

  @Put('assignments/:id')
  async updateAssignment(@Param('id') id: string, @Body() updateData: any) {
    return await this.mysqlService.updateAssignment(id, updateData);
  }

  // 评估管理接口
  @Post('assessments')
  async createAssessment(@Body() assessmentData: any) {
    return await this.mysqlService.createAssessment(assessmentData);
  }

  @Get('assessments/:id')
  async getAssessmentById(@Param('id') id: string) {
    return await this.mysqlService.findAssessmentById(id);
  }

  @Get('assessments/student/:studentId')
  async getAssessmentsByStudent(@Param('studentId') studentId: string) {
    return await this.mysqlService.findAssessmentsByStudent(studentId);
  }

  @Put('assessments/:id')
  async updateAssessment(@Param('id') id: string, @Body() updateData: any) {
    return await this.mysqlService.updateAssessment(id, updateData);
  }

  // 迁移管理接口
  @Get('migrations')
  async getAllMigrations() {
    return await this.migrationService.getAllMigrationStatuses();
  }

  @Get('migrations/:id')
  async getMigrationStatus(@Param('id') id: string) {
    return await this.migrationService.getMigrationStatus(id);
  }

  @Post('migrations/:id/rollback')
  async rollbackMigration(@Param('id') id: string) {
    await this.migrationService.rollbackMigration(id);
    return { success: true };
  }

  @Post('data/validate')
  async validateDataConsistency() {
    return await this.migrationService.validateDataConsistency();
  }

  @Post('data/backup')
  async createDataBackup(@Body() { tables }: { tables?: string[] }) {
    const backupId = await this.migrationService.createDataBackup(tables);
    return { backupId };
  }

  @Post('data/restore')
  async restoreDataBackup(@Body() { backupId }: { backupId: string }) {
    await this.migrationService.restoreDataBackup(backupId);
    return { success: true };
  }

  // 批量事务操作示例
  @Post('batch/users')
  async createUsersInBatch(@Body() { users }: { users: any[] }) {
    return await this.transactionService.executeBatchTransaction(
      users.map(userData => async (context) => {
        return await context.manager.save('UserEntity', userData);
      })
    );
  }

  @Post('batch/projects')
  async createProjectWithScenes(@Body() { project, scenes }: { project: any; scenes: any[] }) {
    return await this.transactionService.executeTransaction(async (context) => {
      // 创建项目
      const savedProject = await context.manager.save('ProjectEntity', project);
      
      // 创建场景
      const savedScenes = [];
      for (const sceneData of scenes) {
        sceneData.projectId = savedProject.id;
        const savedScene = await context.manager.save('SceneEntity', sceneData);
        savedScenes.push(savedScene);
      }
      
      return {
        project: savedProject,
        scenes: savedScenes,
      };
    });
  }
}
