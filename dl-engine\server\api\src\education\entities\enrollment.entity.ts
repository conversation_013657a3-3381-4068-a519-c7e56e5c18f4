import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn, Index } from 'typeorm'
import { Classroom } from './classroom.entity'

@Entity('enrollments')
@Index(['classroomId'])
@Index(['studentId'])
@Index(['status'])
export class Enrollment {
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Column({ 
    type: 'enum',
    enum: ['pending', 'active', 'completed', 'dropped', 'suspended'],
    default: 'pending'
  })
  status: string

  @Column({ 
    type: 'enum',
    enum: ['student', 'assistant', 'observer'],
    default: 'student'
  })
  role: string

  @Column({ type: 'datetime' })
  enrolledAt: Date

  @Column({ type: 'datetime', nullable: true })
  completedAt: Date

  @Column({ type: 'datetime', nullable: true })
  lastAccessedAt: Date

  @Column({ type: 'decimal', precision: 5, scale: 2, default: 0 })
  progress: number

  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: true })
  finalGrade: number

  @Column({ type: 'json', nullable: true })
  preferences: any // 学习偏好

  @Column({ type: 'json', nullable: true })
  analytics: any // 学习分析数据

  @Column({ type: 'json', nullable: true })
  metadata: any

  // 关联关系
  @Column({ name: 'classroom_id' })
  classroomId: string

  @Column({ name: 'student_id' })
  studentId: string

  @ManyToOne(() => Classroom, classroom => classroom.enrollments)
  @JoinColumn({ name: 'classroom_id' })
  classroom: Classroom

  @CreateDateColumn()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date
}
