import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, Index } from 'typeorm'

@Entity('ai_models')
@Index(['name'])
@Index(['type'])
@Index(['status'])
export class AIModel {
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Column({ length: 100, unique: true })
  name: string

  @Column({ 
    type: 'enum',
    enum: ['ollama', 'openai', 'huggingface', 'custom'],
    default: 'ollama'
  })
  type: string

  @Column({ 
    type: 'enum', 
    enum: ['available', 'downloading', 'loading', 'error', 'deleted'],
    default: 'available'
  })
  status: string

  @Column({ type: 'bigint', default: 0 })
  size: number

  @Column({ length: 100, nullable: true })
  digest: string

  @Column({ type: 'text', nullable: true })
  details: string

  @Column({ type: 'json', nullable: true })
  capabilities: any

  @Column({ type: 'json', nullable: true })
  performance: any

  @CreateDateColumn()
  createdAt: Date

  @Column({ type: 'datetime', nullable: true })
  lastUsed: Date

  @UpdateDateColumn()
  updatedAt: Date
}
