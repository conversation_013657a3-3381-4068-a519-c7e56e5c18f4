#!/bin/bash

# DL-Engine Docker镜像构建和推送脚本
# 支持多架构构建和私有仓库推送

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
REGISTRY=${REGISTRY:-"docker.io"}
NAMESPACE=${NAMESPACE:-"dl-engine"}
VERSION=${VERSION:-"1.0.0"}
PLATFORM=${PLATFORM:-"linux/amd64,linux/arm64"}
PUSH=${PUSH:-"false"}
CACHE=${CACHE:-"true"}

# 服务列表
SERVICES=(
    "gateway"
    "auth"
    "api"
    "instance"
    "media"
    "ai"
    "task"
    "client"
)

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
DL-Engine Docker镜像构建和推送脚本

用法: $0 [选项] [服务名...]

选项:
    -r, --registry REGISTRY     Docker仓库地址 (默认: docker.io)
    -n, --namespace NAMESPACE   命名空间 (默认: dl-engine)
    -v, --version VERSION       版本标签 (默认: 1.0.0)
    -p, --platform PLATFORM    目标平台 (默认: linux/amd64,linux/arm64)
    --push                      构建后推送到仓库
    --no-cache                  禁用构建缓存
    -h, --help                  显示帮助信息

服务名:
    gateway     API网关服务
    auth        认证服务
    api         API服务
    instance    实例服务
    media       媒体服务
    ai          AI服务
    task        任务服务
    client      客户端应用
    all         构建所有服务 (默认)

示例:
    $0 --push gateway auth              # 构建并推送网关和认证服务
    $0 -r registry.example.com all     # 构建所有服务到私有仓库
    $0 -v 2.0.0 --push instance        # 构建并推送指定版本的实例服务

EOF
}

# 解析命令行参数
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -r|--registry)
                REGISTRY="$2"
                shift 2
                ;;
            -n|--namespace)
                NAMESPACE="$2"
                shift 2
                ;;
            -v|--version)
                VERSION="$2"
                shift 2
                ;;
            -p|--platform)
                PLATFORM="$2"
                shift 2
                ;;
            --push)
                PUSH="true"
                shift
                ;;
            --no-cache)
                CACHE="false"
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            all)
                # 构建所有服务
                shift
                ;;
            gateway|auth|api|instance|media|ai|task|client)
                # 添加到服务列表
                SELECTED_SERVICES+=("$1")
                shift
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 如果没有指定服务，构建所有服务
    if [ ${#SELECTED_SERVICES[@]} -eq 0 ]; then
        SELECTED_SERVICES=("${SERVICES[@]}")
    fi
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装"
        exit 1
    fi
    
    # 检查Docker Buildx
    if ! docker buildx version &> /dev/null; then
        log_error "Docker Buildx未安装"
        exit 1
    fi
    
    # 创建buildx builder（如果不存在）
    if ! docker buildx ls | grep -q "dl-engine-builder"; then
        log_info "创建Docker Buildx builder..."
        docker buildx create --name dl-engine-builder --use
    else
        docker buildx use dl-engine-builder
    fi
    
    log_info "依赖检查完成"
}

# 登录Docker仓库
docker_login() {
    if [ "$PUSH" = "true" ] && [ "$REGISTRY" != "docker.io" ]; then
        log_info "登录Docker仓库: $REGISTRY"
        
        if [ -n "$DOCKER_USERNAME" ] && [ -n "$DOCKER_PASSWORD" ]; then
            echo "$DOCKER_PASSWORD" | docker login "$REGISTRY" -u "$DOCKER_USERNAME" --password-stdin
        else
            log_warn "未设置DOCKER_USERNAME和DOCKER_PASSWORD环境变量"
            log_info "请手动登录: docker login $REGISTRY"
        fi
    fi
}

# 构建单个服务
build_service() {
    local service=$1
    local dockerfile="dl-engine/server/deployment/docker/images/Dockerfile.$service"
    local image_name="$REGISTRY/$NAMESPACE/$service:$VERSION"
    local latest_name="$REGISTRY/$NAMESPACE/$service:latest"
    
    log_info "构建服务: $service"
    log_debug "Dockerfile: $dockerfile"
    log_debug "镜像名称: $image_name"
    log_debug "平台: $PLATFORM"
    
    # 检查Dockerfile是否存在
    if [ ! -f "$dockerfile" ]; then
        log_error "Dockerfile不存在: $dockerfile"
        return 1
    fi
    
    # 构建参数
    local build_args=(
        "buildx" "build"
        "--platform" "$PLATFORM"
        "--file" "$dockerfile"
        "--tag" "$image_name"
        "--tag" "$latest_name"
    )
    
    # 缓存配置
    if [ "$CACHE" = "true" ]; then
        build_args+=(
            "--cache-from" "type=local,src=/tmp/.buildx-cache"
            "--cache-to" "type=local,dest=/tmp/.buildx-cache-new,mode=max"
        )
    else
        build_args+=("--no-cache")
    fi
    
    # 推送配置
    if [ "$PUSH" = "true" ]; then
        build_args+=("--push")
    else
        build_args+=("--load")
    fi
    
    # 构建上下文
    build_args+=(".")
    
    # 执行构建
    log_info "开始构建 $service..."
    if docker "${build_args[@]}"; then
        log_info "✅ $service 构建成功"
        
        # 更新缓存
        if [ "$CACHE" = "true" ] && [ -d "/tmp/.buildx-cache-new" ]; then
            rm -rf /tmp/.buildx-cache
            mv /tmp/.buildx-cache-new /tmp/.buildx-cache
        fi
        
        return 0
    else
        log_error "❌ $service 构建失败"
        return 1
    fi
}

# 构建所有选定的服务
build_services() {
    local failed_services=()
    local success_count=0
    local total_count=${#SELECTED_SERVICES[@]}
    
    log_info "开始构建 $total_count 个服务..."
    log_info "目标平台: $PLATFORM"
    log_info "版本标签: $VERSION"
    log_info "推送到仓库: $PUSH"
    
    for service in "${SELECTED_SERVICES[@]}"; do
        if build_service "$service"; then
            ((success_count++))
        else
            failed_services+=("$service")
        fi
    done
    
    # 构建结果汇总
    log_info "构建完成: $success_count/$total_count 成功"
    
    if [ ${#failed_services[@]} -gt 0 ]; then
        log_error "失败的服务: ${failed_services[*]}"
        return 1
    else
        log_info "🎉 所有服务构建成功!"
        return 0
    fi
}

# 清理函数
cleanup() {
    log_info "执行清理..."
    
    # 清理构建缓存
    if [ -d "/tmp/.buildx-cache-new" ]; then
        rm -rf /tmp/.buildx-cache-new
    fi
    
    # 清理悬空镜像
    docker image prune -f &> /dev/null || true
    
    log_info "清理完成"
}

# 主函数
main() {
    log_info "DL-Engine Docker镜像构建脚本"
    log_info "版本: 1.0.0"
    
    # 解析参数
    parse_args "$@"
    
    # 检查依赖
    check_dependencies
    
    # 登录Docker仓库
    docker_login
    
    # 构建服务
    if build_services; then
        log_info "🎉 构建任务完成!"
        exit 0
    else
        log_error "❌ 构建任务失败!"
        exit 1
    fi
}

# 信号处理
trap cleanup SIGTERM SIGINT

# 执行主函数
main "$@"
