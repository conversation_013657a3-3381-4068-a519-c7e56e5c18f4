import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { Exclude, Expose, Type } from 'class-transformer'

import { UserStatus, UserType } from '../entities/user.entity'
import { UserProfile } from '../entities/user-profile.entity'
import { UserPreference } from '../entities/user-preference.entity'
import { UserPrivacy } from '../entities/user-privacy.entity'
import { UserEducation } from '../entities/user-education.entity'

/**
 * 用户响应DTO
 * 
 * 用于API响应的用户数据结构
 */
export class UserResponseDto {
  /**
   * 用户ID
   */
  @ApiProperty({
    description: '用户ID',
    example: '123e4567-e89b-12d3-a456-426614174000'
  })
  @Expose()
  id: string

  /**
   * 用户名
   */
  @ApiPropertyOptional({
    description: '用户名',
    example: 'zhang_san_123'
  })
  @Expose()
  username?: string

  /**
   * 手机号码（脱敏显示）
   */
  @ApiPropertyOptional({
    description: '手机号码（脱敏显示）',
    example: '138****8000'
  })
  @Expose()
  get maskedPhone(): string | undefined {
    if (!this.phone) return undefined
    if (this.phone.length <= 4) return this.phone
    const start = this.phone.slice(0, 3)
    const end = this.phone.slice(-4)
    const middle = '*'.repeat(this.phone.length - 7)
    return `${start}${middle}${end}`
  }

  /**
   * 原始手机号（内部使用，不暴露给API）
   */
  @Exclude()
  phone?: string

  /**
   * 国家代码
   */
  @ApiPropertyOptional({
    description: '国家代码',
    example: '+86'
  })
  @Expose()
  countryCode: string

  /**
   * 邮箱地址（脱敏显示）
   */
  @ApiPropertyOptional({
    description: '邮箱地址（脱敏显示）',
    example: 'u***@example.com'
  })
  @Expose()
  get maskedEmail(): string | undefined {
    if (!this.email) return undefined
    const [localPart, domain] = this.email.split('@')
    if (localPart.length <= 2) return this.email
    const maskedLocal = localPart[0] + '*'.repeat(localPart.length - 2) + localPart.slice(-1)
    return `${maskedLocal}@${domain}`
  }

  /**
   * 原始邮箱（内部使用，不暴露给API）
   */
  @Exclude()
  email?: string

  /**
   * 显示名称
   */
  @ApiProperty({
    description: '显示名称',
    example: '张三'
  })
  @Expose()
  displayName: string

  /**
   * 头像URL
   */
  @ApiPropertyOptional({
    description: '头像URL',
    example: 'https://example.com/avatar.jpg'
  })
  @Expose()
  avatarUrl?: string

  /**
   * 用户状态
   */
  @ApiProperty({
    description: '用户状态',
    enum: UserStatus,
    example: UserStatus.ACTIVE
  })
  @Expose()
  status: UserStatus

  /**
   * 用户类型
   */
  @ApiProperty({
    description: '用户类型',
    enum: UserType,
    example: UserType.STUDENT
  })
  @Expose()
  userType: UserType

  /**
   * 是否已验证手机号
   */
  @ApiProperty({
    description: '是否已验证手机号',
    example: true
  })
  @Expose()
  phoneVerified: boolean

  /**
   * 是否已验证邮箱
   */
  @ApiProperty({
    description: '是否已验证邮箱',
    example: true
  })
  @Expose()
  emailVerified: boolean

  /**
   * 最后登录时间
   */
  @ApiPropertyOptional({
    description: '最后登录时间',
    example: '2024-01-01T12:00:00Z'
  })
  @Expose()
  lastLoginAt?: Date

  /**
   * 最后活跃时间
   */
  @ApiPropertyOptional({
    description: '最后活跃时间',
    example: '2024-01-01T12:00:00Z'
  })
  @Expose()
  lastActiveAt?: Date

  /**
   * 登录次数
   */
  @ApiProperty({
    description: '登录次数',
    example: 42
  })
  @Expose()
  loginCount: number

  /**
   * 是否接受服务条款
   */
  @ApiProperty({
    description: '是否接受服务条款',
    example: true
  })
  @Expose()
  termsAccepted: boolean

  /**
   * 接受服务条款的时间
   */
  @ApiPropertyOptional({
    description: '接受服务条款的时间',
    example: '2024-01-01T12:00:00Z'
  })
  @Expose()
  termsAcceptedAt?: Date

  /**
   * 创建时间
   */
  @ApiProperty({
    description: '创建时间',
    example: '2024-01-01T12:00:00Z'
  })
  @Expose()
  createdAt: Date

  /**
   * 更新时间
   */
  @ApiProperty({
    description: '更新时间',
    example: '2024-01-01T12:00:00Z'
  })
  @Expose()
  updatedAt: Date

  /**
   * 用户资料
   */
  @ApiPropertyOptional({
    description: '用户资料',
    type: () => UserProfile
  })
  @Expose()
  @Type(() => UserProfile)
  profile?: UserProfile

  /**
   * 用户偏好设置
   */
  @ApiPropertyOptional({
    description: '用户偏好设置',
    type: () => UserPreference
  })
  @Expose()
  @Type(() => UserPreference)
  preference?: UserPreference

  /**
   * 用户隐私设置
   */
  @ApiPropertyOptional({
    description: '用户隐私设置',
    type: () => UserPrivacy
  })
  @Expose()
  @Type(() => UserPrivacy)
  privacy?: UserPrivacy

  /**
   * 用户教育信息
   */
  @ApiPropertyOptional({
    description: '用户教育信息',
    type: () => [UserEducation]
  })
  @Expose()
  @Type(() => UserEducation)
  educations?: UserEducation[]

  /**
   * 是否在线（计算属性）
   */
  @ApiProperty({
    description: '是否在线（基于最后活跃时间计算）',
    example: true
  })
  @Expose()
  get isOnline(): boolean {
    if (!this.lastActiveAt) return false
    const now = new Date()
    const lastActive = new Date(this.lastActiveAt)
    const diffMinutes = (now.getTime() - lastActive.getTime()) / (1000 * 60)
    return diffMinutes <= 5 // 5分钟内活跃视为在线
  }

  /**
   * 账户年龄（天数）
   */
  @ApiProperty({
    description: '账户年龄（天数）',
    example: 365
  })
  @Expose()
  get accountAgeDays(): number {
    const now = new Date()
    const created = new Date(this.createdAt)
    const diffTime = now.getTime() - created.getTime()
    return Math.floor(diffTime / (1000 * 60 * 60 * 24))
  }

  /**
   * 验证状态摘要
   */
  @ApiProperty({
    description: '验证状态摘要',
    example: { phone: true, email: true, complete: true }
  })
  @Expose()
  get verificationStatus(): {
    phone: boolean
    email: boolean
    complete: boolean
  } {
    return {
      phone: this.phoneVerified,
      email: this.emailVerified,
      complete: this.phoneVerified && this.emailVerified
    }
  }
}
