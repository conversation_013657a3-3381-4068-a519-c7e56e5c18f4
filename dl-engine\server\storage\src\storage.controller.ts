import { Controller, Get, Post, Body, Query, Logger } from '@nestjs/common';
import { StorageService } from './storage.service';

@Controller('storage')
export class StorageController {
  private readonly logger = new Logger(StorageController.name);

  constructor(private storageService: StorageService) {}

  /**
   * 健康检查
   */
  @Get('health')
  async getHealthStatus() {
    return await this.storageService.getHealthStatus();
  }

  /**
   * 获取存储指标
   */
  @Get('metrics')
  async getMetrics() {
    return await this.storageService.getMetrics();
  }

  /**
   * 存储优化
   */
  @Post('optimize')
  async optimizeStorage() {
    return await this.storageService.optimizeStorage();
  }

  /**
   * 存储清理
   */
  @Post('cleanup')
  async cleanupStorage(@Body() options: any) {
    return await this.storageService.cleanupStorage(options);
  }

  /**
   * 数据同步
   */
  @Post('sync')
  async syncData(@Body() config: any) {
    return await this.storageService.syncData(config);
  }

  /**
   * 系统信息
   */
  @Get('info')
  async getSystemInfo() {
    const health = await this.storageService.getHealthStatus();
    const metrics = await this.storageService.getMetrics();

    return {
      health,
      metrics,
      timestamp: new Date().toISOString(),
    };
  }
}
