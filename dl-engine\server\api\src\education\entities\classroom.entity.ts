import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, OneToMany, Index } from 'typeorm'
import { Enrollment } from './enrollment.entity'

@Entity('classrooms')
@Index(['teacherId'])
@Index(['status'])
@Index(['type'])
export class Classroom {
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Column({ length: 200 })
  name: string

  @Column({ type: 'text', nullable: true })
  description: string

  @Column({ 
    type: 'enum',
    enum: ['virtual', 'hybrid', 'physical'],
    default: 'virtual'
  })
  type: string

  @Column({ 
    type: 'enum',
    enum: ['active', 'inactive', 'archived'],
    default: 'active'
  })
  status: string

  @Column({ type: 'int', default: 30 })
  maxStudents: number

  @Column({ type: 'int', default: 0 })
  currentStudents: number

  @Column({ type: 'json', nullable: true })
  schedule: any // 课程安排

  @Column({ type: 'json', nullable: true })
  settings: any // 教室设置

  @Column({ type: 'text', nullable: true })
  virtualRoomUrl: string // 虚拟教室链接

  @Column({ type: 'text', nullable: true })
  accessCode: string // 访问码

  @Column({ type: 'datetime', nullable: true })
  startDate: Date

  @Column({ type: 'datetime', nullable: true })
  endDate: Date

  @Column({ type: 'json', nullable: true })
  resources: any // 教室资源

  @Column({ type: 'json', nullable: true })
  metadata: any

  // 关联关系
  @Column({ name: 'teacher_id' })
  teacherId: string

  @OneToMany(() => Enrollment, enrollment => enrollment.classroom)
  enrollments: Enrollment[]

  @CreateDateColumn()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date
}
