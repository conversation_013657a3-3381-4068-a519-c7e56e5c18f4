# DL-Engine 部署配置

本目录包含DL-Engine项目的完整部署配置，支持Kubernetes、Docker和边缘计算部署。

## 目录结构

```
deployment/
├── kubernetes/          # Kubernetes配置 (8,000行)
│   ├── helm/            # Helm Charts
│   ├── agones/          # Agones游戏服务器
│   └── monitoring/      # 监控配置
├── docker/              # Docker配置 (6,000行)
│   ├── images/          # 镜像构建
│   ├── compose/         # Docker Compose
│   └── registry/        # 镜像仓库
├── edge/                # 边缘计算 (3,000行)
│   ├── nodes/           # 边缘节点
│   └── distribution/    # 内容分发
└── monitoring/          # 监控运维 (3,000行)
    ├── prometheus/      # Prometheus
    ├── grafana/         # Grafana
    └── logging/         # 日志系统
```

## 部署方式

### 1. 本地开发环境
```bash
cd docker/compose
docker-compose up -d
```

### 2. Kubernetes生产环境
```bash
cd kubernetes/helm
helm install dl-engine ./dl-engine-chart
```

### 3. 边缘计算部署
```bash
cd edge/nodes
kubectl apply -f edge-deployment.yaml
```

## 配置说明

- **Kubernetes**: 支持自动扩缩容、服务发现、负载均衡
- **Agones**: 游戏服务器管理，支持实时多人协作
- **监控**: Prometheus + Grafana + ELK Stack
- **边缘计算**: 就近部署，降低延迟

## 环境要求

- Kubernetes 1.23+
- Docker 20.10+
- Helm 3.8+
- Agones 1.26+
