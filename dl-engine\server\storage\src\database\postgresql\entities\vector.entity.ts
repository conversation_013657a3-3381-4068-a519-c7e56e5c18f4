import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';

export enum VectorType {
  TEXT = 'text',
  IMAGE = 'image',
  AUDIO = 'audio',
  VIDEO = 'video',
  DOCUMENT = 'document',
  CODE = 'code',
  SCENE = 'scene',
  ASSET = 'asset',
}

export enum VectorModel {
  OPENAI_ADA_002 = 'text-embedding-ada-002',
  OPENAI_3_SMALL = 'text-embedding-3-small',
  OPENAI_3_LARGE = 'text-embedding-3-large',
  SENTENCE_TRANSFORMERS = 'sentence-transformers',
  CLIP = 'clip',
  CUSTOM = 'custom',
}

@Entity('vectors')
@Index(['sourceType', 'sourceId'])
@Index(['vectorType'])
@Index(['model'])
@Index(['namespace'])
export class VectorEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 100 })
  sourceType: string; // 'user', 'project', 'scene', 'asset', 'course', etc.

  @Column({ type: 'uuid' })
  sourceId: string;

  @Column({ type: 'enum', enum: VectorType })
  vectorType: VectorType;

  @Column({ type: 'enum', enum: VectorModel })
  model: VectorModel;

  @Column({ type: 'varchar', length: 100, nullable: true })
  namespace: string; // 命名空间，用于分组

  // 使用 pgvector 扩展的 vector 类型
  @Column({ type: 'vector', length: 1536 }) // OpenAI ada-002 的维度
  embedding: number[];

  @Column({ type: 'text' })
  content: string; // 原始内容

  @Column({ type: 'text', nullable: true })
  summary: string; // 内容摘要

  @Column({ type: 'json', nullable: true })
  metadata: {
    title?: string;
    description?: string;
    tags?: string[];
    language?: string;
    contentType?: string;
    fileSize?: number;
    duration?: number; // 音频/视频时长
    dimensions?: {
      width: number;
      height: number;
      depth?: number;
    };
    extractedText?: string; // 从图像/文档中提取的文本
    keywords?: string[];
    entities?: Array<{
      name: string;
      type: string;
      confidence: number;
    }>;
    sentiment?: {
      score: number;
      label: 'positive' | 'negative' | 'neutral';
    };
    topics?: Array<{
      name: string;
      confidence: number;
    }>;
  };

  @Column({ type: 'varchar', length: 32, nullable: true })
  contentHash: string; // 内容哈希，用于去重

  @Column({ type: 'int', default: 0 })
  accessCount: number;

  @Column({ type: 'datetime', nullable: true })
  lastAccessedAt: Date;

  @Column({ type: 'boolean', default: true })
  isActive: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // 虚拟字段
  get embeddingDimension(): number {
    return this.embedding?.length || 0;
  }

  get hasMetadata(): boolean {
    return this.metadata !== null && Object.keys(this.metadata || {}).length > 0;
  }

  get contentPreview(): string {
    if (!this.content) return '';
    return this.content.length > 200 
      ? this.content.substring(0, 200) + '...'
      : this.content;
  }
}
