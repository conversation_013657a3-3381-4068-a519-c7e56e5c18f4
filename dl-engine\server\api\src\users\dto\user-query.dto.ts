import {
  IsOptional,
  IsString,
  IsEnum,
  <PERSON>Int,
  <PERSON>,
  Max,
  Transform
} from 'class-validator'
import { ApiPropertyOptional } from '@nestjs/swagger'
import { Type } from 'class-transformer'

import { UserType, UserStatus } from '../entities/user.entity'

/**
 * 用户查询DTO
 */
export class UserQueryDto {
  /**
   * 页码
   */
  @ApiPropertyOptional({
    description: '页码',
    example: 1,
    minimum: 1,
    default: 1
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: '页码必须是整数' })
  @Min(1, { message: '页码必须大于0' })
  page?: number = 1

  /**
   * 每页数量
   */
  @ApiPropertyOptional({
    description: '每页数量',
    example: 20,
    minimum: 1,
    maximum: 100,
    default: 20
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: '每页数量必须是整数' })
  @Min(1, { message: '每页数量必须大于0' })
  @Max(100, { message: '每页数量不能超过100' })
  limit?: number = 20

  /**
   * 搜索关键词
   */
  @ApiPropertyOptional({
    description: '搜索关键词，支持搜索显示名称、用户名、真实姓名',
    example: '张三'
  })
  @IsOptional()
  @IsString()
  @Transform(({ value }) => value?.trim())
  search?: string

  /**
   * 用户类型过滤
   */
  @ApiPropertyOptional({
    description: '用户类型过滤',
    enum: UserType,
    example: UserType.STUDENT
  })
  @IsOptional()
  @IsEnum(UserType, { message: '用户类型不正确' })
  userType?: UserType

  /**
   * 用户状态过滤
   */
  @ApiPropertyOptional({
    description: '用户状态过滤',
    enum: UserStatus,
    example: UserStatus.ACTIVE
  })
  @IsOptional()
  @IsEnum(UserStatus, { message: '用户状态不正确' })
  status?: UserStatus

  /**
   * 排序字段
   */
  @ApiPropertyOptional({
    description: '排序字段',
    example: 'createdAt',
    enum: ['createdAt', 'updatedAt', 'displayName', 'lastLoginAt', 'loginCount'],
    default: 'createdAt'
  })
  @IsOptional()
  @IsString()
  @IsEnum(['createdAt', 'updatedAt', 'displayName', 'lastLoginAt', 'loginCount'], {
    message: '排序字段不正确'
  })
  sortBy?: string = 'createdAt'

  /**
   * 排序方向
   */
  @ApiPropertyOptional({
    description: '排序方向',
    example: 'DESC',
    enum: ['ASC', 'DESC'],
    default: 'DESC'
  })
  @IsOptional()
  @IsString()
  @IsEnum(['ASC', 'DESC'], { message: '排序方向必须是ASC或DESC' })
  sortOrder?: 'ASC' | 'DESC' = 'DESC'

  /**
   * 是否包含已删除用户
   */
  @ApiPropertyOptional({
    description: '是否包含已删除用户',
    example: false,
    default: false
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.toLowerCase() === 'true'
    }
    return Boolean(value)
  })
  includeDeleted?: boolean = false

  /**
   * 是否只显示已验证用户
   */
  @ApiPropertyOptional({
    description: '是否只显示已验证用户',
    example: false,
    default: false
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.toLowerCase() === 'true'
    }
    return Boolean(value)
  })
  verifiedOnly?: boolean = false

  /**
   * 注册时间范围 - 开始时间
   */
  @ApiPropertyOptional({
    description: '注册时间范围 - 开始时间',
    example: '2024-01-01'
  })
  @IsOptional()
  @IsString()
  createdAfter?: string

  /**
   * 注册时间范围 - 结束时间
   */
  @ApiPropertyOptional({
    description: '注册时间范围 - 结束时间',
    example: '2024-12-31'
  })
  @IsOptional()
  @IsString()
  createdBefore?: string

  /**
   * 最后登录时间范围 - 开始时间
   */
  @ApiPropertyOptional({
    description: '最后登录时间范围 - 开始时间',
    example: '2024-01-01'
  })
  @IsOptional()
  @IsString()
  lastLoginAfter?: string

  /**
   * 最后登录时间范围 - 结束时间
   */
  @ApiPropertyOptional({
    description: '最后登录时间范围 - 结束时间',
    example: '2024-12-31'
  })
  @IsOptional()
  @IsString()
  lastLoginBefore?: string
}
