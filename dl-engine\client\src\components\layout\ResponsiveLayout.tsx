import React, { useState, useEffect, useCallback } from 'react'
import { Layout, Drawer, Grid, FloatButton } from 'antd'
import { MenuOutlined, SettingOutlined, QuestionCircleOutlined } from '@ant-design/icons'
import classNames from 'classnames'

// 导入组件
import { Header } from './Header'
import { Sidebar } from './Sidebar'
import { Footer } from './Footer'
import { MobileNavigation } from './MobileNavigation'
import { QuickActions } from './QuickActions'

// 导入hooks
import { useBreakpoint } from '@hooks/useBreakpoint'
import { useLocalStorage } from '@hooks/useLocalStorage'
import { useI18n } from '@hooks/useI18n'

const { Content, Sider } = Layout
const { useBreakpoint: useAntdBreakpoint } = Grid

interface ResponsiveLayoutProps {
  children: React.ReactNode
  className?: string
}

export const ResponsiveLayout: React.FC<ResponsiveLayoutProps> = ({
  children,
  className
}) => {
  const { t } = useI18n()
  const screens = useAntdBreakpoint()
  const { isMobile, isTablet, isDesktop } = useBreakpoint()
  
  // 侧边栏状态管理
  const [sidebarCollapsed, setSidebarCollapsed] = useLocalStorage('sidebar-collapsed', false)
  const [mobileDrawerVisible, setMobileDrawerVisible] = useState(false)
  const [quickActionsVisible, setQuickActionsVisible] = useState(false)

  // 响应式侧边栏宽度
  const sidebarWidth = React.useMemo(() => {
    if (isMobile) return 280
    if (isTablet) return 240
    return 260
  }, [isMobile, isTablet])

  const collapsedWidth = React.useMemo(() => {
    if (isMobile) return 0
    return 80
  }, [isMobile])

  // 自动折叠侧边栏（移动端）
  useEffect(() => {
    if (isMobile && !sidebarCollapsed) {
      setSidebarCollapsed(true)
    }
  }, [isMobile, sidebarCollapsed, setSidebarCollapsed])

  // 处理侧边栏折叠
  const handleSidebarCollapse = useCallback((collapsed: boolean) => {
    setSidebarCollapsed(collapsed)
    if (isMobile) {
      setMobileDrawerVisible(!collapsed)
    }
  }, [isMobile, setSidebarCollapsed])

  // 处理移动端抽屉
  const handleMobileDrawerToggle = useCallback(() => {
    setMobileDrawerVisible(!mobileDrawerVisible)
  }, [mobileDrawerVisible])

  // 处理快捷操作面板
  const handleQuickActionsToggle = useCallback(() => {
    setQuickActionsVisible(!quickActionsVisible)
  }, [quickActionsVisible])

  // 布局类名
  const layoutClassName = classNames(
    'responsive-layout',
    {
      'layout-mobile': isMobile,
      'layout-tablet': isTablet,
      'layout-desktop': isDesktop,
      'sidebar-collapsed': sidebarCollapsed
    },
    className
  )

  // 移动端布局
  if (isMobile) {
    return (
      <Layout className={layoutClassName}>
        {/* 移动端头部 */}
        <Header
          isMobile={true}
          onMenuClick={handleMobileDrawerToggle}
          onQuickActionsClick={handleQuickActionsToggle}
        />

        {/* 移动端侧边栏抽屉 */}
        <Drawer
          title={t('common.menu')}
          placement="left"
          width={sidebarWidth}
          open={mobileDrawerVisible}
          onClose={() => setMobileDrawerVisible(false)}
          bodyStyle={{ padding: 0 }}
          headerStyle={{ borderBottom: '1px solid #f0f0f0' }}
        >
          <Sidebar
            isMobile={true}
            collapsed={false}
            onItemClick={() => setMobileDrawerVisible(false)}
          />
        </Drawer>

        {/* 主内容区域 */}
        <Content className="layout-content mobile-content">
          <div className="content-wrapper">
            {children}
          </div>
        </Content>

        {/* 移动端底部导航 */}
        <MobileNavigation />

        {/* 快捷操作抽屉 */}
        <Drawer
          title={t('common.quickActions')}
          placement="right"
          width={320}
          open={quickActionsVisible}
          onClose={() => setQuickActionsVisible(false)}
          bodyStyle={{ padding: 16 }}
        >
          <QuickActions isMobile={true} />
        </Drawer>

        {/* 浮动按钮组 */}
        <FloatButton.Group
          trigger="hover"
          type="primary"
          style={{ right: 24, bottom: 80 }}
          icon={<SettingOutlined />}
        >
          <FloatButton
            icon={<QuestionCircleOutlined />}
            tooltip={t('common.help')}
            onClick={() => {
              // 打开帮助
            }}
          />
          <FloatButton
            icon={<SettingOutlined />}
            tooltip={t('common.settings')}
            onClick={handleQuickActionsToggle}
          />
        </FloatButton.Group>
      </Layout>
    )
  }

  // 桌面端和平板布局
  return (
    <Layout className={layoutClassName}>
      {/* 侧边栏 */}
      <Sider
        width={sidebarWidth}
        collapsedWidth={collapsedWidth}
        collapsed={sidebarCollapsed}
        onCollapse={handleSidebarCollapse}
        breakpoint="lg"
        collapsible
        trigger={null}
        className="layout-sider"
        style={{
          overflow: 'auto',
          height: '100vh',
          position: 'fixed',
          left: 0,
          top: 0,
          bottom: 0,
          zIndex: 100
        }}
      >
        <Sidebar
          collapsed={sidebarCollapsed}
          isMobile={false}
        />
      </Sider>

      {/* 主布局容器 */}
      <Layout
        style={{
          marginLeft: sidebarCollapsed ? collapsedWidth : sidebarWidth,
          transition: 'margin-left 0.2s'
        }}
      >
        {/* 头部 */}
        <Header
          isMobile={false}
          collapsed={sidebarCollapsed}
          onCollapse={handleSidebarCollapse}
          onQuickActionsClick={handleQuickActionsToggle}
        />

        {/* 主内容区域 */}
        <Content className="layout-content">
          <div className="content-wrapper">
            {children}
          </div>
        </Content>

        {/* 底部 */}
        <Footer />
      </Layout>

      {/* 快捷操作面板 */}
      <Drawer
        title={t('common.quickActions')}
        placement="right"
        width={isTablet ? 320 : 400}
        open={quickActionsVisible}
        onClose={() => setQuickActionsVisible(false)}
        bodyStyle={{ padding: 16 }}
        mask={isTablet}
      >
        <QuickActions isMobile={false} />
      </Drawer>

      {/* 浮动按钮（仅平板显示） */}
      {isTablet && (
        <FloatButton.Group
          trigger="hover"
          type="primary"
          style={{ right: 24, bottom: 24 }}
          icon={<SettingOutlined />}
        >
          <FloatButton
            icon={<QuestionCircleOutlined />}
            tooltip={t('common.help')}
            onClick={() => {
              // 打开帮助
            }}
          />
          <FloatButton
            icon={<SettingOutlined />}
            tooltip={t('common.quickActions')}
            onClick={handleQuickActionsToggle}
          />
        </FloatButton.Group>
      )}
    </Layout>
  )
}

// 样式
const styles = `
.responsive-layout {
  min-height: 100vh;
}

.layout-content {
  min-height: calc(100vh - 64px);
  background: #f5f5f5;
}

.content-wrapper {
  padding: 16px;
  max-width: 100%;
  margin: 0 auto;
}

/* 移动端样式 */
.layout-mobile .content-wrapper {
  padding: 12px;
}

.mobile-content {
  margin-bottom: 60px; /* 为底部导航留出空间 */
}

/* 平板样式 */
.layout-tablet .content-wrapper {
  padding: 16px;
  max-width: 1200px;
}

/* 桌面端样式 */
.layout-desktop .content-wrapper {
  padding: 24px;
  max-width: 1400px;
}

/* 侧边栏样式 */
.layout-sider {
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
}

.layout-sider .ant-layout-sider-children {
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* 响应式断点 */
@media (max-width: 768px) {
  .layout-content {
    min-height: calc(100vh - 56px);
  }
  
  .content-wrapper {
    padding: 8px;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .content-wrapper {
    padding: 16px;
  }
}

@media (min-width: 1025px) {
  .content-wrapper {
    padding: 24px;
  }
}

/* 动画效果 */
.responsive-layout * {
  transition: all 0.2s ease-in-out;
}

/* 暗色主题适配 */
[data-theme="dark"] .layout-content {
  background: #141414;
}

[data-theme="dark"] .layout-sider {
  box-shadow: 2px 0 8px rgba(255, 255, 255, 0.1);
}
`

// 注入样式
if (typeof document !== 'undefined') {
  const styleElement = document.createElement('style')
  styleElement.textContent = styles
  document.head.appendChild(styleElement)
}
