import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, OneToMany, Index } from 'typeorm'
import { LearningProgress } from './learning-progress.entity'

@Entity('learning_paths')
@Index(['category'])
@Index(['difficulty'])
@Index(['status'])
export class LearningPath {
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Column({ length: 200 })
  title: string

  @Column({ type: 'text', nullable: true })
  description: string

  @Column({ length: 100, nullable: true })
  category: string

  @Column({ 
    type: 'enum',
    enum: ['beginner', 'intermediate', 'advanced'],
    default: 'beginner'
  })
  difficulty: string

  @Column({ 
    type: 'enum',
    enum: ['draft', 'published', 'archived'],
    default: 'draft'
  })
  status: string

  @Column({ type: 'json' })
  courseSequence: any // 课程序列

  @Column({ type: 'json', nullable: true })
  prerequisites: any // 前置要求

  @Column({ type: 'json', nullable: true })
  learningObjectives: any // 学习目标

  @Column({ type: 'int', default: 0 })
  estimatedHours: number // 预计学习时长

  @Column({ type: 'json', nullable: true })
  skills: any // 技能标签

  @Column({ type: 'json', nullable: true })
  metadata: any

  @Column({ length: 36 })
  createdBy: string

  // 关联关系
  @OneToMany(() => LearningProgress, progress => progress.learningPath)
  progresses: LearningProgress[]

  @CreateDateColumn()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date
}
