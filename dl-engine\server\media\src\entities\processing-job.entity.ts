import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn, Index } from 'typeorm'
import { MediaFile } from './media-file.entity'

@Entity('processing_jobs')
@Index(['status'])
@Index(['type'])
@Index(['createdAt'])
export class ProcessingJob {
  @PrimaryGeneratedColumn('uuid')
  id: string

  @ManyToOne(() => MediaFile)
  @JoinColumn({ name: 'media_file_id' })
  mediaFile: MediaFile

  @Column({ 
    type: 'enum',
    enum: ['image', 'video', 'audio', '3d_model'],
  })
  type: string

  @Column({ 
    type: 'enum', 
    enum: ['pending', 'queued', 'processing', 'completed', 'failed', 'cancelled'],
    default: 'pending'
  })
  status: string

  @Column({ type: 'int', default: 0 })
  progress: number

  @Column({ type: 'text', nullable: true })
  options: string

  @Column({ type: 'text', nullable: true })
  result: string

  @Column({ type: 'text', nullable: true })
  error: string

  @Column({ length: 50, nullable: true })
  queueJobId: string

  @CreateDateColumn()
  createdAt: Date

  @Column({ type: 'datetime', nullable: true })
  startedAt: Date

  @Column({ type: 'datetime', nullable: true })
  completedAt: Date

  @UpdateDateColumn()
  updatedAt: Date
}
