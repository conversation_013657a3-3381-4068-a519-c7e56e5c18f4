#!/bin/bash

# DL-Engine 服务器端部署脚本
# 用于自动化部署第三批次服务器端核心服务

set -e

echo "🚀 开始部署 DL-Engine 服务器端核心服务..."

# 检查Docker和Docker Compose
if ! command -v docker &> /dev/null; then
    echo "❌ Docker 未安装，请先安装 Docker"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose 未安装，请先安装 Docker Compose"
    exit 1
fi

# 设置环境变量
export NODE_ENV=${NODE_ENV:-production}
export COMPOSE_PROJECT_NAME=dl-engine

# 创建必要的目录
mkdir -p logs
mkdir -p data/mysql
mkdir -p data/redis
mkdir -p ssl

# 停止现有服务
echo "🛑 停止现有服务..."
docker-compose down --remove-orphans

# 清理旧镜像（可选）
if [ "$1" = "--clean" ]; then
    echo "🧹 清理旧镜像..."
    docker system prune -f
    docker-compose build --no-cache
else
    echo "🔨 构建服务镜像..."
    docker-compose build
fi

# 启动数据库服务
echo "🗄️ 启动数据库服务..."
docker-compose up -d mysql redis

# 等待数据库启动
echo "⏳ 等待数据库启动..."
sleep 30

# 运行数据库迁移
echo "📊 运行数据库迁移..."
docker-compose run --rm api npm run migration:run

# 启动所有服务
echo "🚀 启动所有服务..."
docker-compose up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 60

# 健康检查
echo "🔍 执行健康检查..."
services=("gateway:8080" "api:3030" "auth:3031" "instance:3032" "task:3033")

for service in "${services[@]}"; do
    IFS=':' read -r name port <<< "$service"
    echo "检查 $name 服务..."
    
    max_attempts=30
    attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f "http://localhost:$port/health" &> /dev/null; then
            echo "✅ $name 服务健康"
            break
        fi
        
        if [ $attempt -eq $max_attempts ]; then
            echo "❌ $name 服务健康检查失败"
            docker-compose logs $name
            exit 1
        fi
        
        echo "⏳ 等待 $name 服务启动... ($attempt/$max_attempts)"
        sleep 5
        ((attempt++))
    done
done

# 显示服务状态
echo "📊 服务状态:"
docker-compose ps

# 显示访问信息
echo ""
echo "🎉 DL-Engine 服务器端核心服务部署完成！"
echo ""
echo "📍 服务访问地址:"
echo "  🌐 API网关:     http://localhost:8080"
echo "  📚 API文档:     http://localhost:3030/api/docs"
echo "  🔐 认证服务:    http://localhost:3031/auth"
echo "  🏠 实例服务:    http://localhost:3032"
echo "  ⚙️  任务服务:    http://localhost:3033"
echo ""
echo "📊 监控地址:"
echo "  🔍 健康检查:    http://localhost:8080/health"
echo "  📈 系统监控:    http://localhost:3030/api/v1/monitoring"
echo ""
echo "🗄️ 数据库连接:"
echo "  MySQL:         localhost:3306"
echo "  Redis:         localhost:6379"
echo ""
echo "📝 查看日志: docker-compose logs -f [service_name]"
echo "🛑 停止服务: docker-compose down"
echo ""

# 可选：运行集成测试
if [ "$2" = "--test" ]; then
    echo "🧪 运行集成测试..."
    npm run test:e2e
fi

echo "✨ 部署完成！"
