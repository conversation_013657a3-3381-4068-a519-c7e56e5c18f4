/*
DL-Engine 服务器端集成测试
测试实例服务和任务服务的集成功能
*/

import { describe, it, expect, beforeAll, afterAll, beforeEach } from '@jest/globals'
import { Application } from '@feathersjs/feathers'
import { createFeathersKoaApp } from '@ir-engine/server-core/src/createApp'
import { ServerMode } from '@ir-engine/server-core/src/ServerState'

// 导入服务
import { createInstanceServices, InstanceServices } from '../instance'
import { createTaskServices, TaskServices } from '../task'

describe('DL-Engine 服务器端集成测试', () => {
  let app: Application
  let instanceServices: InstanceServices
  let taskServices: TaskServices

  beforeAll(async () => {
    // 创建测试应用
    app = await createFeathersKoaApp(ServerMode.Test)
    
    // 初始化服务
    instanceServices = createInstanceServices(app, {
      instanceManager: {
        enabled: true,
        defaultResourceConfig: {
          cpu: 1,
          memory: 512,
          maxUsers: 10,
          priority: 5
        }
      },
      networkSync: {
        enabled: true,
        maxClientsPerInstance: 10,
        stateUpdateInterval: 100
      },
      physicsSync: {
        enabled: false // 测试中禁用物理同步
      },
      scalingManager: {
        enabled: true,
        defaultStrategy: 'manual',
        minInstances: 1,
        maxInstances: 5
      }
    })

    taskServices = createTaskServices(app, {
      scheduler: {
        enabled: true,
        maxConcurrentTasks: 5
      },
      queue: {
        enabled: false // 测试中禁用队列
      },
      worker: {
        enabled: true,
        defaultWorkerCount: 2
      }
    })

    // 启动服务
    await instanceServices.initialize()
    await taskServices.initialize()
    await instanceServices.start()
    await taskServices.start()
  })

  afterAll(async () => {
    // 停止服务
    await instanceServices.stop()
    await taskServices.stop()
  })

  describe('实例服务测试', () => {
    let instanceId: string

    it('应该能够创建实例', async () => {
      const instanceManager = instanceServices.getService('instanceManager')
      expect(instanceManager).toBeTruthy()

      const instance = await instanceManager.createInstance({
        locationId: 'test-location-1',
        resourceConfig: {
          cpu: 1,
          memory: 512,
          maxUsers: 5,
          priority: 3
        }
      })

      expect(instance).toBeTruthy()
      expect(instance.id).toBeTruthy()
      instanceId = instance.id
    })

    it('应该能够获取实例状态', async () => {
      const instanceManager = instanceServices.getService('instanceManager')
      const state = await instanceManager.getInstanceState(instanceId)
      
      expect(state).toBe('active')
    })

    it('应该能够获取实例资源使用情况', async () => {
      const instanceManager = instanceServices.getService('instanceManager')
      const resources = await instanceManager.getInstanceResources(instanceId)
      
      expect(resources).toBeTruthy()
      expect(resources.instanceId).toBe(instanceId)
      expect(resources.users).toBe(0)
      expect(resources.maxUsers).toBe(5)
    })

    it('应该能够列出活跃实例', async () => {
      const instanceManager = instanceServices.getService('instanceManager')
      const instances = await instanceManager.listActiveInstances()
      
      expect(instances).toBeTruthy()
      expect(instances.length).toBeGreaterThan(0)
      expect(instances.some(i => i.instanceId === instanceId)).toBe(true)
    })

    it('应该能够销毁实例', async () => {
      const instanceManager = instanceServices.getService('instanceManager')
      await instanceManager.destroyInstance(instanceId, true)
      
      const state = await instanceManager.getInstanceState(instanceId)
      expect(state).toBe('terminated')
    })
  })

  describe('网络同步服务测试', () => {
    let mockSpark: any
    let instanceId: string

    beforeEach(async () => {
      // 创建测试实例
      const instanceManager = instanceServices.getService('instanceManager')
      const instance = await instanceManager.createInstance({
        locationId: 'test-location-2'
      })
      instanceId = instance.id

      // 模拟 Spark 连接
      mockSpark = {
        id: 'test-client-1',
        write: jest.fn(),
        on: jest.fn(),
        end: jest.fn()
      }
    })

    it('应该能够处理客户端连接', async () => {
      const networkSync = instanceServices.getService('networkSync')
      expect(networkSync).toBeTruthy()

      await networkSync.handleClientConnection(mockSpark, 'test-user-1', instanceId)
      
      const clientCount = networkSync.getInstanceClientCount(instanceId)
      expect(clientCount).toBe(1)
    })

    it('应该能够广播消息到实例', async () => {
      const networkSync = instanceServices.getService('networkSync')
      
      await networkSync.broadcastToInstance(instanceId, {
        type: 'test_message',
        data: 'Hello World!'
      })

      // 验证消息已发送
      expect(mockSpark.write).toHaveBeenCalled()
    })

    it('应该能够处理客户端断开连接', async () => {
      const networkSync = instanceServices.getService('networkSync')
      
      await networkSync.handleClientDisconnection(mockSpark.id)
      
      const clientCount = networkSync.getInstanceClientCount(instanceId)
      expect(clientCount).toBe(0)
    })
  })

  describe('扩缩容管理测试', () => {
    const locationId = 'test-location-3'

    it('应该能够设置扩缩容配置', async () => {
      const scalingManager = instanceServices.getService('scalingManager')
      expect(scalingManager).toBeTruthy()

      await scalingManager.setScalingConfig(locationId, {
        strategy: 'manual',
        minInstances: 1,
        maxInstances: 3,
        targetCpuUsage: 70,
        targetMemoryUsage: 80,
        targetUserRatio: 0.8,
        scaleUpThreshold: 85,
        scaleDownThreshold: 30,
        cooldownPeriod: 60,
        evaluationPeriod: 30
      })

      const config = scalingManager.getScalingConfig(locationId)
      expect(config).toBeTruthy()
      expect(config.minInstances).toBe(1)
      expect(config.maxInstances).toBe(3)
    })

    it('应该能够手动扩容', async () => {
      const scalingManager = instanceServices.getService('scalingManager')
      
      await scalingManager.scaleUp(locationId, 2)
      
      const stats = await scalingManager.getLocationLoadStats(locationId)
      expect(stats.instanceCount).toBe(2)
    })

    it('应该能够手动缩容', async () => {
      const scalingManager = instanceServices.getService('scalingManager')
      
      await scalingManager.scaleDown(locationId, 1)
      
      const stats = await scalingManager.getLocationLoadStats(locationId)
      expect(stats.instanceCount).toBe(1)
    })
  })

  describe('任务服务测试', () => {
    it('应该能够注册任务处理器', () => {
      const mockHandler = {
        async execute(payload: any, context: any) {
          return { success: true, payload }
        }
      }

      taskServices.registerTaskHandler('test-handler', mockHandler)
      
      // 验证处理器已注册
      const taskScheduler = taskServices.getService('taskScheduler')
      expect(taskScheduler).toBeTruthy()
    })

    it('应该能够创建立即执行任务', async () => {
      const taskId = await taskServices.createTask({
        name: '测试任务',
        type: 'immediate',
        priority: 'normal',
        handler: 'test-handler',
        payload: { test: true }
      })

      expect(taskId).toBeTruthy()
      
      // 等待任务执行
      await new Promise(resolve => setTimeout(resolve, 100))
      
      const status = taskServices.getTaskStatus(taskId)
      expect(status).toBeTruthy()
    })

    it('应该能够创建定时任务', async () => {
      const taskId = await taskServices.createTask({
        name: '定时测试任务',
        type: 'scheduled',
        priority: 'high',
        handler: 'test-handler',
        schedule: {
          startTime: new Date(Date.now() + 1000) // 1秒后执行
        },
        payload: { scheduled: true }
      })

      expect(taskId).toBeTruthy()
    })

    it('应该能够取消任务', async () => {
      const taskId = await taskServices.createTask({
        name: '待取消任务',
        type: 'delayed',
        priority: 'low',
        handler: 'test-handler',
        schedule: {
          interval: 5000 // 5秒后执行
        },
        payload: { cancel: true }
      })

      await taskServices.cancelTask(taskId)
      
      const status = taskServices.getTaskStatus(taskId)
      expect(status).toBe('cancelled')
    })
  })

  describe('工作进程测试', () => {
    it('应该能够获取工作池统计', () => {
      const taskWorker = taskServices.getService('taskWorker')
      expect(taskWorker).toBeTruthy()

      const stats = taskWorker.getPoolStats()
      expect(stats).toBeTruthy()
      expect(stats.totalWorkers).toBeGreaterThan(0)
    })

    it('应该能够列出工作节点', () => {
      const taskWorker = taskServices.getService('taskWorker')
      const workers = taskWorker.listWorkers()
      
      expect(workers).toBeTruthy()
      expect(workers.length).toBeGreaterThan(0)
      expect(workers[0]).toHaveProperty('id')
      expect(workers[0]).toHaveProperty('status')
      expect(workers[0]).toHaveProperty('currentTasks')
    })
  })

  describe('服务集成测试', () => {
    it('应该能够获取所有服务状态', () => {
      const instanceStatus = instanceServices.getServicesStatus()
      const taskStatus = taskServices.getServicesStatus()

      expect(instanceStatus).toBeTruthy()
      expect(instanceStatus.instanceManager).toBeTruthy()
      expect(instanceStatus.networkSync).toBeTruthy()
      expect(instanceStatus.scalingManager).toBeTruthy()

      expect(taskStatus).toBeTruthy()
      expect(taskStatus.taskScheduler).toBeTruthy()
      expect(taskStatus.taskWorker).toBeTruthy()
    })

    it('应该能够通过任务创建实例', async () => {
      // 注册实例创建处理器
      taskServices.registerTaskHandler('instance-create', {
        async execute(payload: any, context: any) {
          const instanceManager = context.app.get('instanceManager')
          return await instanceManager.createInstance(payload)
        }
      })

      // 通过任务创建实例
      const taskId = await taskServices.createTask({
        name: '创建实例任务',
        type: 'immediate',
        priority: 'high',
        handler: 'instance-create',
        payload: {
          locationId: 'test-location-4',
          resourceConfig: {
            cpu: 1,
            memory: 256,
            maxUsers: 5,
            priority: 3
          }
        }
      })

      expect(taskId).toBeTruthy()
      
      // 等待任务执行
      await new Promise(resolve => setTimeout(resolve, 200))
      
      // 验证实例已创建
      const instanceManager = instanceServices.getService('instanceManager')
      const instances = await instanceManager.listActiveInstances()
      expect(instances.some(i => i.locationId === 'test-location-4')).toBe(true)
    })

    it('应该能够处理服务间事件', async () => {
      const networkSync = instanceServices.getService('networkSync')
      const scalingManager = instanceServices.getService('scalingManager')

      // 模拟客户端连接事件
      const mockSpark = {
        id: 'integration-test-client',
        write: jest.fn(),
        on: jest.fn(),
        end: jest.fn()
      }

      // 创建测试实例
      const instanceManager = instanceServices.getService('instanceManager')
      const instance = await instanceManager.createInstance({
        locationId: 'test-location-5'
      })

      // 连接客户端
      await networkSync.handleClientConnection(mockSpark, 'test-user', instance.id)

      // 验证客户端已连接
      const clientCount = networkSync.getInstanceClientCount(instance.id)
      expect(clientCount).toBe(1)

      // 断开客户端
      await networkSync.handleClientDisconnection(mockSpark.id)

      // 验证客户端已断开
      const finalClientCount = networkSync.getInstanceClientCount(instance.id)
      expect(finalClientCount).toBe(0)
    })
  })

  describe('错误处理测试', () => {
    it('应该正确处理实例创建失败', async () => {
      const instanceManager = instanceServices.getService('instanceManager')
      
      await expect(instanceManager.createInstance({
        // 缺少必要参数
      })).rejects.toThrow()
    })

    it('应该正确处理任务执行失败', async () => {
      // 注册会失败的处理器
      taskServices.registerTaskHandler('failing-handler', {
        async execute(payload: any, context: any) {
          throw new Error('任务执行失败')
        }
      })

      const taskId = await taskServices.createTask({
        name: '失败任务',
        type: 'immediate',
        priority: 'normal',
        handler: 'failing-handler',
        payload: {}
      })

      // 等待任务执行失败
      await new Promise(resolve => setTimeout(resolve, 100))
      
      const status = taskServices.getTaskStatus(taskId)
      expect(status).toBe('failed')
    })

    it('应该正确处理不存在的实例操作', async () => {
      const instanceManager = instanceServices.getService('instanceManager')
      
      await expect(instanceManager.destroyInstance('non-existent-id')).rejects.toThrow()
    })
  })
})

// 性能测试
describe('性能测试', () => {
  let app: Application
  let instanceServices: InstanceServices
  let taskServices: TaskServices

  beforeAll(async () => {
    app = await createFeathersKoaApp(ServerMode.Test)
    instanceServices = createInstanceServices(app)
    taskServices = createTaskServices(app)
    
    await instanceServices.initialize()
    await taskServices.initialize()
    await instanceServices.start()
    await taskServices.start()
  })

  afterAll(async () => {
    await instanceServices.stop()
    await taskServices.stop()
  })

  it('应该能够处理大量并发实例创建', async () => {
    const instanceManager = instanceServices.getService('instanceManager')
    const startTime = Date.now()
    
    // 创建100个实例
    const promises = Array.from({ length: 100 }, (_, i) =>
      instanceManager.createInstance({
        locationId: `perf-test-location-${i}`,
        resourceConfig: {
          cpu: 0.1,
          memory: 64,
          maxUsers: 1,
          priority: 1
        }
      })
    )

    const instances = await Promise.all(promises)
    const endTime = Date.now()
    
    expect(instances.length).toBe(100)
    expect(endTime - startTime).toBeLessThan(5000) // 5秒内完成
    
    console.log(`创建100个实例耗时: ${endTime - startTime}ms`)
  })

  it('应该能够处理大量并发任务', async () => {
    // 注册快速处理器
    taskServices.registerTaskHandler('fast-handler', {
      async execute(payload: any, context: any) {
        return { processed: payload.id }
      }
    })

    const startTime = Date.now()
    
    // 创建1000个任务
    const promises = Array.from({ length: 1000 }, (_, i) =>
      taskServices.createTask({
        name: `性能测试任务-${i}`,
        type: 'immediate',
        priority: 'normal',
        handler: 'fast-handler',
        payload: { id: i }
      })
    )

    const taskIds = await Promise.all(promises)
    const endTime = Date.now()
    
    expect(taskIds.length).toBe(1000)
    expect(endTime - startTime).toBeLessThan(3000) // 3秒内完成
    
    console.log(`创建1000个任务耗时: ${endTime - startTime}ms`)
  })
})
