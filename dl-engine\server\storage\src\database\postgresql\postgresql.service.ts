import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository, InjectDataSource } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { VectorEntity } from './entities/vector.entity';
import { EmbeddingEntity } from './entities/embedding.entity';
import { DocumentEntity } from './entities/document.entity';
import { KnowledgeGraphEntity } from './entities/knowledge-graph.entity';

@Injectable()
export class PostgreSQLService {
  private readonly logger = new Logger(PostgreSQLService.name);

  constructor(
    @InjectDataSource('postgresql') private dataSource: DataSource,
    @InjectRepository(VectorEntity, 'postgresql')
    private vectorRepository: Repository<VectorEntity>,
    @InjectRepository(EmbeddingEntity, 'postgresql')
    private embeddingRepository: Repository<EmbeddingEntity>,
    @InjectRepository(DocumentEntity, 'postgresql')
    private documentRepository: Repository<DocumentEntity>,
    @InjectRepository(KnowledgeGraphEntity, 'postgresql')
    private knowledgeGraphRepository: Repository<KnowledgeGraphEntity>,
  ) {}

  // 向量操作
  async createVector(vectorData: Partial<VectorEntity>): Promise<VectorEntity> {
    const vector = this.vectorRepository.create(vectorData);
    return await this.vectorRepository.save(vector);
  }

  async findVectorById(id: string): Promise<VectorEntity | null> {
    return await this.vectorRepository.findOne({ where: { id } });
  }

  async findVectorsBySource(sourceType: string, sourceId: string): Promise<VectorEntity[]> {
    return await this.vectorRepository.find({
      where: { sourceType, sourceId },
      order: { createdAt: 'DESC' },
    });
  }

  async updateVector(id: string, updateData: Partial<VectorEntity>): Promise<VectorEntity> {
    await this.vectorRepository.update(id, updateData);
    return await this.findVectorById(id);
  }

  async deleteVector(id: string): Promise<boolean> {
    const result = await this.vectorRepository.delete(id);
    return result.affected > 0;
  }

  async updateVectorAccess(id: string): Promise<void> {
    await this.vectorRepository.update(id, {
      lastAccessedAt: new Date(),
      accessCount: () => 'accessCount + 1',
    });
  }

  // 嵌入操作
  async createEmbedding(embeddingData: Partial<EmbeddingEntity>): Promise<EmbeddingEntity> {
    const embedding = this.embeddingRepository.create(embeddingData);
    return await this.embeddingRepository.save(embedding);
  }

  async findEmbeddingById(id: string): Promise<EmbeddingEntity | null> {
    return await this.embeddingRepository.findOne({ where: { id } });
  }

  async findPendingEmbeddings(limit: number = 10): Promise<EmbeddingEntity[]> {
    return await this.embeddingRepository.find({
      where: { status: 'pending' },
      order: { createdAt: 'ASC' },
      take: limit,
    });
  }

  async updateEmbedding(id: string, updateData: Partial<EmbeddingEntity>): Promise<EmbeddingEntity> {
    await this.embeddingRepository.update(id, updateData);
    return await this.findEmbeddingById(id);
  }

  async findEmbeddingsByBatch(batchId: string): Promise<EmbeddingEntity[]> {
    return await this.embeddingRepository.find({
      where: { batchId },
      order: { createdAt: 'ASC' },
    });
  }

  // 文档操作
  async createDocument(documentData: Partial<DocumentEntity>): Promise<DocumentEntity> {
    const document = this.documentRepository.create(documentData);
    return await this.documentRepository.save(document);
  }

  async findDocumentById(id: string): Promise<DocumentEntity | null> {
    return await this.documentRepository.findOne({ where: { id } });
  }

  async findDocumentsByOwner(ownerId: string): Promise<DocumentEntity[]> {
    return await this.documentRepository.find({
      where: { ownerId },
      order: { createdAt: 'DESC' },
    });
  }

  async updateDocument(id: string, updateData: Partial<DocumentEntity>): Promise<DocumentEntity> {
    await this.documentRepository.update(id, updateData);
    return await this.findDocumentById(id);
  }

  async updateDocumentAccess(id: string): Promise<void> {
    await this.documentRepository.update(id, {
      lastAccessedAt: new Date(),
      accessCount: () => 'accessCount + 1',
    });
  }

  // 知识图谱操作
  async createKnowledgeNode(nodeData: Partial<KnowledgeGraphEntity>): Promise<KnowledgeGraphEntity> {
    const node = this.knowledgeGraphRepository.create(nodeData);
    return await this.knowledgeGraphRepository.save(node);
  }

  async findKnowledgeNodeById(id: string): Promise<KnowledgeGraphEntity | null> {
    return await this.knowledgeGraphRepository.findOne({ where: { id } });
  }

  async findKnowledgeNodesByType(type: string): Promise<KnowledgeGraphEntity[]> {
    return await this.knowledgeGraphRepository.find({
      where: { type: type as any },
      order: { name: 'ASC' },
    });
  }

  async updateKnowledgeNode(id: string, updateData: Partial<KnowledgeGraphEntity>): Promise<KnowledgeGraphEntity> {
    await this.knowledgeGraphRepository.update(id, updateData);
    return await this.findKnowledgeNodeById(id);
  }

  async updateKnowledgeNodeAccess(id: string): Promise<void> {
    await this.knowledgeGraphRepository.update(id, {
      lastAccessedAt: new Date(),
      accessCount: () => 'accessCount + 1',
    });
  }

  // 相似度搜索
  async findSimilarVectors(
    embedding: number[],
    limit: number = 10,
    threshold: number = 0.7,
    namespace?: string
  ): Promise<Array<VectorEntity & { similarity: number }>> {
    let query = `
      SELECT *, (embedding <=> $1::vector) as distance,
             (1 - (embedding <=> $1::vector)) as similarity
      FROM vectors 
      WHERE (1 - (embedding <=> $1::vector)) >= $2
    `;
    
    const params: any[] = [JSON.stringify(embedding), threshold];
    let paramIndex = 3;

    if (namespace) {
      query += ` AND namespace = $${paramIndex}`;
      params.push(namespace);
      paramIndex++;
    }

    query += ` AND isActive = true`;
    query += ` ORDER BY embedding <=> $1::vector`;
    query += ` LIMIT $${paramIndex}`;
    params.push(limit);

    const results = await this.dataSource.query(query, params);
    
    return results.map(row => ({
      ...row,
      embedding: JSON.parse(row.embedding),
      similarity: parseFloat(row.similarity),
    }));
  }

  // 批量操作
  async batchCreateVectors(vectors: Partial<VectorEntity>[]): Promise<VectorEntity[]> {
    const entities = vectors.map(data => this.vectorRepository.create(data));
    return await this.vectorRepository.save(entities);
  }

  async batchUpdateVectors(updates: Array<{ id: string; data: Partial<VectorEntity> }>): Promise<void> {
    await this.dataSource.transaction(async manager => {
      for (const { id, data } of updates) {
        await manager.update(VectorEntity, id, data);
      }
    });
  }

  // 统计信息
  async getVectorStats(): Promise<{
    totalVectors: number;
    vectorsByType: Record<string, number>;
    vectorsByModel: Record<string, number>;
    vectorsByNamespace: Record<string, number>;
    averageEmbeddingDimension: number;
  }> {
    const [
      totalResult,
      typeResults,
      modelResults,
      namespaceResults,
      dimensionResult,
    ] = await Promise.all([
      this.vectorRepository.count(),
      this.vectorRepository
        .createQueryBuilder('vector')
        .select('vector.vectorType', 'type')
        .addSelect('COUNT(*)', 'count')
        .groupBy('vector.vectorType')
        .getRawMany(),
      this.vectorRepository
        .createQueryBuilder('vector')
        .select('vector.model', 'model')
        .addSelect('COUNT(*)', 'count')
        .groupBy('vector.model')
        .getRawMany(),
      this.vectorRepository
        .createQueryBuilder('vector')
        .select('vector.namespace', 'namespace')
        .addSelect('COUNT(*)', 'count')
        .where('vector.namespace IS NOT NULL')
        .groupBy('vector.namespace')
        .getRawMany(),
      this.dataSource.query('SELECT AVG(array_length(embedding, 1)) as avg_dimension FROM vectors'),
    ]);

    const vectorsByType = {};
    typeResults.forEach(row => {
      vectorsByType[row.type] = parseInt(row.count);
    });

    const vectorsByModel = {};
    modelResults.forEach(row => {
      vectorsByModel[row.model] = parseInt(row.count);
    });

    const vectorsByNamespace = {};
    namespaceResults.forEach(row => {
      vectorsByNamespace[row.namespace] = parseInt(row.count);
    });

    return {
      totalVectors: totalResult,
      vectorsByType,
      vectorsByModel,
      vectorsByNamespace,
      averageEmbeddingDimension: parseFloat(dimensionResult[0]?.avg_dimension || '0'),
    };
  }

  // 健康检查
  async healthCheck(): Promise<{ status: string; details: any }> {
    try {
      const result = await this.dataSource.query('SELECT 1 as health');
      const stats = await this.getConnectionStats();
      
      return {
        status: 'healthy',
        details: {
          connection: 'active',
          stats,
          timestamp: new Date().toISOString(),
        },
      };
    } catch (error) {
      this.logger.error('PostgreSQL health check failed:', error);
      return {
        status: 'unhealthy',
        details: {
          error: error.message,
          timestamp: new Date().toISOString(),
        },
      };
    }
  }

  private async getConnectionStats(): Promise<any> {
    try {
      const [connections] = await this.dataSource.query(`
        SELECT count(*) as active_connections
        FROM pg_stat_activity 
        WHERE state = 'active'
      `);
      
      const [maxConnections] = await this.dataSource.query(`
        SELECT setting as max_connections 
        FROM pg_settings 
        WHERE name = 'max_connections'
      `);
      
      return {
        activeConnections: parseInt(connections.active_connections),
        maxConnections: parseInt(maxConnections.max_connections),
      };
    } catch (error) {
      this.logger.warn('Failed to get connection stats:', error);
      return {};
    }
  }

  // 获取数据源
  getDataSource(): DataSource {
    return this.dataSource;
  }
}
