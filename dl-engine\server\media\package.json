{"name": "@dl-engine/server-media", "version": "1.0.0", "description": "DL-Engine 媒体处理服务", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "ts-node src/index.ts", "test": "jest", "lint": "eslint src/**/*.ts"}, "dependencies": {"@nestjs/common": "^10.0.0", "@nestjs/core": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/typeorm": "^10.0.0", "@nestjs/config": "^3.0.0", "@nestjs/bull": "^10.0.0", "typeorm": "^0.3.0", "mysql2": "^3.0.0", "redis": "^4.0.0", "bull": "^4.0.0", "multer": "^1.4.5-lts.1", "sharp": "^0.32.0", "ffmpeg-static": "^5.0.0", "fluent-ffmpeg": "^2.1.2", "minio": "^7.0.0", "class-validator": "^0.14.0", "class-transformer": "^0.5.0"}, "devDependencies": {"@types/node": "^20.0.0", "@types/multer": "^1.4.7", "@types/fluent-ffmpeg": "^2.1.21", "typescript": "^5.0.0", "ts-node": "^10.0.0", "jest": "^29.0.0", "@types/jest": "^29.0.0", "eslint": "^8.0.0"}}