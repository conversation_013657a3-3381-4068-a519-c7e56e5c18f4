import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@nestjs/typeorm'
import { ConfigModule } from '@nestjs/config'

import { Project } from './entities/project.entity'
import { ProjectPermission } from './entities/project-permission.entity'
import { ProjectVersion } from './entities/project-version.entity'
import { ProjectCollaborator } from './entities/project-collaborator.entity'
import { ProjectTemplate } from './entities/project-template.entity'
import { ProjectCategory } from './entities/project-category.entity'
import { ProjectTag } from './entities/project-tag.entity'
import { ProjectActivity } from './entities/project-activity.entity'

import { ProjectsController } from './controllers/projects.controller'
import { ProjectPermissionsController } from './controllers/project-permissions.controller'
import { ProjectVersionsController } from './controllers/project-versions.controller'
import { ProjectCollaboratorsController } from './controllers/project-collaborators.controller'
import { ProjectTemplatesController } from './controllers/project-templates.controller'
import { ProjectCategoriesController } from './controllers/project-categories.controller'
import { ProjectActivitiesController } from './controllers/project-activities.controller'

import { ProjectsService } from './services/projects.service'
import { ProjectPermissionsService } from './services/project-permissions.service'
import { ProjectVersionsService } from './services/project-versions.service'
import { ProjectCollaboratorsService } from './services/project-collaborators.service'
import { ProjectTemplatesService } from './services/project-templates.service'
import { ProjectCategoriesService } from './services/project-categories.service'
import { ProjectActivitiesService } from './services/project-activities.service'
import { ProjectValidationService } from './services/project-validation.service'
import { ProjectNotificationService } from './services/project-notification.service'

import { UsersModule } from '../users/users.module'
import { NotificationModule } from '../notifications/notification.module'
import { StorageModule } from '../storage/storage.module'

/**
 * 项目管理模块
 * 
 * 功能包括：
 * - 项目创建和管理
 * - 版本控制
 * - 协作管理
 * - 权限分配
 * - 项目模板
 * - 分类和标签
 * - 活动记录
 */
@Module({
  imports: [
    ConfigModule,
    TypeOrmModule.forFeature([
      Project,
      ProjectPermission,
      ProjectVersion,
      ProjectCollaborator,
      ProjectTemplate,
      ProjectCategory,
      ProjectTag,
      ProjectActivity
    ]),
    UsersModule,
    NotificationModule,
    StorageModule
  ],
  controllers: [
    ProjectsController,
    ProjectPermissionsController,
    ProjectVersionsController,
    ProjectCollaboratorsController,
    ProjectTemplatesController,
    ProjectCategoriesController,
    ProjectActivitiesController
  ],
  providers: [
    ProjectsService,
    ProjectPermissionsService,
    ProjectVersionsService,
    ProjectCollaboratorsService,
    ProjectTemplatesService,
    ProjectCategoriesService,
    ProjectActivitiesService,
    ProjectValidationService,
    ProjectNotificationService
  ],
  exports: [
    ProjectsService,
    ProjectPermissionsService,
    ProjectVersionsService,
    ProjectCollaboratorsService,
    ProjectTemplatesService,
    ProjectCategoriesService,
    ProjectActivitiesService
  ]
})
export class ProjectsModule {}
