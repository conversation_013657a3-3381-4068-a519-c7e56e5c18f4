import { Controller, Get, Post, Put, Delete, Body, Param, Query, UseGuards, Request } from '@nestjs/common'
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger'
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard'
import { RolesGuard } from '../../auth/guards/roles.guard'
import { Roles } from '../../auth/decorators/roles.decorator'
import { LessonService } from '../services/lesson.service'
import { CreateLessonDto, UpdateLessonDto, LessonQueryDto } from '../dto/lesson.dto'

@ApiTags('教育-课时管理')
@Controller('education/lessons')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class LessonController {
  constructor(private readonly lessonService: LessonService) {}

  @Post()
  @Roles('teacher', 'admin')
  @ApiOperation({ summary: '创建课时' })
  @ApiResponse({ status: 201, description: '课时创建成功' })
  async create(@Body() createLessonDto: CreateLessonDto, @Request() req) {
    return this.lessonService.create(createLessonDto, req.user.id)
  }

  @Get()
  @ApiOperation({ summary: '获取课时列表' })
  @ApiResponse({ status: 200, description: '课时列表获取成功' })
  async findAll(@Query() query: LessonQueryDto) {
    return this.lessonService.findAll(query)
  }

  @Get(':id')
  @ApiOperation({ summary: '获取课时详情' })
  @ApiResponse({ status: 200, description: '课时详情获取成功' })
  async findOne(@Param('id') id: string, @Request() req) {
    return this.lessonService.findOne(id, req.user.id)
  }

  @Put(':id')
  @Roles('teacher', 'admin')
  @ApiOperation({ summary: '更新课时' })
  @ApiResponse({ status: 200, description: '课时更新成功' })
  async update(@Param('id') id: string, @Body() updateLessonDto: UpdateLessonDto, @Request() req) {
    return this.lessonService.update(id, updateLessonDto, req.user.id)
  }

  @Delete(':id')
  @Roles('teacher', 'admin')
  @ApiOperation({ summary: '删除课时' })
  @ApiResponse({ status: 200, description: '课时删除成功' })
  async remove(@Param('id') id: string, @Request() req) {
    return this.lessonService.remove(id, req.user.id)
  }

  @Post(':id/publish')
  @Roles('teacher', 'admin')
  @ApiOperation({ summary: '发布课时' })
  @ApiResponse({ status: 200, description: '课时发布成功' })
  async publish(@Param('id') id: string, @Request() req) {
    return this.lessonService.publish(id, req.user.id)
  }

  @Post(':id/archive')
  @Roles('teacher', 'admin')
  @ApiOperation({ summary: '归档课时' })
  @ApiResponse({ status: 200, description: '课时归档成功' })
  async archive(@Param('id') id: string, @Request() req) {
    return this.lessonService.archive(id, req.user.id)
  }

  @Get(':id/progress')
  @ApiOperation({ summary: '获取课时学习进度' })
  @ApiResponse({ status: 200, description: '学习进度获取成功' })
  async getProgress(@Param('id') id: string, @Request() req) {
    return this.lessonService.getProgress(id, req.user.id)
  }

  @Post(':id/complete')
  @Roles('student')
  @ApiOperation({ summary: '标记课时完成' })
  @ApiResponse({ status: 200, description: '课时完成标记成功' })
  async markComplete(@Param('id') id: string, @Request() req) {
    return this.lessonService.markComplete(id, req.user.id)
  }
}
