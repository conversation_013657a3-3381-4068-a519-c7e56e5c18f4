import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, Index } from 'typeorm'

@Entity('achievements')
@Index(['type'])
@Index(['category'])
@Index(['studentId'])
export class Achievement {
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Column({ length: 200 })
  title: string

  @Column({ type: 'text', nullable: true })
  description: string

  @Column({ 
    type: 'enum',
    enum: ['course_completion', 'perfect_score', 'streak', 'milestone', 'skill_mastery', 'collaboration', 'innovation'],
    default: 'course_completion'
  })
  type: string

  @Column({ length: 100, nullable: true })
  category: string

  @Column({ type: 'text', nullable: true })
  iconUrl: string

  @Column({ type: 'text', nullable: true })
  badgeUrl: string

  @Column({ type: 'int', default: 1 })
  points: number // 积分奖励

  @Column({ 
    type: 'enum',
    enum: ['bronze', 'silver', 'gold', 'platinum'],
    default: 'bronze'
  })
  level: string

  @Column({ type: 'json', nullable: true })
  criteria: any // 获得条件

  @Column({ type: 'json', nullable: true })
  metadata: any

  @Column({ type: 'datetime' })
  earnedAt: Date

  // 关联关系
  @Column({ name: 'student_id' })
  studentId: string

  @Column({ name: 'course_id', nullable: true })
  courseId: string

  @Column({ name: 'assignment_id', nullable: true })
  assignmentId: string

  @Column({ name: 'assessment_id', nullable: true })
  assessmentId: string

  @CreateDateColumn()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date
}
