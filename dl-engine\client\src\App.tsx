import React, { Suspense, useEffect } from 'react'
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import { ConfigProvider, App as AntdApp, theme } from 'antd'
import { HelmetProvider } from 'react-helmet-async'
import { ErrorBoundary } from 'react-error-boundary'
import zhCN from 'antd/locale/zh_CN'
import enUS from 'antd/locale/en_US'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
import 'dayjs/locale/en'

// 导入样式
import './styles/index.css'
import './styles/antd-overrides.css'
import './styles/three-overrides.css'

// 导入组件
import { LoadingScreen } from '@components/common/LoadingScreen'
import { ErrorFallback } from '@components/common/ErrorFallback'
import { Layout } from '@components/layout/Layout'
import { AuthGuard } from '@components/auth/AuthGuard'
import { PWAPrompt } from '@components/pwa/PWAPrompt'
import { NetworkStatus } from '@components/common/NetworkStatus'

// 导入页面组件（懒加载）
const HomePage = React.lazy(() => import('@pages/Home'))
const LoginPage = React.lazy(() => import('@pages/auth/Login'))
const RegisterPage = React.lazy(() => import('@pages/auth/Register'))
const DashboardPage = React.lazy(() => import('@pages/Dashboard'))
const EditorPage = React.lazy(() => import('@pages/editor/Editor'))
const ScenePage = React.lazy(() => import('@pages/scene/Scene'))
const CoursePage = React.lazy(() => import('@pages/course/Course'))
const ProfilePage = React.lazy(() => import('@pages/profile/Profile'))
const SettingsPage = React.lazy(() => import('@pages/settings/Settings'))
const NotFoundPage = React.lazy(() => import('@pages/NotFound'))

// 导入hooks和stores
import { useAppStore } from '@stores/app'
import { useAuthStore } from '@stores/auth'
import { useI18n } from '@hooks/useI18n'
import { useTheme } from '@hooks/useTheme'
import { usePWA } from '@hooks/usePWA'

// 导入服务
import { initializeServices } from '@services/index'

// 应用主组件
const App: React.FC = () => {
  const { locale, t } = useI18n()
  const { isDarkMode } = useTheme()
  const { isAuthenticated, user, initialize: initAuth } = useAuthStore()
  const { initialize: initApp, isInitialized } = useAppStore()
  const { isInstallable, showInstallPrompt } = usePWA()

  // 设置dayjs语言
  useEffect(() => {
    dayjs.locale(locale === 'zh-CN' ? 'zh-cn' : 'en')
  }, [locale])

  // 初始化应用
  useEffect(() => {
    const init = async () => {
      try {
        // 初始化服务
        await initializeServices()
        
        // 初始化认证
        await initAuth()
        
        // 初始化应用状态
        await initApp()
      } catch (error) {
        console.error('应用初始化失败:', error)
      }
    }

    init()
  }, [initAuth, initApp])

  // Ant Design主题配置
  const antdTheme = {
    algorithm: isDarkMode ? theme.darkAlgorithm : theme.defaultAlgorithm,
    token: {
      colorPrimary: '#1890ff',
      colorSuccess: '#52c41a',
      colorWarning: '#faad14',
      colorError: '#f5222d',
      borderRadius: 6,
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"'
    },
    components: {
      Layout: {
        headerBg: isDarkMode ? '#001529' : '#ffffff',
        siderBg: isDarkMode ? '#001529' : '#ffffff'
      },
      Menu: {
        darkItemBg: '#001529',
        darkSubMenuItemBg: '#000c17'
      }
    }
  }

  // 如果应用未初始化，显示加载屏幕
  if (!isInitialized) {
    return <LoadingScreen message={t('common.initializing')} />
  }

  return (
    <HelmetProvider>
      <ConfigProvider
        locale={locale === 'zh-CN' ? zhCN : enUS}
        theme={antdTheme}
        componentSize="middle"
      >
        <AntdApp>
          <ErrorBoundary
            FallbackComponent={ErrorFallback}
            onError={(error, errorInfo) => {
              console.error('应用错误:', error, errorInfo)
              // 这里可以添加错误上报逻辑
            }}
          >
            <Router>
              <div className="app" data-theme={isDarkMode ? 'dark' : 'light'}>
                {/* 网络状态指示器 */}
                <NetworkStatus />
                
                {/* PWA安装提示 */}
                {isInstallable && <PWAPrompt onInstall={showInstallPrompt} />}
                
                {/* 路由配置 */}
                <Routes>
                  {/* 公开路由 */}
                  <Route
                    path="/"
                    element={
                      <Suspense fallback={<LoadingScreen />}>
                        <HomePage />
                      </Suspense>
                    }
                  />
                  
                  <Route
                    path="/login"
                    element={
                      <Suspense fallback={<LoadingScreen />}>
                        <LoginPage />
                      </Suspense>
                    }
                  />
                  
                  <Route
                    path="/register"
                    element={
                      <Suspense fallback={<LoadingScreen />}>
                        <RegisterPage />
                      </Suspense>
                    }
                  />
                  
                  {/* 受保护的路由 */}
                  <Route
                    path="/app"
                    element={
                      <AuthGuard>
                        <Layout />
                      </AuthGuard>
                    }
                  >
                    <Route
                      index
                      element={
                        <Suspense fallback={<LoadingScreen />}>
                          <DashboardPage />
                        </Suspense>
                      }
                    />
                    
                    <Route
                      path="editor/:projectId?"
                      element={
                        <Suspense fallback={<LoadingScreen />}>
                          <EditorPage />
                        </Suspense>
                      }
                    />
                    
                    <Route
                      path="scene/:sceneId"
                      element={
                        <Suspense fallback={<LoadingScreen />}>
                          <ScenePage />
                        </Suspense>
                      }
                    />
                    
                    <Route
                      path="course/:courseId?"
                      element={
                        <Suspense fallback={<LoadingScreen />}>
                          <CoursePage />
                        </Suspense>
                      }
                    />
                    
                    <Route
                      path="profile"
                      element={
                        <Suspense fallback={<LoadingScreen />}>
                          <ProfilePage />
                        </Suspense>
                      }
                    />
                    
                    <Route
                      path="settings"
                      element={
                        <Suspense fallback={<LoadingScreen />}>
                          <SettingsPage />
                        </Suspense>
                      }
                    />
                  </Route>
                  
                  {/* 重定向规则 */}
                  <Route
                    path="/dashboard"
                    element={<Navigate to="/app" replace />}
                  />
                  
                  {/* 404页面 */}
                  <Route
                    path="*"
                    element={
                      <Suspense fallback={<LoadingScreen />}>
                        <NotFoundPage />
                      </Suspense>
                    }
                  />
                </Routes>
              </div>
            </Router>
          </ErrorBoundary>
        </AntdApp>
      </ConfigProvider>
    </HelmetProvider>
  )
}

export default App
