import { Injectable, Logger } from '@nestjs/common';
import { RedisService } from './redis.service';

export interface CacheOptions {
  ttl?: number; // 过期时间（秒）
  prefix?: string; // 键前缀
  compress?: boolean; // 是否压缩
  tags?: string[]; // 缓存标签
}

export interface CacheStats {
  hits: number;
  misses: number;
  hitRate: number;
  totalKeys: number;
  memoryUsage: number;
}

@Injectable()
export class CacheService {
  private readonly logger = new Logger(CacheService.name);
  private readonly defaultTTL = 3600; // 1小时
  private readonly defaultPrefix = 'dl:cache:';
  
  // 统计信息
  private stats = {
    hits: 0,
    misses: 0,
  };

  constructor(private redisService: RedisService) {}

  /**
   * 设置缓存
   */
  async set<T>(key: string, value: T, options: CacheOptions = {}): Promise<void> {
    const {
      ttl = this.defaultTTL,
      prefix = this.defaultPrefix,
      compress = false,
      tags = [],
    } = options;

    const fullKey = `${prefix}${key}`;

    try {
      let serializedValue = JSON.stringify(value);
      
      // 压缩处理
      if (compress && serializedValue.length > 1024) {
        const zlib = require('zlib');
        serializedValue = zlib.gzipSync(serializedValue).toString('base64');
        await this.redisService.hset(fullKey, 'compressed', 'true');
      }

      await this.redisService.set(fullKey, serializedValue, { ttl });

      // 设置标签
      if (tags.length > 0) {
        await this.setTags(fullKey, tags);
      }

      this.logger.debug(`Cache set: ${fullKey}`);
    } catch (error) {
      this.logger.error(`Failed to set cache ${fullKey}:`, error);
      throw error;
    }
  }

  /**
   * 获取缓存
   */
  async get<T>(key: string, options: CacheOptions = {}): Promise<T | null> {
    const { prefix = this.defaultPrefix } = options;
    const fullKey = `${prefix}${key}`;

    try {
      const value = await this.redisService.get<string>(fullKey);
      
      if (value === null) {
        this.stats.misses++;
        return null;
      }

      this.stats.hits++;

      // 检查是否压缩
      const isCompressed = await this.redisService.hget(fullKey, 'compressed');
      
      if (isCompressed === 'true') {
        const zlib = require('zlib');
        const decompressed = zlib.gunzipSync(Buffer.from(value, 'base64')).toString();
        return JSON.parse(decompressed);
      }

      return JSON.parse(value);
    } catch (error) {
      this.logger.error(`Failed to get cache ${fullKey}:`, error);
      this.stats.misses++;
      return null;
    }
  }

  /**
   * 删除缓存
   */
  async del(key: string | string[], options: CacheOptions = {}): Promise<number> {
    const { prefix = this.defaultPrefix } = options;
    const keys = Array.isArray(key) ? key : [key];
    const fullKeys = keys.map(k => `${prefix}${k}`);

    try {
      // 删除标签关联
      for (const fullKey of fullKeys) {
        await this.removeTags(fullKey);
      }

      const result = await this.redisService.del(fullKeys);
      this.logger.debug(`Cache deleted: ${fullKeys.join(', ')}`);
      return result;
    } catch (error) {
      this.logger.error(`Failed to delete cache:`, error);
      throw error;
    }
  }

  /**
   * 检查缓存是否存在
   */
  async exists(key: string, options: CacheOptions = {}): Promise<boolean> {
    const { prefix = this.defaultPrefix } = options;
    const fullKey = `${prefix}${key}`;

    try {
      return await this.redisService.exists(fullKey);
    } catch (error) {
      this.logger.error(`Failed to check cache existence ${fullKey}:`, error);
      return false;
    }
  }

  /**
   * 设置缓存过期时间
   */
  async expire(key: string, seconds: number, options: CacheOptions = {}): Promise<boolean> {
    const { prefix = this.defaultPrefix } = options;
    const fullKey = `${prefix}${key}`;

    try {
      return await this.redisService.expire(fullKey, seconds);
    } catch (error) {
      this.logger.error(`Failed to set cache expiration ${fullKey}:`, error);
      return false;
    }
  }

  /**
   * 获取缓存TTL
   */
  async ttl(key: string, options: CacheOptions = {}): Promise<number> {
    const { prefix = this.defaultPrefix } = options;
    const fullKey = `${prefix}${key}`;

    try {
      return await this.redisService.ttl(fullKey);
    } catch (error) {
      this.logger.error(`Failed to get cache TTL ${fullKey}:`, error);
      return -1;
    }
  }

  /**
   * 缓存装饰器方法
   */
  async remember<T>(
    key: string,
    factory: () => Promise<T>,
    options: CacheOptions = {}
  ): Promise<T> {
    const cached = await this.get<T>(key, options);
    
    if (cached !== null) {
      return cached;
    }

    const value = await factory();
    await this.set(key, value, options);
    
    return value;
  }

  /**
   * 批量获取缓存
   */
  async mget<T>(keys: string[], options: CacheOptions = {}): Promise<(T | null)[]> {
    const { prefix = this.defaultPrefix } = options;
    const fullKeys = keys.map(k => `${prefix}${k}`);

    try {
      const values = await Promise.all(
        fullKeys.map(key => this.redisService.get<string>(key))
      );

      return values.map(value => {
        if (value === null) {
          this.stats.misses++;
          return null;
        }
        
        this.stats.hits++;
        try {
          return JSON.parse(value);
        } catch {
          return value as T;
        }
      });
    } catch (error) {
      this.logger.error('Failed to mget cache:', error);
      return keys.map(() => null);
    }
  }

  /**
   * 批量设置缓存
   */
  async mset<T>(data: Record<string, T>, options: CacheOptions = {}): Promise<void> {
    const { prefix = this.defaultPrefix } = options;

    try {
      const operations = Object.entries(data).map(([key, value]) => ({
        command: 'set',
        args: [`${prefix}${key}`, JSON.stringify(value)],
      }));

      await this.redisService.pipeline(operations);
      
      // 设置过期时间
      if (options.ttl) {
        const expireOps = Object.keys(data).map(key => ({
          command: 'expire',
          args: [`${prefix}${key}`, options.ttl],
        }));
        
        await this.redisService.pipeline(expireOps);
      }
    } catch (error) {
      this.logger.error('Failed to mset cache:', error);
      throw error;
    }
  }

  /**
   * 按标签删除缓存
   */
  async deleteByTag(tag: string): Promise<number> {
    try {
      const tagKey = `${this.defaultPrefix}tags:${tag}`;
      const keys = await this.redisService.smembers<string>(tagKey);
      
      if (keys.length === 0) return 0;

      // 删除缓存键
      const deleted = await this.redisService.del(keys);
      
      // 删除标签集合
      await this.redisService.del(tagKey);
      
      this.logger.debug(`Deleted ${deleted} cache entries by tag: ${tag}`);
      return deleted;
    } catch (error) {
      this.logger.error(`Failed to delete cache by tag ${tag}:`, error);
      return 0;
    }
  }

  /**
   * 按模式删除缓存
   */
  async deleteByPattern(pattern: string): Promise<number> {
    try {
      const keys = await this.redisService.keys(pattern);
      
      if (keys.length === 0) return 0;

      const deleted = await this.redisService.del(keys);
      this.logger.debug(`Deleted ${deleted} cache entries by pattern: ${pattern}`);
      return deleted;
    } catch (error) {
      this.logger.error(`Failed to delete cache by pattern ${pattern}:`, error);
      return 0;
    }
  }

  /**
   * 清空所有缓存
   */
  async flush(): Promise<void> {
    try {
      await this.deleteByPattern(`${this.defaultPrefix}*`);
      this.stats.hits = 0;
      this.stats.misses = 0;
      this.logger.log('Cache flushed');
    } catch (error) {
      this.logger.error('Failed to flush cache:', error);
      throw error;
    }
  }

  /**
   * 获取缓存统计信息
   */
  async getStats(): Promise<CacheStats> {
    try {
      const info = await this.redisService.info('memory');
      const memoryMatch = info.match(/used_memory:(\d+)/);
      const memoryUsage = memoryMatch ? parseInt(memoryMatch[1]) : 0;

      const keys = await this.redisService.keys(`${this.defaultPrefix}*`);
      const totalKeys = keys.length;

      const total = this.stats.hits + this.stats.misses;
      const hitRate = total > 0 ? (this.stats.hits / total) * 100 : 0;

      return {
        hits: this.stats.hits,
        misses: this.stats.misses,
        hitRate: Math.round(hitRate * 100) / 100,
        totalKeys,
        memoryUsage,
      };
    } catch (error) {
      this.logger.error('Failed to get cache stats:', error);
      return {
        hits: this.stats.hits,
        misses: this.stats.misses,
        hitRate: 0,
        totalKeys: 0,
        memoryUsage: 0,
      };
    }
  }

  /**
   * 预热缓存
   */
  async warmup<T>(
    keys: string[],
    factory: (key: string) => Promise<T>,
    options: CacheOptions = {}
  ): Promise<void> {
    this.logger.log(`Warming up cache for ${keys.length} keys`);

    try {
      const operations = keys.map(async (key) => {
        const exists = await this.exists(key, options);
        if (!exists) {
          const value = await factory(key);
          await this.set(key, value, options);
        }
      });

      await Promise.all(operations);
      this.logger.log('Cache warmup completed');
    } catch (error) {
      this.logger.error('Cache warmup failed:', error);
      throw error;
    }
  }

  private async setTags(key: string, tags: string[]): Promise<void> {
    for (const tag of tags) {
      const tagKey = `${this.defaultPrefix}tags:${tag}`;
      await this.redisService.sadd(tagKey, key);
    }
  }

  private async removeTags(key: string): Promise<void> {
    const tagPattern = `${this.defaultPrefix}tags:*`;
    const tagKeys = await this.redisService.keys(tagPattern);
    
    for (const tagKey of tagKeys) {
      await this.redisService.srem(tagKey, key);
    }
  }
}
