import { Injectable, Logger } from '@nestjs/common';
import { Process, Processor } from '@nestjs/bull';
import { Job } from 'bull';
import { MinioService } from '../minio/minio.service';
import * as sharp from 'sharp';

export interface ImageProcessingOptions {
  resize?: {
    width?: number;
    height?: number;
    fit?: 'cover' | 'contain' | 'fill' | 'inside' | 'outside';
    position?: string;
    background?: string;
  };
  crop?: {
    left: number;
    top: number;
    width: number;
    height: number;
  };
  rotate?: number;
  flip?: boolean;
  flop?: boolean;
  blur?: number;
  sharpen?: {
    sigma?: number;
    flat?: number;
    jagged?: number;
  };
  brightness?: number; // -1 to 1
  contrast?: number; // -1 to 1
  saturation?: number; // -1 to 1
  hue?: number; // -180 to 180
  gamma?: number;
  normalize?: boolean;
  grayscale?: boolean;
  sepia?: boolean;
  negate?: boolean;
  threshold?: number; // 0 to 255
  quality?: number; // 1 to 100
  format?: 'jpeg' | 'png' | 'webp' | 'avif' | 'tiff' | 'gif';
  progressive?: boolean;
  lossless?: boolean;
  watermark?: {
    image?: Buffer;
    text?: string;
    position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right' | 'center';
    opacity?: number;
    fontSize?: number;
    fontColor?: string;
  };
  effects?: {
    vintage?: boolean;
    polaroid?: boolean;
    vignette?: boolean;
    emboss?: boolean;
    edge?: boolean;
  };
}

export interface ThumbnailOptions {
  sizes: Array<{
    name: string;
    width: number;
    height: number;
    quality?: number;
    format?: string;
  }>;
  crop?: boolean;
  background?: string;
}

@Injectable()
@Processor('image-processing')
export class ImageProcessingService {
  private readonly logger = new Logger(ImageProcessingService.name);

  constructor(private minioService: MinioService) {}

  @Process('process-image')
  async processImage(job: Job): Promise<void> {
    const { fileId, bucketName, objectName, options } = job.data;
    
    try {
      this.logger.log(`Processing image: ${fileId}`);
      
      // 下载原始图像
      const imageBuffer = await this.minioService.getObject(bucketName, objectName);
      
      // 生成缩略图
      await this.generateThumbnails(imageBuffer, bucketName, fileId);
      
      // 应用图像处理
      if (options.imageProcessing) {
        await this.applyImageProcessing(imageBuffer, bucketName, fileId, options.imageProcessing);
      }
      
      // 提取图像信息
      await this.extractImageInfo(imageBuffer, fileId);
      
      this.logger.log(`Image processing completed: ${fileId}`);
    } catch (error) {
      this.logger.error(`Image processing failed: ${fileId}`, error);
      throw error;
    }
  }

  /**
   * 生成缩略图
   */
  async generateThumbnails(
    imageBuffer: Buffer,
    bucketName: string,
    fileId: string,
    options?: ThumbnailOptions
  ): Promise<string[]> {
    const defaultSizes = [
      { name: 'small', width: 150, height: 150, quality: 80 },
      { name: 'medium', width: 300, height: 300, quality: 85 },
      { name: 'large', width: 600, height: 600, quality: 90 },
    ];

    const sizes = options?.sizes || defaultSizes;
    const thumbnailUrls: string[] = [];

    try {
      for (const size of sizes) {
        let pipeline = sharp(imageBuffer)
          .resize(size.width, size.height, {
            fit: options?.crop ? 'cover' : 'inside',
            background: options?.background || { r: 255, g: 255, b: 255, alpha: 1 },
          });

        // 设置格式和质量
        const format = size.format || 'jpeg';
        switch (format) {
          case 'jpeg':
            pipeline = pipeline.jpeg({ quality: size.quality || 85, progressive: true });
            break;
          case 'png':
            pipeline = pipeline.png({ quality: size.quality || 85 });
            break;
          case 'webp':
            pipeline = pipeline.webp({ quality: size.quality || 85 });
            break;
          case 'avif':
            pipeline = pipeline.avif({ quality: size.quality || 85 });
            break;
        }

        const thumbnailBuffer = await pipeline.toBuffer();
        
        // 上传缩略图
        const thumbnailObjectName = `thumbnails/${fileId}/${size.name}.${format}`;
        await this.minioService.putObject(bucketName, thumbnailObjectName, thumbnailBuffer);
        
        const thumbnailUrl = await this.minioService.getPresignedUrl('GET', bucketName, thumbnailObjectName);
        thumbnailUrls.push(thumbnailUrl);
        
        this.logger.debug(`Thumbnail generated: ${size.name} for ${fileId}`);
      }

      return thumbnailUrls;
    } catch (error) {
      this.logger.error(`Failed to generate thumbnails for ${fileId}:`, error);
      throw error;
    }
  }

  /**
   * 应用图像处理
   */
  async applyImageProcessing(
    imageBuffer: Buffer,
    bucketName: string,
    fileId: string,
    options: ImageProcessingOptions
  ): Promise<string> {
    try {
      let pipeline = sharp(imageBuffer);

      // 调整大小
      if (options.resize) {
        pipeline = pipeline.resize(options.resize.width, options.resize.height, {
          fit: options.resize.fit || 'cover',
          position: options.resize.position as any,
          background: options.resize.background,
        });
      }

      // 裁剪
      if (options.crop) {
        pipeline = pipeline.extract({
          left: options.crop.left,
          top: options.crop.top,
          width: options.crop.width,
          height: options.crop.height,
        });
      }

      // 旋转
      if (options.rotate) {
        pipeline = pipeline.rotate(options.rotate);
      }

      // 翻转
      if (options.flip) {
        pipeline = pipeline.flip();
      }
      if (options.flop) {
        pipeline = pipeline.flop();
      }

      // 模糊
      if (options.blur) {
        pipeline = pipeline.blur(options.blur);
      }

      // 锐化
      if (options.sharpen) {
        pipeline = pipeline.sharpen(options.sharpen);
      }

      // 颜色调整
      if (options.brightness !== undefined || options.contrast !== undefined || 
          options.saturation !== undefined || options.hue !== undefined) {
        pipeline = pipeline.modulate({
          brightness: options.brightness ? 1 + options.brightness : undefined,
          saturation: options.saturation ? 1 + options.saturation : undefined,
          hue: options.hue,
        });
      }

      // Gamma校正
      if (options.gamma) {
        pipeline = pipeline.gamma(options.gamma);
      }

      // 标准化
      if (options.normalize) {
        pipeline = pipeline.normalize();
      }

      // 灰度
      if (options.grayscale) {
        pipeline = pipeline.grayscale();
      }

      // 棕褐色
      if (options.sepia) {
        pipeline = pipeline.tint({ r: 255, g: 240, b: 196 });
      }

      // 反色
      if (options.negate) {
        pipeline = pipeline.negate();
      }

      // 阈值
      if (options.threshold !== undefined) {
        pipeline = pipeline.threshold(options.threshold);
      }

      // 应用特效
      if (options.effects) {
        pipeline = await this.applyEffects(pipeline, options.effects);
      }

      // 添加水印
      if (options.watermark) {
        pipeline = await this.addWatermark(pipeline, options.watermark);
      }

      // 设置输出格式
      const format = options.format || 'jpeg';
      switch (format) {
        case 'jpeg':
          pipeline = pipeline.jpeg({ 
            quality: options.quality || 85, 
            progressive: options.progressive 
          });
          break;
        case 'png':
          pipeline = pipeline.png({ quality: options.quality || 85 });
          break;
        case 'webp':
          pipeline = pipeline.webp({ 
            quality: options.quality || 85,
            lossless: options.lossless 
          });
          break;
        case 'avif':
          pipeline = pipeline.avif({ quality: options.quality || 85 });
          break;
      }

      const processedBuffer = await pipeline.toBuffer();
      
      // 上传处理后的图像
      const processedObjectName = `processed/${fileId}/processed.${format}`;
      await this.minioService.putObject(bucketName, processedObjectName, processedBuffer);
      
      const processedUrl = await this.minioService.getPresignedUrl('GET', bucketName, processedObjectName);
      
      this.logger.debug(`Image processing applied for ${fileId}`);
      return processedUrl;
    } catch (error) {
      this.logger.error(`Failed to apply image processing for ${fileId}:`, error);
      throw error;
    }
  }

  /**
   * 批量调整图像大小
   */
  async batchResize(
    images: Array<{ buffer: Buffer; name: string }>,
    options: {
      width: number;
      height: number;
      quality?: number;
      format?: string;
    }
  ): Promise<Array<{ name: string; buffer: Buffer }>> {
    const results = [];

    for (const image of images) {
      try {
        let pipeline = sharp(image.buffer)
          .resize(options.width, options.height, { fit: 'cover' });

        const format = options.format || 'jpeg';
        switch (format) {
          case 'jpeg':
            pipeline = pipeline.jpeg({ quality: options.quality || 85 });
            break;
          case 'png':
            pipeline = pipeline.png({ quality: options.quality || 85 });
            break;
          case 'webp':
            pipeline = pipeline.webp({ quality: options.quality || 85 });
            break;
        }

        const resizedBuffer = await pipeline.toBuffer();
        results.push({ name: image.name, buffer: resizedBuffer });
      } catch (error) {
        this.logger.error(`Failed to resize image ${image.name}:`, error);
      }
    }

    return results;
  }

  /**
   * 图像格式转换
   */
  async convertFormat(
    imageBuffer: Buffer,
    targetFormat: 'jpeg' | 'png' | 'webp' | 'avif' | 'tiff',
    options?: { quality?: number; lossless?: boolean }
  ): Promise<Buffer> {
    try {
      let pipeline = sharp(imageBuffer);

      switch (targetFormat) {
        case 'jpeg':
          pipeline = pipeline.jpeg({ quality: options?.quality || 85 });
          break;
        case 'png':
          pipeline = pipeline.png({ quality: options?.quality || 85 });
          break;
        case 'webp':
          pipeline = pipeline.webp({ 
            quality: options?.quality || 85,
            lossless: options?.lossless 
          });
          break;
        case 'avif':
          pipeline = pipeline.avif({ quality: options?.quality || 85 });
          break;
        case 'tiff':
          pipeline = pipeline.tiff({ quality: options?.quality || 85 });
          break;
      }

      return await pipeline.toBuffer();
    } catch (error) {
      this.logger.error(`Failed to convert image format to ${targetFormat}:`, error);
      throw error;
    }
  }

  /**
   * 提取图像信息
   */
  async extractImageInfo(imageBuffer: Buffer, fileId: string): Promise<any> {
    try {
      const metadata = await sharp(imageBuffer).metadata();
      
      const imageInfo = {
        format: metadata.format,
        width: metadata.width,
        height: metadata.height,
        channels: metadata.channels,
        depth: metadata.depth,
        density: metadata.density,
        colorSpace: metadata.space,
        hasProfile: metadata.hasProfile,
        hasAlpha: metadata.hasAlpha,
        orientation: metadata.orientation,
        exif: metadata.exif,
        icc: metadata.icc,
        iptc: metadata.iptc,
        xmp: metadata.xmp,
      };

      this.logger.debug(`Image info extracted for ${fileId}:`, imageInfo);
      return imageInfo;
    } catch (error) {
      this.logger.error(`Failed to extract image info for ${fileId}:`, error);
      throw error;
    }
  }

  private async applyEffects(pipeline: sharp.Sharp, effects: any): Promise<sharp.Sharp> {
    // 实现各种图像特效
    if (effects.vintage) {
      pipeline = pipeline
        .modulate({ brightness: 0.9, saturation: 0.8 })
        .tint({ r: 255, g: 240, b: 200 });
    }

    if (effects.vignette) {
      // 实现暗角效果
      // 这需要更复杂的实现
    }

    if (effects.emboss) {
      pipeline = pipeline.convolve({
        width: 3,
        height: 3,
        kernel: [-2, -1, 0, -1, 1, 1, 0, 1, 2],
      });
    }

    return pipeline;
  }

  private async addWatermark(pipeline: sharp.Sharp, watermark: any): Promise<sharp.Sharp> {
    if (watermark.image) {
      // 添加图像水印
      pipeline = pipeline.composite([{
        input: watermark.image,
        gravity: this.getGravity(watermark.position),
        blend: 'over',
      }]);
    } else if (watermark.text) {
      // 添加文字水印
      // 这需要使用SVG或其他方式实现文字渲染
      const textSvg = `
        <svg width="200" height="50">
          <text x="10" y="30" font-family="Arial" font-size="${watermark.fontSize || 16}" 
                fill="${watermark.fontColor || 'white'}" opacity="${watermark.opacity || 0.7}">
            ${watermark.text}
          </text>
        </svg>
      `;
      
      pipeline = pipeline.composite([{
        input: Buffer.from(textSvg),
        gravity: this.getGravity(watermark.position),
        blend: 'over',
      }]);
    }

    return pipeline;
  }

  private getGravity(position: string): any {
    const gravityMap = {
      'top-left': 'northwest',
      'top-right': 'northeast',
      'bottom-left': 'southwest',
      'bottom-right': 'southeast',
      'center': 'center',
    };
    
    return gravityMap[position] || 'southeast';
  }
}
