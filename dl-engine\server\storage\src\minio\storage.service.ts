import { Injectable, Logger } from '@nestjs/common';
import { MinioService } from './minio.service';
import { ConfigService } from '@nestjs/config';

export interface StorageQuota {
  userId: string;
  maxStorage: number; // 字节
  usedStorage: number; // 字节
  maxFiles: number;
  usedFiles: number;
  lastUpdated: Date;
}

export interface BucketPolicy {
  bucketName: string;
  policy: 'private' | 'public-read' | 'public-read-write';
  allowedOrigins?: string[];
  maxFileSize?: number;
  allowedMimeTypes?: string[];
  retentionDays?: number;
}

export interface StorageStats {
  totalBuckets: number;
  totalObjects: number;
  totalSize: number;
  bucketStats: Array<{
    name: string;
    objectCount: number;
    size: number;
    lastModified: Date;
  }>;
}

export interface LifecycleRule {
  id: string;
  status: 'Enabled' | 'Disabled';
  filter?: {
    prefix?: string;
    tags?: Record<string, string>;
  };
  expiration?: {
    days?: number;
    date?: Date;
  };
  transitions?: Array<{
    days: number;
    storageClass: string;
  }>;
}

@Injectable()
export class StorageService {
  private readonly logger = new Logger(StorageService.name);
  private readonly quotaMap = new Map<string, StorageQuota>();

  constructor(
    private minioService: MinioService,
    private configService: ConfigService,
  ) {}

  /**
   * 创建存储桶并设置策略
   */
  async createBucketWithPolicy(
    bucketName: string,
    policy: BucketPolicy,
    region?: string
  ): Promise<void> {
    try {
      // 创建存储桶
      await this.minioService.createBucket(bucketName, region);

      // 设置存储桶策略
      await this.setBucketPolicy(bucketName, policy);

      // 设置生命周期规则
      if (policy.retentionDays) {
        await this.setBucketLifecycle(bucketName, [{
          id: 'auto-delete',
          status: 'Enabled',
          expiration: { days: policy.retentionDays },
        }]);
      }

      this.logger.log(`Bucket created with policy: ${bucketName}`);
    } catch (error) {
      this.logger.error(`Failed to create bucket with policy ${bucketName}:`, error);
      throw error;
    }
  }

  /**
   * 设置存储桶策略
   */
  async setBucketPolicy(bucketName: string, policy: BucketPolicy): Promise<void> {
    try {
      const policyDocument = this.generatePolicyDocument(bucketName, policy);
      await this.minioService.getClient().setBucketPolicy(bucketName, JSON.stringify(policyDocument));
      
      this.logger.debug(`Bucket policy set: ${bucketName}`);
    } catch (error) {
      this.logger.error(`Failed to set bucket policy for ${bucketName}:`, error);
      throw error;
    }
  }

  /**
   * 获取存储桶策略
   */
  async getBucketPolicy(bucketName: string): Promise<any> {
    try {
      const policy = await this.minioService.getClient().getBucketPolicy(bucketName);
      return JSON.parse(policy);
    } catch (error) {
      this.logger.error(`Failed to get bucket policy for ${bucketName}:`, error);
      return null;
    }
  }

  /**
   * 设置存储桶生命周期
   */
  async setBucketLifecycle(bucketName: string, rules: LifecycleRule[]): Promise<void> {
    try {
      const lifecycleConfig = {
        Rule: rules.map(rule => ({
          ID: rule.id,
          Status: rule.status,
          Filter: rule.filter ? {
            Prefix: rule.filter.prefix || '',
            Tag: rule.filter.tags ? Object.entries(rule.filter.tags).map(([key, value]) => ({ Key: key, Value: value })) : undefined,
          } : { Prefix: '' },
          Expiration: rule.expiration ? {
            Days: rule.expiration.days,
            Date: rule.expiration.date?.toISOString(),
          } : undefined,
          Transition: rule.transitions?.map(t => ({
            Days: t.days,
            StorageClass: t.storageClass,
          })),
        })),
      };

      await this.minioService.getClient().setBucketLifecycle(bucketName, lifecycleConfig);
      this.logger.debug(`Bucket lifecycle set: ${bucketName}`);
    } catch (error) {
      this.logger.error(`Failed to set bucket lifecycle for ${bucketName}:`, error);
      throw error;
    }
  }

  /**
   * 获取存储统计信息
   */
  async getStorageStats(): Promise<StorageStats> {
    try {
      const buckets = await this.minioService.listBuckets();
      const bucketStats = [];
      let totalObjects = 0;
      let totalSize = 0;

      for (const bucket of buckets) {
        const objects = await this.minioService.listObjects(bucket.name, '', true);
        const bucketSize = objects.reduce((sum, obj) => sum + obj.size, 0);
        const lastModified = objects.length > 0 
          ? new Date(Math.max(...objects.map(obj => obj.lastModified.getTime())))
          : bucket.creationDate;

        bucketStats.push({
          name: bucket.name,
          objectCount: objects.length,
          size: bucketSize,
          lastModified,
        });

        totalObjects += objects.length;
        totalSize += bucketSize;
      }

      return {
        totalBuckets: buckets.length,
        totalObjects,
        totalSize,
        bucketStats,
      };
    } catch (error) {
      this.logger.error('Failed to get storage stats:', error);
      throw error;
    }
  }

  /**
   * 设置用户存储配额
   */
  setUserQuota(userId: string, quota: Omit<StorageQuota, 'userId' | 'lastUpdated'>): void {
    this.quotaMap.set(userId, {
      userId,
      ...quota,
      lastUpdated: new Date(),
    });
    
    this.logger.debug(`Storage quota set for user ${userId}: ${quota.maxStorage} bytes`);
  }

  /**
   * 检查用户存储配额
   */
  async checkUserQuota(userId: string, additionalSize: number = 0): Promise<{
    withinQuota: boolean;
    quota: StorageQuota | null;
    reason?: string;
  }> {
    const quota = this.quotaMap.get(userId);
    
    if (!quota) {
      return { withinQuota: true, quota: null };
    }

    // 检查存储空间
    if (quota.usedStorage + additionalSize > quota.maxStorage) {
      return {
        withinQuota: false,
        quota,
        reason: 'Storage quota exceeded',
      };
    }

    // 检查文件数量
    if (quota.usedFiles >= quota.maxFiles) {
      return {
        withinQuota: false,
        quota,
        reason: 'File count quota exceeded',
      };
    }

    return { withinQuota: true, quota };
  }

  /**
   * 更新用户存储使用量
   */
  updateUserUsage(userId: string, sizeChange: number, fileCountChange: number = 0): void {
    const quota = this.quotaMap.get(userId);
    if (quota) {
      quota.usedStorage = Math.max(0, quota.usedStorage + sizeChange);
      quota.usedFiles = Math.max(0, quota.usedFiles + fileCountChange);
      quota.lastUpdated = new Date();
      
      this.logger.debug(`Updated storage usage for user ${userId}: ${quota.usedStorage}/${quota.maxStorage} bytes`);
    }
  }

  /**
   * 清理过期对象
   */
  async cleanupExpiredObjects(bucketName: string, retentionDays: number): Promise<number> {
    try {
      const objects = await this.minioService.listObjects(bucketName, '', true);
      const cutoffDate = new Date(Date.now() - retentionDays * 24 * 60 * 60 * 1000);
      
      const expiredObjects = objects.filter(obj => obj.lastModified < cutoffDate);
      
      if (expiredObjects.length > 0) {
        const objectNames = expiredObjects.map(obj => obj.name);
        await this.minioService.removeObjects(bucketName, objectNames);
        
        this.logger.log(`Cleaned up ${expiredObjects.length} expired objects from ${bucketName}`);
      }

      return expiredObjects.length;
    } catch (error) {
      this.logger.error(`Failed to cleanup expired objects in ${bucketName}:`, error);
      return 0;
    }
  }

  /**
   * 备份存储桶
   */
  async backupBucket(sourceBucket: string, targetBucket: string): Promise<number> {
    try {
      // 确保目标存储桶存在
      await this.minioService.createBucket(targetBucket);

      const objects = await this.minioService.listObjects(sourceBucket, '', true);
      let copiedCount = 0;

      for (const obj of objects) {
        try {
          await this.minioService.copyObject(
            targetBucket,
            obj.name,
            `/${sourceBucket}/${obj.name}`
          );
          copiedCount++;
        } catch (error) {
          this.logger.error(`Failed to copy object ${obj.name}:`, error);
        }
      }

      this.logger.log(`Backup completed: ${copiedCount}/${objects.length} objects copied from ${sourceBucket} to ${targetBucket}`);
      return copiedCount;
    } catch (error) {
      this.logger.error(`Failed to backup bucket ${sourceBucket}:`, error);
      throw error;
    }
  }

  /**
   * 同步存储桶
   */
  async syncBuckets(sourceBucket: string, targetBucket: string): Promise<{
    copied: number;
    updated: number;
    deleted: number;
  }> {
    try {
      const [sourceObjects, targetObjects] = await Promise.all([
        this.minioService.listObjects(sourceBucket, '', true),
        this.minioService.listObjects(targetBucket, '', true),
      ]);

      const sourceMap = new Map(sourceObjects.map(obj => [obj.name, obj]));
      const targetMap = new Map(targetObjects.map(obj => [obj.name, obj]));

      let copied = 0;
      let updated = 0;
      let deleted = 0;

      // 复制新文件和更新修改的文件
      for (const [name, sourceObj] of sourceMap) {
        const targetObj = targetMap.get(name);
        
        if (!targetObj) {
          // 新文件，需要复制
          await this.minioService.copyObject(targetBucket, name, `/${sourceBucket}/${name}`);
          copied++;
        } else if (sourceObj.etag !== targetObj.etag) {
          // 文件已修改，需要更新
          await this.minioService.copyObject(targetBucket, name, `/${sourceBucket}/${name}`);
          updated++;
        }
      }

      // 删除目标中不存在于源的文件
      for (const [name] of targetMap) {
        if (!sourceMap.has(name)) {
          await this.minioService.removeObject(targetBucket, name);
          deleted++;
        }
      }

      this.logger.log(`Sync completed: ${copied} copied, ${updated} updated, ${deleted} deleted`);
      return { copied, updated, deleted };
    } catch (error) {
      this.logger.error(`Failed to sync buckets ${sourceBucket} -> ${targetBucket}:`, error);
      throw error;
    }
  }

  /**
   * 获取存储桶使用情况
   */
  async getBucketUsage(bucketName: string): Promise<{
    objectCount: number;
    totalSize: number;
    sizeByType: Record<string, number>;
    lastModified: Date;
  }> {
    try {
      const objects = await this.minioService.listObjects(bucketName, '', true);
      
      const sizeByType: Record<string, number> = {};
      let totalSize = 0;
      let lastModified = new Date(0);

      for (const obj of objects) {
        totalSize += obj.size;
        
        if (obj.lastModified > lastModified) {
          lastModified = obj.lastModified;
        }

        // 根据文件扩展名分类
        const ext = obj.name.split('.').pop()?.toLowerCase() || 'unknown';
        sizeByType[ext] = (sizeByType[ext] || 0) + obj.size;
      }

      return {
        objectCount: objects.length,
        totalSize,
        sizeByType,
        lastModified,
      };
    } catch (error) {
      this.logger.error(`Failed to get bucket usage for ${bucketName}:`, error);
      throw error;
    }
  }

  private generatePolicyDocument(bucketName: string, policy: BucketPolicy): any {
    const statements = [];

    if (policy.policy === 'public-read') {
      statements.push({
        Effect: 'Allow',
        Principal: '*',
        Action: ['s3:GetObject'],
        Resource: [`arn:aws:s3:::${bucketName}/*`],
      });
    } else if (policy.policy === 'public-read-write') {
      statements.push({
        Effect: 'Allow',
        Principal: '*',
        Action: ['s3:GetObject', 's3:PutObject', 's3:DeleteObject'],
        Resource: [`arn:aws:s3:::${bucketName}/*`],
      });
    }

    return {
      Version: '2012-10-17',
      Statement: statements,
    };
  }
}
