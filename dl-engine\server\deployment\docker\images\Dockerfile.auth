# DL-Engine 认证服务 Docker镜像
# 支持手机号登录的中文优先认证服务

# 多阶段构建 - 构建阶段
FROM node:22-alpine AS builder

# 设置工作目录
WORKDIR /app

# 安装pnpm
RUN npm install -g pnpm

# 复制package文件
COPY package.json pnpm-lock.yaml pnpm-workspace.yaml ./
COPY dl-engine/package.json ./dl-engine/
COPY dl-engine/server/package.json ./dl-engine/server/
COPY dl-engine/server/auth/package.json ./dl-engine/server/auth/
COPY dl-engine/shared/package.json ./dl-engine/shared/

# 安装依赖
RUN pnpm install --frozen-lockfile

# 复制源代码
COPY dl-engine/shared ./dl-engine/shared
COPY dl-engine/server/auth ./dl-engine/server/auth
COPY tsconfig.json ./
COPY dl-engine/tsconfig.json ./dl-engine/

# 构建应用
RUN pnpm --filter @dl-engine/auth build

# 生产阶段
FROM node:22-alpine AS production

# 创建非root用户
RUN addgroup -g 1001 -S nodejs && \
    adduser -S dlengine -u 1001

# 安装必要的系统包
RUN apk add --no-cache \
    dumb-init \
    curl \
    ca-certificates \
    openssl

# 设置工作目录
WORKDIR /app

# 安装pnpm
RUN npm install -g pnpm

# 复制package文件
COPY --chown=dlengine:nodejs package.json pnpm-lock.yaml pnpm-workspace.yaml ./
COPY --chown=dlengine:nodejs dl-engine/package.json ./dl-engine/
COPY --chown=dlengine:nodejs dl-engine/server/package.json ./dl-engine/server/
COPY --chown=dlengine:nodejs dl-engine/server/auth/package.json ./dl-engine/server/auth/
COPY --chown=dlengine:nodejs dl-engine/shared/package.json ./dl-engine/shared/

# 安装生产依赖
RUN pnpm install --frozen-lockfile --prod

# 复制构建产物
COPY --from=builder --chown=dlengine:nodejs /app/dl-engine/server/auth/dist ./dl-engine/server/auth/dist
COPY --from=builder --chown=dlengine:nodejs /app/dl-engine/shared/dist ./dl-engine/shared/dist

# 复制配置文件和本地化文件
COPY --chown=dlengine:nodejs dl-engine/server/auth/config ./dl-engine/server/auth/config
COPY --chown=dlengine:nodejs dl-engine/server/auth/locales ./dl-engine/server/auth/locales

# 创建必要的目录
RUN mkdir -p /app/logs /app/tmp /app/certs && \
    chown -R dlengine:nodejs /app/logs /app/tmp /app/certs

# 设置环境变量
ENV NODE_ENV=production
ENV PORT=3031
ENV LOG_LEVEL=info
ENV DEFAULT_LOCALE=zh-CN
ENV ENABLE_CHINESE_UI=true
ENV JWT_EXPIRES_IN=7d
ENV REFRESH_TOKEN_EXPIRES_IN=30d
ENV SMS_RATE_LIMIT=5
ENV SMS_RATE_WINDOW=300000

# 暴露端口
EXPOSE 3031

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3031/health || exit 1

# 切换到非root用户
USER dlengine

# 启动应用
ENTRYPOINT ["dumb-init", "--"]
CMD ["node", "dl-engine/server/auth/dist/index.js"]

# 标签信息
LABEL maintainer="DL-Engine Team <<EMAIL>>"
LABEL version="1.0.0"
LABEL description="DL-Engine Authentication Service with Chinese Phone Login"
LABEL org.opencontainers.image.title="DL-Engine Auth"
LABEL org.opencontainers.image.description="Digital Learning Engine Authentication Service"
LABEL org.opencontainers.image.version="1.0.0"
LABEL org.opencontainers.image.vendor="DL-Engine"
LABEL org.opencontainers.image.licenses="MIT"
LABEL org.opencontainers.image.source="https://github.com/dl-engine/dl-engine"
