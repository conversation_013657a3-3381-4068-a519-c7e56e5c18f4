import { Injectable, Logger } from '@nestjs/common';
import { Process, Processor } from '@nestjs/bull';
import { Job } from 'bull';
import { MinioService } from '../minio/minio.service';
import * as ffmpeg from 'fluent-ffmpeg';
import * as fs from 'fs/promises';
import * as path from 'path';
import * as os from 'os';

export interface AudioProcessingOptions {
  transcode?: {
    codec: 'aac' | 'mp3' | 'opus' | 'vorbis' | 'flac' | 'wav';
    bitrate?: string; // '128k', '320k'
    sampleRate?: number; // 44100, 48000
    channels?: number; // 1, 2
    quality?: number; // 0-9 for VBR
  };
  trim?: {
    start: number; // 秒
    duration?: number; // 秒
    end?: number; // 秒
  };
  volume?: {
    gain: number; // dB, 正数增大音量，负数减小
    normalize?: boolean; // 标准化音量
    limiter?: boolean; // 限制器防止削波
  };
  filters?: {
    highpass?: number; // 高通滤波器频率 (Hz)
    lowpass?: number; // 低通滤波器频率 (Hz)
    bandpass?: { low: number; high: number }; // 带通滤波器
    equalizer?: Array<{ frequency: number; gain: number; q?: number }>; // 均衡器
    compressor?: {
      threshold: number; // dB
      ratio: number; // 压缩比
      attack: number; // ms
      release: number; // ms
    };
    reverb?: {
      roomSize: number; // 0-1
      damping: number; // 0-1
      wetLevel: number; // 0-1
      dryLevel: number; // 0-1
    };
    echo?: {
      delay: number; // ms
      decay: number; // 0-1
    };
    chorus?: {
      rate: number; // Hz
      depth: number; // 0-1
      delay: number; // ms
    };
    noiseReduction?: {
      strength: number; // 0-1
      sensitivity: number; // 0-1
    };
  };
  fade?: {
    in?: number; // 淡入时长（秒）
    out?: number; // 淡出时长（秒）
  };
  speed?: {
    factor: number; // 速度倍数，1.0为原速
    preservePitch?: boolean; // 保持音调
  };
  pitch?: {
    semitones: number; // 半音数，正数升调，负数降调
  };
}

export interface AudioAnalysisResult {
  duration: number;
  sampleRate: number;
  channels: number;
  bitrate: number;
  format: string;
  codec: string;
  loudness: {
    peak: number; // dB
    rms: number; // dB
    lufs: number; // LUFS
  };
  spectrum: {
    frequencies: number[];
    magnitudes: number[];
  };
  tempo?: number; // BPM
  key?: string; // 音调
  silence: Array<{
    start: number;
    end: number;
    duration: number;
  }>;
}

@Injectable()
@Processor('audio-processing')
export class AudioProcessingService {
  private readonly logger = new Logger(AudioProcessingService.name);
  private readonly tempDir: string;

  constructor(private minioService: MinioService) {
    this.tempDir = path.join(os.tmpdir(), 'audio-processing');
    this.ensureTempDirectory();
  }

  @Process('process-audio')
  async processAudio(job: Job): Promise<void> {
    const { fileId, bucketName, objectName, options } = job.data;
    
    try {
      this.logger.log(`Processing audio: ${fileId}`);
      
      // 下载原始音频
      const audioBuffer = await this.minioService.getObject(bucketName, objectName);
      const inputPath = path.join(this.tempDir, `${fileId}_input.mp3`);
      await fs.writeFile(inputPath, audioBuffer);
      
      // 生成波形图
      await this.generateWaveform(inputPath, bucketName, fileId);
      
      // 音频分析
      await this.analyzeAudio(inputPath, fileId);
      
      // 音频处理
      if (options.audioProcessing) {
        await this.transcodeAudio(inputPath, bucketName, fileId, options.audioProcessing);
      }
      
      // 清理临时文件
      await this.cleanupTempFile(inputPath);
      
      this.logger.log(`Audio processing completed: ${fileId}`);
    } catch (error) {
      this.logger.error(`Audio processing failed: ${fileId}`, error);
      throw error;
    }
  }

  /**
   * 音频转码
   */
  async transcodeAudio(
    inputPath: string,
    bucketName: string,
    fileId: string,
    options: AudioProcessingOptions
  ): Promise<string> {
    try {
      const outputPath = path.join(this.tempDir, `${fileId}_transcoded.mp3`);
      
      let command = ffmpeg(inputPath);

      // 音频编码设置
      if (options.transcode) {
        const { codec, bitrate, sampleRate, channels, quality } = options.transcode;
        
        switch (codec) {
          case 'aac':
            command = command.audioCodec('aac');
            break;
          case 'mp3':
            command = command.audioCodec('libmp3lame');
            if (quality) command = command.outputOptions([`-q:a ${quality}`]);
            break;
          case 'opus':
            command = command.audioCodec('libopus');
            break;
          case 'vorbis':
            command = command.audioCodec('libvorbis');
            if (quality) command = command.outputOptions([`-q:a ${quality}`]);
            break;
          case 'flac':
            command = command.audioCodec('flac');
            break;
          case 'wav':
            command = command.audioCodec('pcm_s16le');
            break;
        }
        
        if (bitrate) command = command.audioBitrate(bitrate);
        if (sampleRate) command = command.audioFrequency(sampleRate);
        if (channels) command = command.audioChannels(channels);
      }

      // 裁剪时间
      if (options.trim) {
        if (options.trim.start) command = command.seekInput(options.trim.start);
        if (options.trim.duration) command = command.duration(options.trim.duration);
        if (options.trim.end && !options.trim.duration) {
          command = command.duration(options.trim.end - (options.trim.start || 0));
        }
      }

      // 应用音频滤镜
      const filters = this.buildAudioFilters(options);
      if (filters.length > 0) {
        command = command.audioFilters(filters);
      }

      // 执行转码
      await new Promise<void>((resolve, reject) => {
        command
          .output(outputPath)
          .on('progress', (progress) => {
            this.logger.debug(`Audio transcoding progress: ${progress.percent}% for ${fileId}`);
          })
          .on('end', resolve)
          .on('error', reject)
          .run();
      });

      // 上传转码后的音频
      const transcodedBuffer = await fs.readFile(outputPath);
      const transcodedObjectName = `audio-transcoded/${fileId}/transcoded.mp3`;
      await this.minioService.putObject(bucketName, transcodedObjectName, transcodedBuffer);
      
      const transcodedUrl = await this.minioService.getPresignedUrl('GET', bucketName, transcodedObjectName);
      
      // 清理临时文件
      await this.cleanupTempFile(outputPath);
      
      this.logger.debug(`Audio transcoding completed for ${fileId}`);
      return transcodedUrl;
    } catch (error) {
      this.logger.error(`Failed to transcode audio for ${fileId}:`, error);
      throw error;
    }
  }

  /**
   * 生成波形图
   */
  async generateWaveform(
    inputPath: string,
    bucketName: string,
    fileId: string,
    options?: {
      width?: number;
      height?: number;
      color?: string;
      backgroundColor?: string;
    }
  ): Promise<string> {
    try {
      const width = options?.width || 1200;
      const height = options?.height || 200;
      const color = options?.color || '#3498db';
      const backgroundColor = options?.backgroundColor || '#ffffff';
      
      const waveformPath = path.join(this.tempDir, `${fileId}_waveform.png`);
      
      await new Promise<void>((resolve, reject) => {
        ffmpeg(inputPath)
          .complexFilter([
            `[0:a]showwavespic=s=${width}x${height}:colors=${color}[v]`
          ])
          .outputOptions([
            '-map', '[v]',
            '-frames:v', '1'
          ])
          .output(waveformPath)
          .on('end', resolve)
          .on('error', reject)
          .run();
      });

      // 上传波形图
      const waveformBuffer = await fs.readFile(waveformPath);
      const waveformObjectName = `audio-waveforms/${fileId}/waveform.png`;
      await this.minioService.putObject(bucketName, waveformObjectName, waveformBuffer);
      
      const waveformUrl = await this.minioService.getPresignedUrl('GET', bucketName, waveformObjectName);
      
      // 清理临时文件
      await this.cleanupTempFile(waveformPath);
      
      this.logger.debug(`Waveform generated for ${fileId}`);
      return waveformUrl;
    } catch (error) {
      this.logger.error(`Failed to generate waveform for ${fileId}:`, error);
      throw error;
    }
  }

  /**
   * 音频分析
   */
  async analyzeAudio(inputPath: string, fileId: string): Promise<AudioAnalysisResult> {
    try {
      // 获取基本信息
      const basicInfo = await this.getAudioInfo(inputPath);
      
      // 分析音量
      const loudnessInfo = await this.analyzeLoudness(inputPath);
      
      // 检测静音段
      const silenceInfo = await this.detectSilence(inputPath);
      
      const analysisResult: AudioAnalysisResult = {
        duration: basicInfo.duration,
        sampleRate: basicInfo.sampleRate,
        channels: basicInfo.channels,
        bitrate: basicInfo.bitrate,
        format: basicInfo.format,
        codec: basicInfo.codec,
        loudness: loudnessInfo,
        spectrum: { frequencies: [], magnitudes: [] }, // 需要更复杂的实现
        silence: silenceInfo,
      };

      this.logger.debug(`Audio analysis completed for ${fileId}:`, analysisResult);
      return analysisResult;
    } catch (error) {
      this.logger.error(`Failed to analyze audio for ${fileId}:`, error);
      throw error;
    }
  }

  /**
   * 音频混合
   */
  async mixAudio(
    inputs: Array<{
      path: string;
      volume?: number; // 0-1
      delay?: number; // 秒
      fadeIn?: number; // 秒
      fadeOut?: number; // 秒
    }>,
    outputPath: string,
    options?: {
      duration?: number;
      sampleRate?: number;
      channels?: number;
    }
  ): Promise<void> {
    try {
      let command = ffmpeg();
      
      // 添加输入文件
      inputs.forEach(input => {
        command = command.input(input.path);
      });

      // 构建混合滤镜
      const filterInputs = inputs.map((_, index) => `[${index}:a]`).join('');
      const mixFilter = `${filterInputs}amix=inputs=${inputs.length}:duration=longest[out]`;
      
      command = command
        .complexFilter([mixFilter])
        .outputOptions(['-map', '[out]']);

      if (options?.sampleRate) {
        command = command.audioFrequency(options.sampleRate);
      }
      
      if (options?.channels) {
        command = command.audioChannels(options.channels);
      }

      if (options?.duration) {
        command = command.duration(options.duration);
      }

      await new Promise<void>((resolve, reject) => {
        command
          .output(outputPath)
          .on('end', resolve)
          .on('error', reject)
          .run();
      });

      this.logger.debug('Audio mixing completed');
    } catch (error) {
      this.logger.error('Failed to mix audio:', error);
      throw error;
    }
  }

  /**
   * 音频拼接
   */
  async concatenateAudio(
    inputPaths: string[],
    outputPath: string,
    options?: {
      crossfade?: number; // 交叉淡化时长（秒）
      normalize?: boolean;
    }
  ): Promise<void> {
    try {
      let command = ffmpeg();
      
      // 添加输入文件
      inputPaths.forEach(path => {
        command = command.input(path);
      });

      if (options?.crossfade) {
        // 使用交叉淡化拼接
        const filters = [];
        for (let i = 0; i < inputPaths.length - 1; i++) {
          const fadeTime = options.crossfade;
          filters.push(`[${i}:a][${i + 1}:a]acrossfade=d=${fadeTime}[a${i}]`);
        }
        
        command = command.complexFilter(filters);
      } else {
        // 简单拼接
        const concatFilter = inputPaths.map((_, index) => `[${index}:a]`).join('') + 
                           `concat=n=${inputPaths.length}:v=0:a=1[out]`;
        
        command = command
          .complexFilter([concatFilter])
          .outputOptions(['-map', '[out]']);
      }

      if (options?.normalize) {
        command = command.audioFilters(['loudnorm']);
      }

      await new Promise<void>((resolve, reject) => {
        command
          .output(outputPath)
          .on('end', resolve)
          .on('error', reject)
          .run();
      });

      this.logger.debug('Audio concatenation completed');
    } catch (error) {
      this.logger.error('Failed to concatenate audio:', error);
      throw error;
    }
  }

  private buildAudioFilters(options: AudioProcessingOptions): string[] {
    const filters: string[] = [];

    // 音量调整
    if (options.volume) {
      if (options.volume.gain !== undefined) {
        filters.push(`volume=${options.volume.gain}dB`);
      }
      
      if (options.volume.normalize) {
        filters.push('loudnorm');
      }
      
      if (options.volume.limiter) {
        filters.push('alimiter');
      }
    }

    // 滤波器
    if (options.filters) {
      if (options.filters.highpass) {
        filters.push(`highpass=f=${options.filters.highpass}`);
      }
      
      if (options.filters.lowpass) {
        filters.push(`lowpass=f=${options.filters.lowpass}`);
      }
      
      if (options.filters.bandpass) {
        filters.push(`bandpass=f=${options.filters.bandpass.low}:width_type=h:width=${options.filters.bandpass.high - options.filters.bandpass.low}`);
      }

      // 均衡器
      if (options.filters.equalizer) {
        options.filters.equalizer.forEach(eq => {
          const q = eq.q || 1;
          filters.push(`equalizer=f=${eq.frequency}:width_type=q:width=${q}:g=${eq.gain}`);
        });
      }

      // 压缩器
      if (options.filters.compressor) {
        const comp = options.filters.compressor;
        filters.push(`acompressor=threshold=${comp.threshold}dB:ratio=${comp.ratio}:attack=${comp.attack}:release=${comp.release}`);
      }

      // 回响
      if (options.filters.reverb) {
        const rev = options.filters.reverb;
        filters.push(`afreqshift=shift=0,aecho=0.8:0.9:${rev.roomSize * 1000}:${rev.damping}`);
      }

      // 回声
      if (options.filters.echo) {
        const echo = options.filters.echo;
        filters.push(`aecho=0.8:0.9:${echo.delay}:${echo.decay}`);
      }

      // 降噪
      if (options.filters.noiseReduction) {
        filters.push(`afftdn=nr=${options.filters.noiseReduction.strength}:nf=${options.filters.noiseReduction.sensitivity}`);
      }
    }

    // 淡入淡出
    if (options.fade) {
      if (options.fade.in) {
        filters.push(`afade=t=in:d=${options.fade.in}`);
      }
      if (options.fade.out) {
        filters.push(`afade=t=out:d=${options.fade.out}`);
      }
    }

    // 速度调整
    if (options.speed) {
      if (options.speed.preservePitch) {
        filters.push(`atempo=${options.speed.factor}`);
      } else {
        filters.push(`asetrate=44100*${options.speed.factor},aresample=44100`);
      }
    }

    // 音调调整
    if (options.pitch) {
      const pitchFactor = Math.pow(2, options.pitch.semitones / 12);
      filters.push(`asetrate=44100*${pitchFactor},aresample=44100`);
    }

    return filters;
  }

  private async getAudioInfo(inputPath: string): Promise<any> {
    return new Promise((resolve, reject) => {
      ffmpeg.ffprobe(inputPath, (err, metadata) => {
        if (err) {
          reject(err);
          return;
        }

        const audioStream = metadata.streams.find(s => s.codec_type === 'audio');
        if (!audioStream) {
          reject(new Error('No audio stream found'));
          return;
        }

        resolve({
          duration: metadata.format.duration || 0,
          sampleRate: audioStream.sample_rate || 0,
          channels: audioStream.channels || 0,
          bitrate: parseInt(audioStream.bit_rate) || 0,
          format: metadata.format.format_name || '',
          codec: audioStream.codec_name || '',
        });
      });
    });
  }

  private async analyzeLoudness(inputPath: string): Promise<any> {
    // 实现音量分析
    return {
      peak: 0,
      rms: 0,
      lufs: 0,
    };
  }

  private async detectSilence(inputPath: string): Promise<any[]> {
    // 实现静音检测
    return [];
  }

  private async ensureTempDirectory(): Promise<void> {
    try {
      await fs.access(this.tempDir);
    } catch {
      await fs.mkdir(this.tempDir, { recursive: true });
    }
  }

  private async cleanupTempFile(filePath: string): Promise<void> {
    try {
      await fs.unlink(filePath);
    } catch (error) {
      this.logger.warn(`Failed to cleanup temp file: ${filePath}`, error);
    }
  }
}
