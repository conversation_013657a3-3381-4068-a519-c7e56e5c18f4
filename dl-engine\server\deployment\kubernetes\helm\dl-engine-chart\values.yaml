# DL-Engine Helm Chart 默认配置
# 支持教育场景的数字化学习引擎部署

# 全局配置
global:
  imageRegistry: ""
  imagePullSecrets: []
  storageClass: ""
  
# 镜像配置
image:
  registry: docker.io
  repository: dl-engine
  tag: "1.0.0"
  pullPolicy: IfNotPresent

# 服务配置
services:
  # API网关服务
  gateway:
    enabled: true
    replicaCount: 2
    image:
      repository: dl-engine/gateway
      tag: "1.0.0"
    service:
      type: ClusterIP
      port: 3030
      targetPort: 3030
    resources:
      limits:
        cpu: 1000m
        memory: 1Gi
      requests:
        cpu: 500m
        memory: 512Mi
    autoscaling:
      enabled: true
      minReplicas: 2
      maxReplicas: 10
      targetCPUUtilizationPercentage: 70

  # 认证服务
  auth:
    enabled: true
    replicaCount: 2
    image:
      repository: dl-engine/auth
      tag: "1.0.0"
    service:
      type: ClusterIP
      port: 3031
      targetPort: 3031
    resources:
      limits:
        cpu: 500m
        memory: 512Mi
      requests:
        cpu: 250m
        memory: 256Mi

  # API服务
  api:
    enabled: true
    replicaCount: 3
    image:
      repository: dl-engine/api
      tag: "1.0.0"
    service:
      type: ClusterIP
      port: 3032
      targetPort: 3032
    resources:
      limits:
        cpu: 1000m
        memory: 1Gi
      requests:
        cpu: 500m
        memory: 512Mi
    autoscaling:
      enabled: true
      minReplicas: 3
      maxReplicas: 15
      targetCPUUtilizationPercentage: 70

  # 实例服务
  instance:
    enabled: true
    replicaCount: 2
    image:
      repository: dl-engine/instance
      tag: "1.0.0"
    service:
      type: ClusterIP
      port: 3033
      targetPort: 3033
    resources:
      limits:
        cpu: 2000m
        memory: 2Gi
      requests:
        cpu: 1000m
        memory: 1Gi

  # 媒体服务
  media:
    enabled: true
    replicaCount: 2
    image:
      repository: dl-engine/media
      tag: "1.0.0"
    service:
      type: ClusterIP
      port: 3034
      targetPort: 3034
    resources:
      limits:
        cpu: 1500m
        memory: 2Gi
      requests:
        cpu: 750m
        memory: 1Gi

  # AI服务
  ai:
    enabled: true
    replicaCount: 1
    image:
      repository: dl-engine/ai
      tag: "1.0.0"
    service:
      type: ClusterIP
      port: 3035
      targetPort: 3035
    resources:
      limits:
        cpu: 2000m
        memory: 4Gi
      requests:
        cpu: 1000m
        memory: 2Gi

  # 任务服务
  task:
    enabled: true
    replicaCount: 2
    image:
      repository: dl-engine/task
      tag: "1.0.0"
    service:
      type: ClusterIP
      port: 3036
      targetPort: 3036
    resources:
      limits:
        cpu: 1000m
        memory: 1Gi
      requests:
        cpu: 500m
        memory: 512Mi

# Web客户端
client:
  enabled: true
  replicaCount: 2
  image:
    repository: dl-engine/client
    tag: "1.0.0"
  service:
    type: LoadBalancer
    port: 3000
    targetPort: 3000
  resources:
    limits:
      cpu: 500m
      memory: 512Mi
    requests:
      cpu: 250m
      memory: 256Mi
  autoscaling:
    enabled: true
    minReplicas: 2
    maxReplicas: 8
    targetCPUUtilizationPercentage: 70

# Ingress配置
ingress:
  enabled: true
  className: "nginx"
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
  hosts:
    - host: dl-engine.example.com
      paths:
        - path: /
          pathType: Prefix
          service:
            name: dl-engine-client
            port: 3000
        - path: /api
          pathType: Prefix
          service:
            name: dl-engine-gateway
            port: 3030
  tls:
    - secretName: dl-engine-tls
      hosts:
        - dl-engine.example.com

# 数据库配置
mysql:
  enabled: true
  auth:
    rootPassword: "dl-engine-root-password"
    database: "dl_engine"
    username: "dl_engine"
    password: "dl-engine-password"
  primary:
    persistence:
      enabled: true
      size: 20Gi
    resources:
      limits:
        cpu: 1000m
        memory: 2Gi
      requests:
        cpu: 500m
        memory: 1Gi

redis:
  enabled: true
  auth:
    enabled: true
    password: "dl-engine-redis-password"
  master:
    persistence:
      enabled: true
      size: 8Gi
    resources:
      limits:
        cpu: 500m
        memory: 1Gi
      requests:
        cpu: 250m
        memory: 512Mi

postgresql:
  enabled: true
  auth:
    postgresPassword: "dl-engine-postgres-password"
    database: "dl_engine_vector"
    username: "dl_engine"
    password: "dl-engine-postgres-password"
  primary:
    persistence:
      enabled: true
      size: 20Gi
    resources:
      limits:
        cpu: 1000m
        memory: 2Gi
      requests:
        cpu: 500m
        memory: 1Gi

# 对象存储
minio:
  enabled: true
  auth:
    rootUser: "dl-engine"
    rootPassword: "dl-engine-minio-password"
  persistence:
    enabled: true
    size: 100Gi
  resources:
    limits:
      cpu: 1000m
      memory: 2Gi
    requests:
      cpu: 500m
      memory: 1Gi

# Agones游戏服务器
agones:
  enabled: true
  gameservers:
    namespaces:
      - dl-engine-gameservers
  controller:
    resources:
      limits:
        cpu: 1000m
        memory: 1Gi
      requests:
        cpu: 500m
        memory: 512Mi

# 监控配置
monitoring:
  prometheus:
    enabled: true
    server:
      persistentVolume:
        enabled: true
        size: 20Gi
      resources:
        limits:
          cpu: 1000m
          memory: 2Gi
        requests:
          cpu: 500m
          memory: 1Gi
  
  grafana:
    enabled: true
    persistence:
      enabled: true
      size: 10Gi
    resources:
      limits:
        cpu: 500m
        memory: 1Gi
      requests:
        cpu: 250m
        memory: 512Mi
    adminPassword: "dl-engine-grafana-password"

# 配置映射
configMaps:
  app:
    NODE_ENV: "production"
    LOG_LEVEL: "info"
    MYSQL_HOST: "dl-engine-mysql"
    MYSQL_PORT: "3306"
    MYSQL_DATABASE: "dl_engine"
    REDIS_HOST: "dl-engine-redis-master"
    REDIS_PORT: "6379"
    POSTGRESQL_HOST: "dl-engine-postgresql"
    POSTGRESQL_PORT: "5432"
    MINIO_HOST: "dl-engine-minio"
    MINIO_PORT: "9000"
    AI_SERVICE_URL: "http://dl-engine-ai:3035"
    ENABLE_CHINESE_UI: "true"
    DEFAULT_LOCALE: "zh-CN"

# 密钥配置
secrets:
  app:
    MYSQL_PASSWORD: "dl-engine-password"
    REDIS_PASSWORD: "dl-engine-redis-password"
    POSTGRESQL_PASSWORD: "dl-engine-postgres-password"
    MINIO_ACCESS_KEY: "dl-engine"
    MINIO_SECRET_KEY: "dl-engine-minio-password"
    JWT_SECRET: "dl-engine-jwt-secret-key"
    SMS_API_KEY: "your-sms-api-key"
    OLLAMA_API_KEY: "your-ollama-api-key"

# 网络策略
networkPolicy:
  enabled: true
  ingress:
    - from:
        - namespaceSelector:
            matchLabels:
              name: dl-engine
      ports:
        - protocol: TCP
          port: 3030

# 服务账户
serviceAccount:
  create: true
  annotations: {}
  name: "dl-engine"

# Pod安全策略
podSecurityContext:
  fsGroup: 2000

securityContext:
  capabilities:
    drop:
    - ALL
  readOnlyRootFilesystem: true
  runAsNonRoot: true
  runAsUser: 1000

# 节点选择器
nodeSelector: {}

# 容忍度
tolerations: []

# 亲和性
affinity: {}

# 持久化卷声明
persistence:
  enabled: true
  storageClass: ""
  accessMode: ReadWriteOnce
  size: 10Gi
