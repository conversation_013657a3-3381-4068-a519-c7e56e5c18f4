/**
 * DL-Engine 项目协作管理服务
 * 
 * 核心功能：
 * - 协作者邀请和管理
 * - 实时协作状态
 * - 权限分配和控制
 * - 协作冲突解决
 * - 协作历史记录
 */

import { Injectable, NotFoundException, ConflictException, ForbiddenException } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { Repository, In } from 'typeorm'
import { ConfigService } from '@nestjs/config'

import { Project } from '../entities/project.entity'
import { ProjectPermission, PermissionType } from '../entities/project-permission.entity'
import { ProjectCollaborator } from '../entities/project-collaborator.entity'
import { ProjectInvitation, InvitationStatus } from '../entities/project-invitation.entity'
import { User } from '../../users/entities/user.entity'

@Injectable()
export class ProjectCollaborationService {
  constructor(
    @InjectRepository(Project)
    private readonly projectRepository: Repository<Project>,
    @InjectRepository(ProjectPermission)
    private readonly permissionRepository: Repository<ProjectPermission>,
    @InjectRepository(ProjectCollaborator)
    private readonly collaboratorRepository: Repository<ProjectCollaborator>,
    @InjectRepository(ProjectInvitation)
    private readonly invitationRepository: Repository<ProjectInvitation>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly configService: ConfigService
  ) {}

  /**
   * 邀请协作者
   */
  async inviteCollaborator(
    projectId: string, 
    inviterUserId: string, 
    inviteeEmail: string, 
    permissionType: PermissionType,
    message?: string
  ): Promise<ProjectInvitation> {
    // 检查项目是否存在
    const project = await this.projectRepository.findOne({ where: { id: projectId } })
    if (!project) {
      throw new NotFoundException('项目不存在')
    }

    // 检查邀请者权限
    await this.checkInvitePermission(projectId, inviterUserId)

    // 查找被邀请用户
    const inviteeUser = await this.userRepository.findOne({ where: { email: inviteeEmail } })
    if (!inviteeUser) {
      throw new NotFoundException('被邀请用户不存在')
    }

    // 检查是否已经是协作者
    const existingCollaborator = await this.collaboratorRepository.findOne({
      where: { projectId, userId: inviteeUser.id }
    })

    if (existingCollaborator) {
      throw new ConflictException('用户已经是项目协作者')
    }

    // 检查是否已有待处理的邀请
    const existingInvitation = await this.invitationRepository.findOne({
      where: { 
        projectId, 
        inviteeUserId: inviteeUser.id, 
        status: InvitationStatus.PENDING 
      }
    })

    if (existingInvitation) {
      throw new ConflictException('已有待处理的邀请')
    }

    // 创建邀请
    const invitation = this.invitationRepository.create({
      projectId,
      inviterUserId,
      inviteeUserId: inviteeUser.id,
      inviteeEmail,
      permissionType,
      message,
      status: InvitationStatus.PENDING,
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7天后过期
    })

    const savedInvitation = await this.invitationRepository.save(invitation)

    // 发送邀请通知
    await this.sendInvitationNotification(savedInvitation)

    return savedInvitation
  }

  /**
   * 接受邀请
   */
  async acceptInvitation(invitationId: string, userId: string): Promise<ProjectCollaborator> {
    const invitation = await this.invitationRepository.findOne({
      where: { id: invitationId },
      relations: ['project', 'inviter', 'invitee']
    })

    if (!invitation) {
      throw new NotFoundException('邀请不存在')
    }

    if (invitation.inviteeUserId !== userId) {
      throw new ForbiddenException('无权接受此邀请')
    }

    if (invitation.status !== InvitationStatus.PENDING) {
      throw new ConflictException('邀请已处理')
    }

    if (invitation.expiresAt < new Date()) {
      throw new ConflictException('邀请已过期')
    }

    // 创建协作者记录
    const collaborator = this.collaboratorRepository.create({
      projectId: invitation.projectId,
      userId,
      role: invitation.permissionType,
      joinedAt: new Date(),
      invitedBy: invitation.inviterUserId
    })

    const savedCollaborator = await this.collaboratorRepository.save(collaborator)

    // 创建权限记录
    await this.permissionRepository.save({
      projectId: invitation.projectId,
      userId,
      type: invitation.permissionType,
      grantedBy: invitation.inviterUserId,
      grantedAt: new Date()
    })

    // 更新邀请状态
    invitation.status = InvitationStatus.ACCEPTED
    invitation.respondedAt = new Date()
    await this.invitationRepository.save(invitation)

    // 更新项目协作者统计
    await this.updateCollaboratorStats(invitation.projectId)

    return savedCollaborator
  }

  /**
   * 拒绝邀请
   */
  async rejectInvitation(invitationId: string, userId: string): Promise<void> {
    const invitation = await this.invitationRepository.findOne({
      where: { id: invitationId }
    })

    if (!invitation) {
      throw new NotFoundException('邀请不存在')
    }

    if (invitation.inviteeUserId !== userId) {
      throw new ForbiddenException('无权处理此邀请')
    }

    if (invitation.status !== InvitationStatus.PENDING) {
      throw new ConflictException('邀请已处理')
    }

    // 更新邀请状态
    invitation.status = InvitationStatus.REJECTED
    invitation.respondedAt = new Date()
    await this.invitationRepository.save(invitation)
  }

  /**
   * 获取项目协作者列表
   */
  async getCollaborators(projectId: string, userId: string): Promise<{
    collaborators: any[]
    total: number
    permissions: any
  }> {
    // 检查访问权限
    await this.checkProjectAccess(projectId, userId)

    const collaborators = await this.collaboratorRepository.find({
      where: { projectId },
      relations: ['user', 'user.profile']
    })

    const permissions = await this.permissionRepository.find({
      where: { projectId },
      relations: ['user']
    })

    return {
      collaborators: collaborators.map(c => ({
        id: c.id,
        userId: c.userId,
        user: {
          id: c.user.id,
          username: c.user.username,
          displayName: c.user.displayName,
          avatarUrl: c.user.avatarUrl,
          profile: c.user.profile
        },
        role: c.role,
        joinedAt: c.joinedAt,
        lastActiveAt: c.lastActiveAt,
        isOnline: c.isOnline
      })),
      total: collaborators.length,
      permissions: permissions.reduce((acc, p) => {
        acc[p.userId] = p.type
        return acc
      }, {})
    }
  }

  /**
   * 移除协作者
   */
  async removeCollaborator(
    projectId: string, 
    collaboratorUserId: string, 
    removerUserId: string
  ): Promise<void> {
    // 检查移除权限
    await this.checkRemovePermission(projectId, removerUserId, collaboratorUserId)

    // 删除协作者记录
    await this.collaboratorRepository.delete({
      projectId,
      userId: collaboratorUserId
    })

    // 删除权限记录
    await this.permissionRepository.delete({
      projectId,
      userId: collaboratorUserId
    })

    // 更新项目协作者统计
    await this.updateCollaboratorStats(projectId)
  }

  /**
   * 更新协作者权限
   */
  async updateCollaboratorPermission(
    projectId: string,
    collaboratorUserId: string,
    newPermissionType: PermissionType,
    updaterUserId: string
  ): Promise<void> {
    // 检查更新权限
    await this.checkUpdatePermission(projectId, updaterUserId)

    // 更新协作者角色
    await this.collaboratorRepository.update(
      { projectId, userId: collaboratorUserId },
      { role: newPermissionType }
    )

    // 更新权限记录
    await this.permissionRepository.update(
      { projectId, userId: collaboratorUserId },
      { 
        type: newPermissionType,
        grantedBy: updaterUserId,
        grantedAt: new Date()
      }
    )
  }

  /**
   * 获取用户的邀请列表
   */
  async getUserInvitations(userId: string, status?: InvitationStatus): Promise<ProjectInvitation[]> {
    const where: any = { inviteeUserId: userId }
    if (status) {
      where.status = status
    }

    return await this.invitationRepository.find({
      where,
      relations: ['project', 'inviter'],
      order: { createdAt: 'DESC' }
    })
  }

  /**
   * 更新协作者在线状态
   */
  async updateCollaboratorOnlineStatus(
    projectId: string, 
    userId: string, 
    isOnline: boolean
  ): Promise<void> {
    await this.collaboratorRepository.update(
      { projectId, userId },
      { 
        isOnline,
        lastActiveAt: new Date()
      }
    )
  }

  /**
   * 检查邀请权限
   */
  private async checkInvitePermission(projectId: string, userId: string): Promise<void> {
    const project = await this.projectRepository.findOne({ where: { id: projectId } })
    
    // 项目所有者可以邀请
    if (project.ownerId === userId) return

    // 检查是否有管理员权限
    const permission = await this.permissionRepository.findOne({
      where: { projectId, userId, type: In([PermissionType.ADMIN, PermissionType.OWNER]) }
    })

    if (!permission) {
      throw new ForbiddenException('无权邀请协作者')
    }
  }

  /**
   * 检查项目访问权限
   */
  private async checkProjectAccess(projectId: string, userId: string): Promise<void> {
    const project = await this.projectRepository.findOne({ where: { id: projectId } })
    
    if (project.ownerId === userId) return

    const collaborator = await this.collaboratorRepository.findOne({
      where: { projectId, userId }
    })

    if (!collaborator) {
      throw new ForbiddenException('无权访问此项目')
    }
  }

  /**
   * 检查移除权限
   */
  private async checkRemovePermission(
    projectId: string, 
    removerUserId: string, 
    targetUserId: string
  ): Promise<void> {
    const project = await this.projectRepository.findOne({ where: { id: projectId } })
    
    // 项目所有者可以移除任何人
    if (project.ownerId === removerUserId) return

    // 不能移除项目所有者
    if (project.ownerId === targetUserId) {
      throw new ForbiddenException('不能移除项目所有者')
    }

    // 检查是否有管理员权限
    const permission = await this.permissionRepository.findOne({
      where: { projectId, userId: removerUserId, type: In([PermissionType.ADMIN]) }
    })

    if (!permission) {
      throw new ForbiddenException('无权移除协作者')
    }
  }

  /**
   * 检查更新权限
   */
  private async checkUpdatePermission(projectId: string, userId: string): Promise<void> {
    const project = await this.projectRepository.findOne({ where: { id: projectId } })
    
    if (project.ownerId === userId) return

    const permission = await this.permissionRepository.findOne({
      where: { projectId, userId, type: In([PermissionType.ADMIN, PermissionType.OWNER]) }
    })

    if (!permission) {
      throw new ForbiddenException('无权更新协作者权限')
    }
  }

  /**
   * 更新协作者统计
   */
  private async updateCollaboratorStats(projectId: string): Promise<void> {
    const count = await this.collaboratorRepository.count({ where: { projectId } })
    
    await this.projectRepository.update(projectId, {
      'statistics.collaborators': count
    })
  }

  /**
   * 发送邀请通知
   */
  private async sendInvitationNotification(invitation: ProjectInvitation): Promise<void> {
    // 这里应该集成通知服务
    // 暂时只记录日志
    console.log(`发送邀请通知: ${invitation.inviteeEmail}`)
  }
}
