import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';

export enum EmbeddingStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
}

export enum EmbeddingPurpose {
  SEARCH = 'search',
  SIMILARITY = 'similarity',
  CLASSIFICATION = 'classification',
  CLUSTERING = 'clustering',
  RECOMMENDATION = 'recommendation',
  ANALYSIS = 'analysis',
}

@Entity('embeddings')
@Index(['status'])
@Index(['purpose'])
@Index(['batchId'])
@Index(['createdAt'])
export class EmbeddingEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid', nullable: true })
  batchId: string; // 批处理ID

  @Column({ type: 'varchar', length: 100 })
  sourceType: string;

  @Column({ type: 'uuid' })
  sourceId: string;

  @Column({ type: 'text' })
  inputText: string;

  @Column({ type: 'varchar', length: 100 })
  model: string;

  @Column({ type: 'enum', enum: EmbeddingStatus, default: EmbeddingStatus.PENDING })
  status: EmbeddingStatus;

  @Column({ type: 'enum', enum: EmbeddingPurpose })
  purpose: EmbeddingPurpose;

  @Column({ type: 'vector', length: 1536, nullable: true })
  embedding: number[];

  @Column({ type: 'json', nullable: true })
  processingConfig: {
    maxTokens?: number;
    temperature?: number;
    chunkSize?: number;
    overlap?: number;
    preprocessingSteps?: string[];
    postprocessingSteps?: string[];
  };

  @Column({ type: 'json', nullable: true })
  processingResult: {
    tokenCount?: number;
    processingTime?: number;
    chunks?: Array<{
      text: string;
      startIndex: number;
      endIndex: number;
      embedding?: number[];
    }>;
    quality?: {
      score: number;
      issues: string[];
    };
  };

  @Column({ type: 'text', nullable: true })
  error: string;

  @Column({ type: 'datetime', nullable: true })
  processedAt: Date;

  @Column({ type: 'int', default: 0 })
  retryCount: number;

  @Column({ type: 'int', default: 3 })
  maxRetries: number;

  @Column({ type: 'json', nullable: true })
  metadata: {
    priority?: number;
    tags?: string[];
    category?: string;
    language?: string;
    encoding?: string;
    originalLength?: number;
    processedLength?: number;
    qualityScore?: number;
  };

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // 虚拟字段
  get isCompleted(): boolean {
    return this.status === EmbeddingStatus.COMPLETED;
  }

  get isFailed(): boolean {
    return this.status === EmbeddingStatus.FAILED;
  }

  get canRetry(): boolean {
    return this.status === EmbeddingStatus.FAILED && this.retryCount < this.maxRetries;
  }

  get processingDuration(): number | null {
    if (!this.processedAt) return null;
    return this.processedAt.getTime() - this.createdAt.getTime();
  }

  get embeddingDimension(): number {
    return this.embedding?.length || 0;
  }

  get hasChunks(): boolean {
    return (this.processingResult?.chunks?.length || 0) > 0;
  }

  get qualityScore(): number {
    return this.processingResult?.quality?.score || 0;
  }
}
