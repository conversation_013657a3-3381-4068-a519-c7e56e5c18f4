import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, Index } from 'typeorm'

@Entity('media_files')
@Index(['hash'])
@Index(['uploadedBy'])
@Index(['mimeType'])
@Index(['status'])
export class MediaFile {
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Column({ length: 255 })
  originalName: string

  @Column({ length: 255 })
  fileName: string

  @Column({ type: 'bigint' })
  size: number

  @Column({ length: 100 })
  mimeType: string

  @Column({ length: 64, unique: true })
  hash: string

  @Column({ type: 'text' })
  url: string

  @Column({ length: 36 })
  uploadedBy: string

  @Column({ 
    type: 'enum', 
    enum: ['pending', 'uploading', 'completed', 'failed', 'deleted'],
    default: 'pending'
  })
  status: string

  @Column({ type: 'json', nullable: true })
  metadata: any

  @CreateDateColumn()
  uploadedAt: Date

  @UpdateDateColumn()
  updatedAt: Date
}
