-- DL-Engine 数据库初始化脚本
-- 创建完整的数据库结构和初始数据

-- =============================================
-- 数据库和用户创建
-- =============================================

-- 创建数据库
CREATE DATABASE IF NOT EXISTS dl_engine 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE dl_engine;

-- =============================================
-- 用户和权限表
-- =============================================

-- 用户表
CREATE TABLE users (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    phone VARCHAR(20) UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    salt VARCHAR(32) NOT NULL,
    role ENUM('student', 'teacher', 'admin', 'super_admin') DEFAULT 'student',
    status ENUM('active', 'inactive', 'suspended', 'deleted') DEFAULT 'active',
    email_verified BOOLEAN DEFAULT FALSE,
    phone_verified BOOLEAN DEFAULT FALSE,
    two_factor_enabled BOOLEAN DEFAULT FALSE,
    two_factor_secret VARCHAR(32),
    last_login_at TIMESTAMP NULL,
    last_login_ip VARCHAR(45),
    failed_login_attempts INT DEFAULT 0,
    locked_until TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_phone (phone),
    INDEX idx_role (role),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);

-- 用户资料表
CREATE TABLE user_profiles (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id VARCHAR(36) NOT NULL,
    first_name VARCHAR(50),
    last_name VARCHAR(50),
    display_name VARCHAR(100),
    avatar_url VARCHAR(500),
    bio TEXT,
    location VARCHAR(100),
    website VARCHAR(200),
    birth_date DATE,
    gender ENUM('male', 'female', 'other', 'prefer_not_to_say'),
    timezone VARCHAR(50) DEFAULT 'UTC',
    locale VARCHAR(10) DEFAULT 'zh-CN',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_display_name (display_name)
);

-- 用户偏好设置表
CREATE TABLE user_preferences (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id VARCHAR(36) NOT NULL,
    theme ENUM('light', 'dark', 'auto') DEFAULT 'light',
    language VARCHAR(10) DEFAULT 'zh-CN',
    notifications JSON,
    privacy JSON,
    accessibility JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY uk_user_preferences (user_id)
);

-- =============================================
-- 课程和学习内容表
-- =============================================

-- 课程分类表
CREATE TABLE course_categories (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    parent_id VARCHAR(36),
    sort_order INT DEFAULT 0,
    icon VARCHAR(100),
    color VARCHAR(7),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (parent_id) REFERENCES course_categories(id) ON DELETE SET NULL,
    INDEX idx_parent_id (parent_id),
    INDEX idx_sort_order (sort_order),
    INDEX idx_is_active (is_active)
);

-- 课程表
CREATE TABLE courses (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    title VARCHAR(200) NOT NULL,
    description TEXT,
    instructor_id VARCHAR(36) NOT NULL,
    category_id VARCHAR(36),
    level ENUM('beginner', 'intermediate', 'advanced', 'expert') DEFAULT 'beginner',
    duration INT DEFAULT 0 COMMENT '课程时长(分钟)',
    thumbnail_url VARCHAR(500),
    cover_image_url VARCHAR(500),
    trailer_video_url VARCHAR(500),
    price DECIMAL(10,2) DEFAULT 0.00,
    currency VARCHAR(3) DEFAULT 'CNY',
    language VARCHAR(10) DEFAULT 'zh-CN',
    tags JSON,
    objectives JSON COMMENT '学习目标',
    prerequisites JSON COMMENT '先修要求',
    status ENUM('draft', 'review', 'published', 'archived') DEFAULT 'draft',
    is_featured BOOLEAN DEFAULT FALSE,
    is_free BOOLEAN DEFAULT TRUE,
    max_students INT DEFAULT 0 COMMENT '最大学生数，0表示无限制',
    enrollment_start_date TIMESTAMP NULL,
    enrollment_end_date TIMESTAMP NULL,
    start_date TIMESTAMP NULL,
    end_date TIMESTAMP NULL,
    published_at TIMESTAMP NULL,
    rating DECIMAL(3,2) DEFAULT 0.00,
    rating_count INT DEFAULT 0,
    enrollment_count INT DEFAULT 0,
    completion_count INT DEFAULT 0,
    view_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (instructor_id) REFERENCES users(id) ON DELETE RESTRICT,
    FOREIGN KEY (category_id) REFERENCES course_categories(id) ON DELETE SET NULL,
    INDEX idx_instructor_id (instructor_id),
    INDEX idx_category_id (category_id),
    INDEX idx_level (level),
    INDEX idx_status (status),
    INDEX idx_is_featured (is_featured),
    INDEX idx_is_free (is_free),
    INDEX idx_published_at (published_at),
    INDEX idx_rating (rating),
    INDEX idx_enrollment_count (enrollment_count),
    FULLTEXT idx_search (title, description)
);

-- 课程章节表
CREATE TABLE course_chapters (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    course_id VARCHAR(36) NOT NULL,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    sort_order INT DEFAULT 0,
    is_published BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    INDEX idx_course_id (course_id),
    INDEX idx_sort_order (sort_order),
    INDEX idx_is_published (is_published)
);

-- 课程课时表
CREATE TABLE course_lessons (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    course_id VARCHAR(36) NOT NULL,
    chapter_id VARCHAR(36),
    title VARCHAR(200) NOT NULL,
    description TEXT,
    type ENUM('video', 'text', 'interactive', '3d', 'quiz', 'assignment', 'live') DEFAULT 'video',
    content JSON COMMENT '课时内容配置',
    duration INT DEFAULT 0 COMMENT '课时时长(秒)',
    sort_order INT DEFAULT 0,
    is_preview BOOLEAN DEFAULT FALSE COMMENT '是否可预览',
    is_required BOOLEAN DEFAULT TRUE COMMENT '是否必修',
    prerequisites JSON COMMENT '前置课时要求',
    resources JSON COMMENT '课时资源',
    is_published BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    FOREIGN KEY (chapter_id) REFERENCES course_chapters(id) ON DELETE SET NULL,
    INDEX idx_course_id (course_id),
    INDEX idx_chapter_id (chapter_id),
    INDEX idx_type (type),
    INDEX idx_sort_order (sort_order),
    INDEX idx_is_published (is_published)
);

-- =============================================
-- 学习进度和报名表
-- =============================================

-- 课程报名表
CREATE TABLE enrollments (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id VARCHAR(36) NOT NULL,
    course_id VARCHAR(36) NOT NULL,
    status ENUM('active', 'completed', 'dropped', 'suspended') DEFAULT 'active',
    progress DECIMAL(5,2) DEFAULT 0.00 COMMENT '完成进度百分比',
    enrolled_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    started_at TIMESTAMP NULL,
    completed_at TIMESTAMP NULL,
    certificate_issued_at TIMESTAMP NULL,
    certificate_url VARCHAR(500),
    total_time_spent INT DEFAULT 0 COMMENT '总学习时间(秒)',
    last_accessed_at TIMESTAMP NULL,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    UNIQUE KEY uk_user_course (user_id, course_id),
    INDEX idx_user_id (user_id),
    INDEX idx_course_id (course_id),
    INDEX idx_status (status),
    INDEX idx_enrolled_at (enrolled_at)
);

-- 学习进度表
CREATE TABLE learning_progress (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id VARCHAR(36) NOT NULL,
    course_id VARCHAR(36) NOT NULL,
    lesson_id VARCHAR(36) NOT NULL,
    status ENUM('not_started', 'in_progress', 'completed', 'skipped') DEFAULT 'not_started',
    progress DECIMAL(5,2) DEFAULT 0.00 COMMENT '课时完成进度',
    time_spent INT DEFAULT 0 COMMENT '学习时间(秒)',
    score DECIMAL(5,2) NULL COMMENT '得分',
    attempts INT DEFAULT 0 COMMENT '尝试次数',
    started_at TIMESTAMP NULL,
    completed_at TIMESTAMP NULL,
    last_position JSON COMMENT '最后学习位置',
    notes TEXT COMMENT '学习笔记',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    FOREIGN KEY (lesson_id) REFERENCES course_lessons(id) ON DELETE CASCADE,
    UNIQUE KEY uk_user_lesson (user_id, lesson_id),
    INDEX idx_user_id (user_id),
    INDEX idx_course_id (course_id),
    INDEX idx_lesson_id (lesson_id),
    INDEX idx_status (status),
    INDEX idx_updated_at (updated_at)
);

-- =============================================
-- 3D场景和对象表
-- =============================================

-- 场景模板表
CREATE TABLE scene_templates (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    category VARCHAR(50),
    thumbnail_url VARCHAR(500),
    preview_images JSON,
    config JSON COMMENT '模板配置',
    scene_data JSON COMMENT '场景数据',
    assets JSON COMMENT '资源列表',
    is_public BOOLEAN DEFAULT TRUE,
    is_featured BOOLEAN DEFAULT FALSE,
    download_count INT DEFAULT 0,
    rating DECIMAL(3,2) DEFAULT 0.00,
    rating_count INT DEFAULT 0,
    created_by VARCHAR(36),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_category (category),
    INDEX idx_is_public (is_public),
    INDEX idx_is_featured (is_featured),
    INDEX idx_created_by (created_by),
    FULLTEXT idx_search (name, description)
);

-- 3D场景表
CREATE TABLE scenes (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    template_id VARCHAR(36),
    creator_id VARCHAR(36) NOT NULL,
    thumbnail_url VARCHAR(500),
    scene_data JSON COMMENT '场景配置数据',
    physics_config JSON COMMENT '物理引擎配置',
    lighting_config JSON COMMENT '光照配置',
    environment_config JSON COMMENT '环境配置',
    max_participants INT DEFAULT 50,
    is_public BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    version INT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (template_id) REFERENCES scene_templates(id) ON DELETE SET NULL,
    FOREIGN KEY (creator_id) REFERENCES users(id) ON DELETE RESTRICT,
    INDEX idx_template_id (template_id),
    INDEX idx_creator_id (creator_id),
    INDEX idx_is_public (is_public),
    INDEX idx_is_active (is_active),
    FULLTEXT idx_search (name, description)
);

-- 场景对象表
CREATE TABLE scene_objects (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    scene_id VARCHAR(36) NOT NULL,
    name VARCHAR(100) NOT NULL,
    type VARCHAR(50) NOT NULL,
    transform JSON COMMENT '变换矩阵',
    geometry JSON COMMENT '几何体配置',
    material JSON COMMENT '材质配置',
    physics JSON COMMENT '物理属性',
    interactions JSON COMMENT '交互配置',
    animations JSON COMMENT '动画配置',
    metadata JSON COMMENT '元数据',
    parent_id VARCHAR(36) COMMENT '父对象ID',
    sort_order INT DEFAULT 0,
    is_visible BOOLEAN DEFAULT TRUE,
    is_interactive BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (scene_id) REFERENCES scenes(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_id) REFERENCES scene_objects(id) ON DELETE CASCADE,
    INDEX idx_scene_id (scene_id),
    INDEX idx_type (type),
    INDEX idx_parent_id (parent_id),
    INDEX idx_sort_order (sort_order),
    INDEX idx_is_visible (is_visible)
);

-- =============================================
-- 协作和会话表
-- =============================================

-- 协作会话表
CREATE TABLE collaboration_sessions (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    type ENUM('class', 'meeting', 'workshop', 'presentation', 'exam') DEFAULT 'class',
    scene_id VARCHAR(36),
    course_id VARCHAR(36),
    host_id VARCHAR(36) NOT NULL,
    max_participants INT DEFAULT 50,
    status ENUM('scheduled', 'active', 'paused', 'ended', 'cancelled') DEFAULT 'scheduled',
    settings JSON COMMENT '会话设置',
    start_time TIMESTAMP NOT NULL,
    end_time TIMESTAMP,
    actual_start_time TIMESTAMP NULL,
    actual_end_time TIMESTAMP NULL,
    recording_url VARCHAR(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (scene_id) REFERENCES scenes(id) ON DELETE SET NULL,
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE SET NULL,
    FOREIGN KEY (host_id) REFERENCES users(id) ON DELETE RESTRICT,
    INDEX idx_scene_id (scene_id),
    INDEX idx_course_id (course_id),
    INDEX idx_host_id (host_id),
    INDEX idx_status (status),
    INDEX idx_start_time (start_time)
);

-- 会话参与者表
CREATE TABLE session_participants (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    session_id VARCHAR(36) NOT NULL,
    user_id VARCHAR(36) NOT NULL,
    role ENUM('host', 'co-host', 'presenter', 'participant', 'observer') DEFAULT 'participant',
    status ENUM('invited', 'joined', 'left', 'kicked', 'banned') DEFAULT 'invited',
    permissions JSON COMMENT '权限设置',
    joined_at TIMESTAMP NULL,
    left_at TIMESTAMP NULL,
    total_time INT DEFAULT 0 COMMENT '参与时间(秒)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (session_id) REFERENCES collaboration_sessions(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY uk_session_user (session_id, user_id),
    INDEX idx_session_id (session_id),
    INDEX idx_user_id (user_id),
    INDEX idx_role (role),
    INDEX idx_status (status)
);

-- =============================================
-- 文件和媒体表
-- =============================================

-- 文件表
CREATE TABLE files (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    filename VARCHAR(255) NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    size BIGINT NOT NULL,
    path VARCHAR(500) NOT NULL,
    url VARCHAR(500) NOT NULL,
    thumbnail_url VARCHAR(500),
    hash VARCHAR(64) COMMENT '文件哈希值',
    metadata JSON COMMENT '文件元数据',
    uploader_id VARCHAR(36) NOT NULL,
    upload_session_id VARCHAR(36),
    status ENUM('uploading', 'processing', 'ready', 'error', 'deleted') DEFAULT 'uploading',
    is_public BOOLEAN DEFAULT FALSE,
    download_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (uploader_id) REFERENCES users(id) ON DELETE RESTRICT,
    INDEX idx_uploader_id (uploader_id),
    INDEX idx_mime_type (mime_type),
    INDEX idx_status (status),
    INDEX idx_hash (hash),
    INDEX idx_created_at (created_at)
);

-- =============================================
-- 系统日志和审计表
-- =============================================

-- 系统日志表
CREATE TABLE system_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    level ENUM('debug', 'info', 'warn', 'error', 'fatal') NOT NULL,
    service VARCHAR(50) NOT NULL,
    message TEXT NOT NULL,
    context JSON,
    user_id VARCHAR(36),
    session_id VARCHAR(36),
    request_id VARCHAR(36),
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_level (level),
    INDEX idx_service (service),
    INDEX idx_user_id (user_id),
    INDEX idx_session_id (session_id),
    INDEX idx_request_id (request_id),
    INDEX idx_created_at (created_at)
) PARTITION BY RANGE (YEAR(created_at) * 100 + MONTH(created_at)) (
    PARTITION p202401 VALUES LESS THAN (202402),
    PARTITION p202402 VALUES LESS THAN (202403),
    PARTITION p202403 VALUES LESS THAN (202404),
    PARTITION p202404 VALUES LESS THAN (202405),
    PARTITION p202405 VALUES LESS THAN (202406),
    PARTITION p202406 VALUES LESS THAN (202407),
    PARTITION p202407 VALUES LESS THAN (202408),
    PARTITION p202408 VALUES LESS THAN (202409),
    PARTITION p202409 VALUES LESS THAN (202410),
    PARTITION p202410 VALUES LESS THAN (202411),
    PARTITION p202411 VALUES LESS THAN (202412),
    PARTITION p202412 VALUES LESS THAN (202501),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);

-- =============================================
-- 初始数据插入
-- =============================================

-- 插入默认管理员用户
INSERT INTO users (id, username, email, password_hash, salt, role, status, email_verified) VALUES
('admin-user-id-0000-0000-************', 'admin', '<EMAIL>', 
 SHA2(CONCAT('admin123', 'default_salt'), 256), 'default_salt', 'super_admin', 'active', TRUE);

-- 插入管理员资料
INSERT INTO user_profiles (user_id, first_name, last_name, display_name) VALUES
('admin-user-id-0000-0000-************', 'System', 'Administrator', 'System Admin');

-- 插入管理员偏好设置
INSERT INTO user_preferences (user_id, theme, language, notifications, privacy, accessibility) VALUES
('admin-user-id-0000-0000-************', 'light', 'zh-CN', 
 '{"email": true, "push": true, "sms": false}',
 '{"profileVisibility": "private", "showOnlineStatus": false}',
 '{"highContrast": false, "largeText": false, "reduceMotion": false}');

-- 插入默认课程分类
INSERT INTO course_categories (id, name, description, sort_order, icon, color) VALUES
('cat-technology-000-0000-************', '技术与编程', '计算机科学、编程语言、软件开发等技术类课程', 1, 'code', '#1890ff'),
('cat-science-0000-0000-************', '自然科学', '物理、化学、生物、数学等自然科学课程', 2, 'experiment', '#52c41a'),
('cat-language-000-0000-************', '语言学习', '外语学习、语言文化等课程', 3, 'global', '#fa8c16'),
('cat-art-0000000-0000-************', '艺术设计', '美术、设计、音乐、影视等艺术类课程', 4, 'palette', '#eb2f96'),
('cat-business-00-0000-************', '商业管理', '商业、管理、经济、金融等课程', 5, 'bank', '#722ed1');

-- 插入默认场景模板
INSERT INTO scene_templates (id, name, description, category, config, scene_data, is_public, is_featured) VALUES
('tpl-empty-scene-0000-************', '空白场景', '一个空白的3D场景，适合从零开始创建内容', 'basic',
 '{"maxParticipants": 50, "enablePhysics": true, "enableVoiceChat": true}',
 '{"environment": {"skybox": "default"}, "lighting": {"ambient": {"color": "#ffffff", "intensity": 0.4}}}',
 TRUE, TRUE),
('tpl-classroom-000-0000-************', '基础教室', '传统教室布局，包含讲台、桌椅和黑板', 'education',
 '{"maxParticipants": 30, "enablePhysics": true, "enableVoiceChat": true}',
 '{"environment": {"skybox": "classroom"}, "objects": [{"type": "desk", "count": 15}, {"type": "blackboard", "count": 1}]}',
 TRUE, TRUE);

-- 创建索引优化
-- 已在表定义中包含

-- 设置自动清理任务
-- 已在优化脚本中定义

COMMIT;
