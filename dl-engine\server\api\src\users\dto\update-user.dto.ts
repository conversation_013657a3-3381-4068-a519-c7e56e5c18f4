import {
  IsString,
  IsEmail,
  IsOptional,
  IsEnum,
  Length,
  Matches
} from 'class-validator'
import { ApiPropertyOptional } from '@nestjs/swagger'
import { Transform } from 'class-transformer'

import { UserType, UserStatus } from '../entities/user.entity'

/**
 * 更新用户DTO
 */
export class UpdateUserDto {
  /**
   * 用户名
   */
  @ApiPropertyOptional({
    description: '用户名，3-50个字符，只能包含字母、数字、下划线和连字符',
    example: 'zhang_san_123',
    minLength: 3,
    maxLength: 50
  })
  @IsOptional()
  @IsString()
  @Length(3, 50, { message: '用户名长度必须在3-50个字符之间' })
  @Matches(/^[a-zA-Z0-9_-]+$/, { message: '用户名只能包含字母、数字、下划线和连字符' })
  @Transform(({ value }) => value?.toLowerCase().trim())
  username?: string

  /**
   * 手机号码
   */
  @ApiPropertyOptional({
    description: '手机号码',
    example: '13800138000'
  })
  @IsOptional()
  @IsString()
  @Matches(/^1[3-9]\d{9}$|^\d{7,15}$/, { message: '手机号格式不正确' })
  @Transform(({ value }) => value?.replace(/\D/g, ''))
  phone?: string

  /**
   * 国家代码
   */
  @ApiPropertyOptional({
    description: '国家代码',
    example: '+86'
  })
  @IsOptional()
  @IsString()
  @Matches(/^\+\d{1,4}$/, { message: '国家代码格式不正确' })
  countryCode?: string

  /**
   * 邮箱地址
   */
  @ApiPropertyOptional({
    description: '邮箱地址',
    example: '<EMAIL>'
  })
  @IsOptional()
  @IsEmail({}, { message: '邮箱格式不正确' })
  @Length(1, 255, { message: '邮箱长度不能超过255个字符' })
  @Transform(({ value }) => value?.toLowerCase().trim())
  email?: string

  /**
   * 显示名称
   */
  @ApiPropertyOptional({
    description: '显示名称',
    example: '张三',
    maxLength: 100
  })
  @IsOptional()
  @IsString()
  @Length(1, 100, { message: '显示名称长度不能超过100个字符' })
  @Transform(({ value }) => value?.trim())
  displayName?: string

  /**
   * 头像URL
   */
  @ApiPropertyOptional({
    description: '头像URL',
    example: 'https://example.com/avatar.jpg'
  })
  @IsOptional()
  @IsString()
  avatarUrl?: string

  /**
   * 用户类型（仅管理员可修改）
   */
  @ApiPropertyOptional({
    description: '用户类型（仅管理员可修改）',
    enum: UserType,
    example: UserType.STUDENT
  })
  @IsOptional()
  @IsEnum(UserType, { message: '用户类型不正确' })
  userType?: UserType

  /**
   * 用户状态（仅管理员可修改）
   */
  @ApiPropertyOptional({
    description: '用户状态（仅管理员可修改）',
    enum: UserStatus,
    example: UserStatus.ACTIVE
  })
  @IsOptional()
  @IsEnum(UserStatus, { message: '用户状态不正确' })
  status?: UserStatus
}
