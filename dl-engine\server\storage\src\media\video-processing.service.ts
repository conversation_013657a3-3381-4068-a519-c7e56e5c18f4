import { Injectable, Logger } from '@nestjs/common';
import { Process, Processor } from '@nestjs/bull';
import { Job } from 'bull';
import { MinioService } from '../minio/minio.service';
import * as ffmpeg from 'fluent-ffmpeg';
import * as fs from 'fs/promises';
import * as path from 'path';
import * as os from 'os';

export interface VideoProcessingOptions {
  transcode?: {
    codec: 'h264' | 'h265' | 'vp9' | 'av1';
    bitrate?: string; // '1000k', '2M'
    resolution?: string; // '1920x1080', '1280x720'
    framerate?: number;
    preset?: 'ultrafast' | 'superfast' | 'veryfast' | 'faster' | 'fast' | 'medium' | 'slow' | 'slower' | 'veryslow';
    crf?: number; // 0-51, lower is better quality
    profile?: string;
    level?: string;
  };
  audio?: {
    codec: 'aac' | 'mp3' | 'opus' | 'vorbis';
    bitrate?: string;
    sampleRate?: number;
    channels?: number;
  };
  trim?: {
    start: number; // 秒
    duration?: number; // 秒
    end?: number; // 秒
  };
  crop?: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  scale?: {
    width: number;
    height: number;
    algorithm?: 'bilinear' | 'bicubic' | 'lanczos';
  };
  filters?: {
    brightness?: number; // -1.0 to 1.0
    contrast?: number; // -1.0 to 1.0
    saturation?: number; // 0.0 to 3.0
    hue?: number; // -180 to 180
    blur?: number;
    sharpen?: number;
    denoise?: boolean;
    deinterlace?: boolean;
    stabilize?: boolean;
  };
  watermark?: {
    image?: string; // 图片路径
    text?: string;
    position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right' | 'center';
    opacity?: number;
    fontSize?: number;
    fontColor?: string;
  };
  segments?: {
    duration: number; // HLS分片时长（秒）
    format: 'hls' | 'dash';
    playlist?: boolean;
  };
}

export interface ThumbnailGenerationOptions {
  count?: number; // 生成缩略图数量
  timestamps?: number[]; // 指定时间点（秒）
  interval?: number; // 间隔时间（秒）
  size?: string; // '320x240'
  quality?: number; // 1-31, lower is better
  format?: 'jpg' | 'png' | 'webp';
}

@Injectable()
@Processor('video-processing')
export class VideoProcessingService {
  private readonly logger = new Logger(VideoProcessingService.name);
  private readonly tempDir: string;

  constructor(private minioService: MinioService) {
    this.tempDir = path.join(os.tmpdir(), 'video-processing');
    this.ensureTempDirectory();
  }

  @Process('process-video')
  async processVideo(job: Job): Promise<void> {
    const { fileId, bucketName, objectName, options } = job.data;
    
    try {
      this.logger.log(`Processing video: ${fileId}`);
      
      // 下载原始视频
      const videoBuffer = await this.minioService.getObject(bucketName, objectName);
      const inputPath = path.join(this.tempDir, `${fileId}_input.mp4`);
      await fs.writeFile(inputPath, videoBuffer);
      
      // 生成缩略图
      await this.generateVideoThumbnails(inputPath, bucketName, fileId);
      
      // 转码处理
      if (options.videoProcessing) {
        await this.transcodeVideo(inputPath, bucketName, fileId, options.videoProcessing);
      }
      
      // 提取视频信息
      await this.extractVideoInfo(inputPath, fileId);
      
      // 清理临时文件
      await this.cleanupTempFile(inputPath);
      
      this.logger.log(`Video processing completed: ${fileId}`);
    } catch (error) {
      this.logger.error(`Video processing failed: ${fileId}`, error);
      throw error;
    }
  }

  /**
   * 生成视频缩略图
   */
  async generateVideoThumbnails(
    inputPath: string,
    bucketName: string,
    fileId: string,
    options?: ThumbnailGenerationOptions
  ): Promise<string[]> {
    const thumbnailUrls: string[] = [];

    try {
      const count = options?.count || 5;
      const size = options?.size || '320x240';
      const quality = options?.quality || 2;
      const format = options?.format || 'jpg';

      // 获取视频时长
      const duration = await this.getVideoDuration(inputPath);
      
      let timestamps: number[];
      if (options?.timestamps) {
        timestamps = options.timestamps;
      } else if (options?.interval) {
        timestamps = [];
        for (let i = 0; i < duration; i += options.interval) {
          timestamps.push(i);
        }
      } else {
        // 均匀分布的时间点
        timestamps = [];
        for (let i = 0; i < count; i++) {
          timestamps.push((duration / (count + 1)) * (i + 1));
        }
      }

      for (let i = 0; i < timestamps.length; i++) {
        const timestamp = timestamps[i];
        const thumbnailPath = path.join(this.tempDir, `${fileId}_thumb_${i}.${format}`);
        
        await new Promise<void>((resolve, reject) => {
          ffmpeg(inputPath)
            .seekInput(timestamp)
            .frames(1)
            .size(size)
            .outputOptions([`-q:v ${quality}`])
            .output(thumbnailPath)
            .on('end', resolve)
            .on('error', reject)
            .run();
        });

        // 上传缩略图
        const thumbnailBuffer = await fs.readFile(thumbnailPath);
        const thumbnailObjectName = `video-thumbnails/${fileId}/thumb_${i}.${format}`;
        await this.minioService.putObject(bucketName, thumbnailObjectName, thumbnailBuffer);
        
        const thumbnailUrl = await this.minioService.getPresignedUrl('GET', bucketName, thumbnailObjectName);
        thumbnailUrls.push(thumbnailUrl);
        
        // 清理临时文件
        await this.cleanupTempFile(thumbnailPath);
        
        this.logger.debug(`Video thumbnail generated: ${i} for ${fileId}`);
      }

      return thumbnailUrls;
    } catch (error) {
      this.logger.error(`Failed to generate video thumbnails for ${fileId}:`, error);
      throw error;
    }
  }

  /**
   * 视频转码
   */
  async transcodeVideo(
    inputPath: string,
    bucketName: string,
    fileId: string,
    options: VideoProcessingOptions
  ): Promise<string> {
    try {
      const outputPath = path.join(this.tempDir, `${fileId}_transcoded.mp4`);
      
      let command = ffmpeg(inputPath);

      // 视频编码设置
      if (options.transcode) {
        const { codec, bitrate, resolution, framerate, preset, crf, profile, level } = options.transcode;
        
        switch (codec) {
          case 'h264':
            command = command.videoCodec('libx264');
            if (preset) command = command.outputOptions([`-preset ${preset}`]);
            if (crf) command = command.outputOptions([`-crf ${crf}`]);
            if (profile) command = command.outputOptions([`-profile:v ${profile}`]);
            if (level) command = command.outputOptions([`-level ${level}`]);
            break;
          case 'h265':
            command = command.videoCodec('libx265');
            if (preset) command = command.outputOptions([`-preset ${preset}`]);
            if (crf) command = command.outputOptions([`-crf ${crf}`]);
            break;
          case 'vp9':
            command = command.videoCodec('libvpx-vp9');
            if (crf) command = command.outputOptions([`-crf ${crf}`]);
            break;
          case 'av1':
            command = command.videoCodec('libaom-av1');
            if (crf) command = command.outputOptions([`-crf ${crf}`]);
            break;
        }
        
        if (bitrate) command = command.videoBitrate(bitrate);
        if (resolution) command = command.size(resolution);
        if (framerate) command = command.fps(framerate);
      }

      // 音频编码设置
      if (options.audio) {
        const { codec, bitrate, sampleRate, channels } = options.audio;
        
        switch (codec) {
          case 'aac':
            command = command.audioCodec('aac');
            break;
          case 'mp3':
            command = command.audioCodec('libmp3lame');
            break;
          case 'opus':
            command = command.audioCodec('libopus');
            break;
          case 'vorbis':
            command = command.audioCodec('libvorbis');
            break;
        }
        
        if (bitrate) command = command.audioBitrate(bitrate);
        if (sampleRate) command = command.audioFrequency(sampleRate);
        if (channels) command = command.audioChannels(channels);
      }

      // 裁剪时间
      if (options.trim) {
        if (options.trim.start) command = command.seekInput(options.trim.start);
        if (options.trim.duration) command = command.duration(options.trim.duration);
        if (options.trim.end && !options.trim.duration) {
          command = command.duration(options.trim.end - (options.trim.start || 0));
        }
      }

      // 应用滤镜
      const filters = this.buildVideoFilters(options);
      if (filters.length > 0) {
        command = command.videoFilters(filters);
      }

      // 执行转码
      await new Promise<void>((resolve, reject) => {
        command
          .output(outputPath)
          .on('progress', (progress) => {
            this.logger.debug(`Transcoding progress: ${progress.percent}% for ${fileId}`);
          })
          .on('end', resolve)
          .on('error', reject)
          .run();
      });

      // 上传转码后的视频
      const transcodedBuffer = await fs.readFile(outputPath);
      const transcodedObjectName = `transcoded/${fileId}/transcoded.mp4`;
      await this.minioService.putObject(bucketName, transcodedObjectName, transcodedBuffer);
      
      const transcodedUrl = await this.minioService.getPresignedUrl('GET', bucketName, transcodedObjectName);
      
      // 清理临时文件
      await this.cleanupTempFile(outputPath);
      
      this.logger.debug(`Video transcoding completed for ${fileId}`);
      return transcodedUrl;
    } catch (error) {
      this.logger.error(`Failed to transcode video for ${fileId}:`, error);
      throw error;
    }
  }

  /**
   * 生成HLS流
   */
  async generateHLSStream(
    inputPath: string,
    bucketName: string,
    fileId: string,
    options: {
      segmentDuration?: number;
      qualities?: Array<{
        name: string;
        resolution: string;
        bitrate: string;
      }>;
    } = {}
  ): Promise<string> {
    try {
      const segmentDuration = options.segmentDuration || 10;
      const qualities = options.qualities || [
        { name: '720p', resolution: '1280x720', bitrate: '2500k' },
        { name: '480p', resolution: '854x480', bitrate: '1000k' },
        { name: '360p', resolution: '640x360', bitrate: '500k' },
      ];

      const hlsDir = path.join(this.tempDir, `${fileId}_hls`);
      await fs.mkdir(hlsDir, { recursive: true });

      // 生成多码率流
      for (const quality of qualities) {
        const outputPath = path.join(hlsDir, `${quality.name}.m3u8`);
        
        await new Promise<void>((resolve, reject) => {
          ffmpeg(inputPath)
            .videoCodec('libx264')
            .audioCodec('aac')
            .videoBitrate(quality.bitrate)
            .size(quality.resolution)
            .outputOptions([
              '-hls_time', segmentDuration.toString(),
              '-hls_list_size', '0',
              '-hls_segment_filename', path.join(hlsDir, `${quality.name}_%03d.ts`),
            ])
            .output(outputPath)
            .on('end', resolve)
            .on('error', reject)
            .run();
        });
      }

      // 生成主播放列表
      const masterPlaylist = this.generateMasterPlaylist(qualities);
      const masterPlaylistPath = path.join(hlsDir, 'master.m3u8');
      await fs.writeFile(masterPlaylistPath, masterPlaylist);

      // 上传所有HLS文件
      const files = await fs.readdir(hlsDir);
      for (const file of files) {
        const filePath = path.join(hlsDir, file);
        const fileBuffer = await fs.readFile(filePath);
        const objectName = `hls/${fileId}/${file}`;
        await this.minioService.putObject(bucketName, objectName, fileBuffer);
      }

      // 清理临时目录
      await fs.rmdir(hlsDir, { recursive: true });

      const masterPlaylistUrl = await this.minioService.getPresignedUrl('GET', bucketName, `hls/${fileId}/master.m3u8`);
      
      this.logger.debug(`HLS stream generated for ${fileId}`);
      return masterPlaylistUrl;
    } catch (error) {
      this.logger.error(`Failed to generate HLS stream for ${fileId}:`, error);
      throw error;
    }
  }

  /**
   * 提取视频信息
   */
  async extractVideoInfo(inputPath: string, fileId: string): Promise<any> {
    try {
      return new Promise((resolve, reject) => {
        ffmpeg.ffprobe(inputPath, (err, metadata) => {
          if (err) {
            reject(err);
            return;
          }

          const videoInfo = {
            format: metadata.format,
            streams: metadata.streams,
            duration: metadata.format.duration,
            bitrate: metadata.format.bit_rate,
            size: metadata.format.size,
            videoStreams: metadata.streams.filter(s => s.codec_type === 'video'),
            audioStreams: metadata.streams.filter(s => s.codec_type === 'audio'),
          };

          this.logger.debug(`Video info extracted for ${fileId}:`, videoInfo);
          resolve(videoInfo);
        });
      });
    } catch (error) {
      this.logger.error(`Failed to extract video info for ${fileId}:`, error);
      throw error;
    }
  }

  private async getVideoDuration(inputPath: string): Promise<number> {
    return new Promise((resolve, reject) => {
      ffmpeg.ffprobe(inputPath, (err, metadata) => {
        if (err) {
          reject(err);
          return;
        }
        resolve(metadata.format.duration || 0);
      });
    });
  }

  private buildVideoFilters(options: VideoProcessingOptions): string[] {
    const filters: string[] = [];

    // 裁剪
    if (options.crop) {
      const { x, y, width, height } = options.crop;
      filters.push(`crop=${width}:${height}:${x}:${y}`);
    }

    // 缩放
    if (options.scale) {
      const { width, height, algorithm } = options.scale;
      const scaleFilter = algorithm 
        ? `scale=${width}:${height}:flags=${algorithm}`
        : `scale=${width}:${height}`;
      filters.push(scaleFilter);
    }

    // 颜色调整
    if (options.filters) {
      const { brightness, contrast, saturation, hue } = options.filters;
      
      if (brightness !== undefined || contrast !== undefined || 
          saturation !== undefined || hue !== undefined) {
        const eqParams = [];
        if (brightness !== undefined) eqParams.push(`brightness=${brightness}`);
        if (contrast !== undefined) eqParams.push(`contrast=${contrast + 1}`);
        if (saturation !== undefined) eqParams.push(`saturation=${saturation}`);
        if (hue !== undefined) eqParams.push(`hue=${hue}`);
        
        filters.push(`eq=${eqParams.join(':')}`);
      }

      // 模糊
      if (options.filters.blur) {
        filters.push(`boxblur=${options.filters.blur}`);
      }

      // 锐化
      if (options.filters.sharpen) {
        filters.push(`unsharp=5:5:${options.filters.sharpen}:5:5:0.0`);
      }

      // 降噪
      if (options.filters.denoise) {
        filters.push('hqdn3d');
      }

      // 去隔行
      if (options.filters.deinterlace) {
        filters.push('yadif');
      }

      // 防抖
      if (options.filters.stabilize) {
        filters.push('deshake');
      }
    }

    // 水印
    if (options.watermark) {
      if (options.watermark.image) {
        const position = this.getWatermarkPosition(options.watermark.position);
        filters.push(`overlay=${position}`);
      } else if (options.watermark.text) {
        const fontSize = options.watermark.fontSize || 24;
        const fontColor = options.watermark.fontColor || 'white';
        const position = this.getTextPosition(options.watermark.position);
        filters.push(`drawtext=text='${options.watermark.text}':fontsize=${fontSize}:fontcolor=${fontColor}:${position}`);
      }
    }

    return filters;
  }

  private getWatermarkPosition(position?: string): string {
    const positions = {
      'top-left': '10:10',
      'top-right': 'W-w-10:10',
      'bottom-left': '10:H-h-10',
      'bottom-right': 'W-w-10:H-h-10',
      'center': '(W-w)/2:(H-h)/2',
    };
    
    return positions[position] || positions['bottom-right'];
  }

  private getTextPosition(position?: string): string {
    const positions = {
      'top-left': 'x=10:y=10',
      'top-right': 'x=w-tw-10:y=10',
      'bottom-left': 'x=10:y=h-th-10',
      'bottom-right': 'x=w-tw-10:y=h-th-10',
      'center': 'x=(w-tw)/2:y=(h-th)/2',
    };
    
    return positions[position] || positions['bottom-right'];
  }

  private generateMasterPlaylist(qualities: any[]): string {
    let playlist = '#EXTM3U\n#EXT-X-VERSION:3\n\n';
    
    for (const quality of qualities) {
      const bandwidth = parseInt(quality.bitrate.replace('k', '000').replace('M', '000000'));
      const resolution = quality.resolution;
      
      playlist += `#EXT-X-STREAM-INF:BANDWIDTH=${bandwidth},RESOLUTION=${resolution}\n`;
      playlist += `${quality.name}.m3u8\n`;
    }
    
    return playlist;
  }

  private async ensureTempDirectory(): Promise<void> {
    try {
      await fs.access(this.tempDir);
    } catch {
      await fs.mkdir(this.tempDir, { recursive: true });
    }
  }

  private async cleanupTempFile(filePath: string): Promise<void> {
    try {
      await fs.unlink(filePath);
    } catch (error) {
      this.logger.warn(`Failed to cleanup temp file: ${filePath}`, error);
    }
  }
}
