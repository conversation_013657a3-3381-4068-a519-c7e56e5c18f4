import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { OllamaService } from './ollama.service';
import { EmbeddingService } from './embedding.service';
import { VectorSearchService } from './vector-search.service';
import { KnowledgeGraphService } from './knowledge-graph.service';
import { ContentAnalysisService } from './content-analysis.service';
import { RecommendationService } from './recommendation.service';
import { LearningPathService } from './learning-path.service';
import { ChatbotService } from './chatbot.service';

export interface AITask {
  id: string;
  type: 'embedding' | 'analysis' | 'recommendation' | 'chat' | 'search' | 'learning_path';
  status: 'pending' | 'processing' | 'completed' | 'failed';
  input: any;
  output?: any;
  error?: string;
  progress: number;
  startTime: Date;
  endTime?: Date;
  metadata?: Record<string, any>;
}

export interface AICapabilities {
  models: {
    available: string[];
    loaded: string[];
    default: string;
  };
  features: {
    textGeneration: boolean;
    embedding: boolean;
    vectorSearch: boolean;
    contentAnalysis: boolean;
    recommendation: boolean;
    knowledgeGraph: boolean;
    chatbot: boolean;
    learningPath: boolean;
  };
  performance: {
    averageResponseTime: number;
    throughput: number;
    accuracy: number;
  };
  resources: {
    memoryUsage: number;
    gpuUsage?: number;
    diskUsage: number;
  };
}

@Injectable()
export class AIService {
  private readonly logger = new Logger(AIService.name);
  private readonly tasks = new Map<string, AITask>();

  constructor(
    private configService: ConfigService,
    private ollamaService: OllamaService,
    private embeddingService: EmbeddingService,
    private vectorSearchService: VectorSearchService,
    private knowledgeGraphService: KnowledgeGraphService,
    private contentAnalysisService: ContentAnalysisService,
    private recommendationService: RecommendationService,
    private learningPathService: LearningPathService,
    private chatbotService: ChatbotService,
  ) {}

  /**
   * 获取AI能力信息
   */
  async getCapabilities(): Promise<AICapabilities> {
    try {
      const [
        ollamaModels,
        embeddingStats,
        vectorStats,
        systemStats,
      ] = await Promise.all([
        this.ollamaService.getAvailableModels(),
        this.embeddingService.getStats(),
        this.vectorSearchService.getStats(),
        this.getSystemStats(),
      ]);

      return {
        models: {
          available: ollamaModels.map(m => m.name),
          loaded: ollamaModels.filter(m => m.loaded).map(m => m.name),
          default: this.configService.get('AI_DEFAULT_MODEL', 'llama2'),
        },
        features: {
          textGeneration: true,
          embedding: true,
          vectorSearch: true,
          contentAnalysis: true,
          recommendation: true,
          knowledgeGraph: true,
          chatbot: true,
          learningPath: true,
        },
        performance: {
          averageResponseTime: systemStats.averageResponseTime,
          throughput: systemStats.throughput,
          accuracy: systemStats.accuracy,
        },
        resources: {
          memoryUsage: systemStats.memoryUsage,
          gpuUsage: systemStats.gpuUsage,
          diskUsage: systemStats.diskUsage,
        },
      };
    } catch (error) {
      this.logger.error('Failed to get AI capabilities:', error);
      throw error;
    }
  }

  /**
   * 文本生成
   */
  async generateText(
    prompt: string,
    options: {
      model?: string;
      maxTokens?: number;
      temperature?: number;
      stream?: boolean;
      context?: string[];
    } = {}
  ): Promise<string | AsyncIterable<string>> {
    try {
      const taskId = this.createTask('chat', { prompt, options });
      
      const result = await this.ollamaService.generateText(prompt, {
        model: options.model || this.configService.get('AI_DEFAULT_MODEL', 'llama2'),
        maxTokens: options.maxTokens || 1000,
        temperature: options.temperature || 0.7,
        stream: options.stream || false,
        context: options.context,
      });

      this.completeTask(taskId, result);
      return result;
    } catch (error) {
      this.logger.error('Failed to generate text:', error);
      throw error;
    }
  }

  /**
   * 文本嵌入
   */
  async generateEmbedding(
    text: string,
    options: {
      model?: string;
      normalize?: boolean;
      metadata?: Record<string, any>;
    } = {}
  ): Promise<number[]> {
    try {
      const taskId = this.createTask('embedding', { text, options });
      
      const embedding = await this.embeddingService.generateEmbedding(text, {
        model: options.model || this.configService.get('AI_EMBEDDING_MODEL', 'nomic-embed-text'),
        normalize: options.normalize !== false,
      });

      this.completeTask(taskId, embedding);
      return embedding;
    } catch (error) {
      this.logger.error('Failed to generate embedding:', error);
      throw error;
    }
  }

  /**
   * 向量搜索
   */
  async searchSimilar(
    query: string | number[],
    options: {
      collection?: string;
      limit?: number;
      threshold?: number;
      filters?: Record<string, any>;
      includeMetadata?: boolean;
    } = {}
  ): Promise<Array<{
    id: string;
    content: string;
    similarity: number;
    metadata?: Record<string, any>;
  }>> {
    try {
      const taskId = this.createTask('search', { query, options });
      
      let queryVector: number[];
      if (typeof query === 'string') {
        queryVector = await this.generateEmbedding(query);
      } else {
        queryVector = query;
      }

      const results = await this.vectorSearchService.searchSimilar(queryVector, {
        collection: options.collection || 'default',
        limit: options.limit || 10,
        threshold: options.threshold || 0.7,
        filters: options.filters,
        includeMetadata: options.includeMetadata !== false,
      });

      this.completeTask(taskId, results);
      return results;
    } catch (error) {
      this.logger.error('Failed to search similar content:', error);
      throw error;
    }
  }

  /**
   * 内容分析
   */
  async analyzeContent(
    content: string,
    type: 'text' | 'image' | 'video' | 'audio',
    options: {
      features?: string[];
      language?: string;
      metadata?: Record<string, any>;
    } = {}
  ): Promise<{
    summary: string;
    keywords: string[];
    topics: Array<{ name: string; confidence: number }>;
    sentiment: { score: number; label: string };
    entities: Array<{ text: string; type: string; confidence: number }>;
    difficulty: number;
    readability: number;
    metadata: Record<string, any>;
  }> {
    try {
      const taskId = this.createTask('analysis', { content, type, options });
      
      const analysis = await this.contentAnalysisService.analyzeContent(content, type, {
        features: options.features || ['summary', 'keywords', 'topics', 'sentiment', 'entities'],
        language: options.language || 'zh',
        metadata: options.metadata,
      });

      this.completeTask(taskId, analysis);
      return analysis;
    } catch (error) {
      this.logger.error('Failed to analyze content:', error);
      throw error;
    }
  }

  /**
   * 生成推荐
   */
  async generateRecommendations(
    userId: string,
    context: {
      type: 'content' | 'course' | 'learning_path';
      currentItem?: string;
      preferences?: Record<string, any>;
      history?: string[];
    },
    options: {
      limit?: number;
      algorithm?: 'collaborative' | 'content_based' | 'hybrid';
      includeExplanation?: boolean;
    } = {}
  ): Promise<Array<{
    id: string;
    type: string;
    title: string;
    score: number;
    reason: string;
    metadata?: Record<string, any>;
  }>> {
    try {
      const taskId = this.createTask('recommendation', { userId, context, options });
      
      const recommendations = await this.recommendationService.generateRecommendations(userId, context, {
        limit: options.limit || 10,
        algorithm: options.algorithm || 'hybrid',
        includeExplanation: options.includeExplanation !== false,
      });

      this.completeTask(taskId, recommendations);
      return recommendations;
    } catch (error) {
      this.logger.error('Failed to generate recommendations:', error);
      throw error;
    }
  }

  /**
   * 生成学习路径
   */
  async generateLearningPath(
    userId: string,
    goal: string,
    options: {
      currentLevel?: string;
      timeConstraint?: number;
      preferences?: Record<string, any>;
      includeAssessments?: boolean;
    } = {}
  ): Promise<{
    id: string;
    title: string;
    description: string;
    estimatedDuration: number;
    difficulty: string;
    steps: Array<{
      id: string;
      title: string;
      type: 'lesson' | 'practice' | 'assessment' | 'project';
      duration: number;
      prerequisites: string[];
      resources: Array<{
        type: string;
        url: string;
        title: string;
      }>;
    }>;
    milestones: Array<{
      title: string;
      description: string;
      criteria: string[];
    }>;
  }> {
    try {
      const taskId = this.createTask('learning_path', { userId, goal, options });
      
      const learningPath = await this.learningPathService.generateLearningPath(userId, goal, {
        currentLevel: options.currentLevel || 'beginner',
        timeConstraint: options.timeConstraint,
        preferences: options.preferences,
        includeAssessments: options.includeAssessments !== false,
      });

      this.completeTask(taskId, learningPath);
      return learningPath;
    } catch (error) {
      this.logger.error('Failed to generate learning path:', error);
      throw error;
    }
  }

  /**
   * 聊天对话
   */
  async chat(
    message: string,
    context: {
      conversationId?: string;
      userId?: string;
      sessionId?: string;
      history?: Array<{ role: 'user' | 'assistant'; content: string }>;
      systemPrompt?: string;
    } = {},
    options: {
      model?: string;
      temperature?: number;
      maxTokens?: number;
      stream?: boolean;
    } = {}
  ): Promise<{
    response: string;
    conversationId: string;
    metadata: {
      model: string;
      tokens: number;
      responseTime: number;
    };
  }> {
    try {
      const taskId = this.createTask('chat', { message, context, options });
      
      const result = await this.chatbotService.chat(message, context, {
        model: options.model || this.configService.get('AI_CHAT_MODEL', 'llama2'),
        temperature: options.temperature || 0.7,
        maxTokens: options.maxTokens || 1000,
        stream: options.stream || false,
      });

      this.completeTask(taskId, result);
      return result;
    } catch (error) {
      this.logger.error('Failed to process chat:', error);
      throw error;
    }
  }

  /**
   * 批量处理
   */
  async batchProcess(
    tasks: Array<{
      type: string;
      input: any;
      options?: any;
    }>,
    options: {
      parallel?: boolean;
      maxConcurrency?: number;
      onProgress?: (completed: number, total: number) => void;
    } = {}
  ): Promise<Array<{
    success: boolean;
    result?: any;
    error?: string;
  }>> {
    try {
      const results = [];
      const maxConcurrency = options.maxConcurrency || 5;
      
      if (options.parallel) {
        // 并行处理
        const chunks = this.chunkArray(tasks, maxConcurrency);
        
        for (const chunk of chunks) {
          const chunkResults = await Promise.allSettled(
            chunk.map(task => this.processTask(task))
          );
          
          results.push(...chunkResults.map(result => ({
            success: result.status === 'fulfilled',
            result: result.status === 'fulfilled' ? result.value : undefined,
            error: result.status === 'rejected' ? result.reason.message : undefined,
          })));
          
          if (options.onProgress) {
            options.onProgress(results.length, tasks.length);
          }
        }
      } else {
        // 串行处理
        for (let i = 0; i < tasks.length; i++) {
          try {
            const result = await this.processTask(tasks[i]);
            results.push({ success: true, result });
          } catch (error) {
            results.push({ success: false, error: error.message });
          }
          
          if (options.onProgress) {
            options.onProgress(i + 1, tasks.length);
          }
        }
      }

      return results;
    } catch (error) {
      this.logger.error('Failed to process batch tasks:', error);
      throw error;
    }
  }

  /**
   * 获取任务状态
   */
  getTask(taskId: string): AITask | null {
    return this.tasks.get(taskId) || null;
  }

  /**
   * 获取任务列表
   */
  getTasks(filter?: {
    type?: string;
    status?: string;
    limit?: number;
  }): AITask[] {
    let tasks = Array.from(this.tasks.values());

    if (filter?.type) {
      tasks = tasks.filter(task => task.type === filter.type);
    }

    if (filter?.status) {
      tasks = tasks.filter(task => task.status === filter.status);
    }

    tasks.sort((a, b) => b.startTime.getTime() - a.startTime.getTime());

    if (filter?.limit) {
      tasks = tasks.slice(0, filter.limit);
    }

    return tasks;
  }

  /**
   * 获取AI服务统计
   */
  async getStats(): Promise<{
    totalTasks: number;
    tasksByType: Record<string, number>;
    tasksByStatus: Record<string, number>;
    averageProcessingTime: number;
    successRate: number;
    resourceUsage: {
      memory: number;
      cpu: number;
      gpu?: number;
    };
  }> {
    const tasks = Array.from(this.tasks.values());
    const completedTasks = tasks.filter(t => t.status === 'completed');
    const failedTasks = tasks.filter(t => t.status === 'failed');

    const tasksByType: Record<string, number> = {};
    const tasksByStatus: Record<string, number> = {};

    for (const task of tasks) {
      tasksByType[task.type] = (tasksByType[task.type] || 0) + 1;
      tasksByStatus[task.status] = (tasksByStatus[task.status] || 0) + 1;
    }

    const averageProcessingTime = completedTasks.length > 0
      ? completedTasks.reduce((sum, task) => {
          return sum + (task.endTime!.getTime() - task.startTime.getTime());
        }, 0) / completedTasks.length
      : 0;

    const successRate = tasks.length > 0
      ? (completedTasks.length / tasks.length) * 100
      : 0;

    const resourceUsage = await this.getResourceUsage();

    return {
      totalTasks: tasks.length,
      tasksByType,
      tasksByStatus,
      averageProcessingTime,
      successRate,
      resourceUsage,
    };
  }

  private createTask(type: string, input: any): string {
    const taskId = `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const task: AITask = {
      id: taskId,
      type: type as any,
      status: 'pending',
      input,
      progress: 0,
      startTime: new Date(),
    };

    this.tasks.set(taskId, task);
    return taskId;
  }

  private completeTask(taskId: string, output: any): void {
    const task = this.tasks.get(taskId);
    if (task) {
      task.status = 'completed';
      task.output = output;
      task.progress = 100;
      task.endTime = new Date();
    }
  }

  private failTask(taskId: string, error: string): void {
    const task = this.tasks.get(taskId);
    if (task) {
      task.status = 'failed';
      task.error = error;
      task.endTime = new Date();
    }
  }

  private async processTask(task: { type: string; input: any; options?: any }): Promise<any> {
    switch (task.type) {
      case 'embedding':
        return await this.generateEmbedding(task.input.text, task.options);
      case 'analysis':
        return await this.analyzeContent(task.input.content, task.input.type, task.options);
      case 'recommendation':
        return await this.generateRecommendations(task.input.userId, task.input.context, task.options);
      case 'chat':
        return await this.chat(task.input.message, task.input.context, task.options);
      case 'search':
        return await this.searchSimilar(task.input.query, task.options);
      case 'learning_path':
        return await this.generateLearningPath(task.input.userId, task.input.goal, task.options);
      default:
        throw new Error(`Unknown task type: ${task.type}`);
    }
  }

  private chunkArray<T>(array: T[], chunkSize: number): T[][] {
    const chunks = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize));
    }
    return chunks;
  }

  private async getSystemStats(): Promise<any> {
    // 获取系统统计信息
    return {
      averageResponseTime: 1000,
      throughput: 10,
      accuracy: 0.95,
      memoryUsage: 1024,
      gpuUsage: 512,
      diskUsage: 2048,
    };
  }

  private async getResourceUsage(): Promise<any> {
    // 获取资源使用情况
    return {
      memory: 1024,
      cpu: 50,
      gpu: 30,
    };
  }
}
