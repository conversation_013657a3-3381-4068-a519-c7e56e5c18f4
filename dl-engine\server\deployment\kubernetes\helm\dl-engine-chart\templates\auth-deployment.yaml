{{- if .Values.services.auth.enabled }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "dl-engine.fullname" . }}-auth
  labels:
    {{- include "dl-engine.labels" . | nindent 4 }}
    app.kubernetes.io/component: auth
spec:
  replicas: {{ .Values.services.auth.replicaCount }}
  selector:
    matchLabels:
      {{- include "dl-engine.selectorLabels" . | nindent 6 }}
      app.kubernetes.io/component: auth
  template:
    metadata:
      annotations:
        checksum/config: {{ include (print $.Template.BasePath "/configmap.yaml") . | sha256sum }}
        checksum/secret: {{ include (print $.Template.BasePath "/secret.yaml") . | sha256sum }}
      labels:
        {{- include "dl-engine.selectorLabels" . | nindent 8 }}
        app.kubernetes.io/component: auth
    spec:
      {{- with .Values.global.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "dl-engine.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: auth
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.registry }}/{{ .Values.services.auth.image.repository }}:{{ .Values.services.auth.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - name: http
              containerPort: {{ .Values.services.auth.service.targetPort }}
              protocol: TCP
          env:
            - name: NODE_ENV
              valueFrom:
                configMapKeyRef:
                  name: {{ include "dl-engine.fullname" . }}-config
                  key: NODE_ENV
            - name: LOG_LEVEL
              valueFrom:
                configMapKeyRef:
                  name: {{ include "dl-engine.fullname" . }}-config
                  key: LOG_LEVEL
            - name: PORT
              value: "{{ .Values.services.auth.service.targetPort }}"
            - name: MYSQL_HOST
              valueFrom:
                configMapKeyRef:
                  name: {{ include "dl-engine.fullname" . }}-config
                  key: MYSQL_HOST
            - name: MYSQL_PORT
              valueFrom:
                configMapKeyRef:
                  name: {{ include "dl-engine.fullname" . }}-config
                  key: MYSQL_PORT
            - name: MYSQL_DATABASE
              valueFrom:
                configMapKeyRef:
                  name: {{ include "dl-engine.fullname" . }}-config
                  key: MYSQL_DATABASE
            - name: MYSQL_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "dl-engine.fullname" . }}-secret
                  key: MYSQL_PASSWORD
            - name: REDIS_HOST
              valueFrom:
                configMapKeyRef:
                  name: {{ include "dl-engine.fullname" . }}-config
                  key: REDIS_HOST
            - name: REDIS_PORT
              valueFrom:
                configMapKeyRef:
                  name: {{ include "dl-engine.fullname" . }}-config
                  key: REDIS_PORT
            - name: REDIS_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "dl-engine.fullname" . }}-secret
                  key: REDIS_PASSWORD
            - name: JWT_SECRET
              valueFrom:
                secretKeyRef:
                  name: {{ include "dl-engine.fullname" . }}-secret
                  key: JWT_SECRET
            - name: SMS_API_KEY
              valueFrom:
                secretKeyRef:
                  name: {{ include "dl-engine.fullname" . }}-secret
                  key: SMS_API_KEY
            - name: ENABLE_CHINESE_UI
              valueFrom:
                configMapKeyRef:
                  name: {{ include "dl-engine.fullname" . }}-config
                  key: ENABLE_CHINESE_UI
            - name: DEFAULT_LOCALE
              valueFrom:
                configMapKeyRef:
                  name: {{ include "dl-engine.fullname" . }}-config
                  key: DEFAULT_LOCALE
          livenessProbe:
            httpGet:
              path: /health
              port: http
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3
          readinessProbe:
            httpGet:
              path: /ready
              port: http
            initialDelaySeconds: 5
            periodSeconds: 5
            timeoutSeconds: 3
            failureThreshold: 3
          resources:
            {{- toYaml .Values.services.auth.resources | nindent 12 }}
          volumeMounts:
            - name: tmp
              mountPath: /tmp
            - name: var-cache
              mountPath: /var/cache
      volumes:
        - name: tmp
          emptyDir: {}
        - name: var-cache
          emptyDir: {}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
---
apiVersion: v1
kind: Service
metadata:
  name: {{ include "dl-engine.fullname" . }}-auth
  labels:
    {{- include "dl-engine.labels" . | nindent 4 }}
    app.kubernetes.io/component: auth
spec:
  type: {{ .Values.services.auth.service.type }}
  ports:
    - port: {{ .Values.services.auth.service.port }}
      targetPort: http
      protocol: TCP
      name: http
  selector:
    {{- include "dl-engine.selectorLabels" . | nindent 4 }}
    app.kubernetes.io/component: auth
{{- end }}
