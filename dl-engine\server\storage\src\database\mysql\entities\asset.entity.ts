import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { ProjectEntity } from './project.entity';

export enum AssetType {
  MODEL_3D = 'model_3d',
  TEXTURE = 'texture',
  MATERIAL = 'material',
  AUDIO = 'audio',
  VIDEO = 'video',
  IMAGE = 'image',
  SCRIPT = 'script',
  ANIMATION = 'animation',
  FONT = 'font',
  SHADER = 'shader',
}

export enum AssetStatus {
  UPLOADING = 'uploading',
  PROCESSING = 'processing',
  READY = 'ready',
  ERROR = 'error',
  DELETED = 'deleted',
}

@Entity('assets')
@Index(['projectId'])
@Index(['type'])
@Index(['status'])
@Index(['mimeType'])
export class AssetEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 200 })
  name: string;

  @Column({ type: 'varchar', length: 500 })
  originalName: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'enum', enum: AssetType })
  type: AssetType;

  @Column({ type: 'enum', enum: AssetStatus, default: AssetStatus.UPLOADING })
  status: AssetStatus;

  @Column({ type: 'varchar', length: 100 })
  mimeType: string;

  @Column({ type: 'bigint' })
  fileSize: number;

  @Column({ type: 'varchar', length: 32 })
  md5Hash: string;

  @Column({ type: 'varchar', length: 1000 })
  url: string;

  @Column({ type: 'varchar', length: 1000, nullable: true })
  thumbnailUrl: string;

  @Column({ type: 'json', nullable: true })
  metadata: {
    dimensions?: {
      width: number;
      height: number;
      depth?: number;
    };
    duration?: number; // 音频/视频时长（秒）
    format?: string;
    compression?: string;
    quality?: number;
    channels?: number; // 音频声道数
    sampleRate?: number; // 音频采样率
    bitrate?: number; // 比特率
    vertices?: number; // 3D模型顶点数
    triangles?: number; // 3D模型三角面数
    materials?: string[]; // 3D模型材质列表
    animations?: string[]; // 动画列表
    bones?: number; // 骨骼数量
    textureSize?: number; // 纹理大小
    colorSpace?: string; // 颜色空间
  };

  @Column({ type: 'json', nullable: true })
  processing: {
    status: 'pending' | 'processing' | 'completed' | 'failed';
    progress: number; // 0-100
    startedAt?: Date;
    completedAt?: Date;
    error?: string;
    tasks: {
      name: string;
      status: 'pending' | 'processing' | 'completed' | 'failed';
      progress: number;
      result?: any;
    }[];
  };

  @Column({ type: 'json', nullable: true })
  variants: {
    name: string; // 'low', 'medium', 'high', 'thumbnail'
    url: string;
    fileSize: number;
    metadata: Record<string, any>;
  }[];

  @Column({ type: 'json', nullable: true })
  tags: string[];

  @Column({ type: 'int', default: 0 })
  downloadCount: number;

  @Column({ type: 'int', default: 0 })
  usageCount: number;

  @Column({ type: 'datetime', nullable: true })
  lastAccessedAt: Date;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // 关联关系
  @Column({ type: 'uuid' })
  projectId: string;

  @ManyToOne(() => ProjectEntity, (project) => project.assets)
  @JoinColumn({ name: 'projectId' })
  project: ProjectEntity;

  // 虚拟字段
  get isReady(): boolean {
    return this.status === AssetStatus.READY;
  }

  get isProcessing(): boolean {
    return this.status === AssetStatus.PROCESSING || this.status === AssetStatus.UPLOADING;
  }

  get hasError(): boolean {
    return this.status === AssetStatus.ERROR;
  }

  get fileSizeFormatted(): string {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = this.fileSize;
    let unitIndex = 0;
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }
    
    return `${size.toFixed(2)} ${units[unitIndex]}`;
  }
}
