import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { ScheduleModule } from '@nestjs/schedule';
import { BackupService } from './backup.service';
import { MigrationService } from './migration.service';
import { BackupController } from './backup.controller';
import { MySQLModule } from '../database/mysql/mysql.module';
import { RedisModule } from '../database/redis/redis.module';
import { PostgreSQLModule } from '../database/postgresql/postgresql.module';
import { MinioModule } from '../minio/minio.module';

@Module({
  imports: [
    ConfigModule,
    ScheduleModule.forRoot(),
    MySQLModule,
    RedisModule,
    PostgreSQLModule,
    MinioModule,
  ],
  providers: [
    BackupService,
    MigrationService,
  ],
  controllers: [BackupController],
  exports: [
    BackupService,
    MigrationService,
  ],
})
export class BackupModule {}
