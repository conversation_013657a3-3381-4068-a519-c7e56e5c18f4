{"name": "@dl-engine/client", "version": "1.0.0", "description": "DL-Engine Web客户端 - 数字化学习引擎前端应用", "type": "module", "scripts": {"dev": "vite --host 0.0.0.0 --port 3000", "build": "tsc && vite build", "preview": "vite preview --host 0.0.0.0 --port 3000", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "type-check": "tsc --noEmit", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "analyze": "npx vite-bundle-analyzer"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.1", "antd": "^5.0.0", "@ant-design/icons": "^5.0.1", "@ant-design/colors": "^7.0.0", "@hookstate/core": "^4.0.1", "three": "^0.176.0", "@types/three": "^0.176.0", "three-stdlib": "^2.29.0", "@react-three/fiber": "^8.15.12", "@react-three/drei": "^9.96.1", "@react-three/postprocessing": "^2.15.11", "postprocessing": "^6.37.3", "rapier3d-compat": "^0.11.2", "@dimforge/rapier3d-compat": "^0.11.2", "i18next": "^23.7.16", "react-i18next": "^13.5.0", "i18next-browser-languagedetector": "^7.2.0", "i18next-http-backend": "^2.4.2", "axios": "^1.6.2", "socket.io-client": "^4.7.4", "simple-peer": "^9.11.1", "@types/simple-peer": "^9.11.8", "workbox-window": "^7.0.0", "workbox-precaching": "^7.0.0", "workbox-routing": "^7.0.0", "workbox-strategies": "^7.0.0", "idb": "^7.1.1", "dayjs": "^1.11.10", "lodash-es": "^4.17.21", "@types/lodash-es": "^4.17.12", "classnames": "^2.3.2", "react-helmet-async": "^2.0.4", "react-error-boundary": "^4.0.11", "react-hotkeys-hook": "^4.4.1", "react-use": "^17.4.2", "framer-motion": "^10.16.16", "lottie-react": "^2.4.0", "react-virtualized-auto-sizer": "^1.0.20", "react-window": "^1.8.8", "@types/react-window": "^1.8.8", "react-intersection-observer": "^9.5.3", "react-dropzone": "^14.2.3", "react-cropper": "^2.3.3", "cropperjs": "^1.6.1", "react-color": "^2.19.3", "@types/react-color": "^3.0.9", "react-markdown": "^9.0.1", "remark-gfm": "^4.0.0", "rehype-highlight": "^7.0.0", "highlight.js": "^11.9.0"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "vite": "^5.0.8", "vite-plugin-pwa": "^0.17.4", "vite-plugin-windicss": "^1.9.3", "windicss": "^3.5.6", "typescript": "^5.2.2", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "prettier": "^3.1.1", "vitest": "^1.0.4", "@vitest/ui": "^1.0.4", "jsdom": "^23.0.1", "@testing-library/react": "^14.1.2", "@testing-library/jest-dom": "^6.1.5", "@testing-library/user-event": "^14.5.1", "@storybook/react": "^7.6.6", "@storybook/react-vite": "^7.6.6", "@storybook/addon-essentials": "^7.6.6", "@storybook/addon-interactions": "^7.6.6", "@storybook/addon-links": "^7.6.6", "@storybook/blocks": "^7.6.6", "@storybook/testing-library": "^0.2.2", "vite-bundle-analyzer": "^0.7.0", "rollup-plugin-visualizer": "^5.12.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "keywords": ["dl-engine", "digital-learning", "education", "3d", "vr", "ar", "react", "typescript", "three.js", "antd"], "author": "DL-Engine Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/dl-engine/dl-engine.git", "directory": "client"}, "homepage": "https://dl-engine.org"}