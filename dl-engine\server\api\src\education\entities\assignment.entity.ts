import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
  Index
} from 'typeorm'
import { User } from '../../users/entities/user.entity'
import { Course } from './course.entity'
import { Lesson } from './lesson.entity'
import { Submission } from './submission.entity'
import { Grade } from './grade.entity'

/**
 * 作业状态枚举
 */
export enum AssignmentStatus {
  DRAFT = 'draft',             // 草稿
  PUBLISHED = 'published',     // 已发布
  CLOSED = 'closed',           // 已关闭
  ARCHIVED = 'archived'        // 已归档
}

/**
 * 作业类型枚举
 */
export enum AssignmentType {
  INDIVIDUAL = 'individual',   // 个人作业
  GROUP = 'group',             // 小组作业
  PEER_REVIEW = 'peer_review', // 同伴评议
  QUIZ = 'quiz',               // 测验
  PROJECT = 'project',         // 项目作业
  PRESENTATION = 'presentation', // 演示作业
  DISCUSSION = 'discussion',   // 讨论作业
  EXPERIMENT = 'experiment'    // 实验作业
}

/**
 * 提交类型枚举
 */
export enum SubmissionType {
  TEXT = 'text',               // 文本提交
  FILE = 'file',               // 文件提交
  URL = 'url',                 // 链接提交
  PROJECT = 'project',         // 项目提交
  SCENE = 'scene',             // 场景提交
  MULTIMEDIA = 'multimedia'    // 多媒体提交
}

/**
 * 作业实体
 * 
 * 存储作业的详细信息和配置
 */
@Entity('assignments')
@Index(['courseId', 'status'])
@Index(['lessonId', 'status'])
@Index(['createdById'])
@Index(['dueDate'])
export class Assignment {
  @PrimaryGeneratedColumn('uuid')
  id: string

  /**
   * 作业标题
   */
  @Column({ type: 'varchar', length: 200 })
  title: string

  /**
   * 作业描述
   */
  @Column({ type: 'text' })
  description: string

  /**
   * 作业说明
   */
  @Column({ type: 'text', nullable: true })
  instructions?: string

  /**
   * 作业状态
   */
  @Column({
    type: 'enum',
    enum: AssignmentStatus,
    default: AssignmentStatus.DRAFT
  })
  status: AssignmentStatus

  /**
   * 作业类型
   */
  @Column({
    type: 'enum',
    enum: AssignmentType,
    default: AssignmentType.INDIVIDUAL
  })
  type: AssignmentType

  /**
   * 提交类型
   */
  @Column({
    type: 'enum',
    enum: SubmissionType,
    default: SubmissionType.TEXT
  })
  submissionType: SubmissionType

  /**
   * 所属课程ID
   */
  @Column({ type: 'uuid' })
  courseId: string

  /**
   * 所属课时ID（可选）
   */
  @Column({ type: 'uuid', nullable: true })
  lessonId?: string

  /**
   * 创建者ID
   */
  @Column({ type: 'uuid' })
  createdById: string

  /**
   * 总分
   */
  @Column({ type: 'int', default: 100 })
  totalPoints: number

  /**
   * 权重（在课程总成绩中的占比）
   */
  @Column({ type: 'decimal', precision: 5, scale: 2, default: 1.0 })
  weight: number

  /**
   * 截止日期
   */
  @Column({ type: 'timestamp', nullable: true })
  dueDate?: Date

  /**
   * 开放日期
   */
  @Column({ type: 'timestamp', nullable: true })
  availableFrom?: Date

  /**
   * 关闭日期
   */
  @Column({ type: 'timestamp', nullable: true })
  availableUntil?: Date

  /**
   * 作业配置
   */
  @Column({ type: 'json', nullable: true })
  configuration?: {
    // 提交设置
    submission: {
      allowLateSubmission: boolean
      latePenaltyPerDay?: number
      maxLateDays?: number
      allowMultipleSubmissions: boolean
      maxSubmissions?: number
      requireFiles: boolean
      allowedFileTypes?: string[]
      maxFileSize?: number // MB
      maxFiles?: number
    }
    // 评分设置
    grading: {
      autoGrade: boolean
      rubricId?: string
      blindGrading: boolean
      requireComments: boolean
      gradingScale: 'points' | 'percentage' | 'letter'
      passingGrade: number
    }
    // 小组设置（仅小组作业）
    group?: {
      minSize: number
      maxSize: number
      allowSelfSelection: boolean
      randomAssignment: boolean
      sameGradeForAll: boolean
    }
    // 同伴评议设置
    peerReview?: {
      enabled: boolean
      reviewsPerStudent: number
      reviewDeadline?: Date
      anonymousReviews: boolean
      reviewRubricId?: string
    }
    // 抄袭检测
    plagiarismDetection?: {
      enabled: boolean
      threshold: number
      checkAgainstInternet: boolean
      checkAgainstDatabase: boolean
    }
  }

  /**
   * 评分标准
   */
  @Column({ type: 'json', nullable: true })
  rubric?: {
    criteria: Array<{
      id: string
      name: string
      description: string
      maxPoints: number
      levels: Array<{
        name: string
        description: string
        points: number
      }>
    }>
    totalPoints: number
  }

  /**
   * 附件资源
   */
  @Column({ type: 'json', nullable: true })
  resources?: Array<{
    type: 'file' | 'url' | 'project' | 'scene'
    name: string
    url: string
    description?: string
    required: boolean
  }>

  /**
   * 学习目标
   */
  @Column({ type: 'json', nullable: true })
  learningObjectives?: string[]

  /**
   * 评估标准
   */
  @Column({ type: 'json', nullable: true })
  assessmentCriteria?: Array<{
    criterion: string
    description: string
    weight: number
  }>

  /**
   * 作业统计
   */
  @Column({ type: 'json', nullable: true })
  statistics?: {
    totalSubmissions: number
    onTimeSubmissions: number
    lateSubmissions: number
    gradedSubmissions: number
    averageGrade: number
    highestGrade: number
    lowestGrade: number
    averageTimeSpent: number // 分钟
    completionRate: number
  }

  /**
   * 发布时间
   */
  @Column({ type: 'timestamp', nullable: true })
  publishedAt?: Date

  /**
   * 创建时间
   */
  @CreateDateColumn()
  createdAt: Date

  /**
   * 更新时间
   */
  @UpdateDateColumn()
  updatedAt: Date

  // 关联关系
  @ManyToOne(() => Course, course => course.assignments)
  @JoinColumn({ name: 'courseId' })
  course: Course

  @ManyToOne(() => Lesson, { nullable: true })
  @JoinColumn({ name: 'lessonId' })
  lesson?: Lesson

  @ManyToOne(() => User)
  @JoinColumn({ name: 'createdById' })
  createdBy: User

  @OneToMany(() => Submission, submission => submission.assignment, { cascade: true })
  submissions: Submission[]

  @OneToMany(() => Grade, grade => grade.assignment, { cascade: true })
  grades: Grade[]

  /**
   * 检查作业是否已发布
   */
  isPublished(): boolean {
    return this.status === AssignmentStatus.PUBLISHED
  }

  /**
   * 检查作业是否可用
   */
  isAvailable(): boolean {
    if (!this.isPublished()) return false
    
    const now = new Date()
    
    if (this.availableFrom && now < this.availableFrom) return false
    if (this.availableUntil && now > this.availableUntil) return false
    
    return true
  }

  /**
   * 检查是否已过期
   */
  isOverdue(): boolean {
    if (!this.dueDate) return false
    return new Date() > this.dueDate
  }

  /**
   * 检查是否接受迟交
   */
  acceptsLateSubmission(): boolean {
    return this.configuration?.submission?.allowLateSubmission || false
  }

  /**
   * 计算迟交天数
   */
  getLateDays(submissionDate: Date): number {
    if (!this.dueDate || submissionDate <= this.dueDate) return 0
    
    const diffTime = submissionDate.getTime() - this.dueDate.getTime()
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  }

  /**
   * 计算迟交扣分
   */
  getLatePenalty(submissionDate: Date): number {
    const lateDays = this.getLateDays(submissionDate)
    if (lateDays === 0) return 0
    
    const penaltyPerDay = this.configuration?.submission?.latePenaltyPerDay || 0
    const maxLateDays = this.configuration?.submission?.maxLateDays
    
    if (maxLateDays && lateDays > maxLateDays) {
      return this.totalPoints // 超过最大迟交天数，扣除全部分数
    }
    
    return Math.min(lateDays * penaltyPerDay, this.totalPoints)
  }

  /**
   * 获取剩余时间（小时）
   */
  getTimeRemaining(): number | null {
    if (!this.dueDate) return null
    
    const now = new Date()
    const diff = this.dueDate.getTime() - now.getTime()
    return Math.max(0, diff / (1000 * 60 * 60))
  }

  /**
   * 检查是否为小组作业
   */
  isGroupAssignment(): boolean {
    return this.type === AssignmentType.GROUP
  }

  /**
   * 检查是否启用同伴评议
   */
  hasPeerReview(): boolean {
    return this.configuration?.peerReview?.enabled || false
  }

  /**
   * 检查是否自动评分
   */
  isAutoGraded(): boolean {
    return this.configuration?.grading?.autoGrade || false
  }

  /**
   * 更新统计信息
   */
  updateStatistics(field: keyof Assignment['statistics'], value: number): void {
    if (!this.statistics) {
      this.statistics = {
        totalSubmissions: 0,
        onTimeSubmissions: 0,
        lateSubmissions: 0,
        gradedSubmissions: 0,
        averageGrade: 0,
        highestGrade: 0,
        lowestGrade: 0,
        averageTimeSpent: 0,
        completionRate: 0
      }
    }

    this.statistics[field] = value
  }

  /**
   * 获取完成率
   */
  getCompletionRate(totalStudents: number): number {
    if (totalStudents === 0) return 0
    const submissions = this.statistics?.totalSubmissions || 0
    return (submissions / totalStudents) * 100
  }

  /**
   * 获取及格率
   */
  getPassingRate(): number {
    const totalGraded = this.statistics?.gradedSubmissions || 0
    if (totalGraded === 0) return 0
    
    const passingGrade = this.configuration?.grading?.passingGrade || 60
    // 这里需要实际计算及格的提交数量
    // 暂时返回0，实际实现时需要查询数据库
    return 0
  }

  /**
   * 检查文件类型是否允许
   */
  isFileTypeAllowed(fileType: string): boolean {
    const allowedTypes = this.configuration?.submission?.allowedFileTypes
    if (!allowedTypes || allowedTypes.length === 0) return true
    
    return allowedTypes.includes(fileType)
  }

  /**
   * 检查文件大小是否超限
   */
  isFileSizeExceeded(fileSizeInMB: number): boolean {
    const maxSize = this.configuration?.submission?.maxFileSize
    if (!maxSize) return false
    
    return fileSizeInMB > maxSize
  }

  /**
   * 获取作业URL友好的slug
   */
  getSlug(): string {
    return this.title
      .toLowerCase()
      .replace(/[^a-z0-9\u4e00-\u9fa5]/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '')
  }
}
