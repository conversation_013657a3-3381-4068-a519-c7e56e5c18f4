import { Modu<PERSON> } from '@nestjs/common'
import { TypeOrmModule } from '@nestjs/typeorm'
import { ConfigModule } from '@nestjs/config'

import { CollaborationSession } from './entities/collaboration-session.entity'
import { CollaborationParticipant } from './entities/collaboration-participant.entity'
import { CollaborationOperation } from './entities/collaboration-operation.entity'
import { CollaborationConflict } from './entities/collaboration-conflict.entity'
import { CollaborationPermission } from './entities/collaboration-permission.entity'
import { CollaborationHistory } from './entities/collaboration-history.entity'
import { CollaborationLock } from './entities/collaboration-lock.entity'
import { CollaborationComment } from './entities/collaboration-comment.entity'

import { CollaborationSessionsController } from './controllers/collaboration-sessions.controller'
import { CollaborationParticipantsController } from './controllers/collaboration-participants.controller'
import { CollaborationOperationsController } from './controllers/collaboration-operations.controller'
import { CollaborationConflictsController } from './controllers/collaboration-conflicts.controller'
import { CollaborationPermissionsController } from './controllers/collaboration-permissions.controller'
import { CollaborationHistoryController } from './controllers/collaboration-history.controller'
import { CollaborationLocksController } from './controllers/collaboration-locks.controller'
import { CollaborationCommentsController } from './controllers/collaboration-comments.controller'

import { CollaborationSessionsService } from './services/collaboration-sessions.service'
import { CollaborationParticipantsService } from './services/collaboration-participants.service'
import { CollaborationOperationsService } from './services/collaboration-operations.service'
import { CollaborationConflictsService } from './services/collaboration-conflicts.service'
import { CollaborationPermissionsService } from './services/collaboration-permissions.service'
import { CollaborationHistoryService } from './services/collaboration-history.service'
import { CollaborationLocksService } from './services/collaboration-locks.service'
import { CollaborationCommentsService } from './services/collaboration-comments.service'
import { RealTimeCollaborationService } from './services/real-time-collaboration.service'
import { OperationalTransformService } from './services/operational-transform.service'
import { ConflictResolutionService } from './services/conflict-resolution.service'

import { UsersModule } from '../users/users.module'
import { ProjectsModule } from '../projects/projects.module'
import { ScenesModule } from '../scenes/scenes.module'
import { NotificationModule } from '../notifications/notification.module'

/**
 * 协作功能模块
 * 
 * 支持多人教学协作的功能，包括：
 * - 实时协作会话管理
 * - 协作参与者管理
 * - 操作同步和冲突解决
 * - 版本冲突处理
 * - 权限同步管理
 * - 操作历史记录
 * - 资源锁定机制
 * - 协作评论系统
 */
@Module({
  imports: [
    ConfigModule,
    TypeOrmModule.forFeature([
      CollaborationSession,
      CollaborationParticipant,
      CollaborationOperation,
      CollaborationConflict,
      CollaborationPermission,
      CollaborationHistory,
      CollaborationLock,
      CollaborationComment
    ]),
    UsersModule,
    ProjectsModule,
    ScenesModule,
    NotificationModule
  ],
  controllers: [
    CollaborationSessionsController,
    CollaborationParticipantsController,
    CollaborationOperationsController,
    CollaborationConflictsController,
    CollaborationPermissionsController,
    CollaborationHistoryController,
    CollaborationLocksController,
    CollaborationCommentsController
  ],
  providers: [
    CollaborationSessionsService,
    CollaborationParticipantsService,
    CollaborationOperationsService,
    CollaborationConflictsService,
    CollaborationPermissionsService,
    CollaborationHistoryService,
    CollaborationLocksService,
    CollaborationCommentsService,
    RealTimeCollaborationService,
    OperationalTransformService,
    ConflictResolutionService
  ],
  exports: [
    CollaborationSessionsService,
    CollaborationParticipantsService,
    CollaborationOperationsService,
    CollaborationConflictsService,
    CollaborationPermissionsService,
    CollaborationHistoryService,
    CollaborationLocksService,
    CollaborationCommentsService,
    RealTimeCollaborationService,
    OperationalTransformService,
    ConflictResolutionService
  ]
})
export class CollaborationModule {}
