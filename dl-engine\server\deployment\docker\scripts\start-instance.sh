#!/bin/bash

# DL-Engine 实例服务启动脚本
# 支持Agones游戏服务器的启动和管理

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    if [ "$LOG_LEVEL" = "debug" ]; then
        echo -e "${BLUE}[DEBUG]${NC} $1"
    fi
}

# 检查环境变量
check_env() {
    log_info "检查环境变量..."
    
    # 必需的环境变量
    required_vars=(
        "NODE_ENV"
        "PORT"
        "WEBSOCKET_PORT"
        "WEBRTC_PORT"
    )
    
    for var in "${required_vars[@]}"; do
        if [ -z "${!var}" ]; then
            log_error "环境变量 $var 未设置"
            exit 1
        fi
    done
    
    log_info "环境变量检查完成"
}

# 检查Agones SDK
check_agones() {
    log_info "检查Agones SDK..."
    
    if [ -f "/usr/local/bin/agones-sdk" ]; then
        log_info "Agones SDK 已安装"
        
        # 检查是否在Kubernetes环境中
        if [ -n "$KUBERNETES_SERVICE_HOST" ]; then
            log_info "检测到Kubernetes环境，启用Agones支持"
            export AGONES_ENABLED=true
        else
            log_warn "非Kubernetes环境，禁用Agones支持"
            export AGONES_ENABLED=false
        fi
    else
        log_warn "Agones SDK 未找到，禁用Agones支持"
        export AGONES_ENABLED=false
    fi
}

# 启动Agones SDK（如果需要）
start_agones_sdk() {
    if [ "$AGONES_ENABLED" = "true" ]; then
        log_info "启动Agones SDK..."
        
        # 在后台启动Agones SDK
        /usr/local/bin/agones-sdk --local &
        AGONES_PID=$!
        
        # 等待Agones SDK启动
        sleep 2
        
        # 检查Agones SDK是否正常运行
        if kill -0 $AGONES_PID 2>/dev/null; then
            log_info "Agones SDK 启动成功 (PID: $AGONES_PID)"
            export AGONES_SDK_PID=$AGONES_PID
        else
            log_error "Agones SDK 启动失败"
            exit 1
        fi
    fi
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    # 检查端口是否可用
    if ! nc -z localhost $WEBSOCKET_PORT; then
        log_error "WebSocket端口 $WEBSOCKET_PORT 不可用"
        return 1
    fi
    
    if ! nc -z localhost $WEBRTC_PORT; then
        log_error "WebRTC端口 $WEBRTC_PORT 不可用"
        return 1
    fi
    
    log_info "健康检查通过"
    return 0
}

# 清理函数
cleanup() {
    log_info "执行清理操作..."
    
    # 停止Agones SDK
    if [ -n "$AGONES_SDK_PID" ]; then
        log_info "停止Agones SDK (PID: $AGONES_SDK_PID)"
        kill $AGONES_SDK_PID 2>/dev/null || true
        wait $AGONES_SDK_PID 2>/dev/null || true
    fi
    
    # 停止Node.js应用
    if [ -n "$NODE_PID" ]; then
        log_info "停止Node.js应用 (PID: $NODE_PID)"
        kill $NODE_PID 2>/dev/null || true
        wait $NODE_PID 2>/dev/null || true
    fi
    
    log_info "清理完成"
}

# 信号处理
trap cleanup SIGTERM SIGINT

# 主函数
main() {
    log_info "启动DL-Engine实例服务..."
    log_info "版本: 1.0.0"
    log_info "环境: $NODE_ENV"
    log_info "端口: UDP $PORT, TCP $WEBSOCKET_PORT, TCP $WEBRTC_PORT"
    
    # 执行检查
    check_env
    check_agones
    
    # 启动Agones SDK
    start_agones_sdk
    
    # 等待一下确保所有服务都准备好
    sleep 1
    
    # 启动Node.js应用
    log_info "启动Node.js应用..."
    node dl-engine/server/instance/dist/index.js &
    NODE_PID=$!
    
    # 等待应用启动
    sleep 5
    
    # 执行健康检查
    if health_check; then
        log_info "DL-Engine实例服务启动成功"
        
        # 如果启用了Agones，标记GameServer为Ready
        if [ "$AGONES_ENABLED" = "true" ]; then
            log_info "标记GameServer为Ready状态"
            curl -X POST http://localhost:59358/ready || log_warn "无法标记GameServer为Ready"
        fi
    else
        log_error "健康检查失败，退出"
        cleanup
        exit 1
    fi
    
    # 等待进程结束
    wait $NODE_PID
    
    # 清理
    cleanup
}

# 执行主函数
main "$@"
