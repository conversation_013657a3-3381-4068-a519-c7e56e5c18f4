import { Injectable, Logger } from '@nestjs/common';
import { RedisService } from './redis.service';

export interface QueueJob<T = any> {
  id: string;
  data: T;
  priority: number;
  delay?: number; // 延迟执行时间（毫秒）
  attempts: number;
  maxAttempts: number;
  createdAt: Date;
  processedAt?: Date;
  completedAt?: Date;
  failedAt?: Date;
  error?: string;
  metadata?: Record<string, any>;
}

export interface QueueOptions {
  priority?: number; // 优先级（数字越大优先级越高）
  delay?: number; // 延迟时间（毫秒）
  maxAttempts?: number; // 最大重试次数
  timeout?: number; // 任务超时时间（毫秒）
  metadata?: Record<string, any>;
}

export interface QueueStats {
  waiting: number;
  active: number;
  completed: number;
  failed: number;
  delayed: number;
  total: number;
}

@Injectable()
export class QueueService {
  private readonly logger = new Logger(QueueService.name);
  private readonly queuePrefix = 'dl:queue:';
  private readonly jobPrefix = 'dl:job:';
  private readonly defaultMaxAttempts = 3;

  constructor(private redisService: RedisService) {}

  /**
   * 添加任务到队列
   */
  async addJob<T>(
    queueName: string,
    data: T,
    options: QueueOptions = {}
  ): Promise<string> {
    const {
      priority = 0,
      delay = 0,
      maxAttempts = this.defaultMaxAttempts,
      timeout = 30000,
      metadata = {},
    } = options;

    const jobId = this.generateJobId();
    const job: QueueJob<T> = {
      id: jobId,
      data,
      priority,
      delay,
      attempts: 0,
      maxAttempts,
      createdAt: new Date(),
      metadata: {
        ...metadata,
        timeout,
      },
    };

    try {
      const jobKey = `${this.jobPrefix}${jobId}`;
      
      // 保存任务数据
      await this.redisService.set(jobKey, job);

      if (delay > 0) {
        // 延迟任务
        const delayedKey = `${this.queuePrefix}${queueName}:delayed`;
        const executeAt = Date.now() + delay;
        await this.redisService.zadd(delayedKey, executeAt, jobId);
      } else {
        // 立即执行任务
        const waitingKey = `${this.queuePrefix}${queueName}:waiting`;
        await this.redisService.zadd(waitingKey, priority, jobId);
      }

      this.logger.debug(`Job added to queue ${queueName}: ${jobId}`);
      return jobId;
    } catch (error) {
      this.logger.error(`Failed to add job to queue ${queueName}:`, error);
      throw error;
    }
  }

  /**
   * 获取下一个任务
   */
  async getNextJob(queueName: string): Promise<QueueJob | null> {
    try {
      // 首先处理延迟任务
      await this.processDelayedJobs(queueName);

      const waitingKey = `${this.queuePrefix}${queueName}:waiting`;
      const activeKey = `${this.queuePrefix}${queueName}:active`;

      // 获取优先级最高的任务
      const jobIds = await this.redisService.zrange<string>(waitingKey, -1, -1);
      
      if (jobIds.length === 0) {
        return null;
      }

      const jobId = jobIds[0];
      const jobKey = `${this.jobPrefix}${jobId}`;
      
      // 获取任务数据
      const job = await this.redisService.get<QueueJob>(jobKey);
      
      if (!job) {
        // 清理无效的任务ID
        await this.redisService.zrem(waitingKey, jobId);
        return null;
      }

      // 移动到活跃队列
      await this.redisService.zrem(waitingKey, jobId);
      await this.redisService.zadd(activeKey, Date.now(), jobId);

      // 更新任务状态
      job.attempts++;
      job.processedAt = new Date();
      await this.redisService.set(jobKey, job);

      this.logger.debug(`Job retrieved from queue ${queueName}: ${jobId}`);
      return job;
    } catch (error) {
      this.logger.error(`Failed to get next job from queue ${queueName}:`, error);
      return null;
    }
  }

  /**
   * 完成任务
   */
  async completeJob(queueName: string, jobId: string, result?: any): Promise<boolean> {
    try {
      const jobKey = `${this.jobPrefix}${jobId}`;
      const activeKey = `${this.queuePrefix}${queueName}:active`;
      const completedKey = `${this.queuePrefix}${queueName}:completed`;

      const job = await this.redisService.get<QueueJob>(jobKey);
      
      if (!job) {
        return false;
      }

      // 更新任务状态
      job.completedAt = new Date();
      if (result !== undefined) {
        job.metadata = { ...job.metadata, result };
      }

      await this.redisService.set(jobKey, job);

      // 移动到完成队列
      await this.redisService.zrem(activeKey, jobId);
      await this.redisService.zadd(completedKey, Date.now(), jobId);

      this.logger.debug(`Job completed in queue ${queueName}: ${jobId}`);
      return true;
    } catch (error) {
      this.logger.error(`Failed to complete job ${jobId}:`, error);
      return false;
    }
  }

  /**
   * 任务失败
   */
  async failJob(queueName: string, jobId: string, error: string): Promise<boolean> {
    try {
      const jobKey = `${this.jobPrefix}${jobId}`;
      const activeKey = `${this.queuePrefix}${queueName}:active`;
      const waitingKey = `${this.queuePrefix}${queueName}:waiting`;
      const failedKey = `${this.queuePrefix}${queueName}:failed`;

      const job = await this.redisService.get<QueueJob>(jobKey);
      
      if (!job) {
        return false;
      }

      job.error = error;
      job.failedAt = new Date();

      // 检查是否需要重试
      if (job.attempts < job.maxAttempts) {
        // 重新加入等待队列
        await this.redisService.zrem(activeKey, jobId);
        await this.redisService.zadd(waitingKey, job.priority, jobId);
        
        this.logger.debug(`Job retry scheduled for queue ${queueName}: ${jobId} (attempt ${job.attempts}/${job.maxAttempts})`);
      } else {
        // 移动到失败队列
        await this.redisService.zrem(activeKey, jobId);
        await this.redisService.zadd(failedKey, Date.now(), jobId);
        
        this.logger.warn(`Job failed permanently in queue ${queueName}: ${jobId}`);
      }

      await this.redisService.set(jobKey, job);
      return true;
    } catch (error) {
      this.logger.error(`Failed to fail job ${jobId}:`, error);
      return false;
    }
  }

  /**
   * 获取任务详情
   */
  async getJob(jobId: string): Promise<QueueJob | null> {
    try {
      const jobKey = `${this.jobPrefix}${jobId}`;
      return await this.redisService.get<QueueJob>(jobKey);
    } catch (error) {
      this.logger.error(`Failed to get job ${jobId}:`, error);
      return null;
    }
  }

  /**
   * 删除任务
   */
  async removeJob(queueName: string, jobId: string): Promise<boolean> {
    try {
      const jobKey = `${this.jobPrefix}${jobId}`;
      const queueKeys = [
        `${this.queuePrefix}${queueName}:waiting`,
        `${this.queuePrefix}${queueName}:active`,
        `${this.queuePrefix}${queueName}:completed`,
        `${this.queuePrefix}${queueName}:failed`,
        `${this.queuePrefix}${queueName}:delayed`,
      ];

      // 从所有队列中移除
      for (const queueKey of queueKeys) {
        await this.redisService.zrem(queueKey, jobId);
      }

      // 删除任务数据
      const deleted = await this.redisService.del(jobKey);
      
      this.logger.debug(`Job removed from queue ${queueName}: ${jobId}`);
      return deleted > 0;
    } catch (error) {
      this.logger.error(`Failed to remove job ${jobId}:`, error);
      return false;
    }
  }

  /**
   * 获取队列统计信息
   */
  async getQueueStats(queueName: string): Promise<QueueStats> {
    try {
      const waitingKey = `${this.queuePrefix}${queueName}:waiting`;
      const activeKey = `${this.queuePrefix}${queueName}:active`;
      const completedKey = `${this.queuePrefix}${queueName}:completed`;
      const failedKey = `${this.queuePrefix}${queueName}:failed`;
      const delayedKey = `${this.queuePrefix}${queueName}:delayed`;

      const [waiting, active, completed, failed, delayed] = await Promise.all([
        this.redisService.zcard(waitingKey),
        this.redisService.zcard(activeKey),
        this.redisService.zcard(completedKey),
        this.redisService.zcard(failedKey),
        this.redisService.zcard(delayedKey),
      ]);

      return {
        waiting,
        active,
        completed,
        failed,
        delayed,
        total: waiting + active + completed + failed + delayed,
      };
    } catch (error) {
      this.logger.error(`Failed to get queue stats for ${queueName}:`, error);
      return {
        waiting: 0,
        active: 0,
        completed: 0,
        failed: 0,
        delayed: 0,
        total: 0,
      };
    }
  }

  /**
   * 清空队列
   */
  async clearQueue(queueName: string, status?: 'waiting' | 'active' | 'completed' | 'failed' | 'delayed'): Promise<number> {
    try {
      let deletedCount = 0;
      const queueKeys = status 
        ? [`${this.queuePrefix}${queueName}:${status}`]
        : [
            `${this.queuePrefix}${queueName}:waiting`,
            `${this.queuePrefix}${queueName}:active`,
            `${this.queuePrefix}${queueName}:completed`,
            `${this.queuePrefix}${queueName}:failed`,
            `${this.queuePrefix}${queueName}:delayed`,
          ];

      for (const queueKey of queueKeys) {
        // 获取所有任务ID
        const jobIds = await this.redisService.zrange<string>(queueKey, 0, -1);
        
        // 删除任务数据
        for (const jobId of jobIds) {
          const jobKey = `${this.jobPrefix}${jobId}`;
          await this.redisService.del(jobKey);
        }

        // 清空队列
        const deleted = await this.redisService.del(queueKey);
        deletedCount += jobIds.length;
      }

      this.logger.log(`Cleared ${deletedCount} jobs from queue ${queueName}${status ? `:${status}` : ''}`);
      return deletedCount;
    } catch (error) {
      this.logger.error(`Failed to clear queue ${queueName}:`, error);
      return 0;
    }
  }

  /**
   * 重试失败的任务
   */
  async retryFailedJobs(queueName: string, jobIds?: string[]): Promise<number> {
    try {
      const failedKey = `${this.queuePrefix}${queueName}:failed`;
      const waitingKey = `${this.queuePrefix}${queueName}:waiting`;

      let targetJobIds = jobIds;
      
      if (!targetJobIds) {
        targetJobIds = await this.redisService.zrange<string>(failedKey, 0, -1);
      }

      let retriedCount = 0;

      for (const jobId of targetJobIds) {
        const jobKey = `${this.jobPrefix}${jobId}`;
        const job = await this.redisService.get<QueueJob>(jobKey);

        if (job) {
          // 重置任务状态
          job.attempts = 0;
          job.error = undefined;
          job.failedAt = undefined;
          job.processedAt = undefined;

          await this.redisService.set(jobKey, job);

          // 移动到等待队列
          await this.redisService.zrem(failedKey, jobId);
          await this.redisService.zadd(waitingKey, job.priority, jobId);

          retriedCount++;
        }
      }

      this.logger.log(`Retried ${retriedCount} failed jobs in queue ${queueName}`);
      return retriedCount;
    } catch (error) {
      this.logger.error(`Failed to retry failed jobs in queue ${queueName}:`, error);
      return 0;
    }
  }

  private async processDelayedJobs(queueName: string): Promise<void> {
    try {
      const delayedKey = `${this.queuePrefix}${queueName}:delayed`;
      const waitingKey = `${this.queuePrefix}${queueName}:waiting`;
      const now = Date.now();

      // 获取到期的延迟任务
      const jobIds = await this.redisService.zrangebyscore(delayedKey, 0, now);

      for (const jobId of jobIds) {
        const jobKey = `${this.jobPrefix}${jobId}`;
        const job = await this.redisService.get<QueueJob>(jobKey);

        if (job) {
          // 移动到等待队列
          await this.redisService.zrem(delayedKey, jobId);
          await this.redisService.zadd(waitingKey, job.priority, jobId);
        }
      }
    } catch (error) {
      this.logger.error(`Failed to process delayed jobs for queue ${queueName}:`, error);
    }
  }

  private async zcard(key: string): Promise<number> {
    try {
      return await this.redisService.getClient().zCard(key);
    } catch (error) {
      return 0;
    }
  }

  private async zrem(key: string, member: any): Promise<number> {
    try {
      return await this.redisService.getClient().zRem(key, member);
    } catch (error) {
      return 0;
    }
  }

  private async zrangebyscore(key: string, min: number, max: number): Promise<string[]> {
    try {
      return await this.redisService.getClient().zRangeByScore(key, min, max);
    } catch (error) {
      return [];
    }
  }

  private generateJobId(): string {
    return `job_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
