{{/*
Expand the name of the chart.
*/}}
{{- define "dl-engine.name" -}}
{{- default .Chart.Name .Values.nameOverride | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Create a default fully qualified app name.
We truncate at 63 chars because some Kubernetes name fields are limited to this (by the DNS naming spec).
If release name contains chart name it will be used as a full name.
*/}}
{{- define "dl-engine.fullname" -}}
{{- if .Values.fullnameOverride }}
{{- .Values.fullnameOverride | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- $name := default .Chart.Name .Values.nameOverride }}
{{- if contains $name .Release.Name }}
{{- .Release.Name | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- printf "%s-%s" .Release.Name $name | trunc 63 | trimSuffix "-" }}
{{- end }}
{{- end }}
{{- end }}

{{/*
Create chart name and version as used by the chart label.
*/}}
{{- define "dl-engine.chart" -}}
{{- printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Common labels
*/}}
{{- define "dl-engine.labels" -}}
helm.sh/chart: {{ include "dl-engine.chart" . }}
{{ include "dl-engine.selectorLabels" . }}
{{- if .Chart.AppVersion }}
app.kubernetes.io/version: {{ .Chart.AppVersion | quote }}
{{- end }}
app.kubernetes.io/managed-by: {{ .Release.Service }}
app.kubernetes.io/part-of: dl-engine
{{- end }}

{{/*
Selector labels
*/}}
{{- define "dl-engine.selectorLabels" -}}
app.kubernetes.io/name: {{ include "dl-engine.name" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
{{- end }}

{{/*
Create the name of the service account to use
*/}}
{{- define "dl-engine.serviceAccountName" -}}
{{- if .Values.serviceAccount.create }}
{{- default (include "dl-engine.fullname" .) .Values.serviceAccount.name }}
{{- else }}
{{- default "default" .Values.serviceAccount.name }}
{{- end }}
{{- end }}

{{/*
Create a default fully qualified MySQL name.
*/}}
{{- define "dl-engine.mysql.fullname" -}}
{{- if .Values.mysql.fullnameOverride }}
{{- .Values.mysql.fullnameOverride | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- $name := default "mysql" .Values.mysql.nameOverride }}
{{- printf "%s-%s" .Release.Name $name | trunc 63 | trimSuffix "-" }}
{{- end }}
{{- end }}

{{/*
Create a default fully qualified Redis name.
*/}}
{{- define "dl-engine.redis.fullname" -}}
{{- if .Values.redis.fullnameOverride }}
{{- .Values.redis.fullnameOverride | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- $name := default "redis" .Values.redis.nameOverride }}
{{- printf "%s-%s" .Release.Name $name | trunc 63 | trimSuffix "-" }}
{{- end }}
{{- end }}

{{/*
Create a default fully qualified PostgreSQL name.
*/}}
{{- define "dl-engine.postgresql.fullname" -}}
{{- if .Values.postgresql.fullnameOverride }}
{{- .Values.postgresql.fullnameOverride | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- $name := default "postgresql" .Values.postgresql.nameOverride }}
{{- printf "%s-%s" .Release.Name $name | trunc 63 | trimSuffix "-" }}
{{- end }}
{{- end }}

{{/*
Create a default fully qualified Minio name.
*/}}
{{- define "dl-engine.minio.fullname" -}}
{{- if .Values.minio.fullnameOverride }}
{{- .Values.minio.fullnameOverride | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- $name := default "minio" .Values.minio.nameOverride }}
{{- printf "%s-%s" .Release.Name $name | trunc 63 | trimSuffix "-" }}
{{- end }}
{{- end }}

{{/*
Generate certificates for DL-Engine
*/}}
{{- define "dl-engine.gen-certs" -}}
{{- $altNames := list ( printf "%s.%s" (include "dl-engine.name" .) .Release.Namespace ) ( printf "%s.%s.svc" (include "dl-engine.name" .) .Release.Namespace ) -}}
{{- $ca := genCA "dl-engine-ca" 365 -}}
{{- $cert := genSignedCert ( include "dl-engine.name" . ) nil $altNames 365 $ca -}}
tls.crt: {{ $cert.Cert | b64enc }}
tls.key: {{ $cert.Key | b64enc }}
{{- end }}

{{/*
Return the proper image name
*/}}
{{- define "dl-engine.image" -}}
{{- $registryName := .Values.image.registry -}}
{{- $repositoryName := .repository -}}
{{- $tag := .tag | toString -}}
{{- if $registryName }}
{{- printf "%s/%s:%s" $registryName $repositoryName $tag -}}
{{- else }}
{{- printf "%s:%s" $repositoryName $tag -}}
{{- end }}
{{- end }}

{{/*
Return the proper Docker Image Registry Secret Names
*/}}
{{- define "dl-engine.imagePullSecrets" -}}
{{- include "common.images.pullSecrets" (dict "images" (list .Values.image) "global" .Values.global) -}}
{{- end }}

{{/*
Create the name of the service account to use for the gateway component
*/}}
{{- define "dl-engine.gateway.serviceAccountName" -}}
{{- if .Values.services.gateway.serviceAccount.create }}
{{- default (printf "%s-gateway" (include "dl-engine.fullname" .)) .Values.services.gateway.serviceAccount.name }}
{{- else }}
{{- default "default" .Values.services.gateway.serviceAccount.name }}
{{- end }}
{{- end }}

{{/*
Create the name of the service account to use for the auth component
*/}}
{{- define "dl-engine.auth.serviceAccountName" -}}
{{- if .Values.services.auth.serviceAccount.create }}
{{- default (printf "%s-auth" (include "dl-engine.fullname" .)) .Values.services.auth.serviceAccount.name }}
{{- else }}
{{- default "default" .Values.services.auth.serviceAccount.name }}
{{- end }}
{{- end }}

{{/*
Validate required values
*/}}
{{- define "dl-engine.validateValues" -}}
{{- if not .Values.mysql.auth.password }}
{{- fail "MySQL password is required" }}
{{- end }}
{{- if not .Values.redis.auth.password }}
{{- fail "Redis password is required" }}
{{- end }}
{{- if not .Values.secrets.app.JWT_SECRET }}
{{- fail "JWT secret is required" }}
{{- end }}
{{- end }}

{{/*
Get the password secret.
*/}}
{{- define "dl-engine.secretName" -}}
{{- if .Values.existingSecret }}
{{- .Values.existingSecret }}
{{- else }}
{{- include "dl-engine.fullname" . }}-secret
{{- end }}
{{- end }}

{{/*
Get the configuration secret.
*/}}
{{- define "dl-engine.configMapName" -}}
{{- if .Values.existingConfigMap }}
{{- .Values.existingConfigMap }}
{{- else }}
{{- include "dl-engine.fullname" . }}-config
{{- end }}
{{- end }}
