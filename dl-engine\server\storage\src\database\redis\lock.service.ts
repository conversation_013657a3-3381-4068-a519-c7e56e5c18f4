import { Injectable, Logger } from '@nestjs/common';
import { RedisService } from './redis.service';

export interface LockOptions {
  ttl?: number; // 锁过期时间（秒）
  retryDelay?: number; // 重试延迟（毫秒）
  retryCount?: number; // 重试次数
  identifier?: string; // 锁标识符
}

export interface LockInfo {
  key: string;
  identifier: string;
  acquiredAt: Date;
  ttl: number;
  isLocked: boolean;
}

@Injectable()
export class LockService {
  private readonly logger = new Logger(LockService.name);
  private readonly lockPrefix = 'dl:lock:';
  private readonly defaultTTL = 30; // 30秒
  private readonly defaultRetryDelay = 100; // 100毫秒
  private readonly defaultRetryCount = 10;

  constructor(private redisService: RedisService) {}

  /**
   * 获取分布式锁
   */
  async acquireLock(
    key: string,
    options: LockOptions = {}
  ): Promise<{ success: boolean; identifier?: string; lockInfo?: LockInfo }> {
    const {
      ttl = this.defaultTTL,
      retryDelay = this.defaultRetryDelay,
      retryCount = this.defaultRetryCount,
      identifier = this.generateIdentifier(),
    } = options;

    const lockKey = `${this.lockPrefix}${key}`;
    let attempts = 0;

    while (attempts < retryCount) {
      try {
        // 尝试获取锁
        const acquired = await this.tryAcquireLock(lockKey, identifier, ttl);
        
        if (acquired) {
          const lockInfo: LockInfo = {
            key: lockKey,
            identifier,
            acquiredAt: new Date(),
            ttl,
            isLocked: true,
          };

          this.logger.debug(`Lock acquired: ${key} by ${identifier}`);
          return { success: true, identifier, lockInfo };
        }

        // 等待后重试
        if (attempts < retryCount - 1) {
          await this.delay(retryDelay);
        }

        attempts++;
      } catch (error) {
        this.logger.error(`Failed to acquire lock ${key}:`, error);
        return { success: false };
      }
    }

    this.logger.warn(`Failed to acquire lock ${key} after ${retryCount} attempts`);
    return { success: false };
  }

  /**
   * 释放分布式锁
   */
  async releaseLock(key: string, identifier: string): Promise<boolean> {
    const lockKey = `${this.lockPrefix}${key}`;

    try {
      // 使用Lua脚本确保原子性
      const luaScript = `
        if redis.call("GET", KEYS[1]) == ARGV[1] then
          return redis.call("DEL", KEYS[1])
        else
          return 0
        end
      `;

      const result = await this.redisService.getClient().eval(
        luaScript,
        1,
        lockKey,
        identifier
      );

      const released = result === 1;
      
      if (released) {
        this.logger.debug(`Lock released: ${key} by ${identifier}`);
      } else {
        this.logger.warn(`Failed to release lock ${key}: identifier mismatch or lock not found`);
      }

      return released;
    } catch (error) {
      this.logger.error(`Failed to release lock ${key}:`, error);
      return false;
    }
  }

  /**
   * 续期锁
   */
  async renewLock(key: string, identifier: string, ttl: number = this.defaultTTL): Promise<boolean> {
    const lockKey = `${this.lockPrefix}${key}`;

    try {
      // 使用Lua脚本确保原子性
      const luaScript = `
        if redis.call("GET", KEYS[1]) == ARGV[1] then
          return redis.call("EXPIRE", KEYS[1], ARGV[2])
        else
          return 0
        end
      `;

      const result = await this.redisService.getClient().eval(
        luaScript,
        1,
        lockKey,
        identifier,
        ttl.toString()
      );

      const renewed = result === 1;
      
      if (renewed) {
        this.logger.debug(`Lock renewed: ${key} by ${identifier} for ${ttl}s`);
      }

      return renewed;
    } catch (error) {
      this.logger.error(`Failed to renew lock ${key}:`, error);
      return false;
    }
  }

  /**
   * 检查锁状态
   */
  async getLockInfo(key: string): Promise<LockInfo | null> {
    const lockKey = `${this.lockPrefix}${key}`;

    try {
      const identifier = await this.redisService.get<string>(lockKey);
      
      if (!identifier) {
        return null;
      }

      const ttl = await this.redisService.ttl(lockKey);
      
      return {
        key: lockKey,
        identifier,
        acquiredAt: new Date(), // 无法获取确切的获取时间
        ttl,
        isLocked: true,
      };
    } catch (error) {
      this.logger.error(`Failed to get lock info for ${key}:`, error);
      return null;
    }
  }

  /**
   * 强制释放锁
   */
  async forceReleaseLock(key: string): Promise<boolean> {
    const lockKey = `${this.lockPrefix}${key}`;

    try {
      const deleted = await this.redisService.del(lockKey);
      
      if (deleted > 0) {
        this.logger.warn(`Lock force released: ${key}`);
        return true;
      }
      
      return false;
    } catch (error) {
      this.logger.error(`Failed to force release lock ${key}:`, error);
      return false;
    }
  }

  /**
   * 执行带锁的操作
   */
  async withLock<T>(
    key: string,
    operation: () => Promise<T>,
    options: LockOptions = {}
  ): Promise<{ success: boolean; result?: T; error?: string }> {
    const lockResult = await this.acquireLock(key, options);
    
    if (!lockResult.success) {
      return {
        success: false,
        error: 'Failed to acquire lock',
      };
    }

    try {
      const result = await operation();
      
      return {
        success: true,
        result,
      };
    } catch (error) {
      this.logger.error(`Operation failed while holding lock ${key}:`, error);
      return {
        success: false,
        error: error.message,
      };
    } finally {
      // 确保释放锁
      await this.releaseLock(key, lockResult.identifier!);
    }
  }

  /**
   * 批量获取锁
   */
  async acquireMultipleLocks(
    keys: string[],
    options: LockOptions = {}
  ): Promise<{ success: boolean; acquiredLocks: string[]; failedLocks: string[] }> {
    const acquiredLocks: string[] = [];
    const failedLocks: string[] = [];

    for (const key of keys) {
      const result = await this.acquireLock(key, options);
      
      if (result.success) {
        acquiredLocks.push(key);
      } else {
        failedLocks.push(key);
      }
    }

    const success = failedLocks.length === 0;
    
    // 如果部分失败，释放已获取的锁
    if (!success && acquiredLocks.length > 0) {
      for (const key of acquiredLocks) {
        await this.forceReleaseLock(key);
      }
      
      return {
        success: false,
        acquiredLocks: [],
        failedLocks: keys,
      };
    }

    return {
      success,
      acquiredLocks,
      failedLocks,
    };
  }

  /**
   * 批量释放锁
   */
  async releaseMultipleLocks(
    lockInfos: Array<{ key: string; identifier: string }>
  ): Promise<{ releasedCount: number; failedCount: number }> {
    let releasedCount = 0;
    let failedCount = 0;

    for (const { key, identifier } of lockInfos) {
      const released = await this.releaseLock(key, identifier);
      
      if (released) {
        releasedCount++;
      } else {
        failedCount++;
      }
    }

    return { releasedCount, failedCount };
  }

  /**
   * 获取所有锁信息
   */
  async getAllLocks(): Promise<LockInfo[]> {
    try {
      const lockKeys = await this.redisService.keys(`${this.lockPrefix}*`);
      const locks: LockInfo[] = [];

      for (const lockKey of lockKeys) {
        const key = lockKey.replace(this.lockPrefix, '');
        const lockInfo = await this.getLockInfo(key);
        
        if (lockInfo) {
          locks.push(lockInfo);
        }
      }

      return locks;
    } catch (error) {
      this.logger.error('Failed to get all locks:', error);
      return [];
    }
  }

  /**
   * 清理过期锁
   */
  async cleanupExpiredLocks(): Promise<number> {
    try {
      const lockKeys = await this.redisService.keys(`${this.lockPrefix}*`);
      let cleanedCount = 0;

      for (const lockKey of lockKeys) {
        const ttl = await this.redisService.ttl(lockKey);
        
        if (ttl <= 0) {
          await this.redisService.del(lockKey);
          cleanedCount++;
        }
      }

      if (cleanedCount > 0) {
        this.logger.log(`Cleaned up ${cleanedCount} expired locks`);
      }

      return cleanedCount;
    } catch (error) {
      this.logger.error('Failed to cleanup expired locks:', error);
      return 0;
    }
  }

  /**
   * 锁统计信息
   */
  async getLockStats(): Promise<{
    totalLocks: number;
    locksByKey: Record<string, LockInfo>;
    averageTTL: number;
  }> {
    try {
      const locks = await this.getAllLocks();
      const locksByKey: Record<string, LockInfo> = {};
      let totalTTL = 0;

      for (const lock of locks) {
        const key = lock.key.replace(this.lockPrefix, '');
        locksByKey[key] = lock;
        totalTTL += lock.ttl;
      }

      const averageTTL = locks.length > 0 ? totalTTL / locks.length : 0;

      return {
        totalLocks: locks.length,
        locksByKey,
        averageTTL: Math.round(averageTTL),
      };
    } catch (error) {
      this.logger.error('Failed to get lock stats:', error);
      return {
        totalLocks: 0,
        locksByKey: {},
        averageTTL: 0,
      };
    }
  }

  private async tryAcquireLock(lockKey: string, identifier: string, ttl: number): Promise<boolean> {
    try {
      // 使用SET命令的NX和EX选项实现原子操作
      const result = await this.redisService.getClient().set(
        lockKey,
        identifier,
        {
          NX: true, // 仅当键不存在时设置
          EX: ttl,  // 设置过期时间
        }
      );

      return result === 'OK';
    } catch (error) {
      this.logger.error(`Failed to try acquire lock ${lockKey}:`, error);
      return false;
    }
  }

  private generateIdentifier(): string {
    return `${process.pid}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
