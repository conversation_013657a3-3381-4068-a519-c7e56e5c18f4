# DL-Engine Docker Compose 配置
# 完整的本地开发和测试环境

version: '3.8'

services:
  # 数据库服务
  mysql:
    image: mysql:8.0
    container_name: dl-engine-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: dl-engine-root-password
      MYSQL_DATABASE: dl_engine
      MYSQL_USER: dl_engine
      MYSQL_PASSWORD: dl-engine-password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./init-scripts/mysql:/docker-entrypoint-initdb.d
    networks:
      - dl-engine-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  redis:
    image: redis:7-alpine
    container_name: dl-engine-redis
    restart: unless-stopped
    command: redis-server --requirepass dl-engine-redis-password
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - dl-engine-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      timeout: 20s
      retries: 10

  postgresql:
    image: pgvector/pgvector:pg15
    container_name: dl-engine-postgresql
    restart: unless-stopped
    environment:
      POSTGRES_DB: dl_engine_vector
      POSTGRES_USER: dl_engine
      POSTGRES_PASSWORD: dl-engine-postgres-password
    ports:
      - "5432:5432"
    volumes:
      - postgresql_data:/var/lib/postgresql/data
      - ./init-scripts/postgresql:/docker-entrypoint-initdb.d
    networks:
      - dl-engine-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U dl_engine"]
      timeout: 20s
      retries: 10

  # 对象存储
  minio:
    image: minio/minio:latest
    container_name: dl-engine-minio
    restart: unless-stopped
    command: server /data --console-address ":9001"
    environment:
      MINIO_ROOT_USER: dl-engine
      MINIO_ROOT_PASSWORD: dl-engine-minio-password
    ports:
      - "9000:9000"
      - "9001:9001"
    volumes:
      - minio_data:/data
    networks:
      - dl-engine-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      timeout: 20s
      retries: 10

  # AI服务
  ollama:
    image: ollama/ollama:latest
    container_name: dl-engine-ollama
    restart: unless-stopped
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    networks:
      - dl-engine-network
    environment:
      - OLLAMA_HOST=0.0.0.0
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:11434/api/tags"]
      timeout: 20s
      retries: 10

  # DL-Engine 服务
  gateway:
    build:
      context: ../../../../
      dockerfile: dl-engine/server/deployment/docker/images/Dockerfile.gateway
    container_name: dl-engine-gateway
    restart: unless-stopped
    ports:
      - "3030:3030"
    environment:
      - NODE_ENV=development
      - LOG_LEVEL=debug
      - PORT=3030
      - MYSQL_HOST=mysql
      - MYSQL_PORT=3306
      - MYSQL_DATABASE=dl_engine
      - MYSQL_USER=dl_engine
      - MYSQL_PASSWORD=dl-engine-password
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=dl-engine-redis-password
      - JWT_SECRET=dl-engine-jwt-secret-key
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - dl-engine-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3030/health"]
      timeout: 20s
      retries: 10

  auth:
    build:
      context: ../../../../
      dockerfile: dl-engine/server/deployment/docker/images/Dockerfile.auth
    container_name: dl-engine-auth
    restart: unless-stopped
    ports:
      - "3031:3031"
    environment:
      - NODE_ENV=development
      - LOG_LEVEL=debug
      - PORT=3031
      - MYSQL_HOST=mysql
      - MYSQL_PORT=3306
      - MYSQL_DATABASE=dl_engine
      - MYSQL_USER=dl_engine
      - MYSQL_PASSWORD=dl-engine-password
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=dl-engine-redis-password
      - JWT_SECRET=dl-engine-jwt-secret-key
      - SMS_API_KEY=your-sms-api-key
      - ENABLE_CHINESE_UI=true
      - DEFAULT_LOCALE=zh-CN
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - dl-engine-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3031/health"]
      timeout: 20s
      retries: 10

  api:
    build:
      context: ../../../../
      dockerfile: dl-engine/server/deployment/docker/images/Dockerfile.api
    container_name: dl-engine-api
    restart: unless-stopped
    ports:
      - "3032:3032"
    environment:
      - NODE_ENV=development
      - LOG_LEVEL=debug
      - PORT=3032
      - MYSQL_HOST=mysql
      - MYSQL_PORT=3306
      - MYSQL_DATABASE=dl_engine
      - MYSQL_USER=dl_engine
      - MYSQL_PASSWORD=dl-engine-password
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=dl-engine-redis-password
      - POSTGRESQL_HOST=postgresql
      - POSTGRESQL_PORT=5432
      - POSTGRESQL_PASSWORD=dl-engine-postgres-password
      - MINIO_HOST=minio
      - MINIO_PORT=9000
      - MINIO_ACCESS_KEY=dl-engine
      - MINIO_SECRET_KEY=dl-engine-minio-password
      - JWT_SECRET=dl-engine-jwt-secret-key
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
      postgresql:
        condition: service_healthy
      minio:
        condition: service_healthy
    networks:
      - dl-engine-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3032/health"]
      timeout: 20s
      retries: 10

  instance:
    build:
      context: ../../../../
      dockerfile: dl-engine/server/deployment/docker/images/Dockerfile.instance
    container_name: dl-engine-instance
    restart: unless-stopped
    ports:
      - "7777:7777/udp"
      - "8080:8080"
      - "8081:8081"
    environment:
      - NODE_ENV=development
      - LOG_LEVEL=debug
      - PORT=7777
      - WEBSOCKET_PORT=8080
      - WEBRTC_PORT=8081
      - MYSQL_HOST=mysql
      - MYSQL_PORT=3306
      - MYSQL_DATABASE=dl_engine
      - MYSQL_USER=dl_engine
      - MYSQL_PASSWORD=dl-engine-password
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=dl-engine-redis-password
      - MAX_PLAYERS=50
      - SESSION_TIMEOUT=300000
      - PHYSICS_TICK_RATE=60
      - NETWORK_TICK_RATE=20
      - ENABLE_VOICE_CHAT=true
      - ENABLE_VIDEO_CHAT=true
      - ENABLE_SCREEN_SHARE=true
      - AGONES_ENABLED=false
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - dl-engine-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      timeout: 20s
      retries: 10

  media:
    build:
      context: ../../../../
      dockerfile: dl-engine/server/deployment/docker/images/Dockerfile.media
    container_name: dl-engine-media
    restart: unless-stopped
    ports:
      - "3034:3034"
    environment:
      - NODE_ENV=development
      - LOG_LEVEL=debug
      - PORT=3034
      - MINIO_HOST=minio
      - MINIO_PORT=9000
      - MINIO_ACCESS_KEY=dl-engine
      - MINIO_SECRET_KEY=dl-engine-minio-password
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=dl-engine-redis-password
      - MAX_FILE_SIZE=100MB
      - ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,mp4,mp3,wav,gltf,glb,fbx
    depends_on:
      minio:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - dl-engine-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3034/health"]
      timeout: 20s
      retries: 10

  ai:
    build:
      context: ../../../../
      dockerfile: dl-engine/server/deployment/docker/images/Dockerfile.ai
    container_name: dl-engine-ai
    restart: unless-stopped
    ports:
      - "3035:3035"
    environment:
      - NODE_ENV=development
      - LOG_LEVEL=debug
      - PORT=3035
      - OLLAMA_HOST=http://ollama:11434
      - POSTGRESQL_HOST=postgresql
      - POSTGRESQL_PORT=5432
      - POSTGRESQL_PASSWORD=dl-engine-postgres-password
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=dl-engine-redis-password
      - ENABLE_AI_RECOMMENDATIONS=true
      - ENABLE_LEARNING_ANALYTICS=true
    depends_on:
      ollama:
        condition: service_healthy
      postgresql:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - dl-engine-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3035/health"]
      timeout: 20s
      retries: 10

  client:
    build:
      context: ../../../../
      dockerfile: dl-engine/client/Dockerfile
    container_name: dl-engine-client
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - VITE_API_URL=http://localhost:3030
      - VITE_AUTH_URL=http://localhost:3031
      - VITE_WEBSOCKET_URL=ws://localhost:8080
      - VITE_WEBRTC_URL=http://localhost:8081
      - VITE_DEFAULT_LOCALE=zh-CN
      - VITE_ENABLE_CHINESE_UI=true
    depends_on:
      gateway:
        condition: service_healthy
      auth:
        condition: service_healthy
    networks:
      - dl-engine-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      timeout: 20s
      retries: 10

  # 监控服务
  prometheus:
    image: prom/prometheus:latest
    container_name: dl-engine-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - dl-engine-network

  grafana:
    image: grafana/grafana:latest
    container_name: dl-engine-grafana
    restart: unless-stopped
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=dl-engine-grafana-password
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - dl-engine-network

# 网络配置
networks:
  dl-engine-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 卷配置
volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  postgresql_data:
    driver: local
  minio_data:
    driver: local
  ollama_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
