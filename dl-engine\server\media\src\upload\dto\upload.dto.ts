import { IsS<PERSON>, <PERSON>N<PERSON><PERSON>, <PERSON>Optional, IsPos<PERSON>, <PERSON>, <PERSON> } from 'class-validator'

export class InitiateUploadDto {
  @IsString()
  fileName: string

  @IsNumber()
  @IsPositive()
  fileSize: number

  @IsOptional()
  @IsNumber()
  @Min(1024 * 1024) // 最小1MB
  @Max(50 * 1024 * 1024) // 最大50MB
  chunkSize?: number

  @IsOptional()
  @IsString()
  contentType?: string
}

export class UploadChunkDto {
  @IsString()
  uploadId: string

  @IsNumber()
  @Min(0)
  chunkIndex: number
}

export class CompleteUploadDto {
  @IsOptional()
  @IsString()
  expectedHash?: string

  @IsOptional()
  @IsString()
  contentType?: string
}
