/**
 * DL-Engine 协作功能服务
 * 
 * 核心功能：
 * - 实时协作状态管理
 * - 版本冲突检测和解决
 * - 权限同步和控制
 * - 操作历史记录和回放
 * - 协作会话管理
 * - 冲突解决策略
 */

import { Injectable, NotFoundException, ConflictException, ForbiddenException } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { Repository, In } from 'typeorm'
import { ConfigService } from '@nestjs/config'

import { CollaborationSession } from '../entities/collaboration-session.entity'
import { CollaborationOperation, OperationType } from '../entities/collaboration-operation.entity'
import { CollaborationConflict, ConflictStatus } from '../entities/collaboration-conflict.entity'
import { CollaborationLock } from '../entities/collaboration-lock.entity'
import { User } from '../../users/entities/user.entity'
import { Project } from '../../projects/entities/project.entity'

@Injectable()
export class CollaborationService {
  constructor(
    @InjectRepository(CollaborationSession)
    private readonly sessionRepository: Repository<CollaborationSession>,
    @InjectRepository(CollaborationOperation)
    private readonly operationRepository: Repository<CollaborationOperation>,
    @InjectRepository(CollaborationConflict)
    private readonly conflictRepository: Repository<CollaborationConflict>,
    @InjectRepository(CollaborationLock)
    private readonly lockRepository: Repository<CollaborationLock>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Project)
    private readonly projectRepository: Repository<Project>,
    private readonly configService: ConfigService
  ) {}

  /**
   * 创建协作会话
   */
  async createSession(projectId: string, userId: string, sessionData: any): Promise<CollaborationSession> {
    // 检查项目是否存在
    const project = await this.projectRepository.findOne({ where: { id: projectId } })
    if (!project) {
      throw new NotFoundException('项目不存在')
    }

    // 检查用户权限
    await this.checkCollaborationPermission(projectId, userId)

    // 检查是否已有活跃会话
    const existingSession = await this.sessionRepository.findOne({
      where: { projectId, userId, isActive: true }
    })

    if (existingSession) {
      // 更新现有会话
      existingSession.lastActiveAt = new Date()
      existingSession.metadata = { ...existingSession.metadata, ...sessionData.metadata }
      return await this.sessionRepository.save(existingSession)
    }

    // 创建新会话
    const session = this.sessionRepository.create({
      projectId,
      userId,
      sessionId: this.generateSessionId(),
      isActive: true,
      startedAt: new Date(),
      lastActiveAt: new Date(),
      metadata: {
        userAgent: sessionData.userAgent,
        ipAddress: sessionData.ipAddress,
        clientVersion: sessionData.clientVersion,
        ...sessionData.metadata
      }
    })

    const savedSession = await this.sessionRepository.save(session)

    // 通知其他协作者
    await this.notifyCollaborators(projectId, userId, 'user_joined', {
      sessionId: savedSession.sessionId,
      user: await this.userRepository.findOne({ where: { id: userId } })
    })

    return savedSession
  }

  /**
   * 结束协作会话
   */
  async endSession(sessionId: string, userId: string): Promise<void> {
    const session = await this.sessionRepository.findOne({
      where: { sessionId, userId }
    })

    if (!session) {
      throw new NotFoundException('协作会话不存在')
    }

    // 更新会话状态
    session.isActive = false
    session.endedAt = new Date()
    await this.sessionRepository.save(session)

    // 释放用户持有的锁
    await this.releaseUserLocks(userId, session.projectId)

    // 通知其他协作者
    await this.notifyCollaborators(session.projectId, userId, 'user_left', {
      sessionId: session.sessionId
    })
  }

  /**
   * 记录协作操作
   */
  async recordOperation(
    sessionId: string,
    userId: string,
    operationData: any
  ): Promise<CollaborationOperation> {
    const session = await this.sessionRepository.findOne({
      where: { sessionId, userId, isActive: true }
    })

    if (!session) {
      throw new NotFoundException('活跃协作会话不存在')
    }

    // 检查操作权限
    await this.checkOperationPermission(session.projectId, userId, operationData.type)

    // 检查是否有冲突
    const conflicts = await this.detectConflicts(session.projectId, operationData)

    // 创建操作记录
    const operation = this.operationRepository.create({
      sessionId: session.sessionId,
      projectId: session.projectId,
      userId,
      type: operationData.type,
      target: operationData.target,
      data: operationData.data,
      metadata: {
        timestamp: new Date(),
        clientId: operationData.clientId,
        version: operationData.version,
        conflicts: conflicts.length > 0
      }
    })

    const savedOperation = await this.operationRepository.save(operation)

    // 处理冲突
    if (conflicts.length > 0) {
      await this.handleConflicts(savedOperation, conflicts)
    }

    // 更新会话活跃时间
    session.lastActiveAt = new Date()
    await this.sessionRepository.save(session)

    // 广播操作给其他协作者
    await this.broadcastOperation(session.projectId, userId, savedOperation)

    return savedOperation
  }

  /**
   * 获取协作状态
   */
  async getCollaborationStatus(projectId: string, userId: string): Promise<{
    activeSessions: any[]
    recentOperations: CollaborationOperation[]
    conflicts: CollaborationConflict[]
    locks: CollaborationLock[]
  }> {
    // 检查访问权限
    await this.checkCollaborationPermission(projectId, userId)

    // 获取活跃会话
    const activeSessions = await this.sessionRepository.find({
      where: { projectId, isActive: true },
      relations: ['user'],
      order: { lastActiveAt: 'DESC' }
    })

    // 获取最近操作
    const recentOperations = await this.operationRepository.find({
      where: { projectId },
      relations: ['user'],
      order: { createdAt: 'DESC' },
      take: 50
    })

    // 获取未解决的冲突
    const conflicts = await this.conflictRepository.find({
      where: { projectId, status: ConflictStatus.PENDING },
      relations: ['operation1', 'operation2'],
      order: { createdAt: 'DESC' }
    })

    // 获取当前锁定
    const locks = await this.lockRepository.find({
      where: { projectId, isActive: true },
      relations: ['user'],
      order: { createdAt: 'DESC' }
    })

    return {
      activeSessions: activeSessions.map(s => ({
        sessionId: s.sessionId,
        user: {
          id: s.user.id,
          username: s.user.username,
          displayName: s.user.displayName,
          avatarUrl: s.user.avatarUrl
        },
        startedAt: s.startedAt,
        lastActiveAt: s.lastActiveAt,
        metadata: s.metadata
      })),
      recentOperations,
      conflicts,
      locks
    }
  }

  /**
   * 申请资源锁定
   */
  async requestLock(
    projectId: string,
    userId: string,
    resourceType: string,
    resourceId: string,
    lockType: string = 'exclusive'
  ): Promise<CollaborationLock> {
    // 检查权限
    await this.checkCollaborationPermission(projectId, userId)

    // 检查是否已被锁定
    const existingLock = await this.lockRepository.findOne({
      where: { projectId, resourceType, resourceId, isActive: true }
    })

    if (existingLock) {
      if (existingLock.userId === userId) {
        // 延长锁定时间
        existingLock.expiresAt = new Date(Date.now() + 30 * 60 * 1000) // 30分钟
        return await this.lockRepository.save(existingLock)
      } else {
        throw new ConflictException('资源已被其他用户锁定')
      }
    }

    // 创建新锁
    const lock = this.lockRepository.create({
      projectId,
      userId,
      resourceType,
      resourceId,
      lockType,
      isActive: true,
      acquiredAt: new Date(),
      expiresAt: new Date(Date.now() + 30 * 60 * 1000) // 30分钟后过期
    })

    const savedLock = await this.lockRepository.save(lock)

    // 通知其他协作者
    await this.notifyCollaborators(projectId, userId, 'resource_locked', {
      resourceType,
      resourceId,
      lockType
    })

    return savedLock
  }

  /**
   * 释放资源锁定
   */
  async releaseLock(lockId: string, userId: string): Promise<void> {
    const lock = await this.lockRepository.findOne({
      where: { id: lockId, userId, isActive: true }
    })

    if (!lock) {
      throw new NotFoundException('锁定不存在或无权释放')
    }

    // 释放锁定
    lock.isActive = false
    lock.releasedAt = new Date()
    await this.lockRepository.save(lock)

    // 通知其他协作者
    await this.notifyCollaborators(lock.projectId, userId, 'resource_unlocked', {
      resourceType: lock.resourceType,
      resourceId: lock.resourceId
    })
  }

  /**
   * 解决冲突
   */
  async resolveConflict(
    conflictId: string,
    userId: string,
    resolution: any
  ): Promise<CollaborationConflict> {
    const conflict = await this.conflictRepository.findOne({
      where: { id: conflictId },
      relations: ['operation1', 'operation2']
    })

    if (!conflict) {
      throw new NotFoundException('冲突不存在')
    }

    // 检查解决权限
    await this.checkConflictResolutionPermission(conflict.projectId, userId)

    // 应用解决方案
    const resolvedData = await this.applyConflictResolution(conflict, resolution)

    // 更新冲突状态
    conflict.status = ConflictStatus.RESOLVED
    conflict.resolvedBy = userId
    conflict.resolvedAt = new Date()
    conflict.resolution = {
      strategy: resolution.strategy,
      data: resolvedData,
      resolvedBy: userId,
      resolvedAt: new Date()
    }

    const resolvedConflict = await this.conflictRepository.save(conflict)

    // 通知相关用户
    await this.notifyConflictResolution(resolvedConflict)

    return resolvedConflict
  }

  /**
   * 获取操作历史
   */
  async getOperationHistory(
    projectId: string,
    userId: string,
    options: any = {}
  ): Promise<{
    operations: CollaborationOperation[]
    total: number
  }> {
    const { page = 1, limit = 50, type, targetUserId, startDate, endDate } = options

    // 检查访问权限
    await this.checkCollaborationPermission(projectId, userId)

    const queryBuilder = this.operationRepository.createQueryBuilder('operation')
      .leftJoinAndSelect('operation.user', 'user')
      .where('operation.projectId = :projectId', { projectId })

    if (type) {
      queryBuilder.andWhere('operation.type = :type', { type })
    }

    if (targetUserId) {
      queryBuilder.andWhere('operation.userId = :targetUserId', { targetUserId })
    }

    if (startDate) {
      queryBuilder.andWhere('operation.createdAt >= :startDate', { startDate })
    }

    if (endDate) {
      queryBuilder.andWhere('operation.createdAt <= :endDate', { endDate })
    }

    queryBuilder.orderBy('operation.createdAt', 'DESC')

    const offset = (page - 1) * limit
    queryBuilder.skip(offset).take(limit)

    const [operations, total] = await queryBuilder.getManyAndCount()

    return { operations, total }
  }

  /**
   * 生成会话ID
   */
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 检查协作权限
   */
  private async checkCollaborationPermission(projectId: string, userId: string): Promise<void> {
    const project = await this.projectRepository.findOne({
      where: { id: projectId },
      relations: ['collaborators']
    })

    if (!project) {
      throw new NotFoundException('项目不存在')
    }

    // 项目所有者有所有权限
    if (project.ownerId === userId) return

    // 检查是否是协作者
    const isCollaborator = project.collaborators?.some(c => c.userId === userId)
    if (!isCollaborator) {
      throw new ForbiddenException('无协作权限')
    }
  }

  /**
   * 检查操作权限
   */
  private async checkOperationPermission(
    projectId: string,
    userId: string,
    operationType: OperationType
  ): Promise<void> {
    // 基础权限检查
    await this.checkCollaborationPermission(projectId, userId)

    // 根据操作类型进行更细粒度的权限检查
    // 这里可以根据具体需求实现
  }

  /**
   * 检查冲突解决权限
   */
  private async checkConflictResolutionPermission(projectId: string, userId: string): Promise<void> {
    // 通常项目所有者和管理员可以解决冲突
    await this.checkCollaborationPermission(projectId, userId)
  }

  /**
   * 检测冲突
   */
  private async detectConflicts(projectId: string, operationData: any): Promise<any[]> {
    // 获取最近的操作
    const recentOperations = await this.operationRepository.find({
      where: { projectId, target: operationData.target },
      order: { createdAt: 'DESC' },
      take: 10
    })

    const conflicts = []

    for (const operation of recentOperations) {
      if (this.isConflicting(operation, operationData)) {
        conflicts.push(operation)
      }
    }

    return conflicts
  }

  /**
   * 判断操作是否冲突
   */
  private isConflicting(operation1: any, operation2: any): boolean {
    // 简单的冲突检测逻辑
    return (
      operation1.target === operation2.target &&
      operation1.type === operation2.type &&
      Math.abs(new Date().getTime() - operation1.createdAt.getTime()) < 5000 // 5秒内
    )
  }

  /**
   * 处理冲突
   */
  private async handleConflicts(operation: CollaborationOperation, conflicts: any[]): Promise<void> {
    for (const conflictingOperation of conflicts) {
      const conflict = this.conflictRepository.create({
        projectId: operation.projectId,
        operation1Id: conflictingOperation.id,
        operation2Id: operation.id,
        type: 'data_conflict',
        status: ConflictStatus.PENDING,
        metadata: {
          target: operation.target,
          conflictReason: 'simultaneous_edit'
        }
      })

      await this.conflictRepository.save(conflict)
    }
  }

  /**
   * 应用冲突解决方案
   */
  private async applyConflictResolution(conflict: CollaborationConflict, resolution: any): Promise<any> {
    // 根据解决策略应用解决方案
    switch (resolution.strategy) {
      case 'accept_mine':
        return conflict.operation2.data
      case 'accept_theirs':
        return conflict.operation1.data
      case 'merge':
        return this.mergeOperationData(conflict.operation1.data, conflict.operation2.data)
      case 'custom':
        return resolution.customData
      default:
        throw new BadRequestException('不支持的解决策略')
    }
  }

  /**
   * 合并操作数据
   */
  private mergeOperationData(data1: any, data2: any): any {
    // 简单的合并逻辑，实际应用中需要更复杂的合并策略
    return { ...data1, ...data2 }
  }

  /**
   * 释放用户锁定
   */
  private async releaseUserLocks(userId: string, projectId: string): Promise<void> {
    await this.lockRepository.update(
      { userId, projectId, isActive: true },
      { isActive: false, releasedAt: new Date() }
    )
  }

  /**
   * 通知协作者
   */
  private async notifyCollaborators(
    projectId: string,
    excludeUserId: string,
    eventType: string,
    data: any
  ): Promise<void> {
    // 这里应该集成实时通信服务（如 WebSocket）
    console.log(`通知协作者: ${eventType}`, { projectId, excludeUserId, data })
  }

  /**
   * 广播操作
   */
  private async broadcastOperation(
    projectId: string,
    excludeUserId: string,
    operation: CollaborationOperation
  ): Promise<void> {
    // 这里应该通过 WebSocket 广播操作给其他协作者
    console.log(`广播操作: ${operation.type}`, { projectId, excludeUserId, operation })
  }

  /**
   * 通知冲突解决
   */
  private async notifyConflictResolution(conflict: CollaborationConflict): Promise<void> {
    // 通知相关用户冲突已解决
    console.log(`冲突已解决: ${conflict.id}`)
  }
}
