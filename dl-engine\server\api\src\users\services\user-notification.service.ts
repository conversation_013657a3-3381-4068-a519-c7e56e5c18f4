import { Injectable, Logger } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'

import { User } from '../entities/user.entity'

/**
 * 用户通知服务
 * 
 * 处理用户相关的通知发送
 */
@Injectable()
export class UserNotificationService {
  private readonly logger = new Logger(UserNotificationService.name)

  constructor(private readonly configService: ConfigService) {}

  /**
   * 发送欢迎通知
   */
  async sendWelcomeNotification(user: User): Promise<void> {
    try {
      const welcomeMessage = this.getWelcomeMessage(user)
      
      // 发送欢迎短信（如果有手机号）
      if (user.phone) {
        await this.sendSmsNotification(user.phone, welcomeMessage.sms, user.countryCode)
      }

      // 发送欢迎邮件（如果有邮箱）
      if (user.email) {
        await this.sendEmailNotification(
          user.email,
          welcomeMessage.email.subject,
          welcomeMessage.email.content,
          'welcome'
        )
      }

      this.logger.log(`Welcome notification sent to user: ${user.id}`)
    } catch (error) {
      this.logger.error(`Failed to send welcome notification to user: ${user.id}`, error)
    }
  }

  /**
   * 发送账户激活通知
   */
  async sendAccountActivationNotification(user: User): Promise<void> {
    try {
      const activationMessage = this.getAccountActivationMessage(user)
      
      if (user.phone) {
        await this.sendSmsNotification(user.phone, activationMessage.sms, user.countryCode)
      }

      if (user.email) {
        await this.sendEmailNotification(
          user.email,
          activationMessage.email.subject,
          activationMessage.email.content,
          'activation'
        )
      }

      this.logger.log(`Account activation notification sent to user: ${user.id}`)
    } catch (error) {
      this.logger.error(`Failed to send activation notification to user: ${user.id}`, error)
    }
  }

  /**
   * 发送账户停用通知
   */
  async sendAccountDeactivationNotification(user: User): Promise<void> {
    try {
      const deactivationMessage = this.getAccountDeactivationMessage(user)
      
      if (user.phone) {
        await this.sendSmsNotification(user.phone, deactivationMessage.sms, user.countryCode)
      }

      if (user.email) {
        await this.sendEmailNotification(
          user.email,
          deactivationMessage.email.subject,
          deactivationMessage.email.content,
          'deactivation'
        )
      }

      this.logger.log(`Account deactivation notification sent to user: ${user.id}`)
    } catch (error) {
      this.logger.error(`Failed to send deactivation notification to user: ${user.id}`, error)
    }
  }

  /**
   * 发送手机验证码
   */
  async sendPhoneVerificationCode(phone: string, code: string, countryCode = '+86'): Promise<void> {
    try {
      const message = `【DL-Engine】您的验证码是：${code}，5分钟内有效。请勿泄露给他人。`
      await this.sendSmsNotification(phone, message, countryCode)
      
      this.logger.log(`Phone verification code sent to: ${countryCode}${phone}`)
    } catch (error) {
      this.logger.error(`Failed to send phone verification code to: ${countryCode}${phone}`, error)
      throw error
    }
  }

  /**
   * 发送邮箱验证链接
   */
  async sendEmailVerificationLink(email: string, verificationLink: string): Promise<void> {
    try {
      const subject = '验证您的邮箱地址 - DL-Engine'
      const content = this.getEmailVerificationTemplate(verificationLink)
      
      await this.sendEmailNotification(email, subject, content, 'email-verification')
      
      this.logger.log(`Email verification link sent to: ${email}`)
    } catch (error) {
      this.logger.error(`Failed to send email verification link to: ${email}`, error)
      throw error
    }
  }

  /**
   * 发送密码重置链接
   */
  async sendPasswordResetLink(email: string, resetLink: string): Promise<void> {
    try {
      const subject = '重置您的密码 - DL-Engine'
      const content = this.getPasswordResetTemplate(resetLink)
      
      await this.sendEmailNotification(email, subject, content, 'password-reset')
      
      this.logger.log(`Password reset link sent to: ${email}`)
    } catch (error) {
      this.logger.error(`Failed to send password reset link to: ${email}`, error)
      throw error
    }
  }

  /**
   * 发送登录异常通知
   */
  async sendLoginAnomalyNotification(user: User, loginInfo: {
    ip: string
    location?: string
    device?: string
    time: Date
  }): Promise<void> {
    try {
      const message = this.getLoginAnomalyMessage(user, loginInfo)
      
      if (user.phone) {
        await this.sendSmsNotification(user.phone, message.sms, user.countryCode)
      }

      if (user.email) {
        await this.sendEmailNotification(
          user.email,
          message.email.subject,
          message.email.content,
          'login-anomaly'
        )
      }

      this.logger.log(`Login anomaly notification sent to user: ${user.id}`)
    } catch (error) {
      this.logger.error(`Failed to send login anomaly notification to user: ${user.id}`, error)
    }
  }

  /**
   * 发送短信通知
   */
  private async sendSmsNotification(phone: string, message: string, countryCode = '+86'): Promise<void> {
    // TODO: 集成实际的短信服务提供商（阿里云、腾讯云等）
    // 这里是模拟实现
    this.logger.debug(`SMS to ${countryCode}${phone}: ${message}`)
    
    // 在开发环境下直接返回
    if (this.configService.get('NODE_ENV') === 'development') {
      return
    }

    // 生产环境下调用实际的短信服务
    // await this.smsProvider.send(countryCode + phone, message)
  }

  /**
   * 发送邮件通知
   */
  private async sendEmailNotification(
    email: string,
    subject: string,
    content: string,
    template: string
  ): Promise<void> {
    // TODO: 集成实际的邮件服务提供商
    // 这里是模拟实现
    this.logger.debug(`Email to ${email}: ${subject}`)
    
    // 在开发环境下直接返回
    if (this.configService.get('NODE_ENV') === 'development') {
      return
    }

    // 生产环境下调用实际的邮件服务
    // await this.emailProvider.send(email, subject, content, template)
  }

  /**
   * 获取欢迎消息模板
   */
  private getWelcomeMessage(user: User) {
    return {
      sms: `【DL-Engine】欢迎加入DL-Engine数字化学习平台！您的账户已创建成功，开始您的学习之旅吧！`,
      email: {
        subject: '欢迎加入DL-Engine数字化学习平台！',
        content: `
          <h2>欢迎，${user.displayName}！</h2>
          <p>感谢您注册DL-Engine数字化学习平台。</p>
          <p>您现在可以：</p>
          <ul>
            <li>创建和管理学习项目</li>
            <li>参与在线课程</li>
            <li>与其他学习者协作</li>
            <li>使用VR/AR学习体验</li>
          </ul>
          <p>如有任何问题，请随时联系我们的客服团队。</p>
          <p>祝您学习愉快！</p>
          <p>DL-Engine团队</p>
        `
      }
    }
  }

  /**
   * 获取账户激活消息模板
   */
  private getAccountActivationMessage(user: User) {
    return {
      sms: `【DL-Engine】您的账户已成功激活！现在可以正常使用所有功能了。`,
      email: {
        subject: '账户激活成功 - DL-Engine',
        content: `
          <h2>账户激活成功！</h2>
          <p>亲爱的${user.displayName}，</p>
          <p>您的DL-Engine账户已成功激活，现在可以使用所有平台功能。</p>
          <p>立即开始您的学习之旅吧！</p>
          <p>DL-Engine团队</p>
        `
      }
    }
  }

  /**
   * 获取账户停用消息模板
   */
  private getAccountDeactivationMessage(user: User) {
    return {
      sms: `【DL-Engine】您的账户已被停用。如有疑问，请联系客服。`,
      email: {
        subject: '账户已停用 - DL-Engine',
        content: `
          <h2>账户停用通知</h2>
          <p>亲爱的${user.displayName}，</p>
          <p>您的DL-Engine账户已被停用。</p>
          <p>如果您认为这是错误操作，请联系我们的客服团队。</p>
          <p>DL-Engine团队</p>
        `
      }
    }
  }

  /**
   * 获取邮箱验证模板
   */
  private getEmailVerificationTemplate(verificationLink: string): string {
    return `
      <h2>验证您的邮箱地址</h2>
      <p>请点击下面的链接验证您的邮箱地址：</p>
      <p><a href="${verificationLink}" style="background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">验证邮箱</a></p>
      <p>如果按钮无法点击，请复制以下链接到浏览器地址栏：</p>
      <p>${verificationLink}</p>
      <p>此链接将在24小时后失效。</p>
      <p>DL-Engine团队</p>
    `
  }

  /**
   * 获取密码重置模板
   */
  private getPasswordResetTemplate(resetLink: string): string {
    return `
      <h2>重置您的密码</h2>
      <p>我们收到了重置您账户密码的请求。</p>
      <p>请点击下面的链接重置密码：</p>
      <p><a href="${resetLink}" style="background-color: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">重置密码</a></p>
      <p>如果按钮无法点击，请复制以下链接到浏览器地址栏：</p>
      <p>${resetLink}</p>
      <p>此链接将在1小时后失效。</p>
      <p>如果您没有请求重置密码，请忽略此邮件。</p>
      <p>DL-Engine团队</p>
    `
  }

  /**
   * 获取登录异常消息模板
   */
  private getLoginAnomalyMessage(user: User, loginInfo: any) {
    const timeStr = loginInfo.time.toLocaleString('zh-CN')
    const locationStr = loginInfo.location || '未知位置'
    
    return {
      sms: `【DL-Engine】检测到异常登录：${timeStr}，位置：${locationStr}。如非本人操作，请立即修改密码。`,
      email: {
        subject: '检测到异常登录 - DL-Engine',
        content: `
          <h2>异常登录提醒</h2>
          <p>亲爱的${user.displayName}，</p>
          <p>我们检测到您的账户有异常登录活动：</p>
          <ul>
            <li>时间：${timeStr}</li>
            <li>IP地址：${loginInfo.ip}</li>
            <li>位置：${locationStr}</li>
            <li>设备：${loginInfo.device || '未知设备'}</li>
          </ul>
          <p>如果这是您本人的操作，请忽略此邮件。</p>
          <p>如果不是您的操作，请立即：</p>
          <ol>
            <li>修改您的密码</li>
            <li>检查账户安全设置</li>
            <li>联系我们的客服团队</li>
          </ol>
          <p>DL-Engine团队</p>
        `
      }
    }
  }
}
