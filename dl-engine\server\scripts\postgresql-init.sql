-- PostgreSQL向量数据库初始化脚本
-- 为DL-Engine AI智能服务创建必要的扩展和表结构

-- 创建pgvector扩展
CREATE EXTENSION IF NOT EXISTS vector;

-- 创建向量嵌入表
CREATE TABLE IF NOT EXISTS embeddings (
    id SERIAL PRIMARY KEY,
    content_id VARCHAR(255) NOT NULL,
    content_type VARCHAR(50) NOT NULL,
    content_text TEXT,
    embedding vector(1536),
    model_name VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_embeddings_content_id ON embeddings(content_id);
CREATE INDEX IF NOT EXISTS idx_embeddings_content_type ON embeddings(content_type);
CREATE INDEX IF NOT EXISTS idx_embeddings_model ON embeddings(model_name);

-- 创建向量相似度搜索索引
CREATE INDEX IF NOT EXISTS idx_embeddings_vector ON embeddings 
USING ivfflat (embedding vector_cosine_ops) WITH (lists = 100);

-- 创建知识图谱节点表
CREATE TABLE IF NOT EXISTS knowledge_nodes (
    id SERIAL PRIMARY KEY,
    node_id VARCHAR(255) UNIQUE NOT NULL,
    node_type VARCHAR(50) NOT NULL,
    title VARCHAR(500) NOT NULL,
    description TEXT,
    properties JSONB,
    embedding vector(1536),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建知识图谱关系表
CREATE TABLE IF NOT EXISTS knowledge_relationships (
    id SERIAL PRIMARY KEY,
    source_node_id VARCHAR(255) NOT NULL,
    target_node_id VARCHAR(255) NOT NULL,
    relationship_type VARCHAR(100) NOT NULL,
    properties JSONB,
    weight FLOAT DEFAULT 1.0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (source_node_id) REFERENCES knowledge_nodes(node_id),
    FOREIGN KEY (target_node_id) REFERENCES knowledge_nodes(node_id)
);

-- 创建学习分析数据表
CREATE TABLE IF NOT EXISTS learning_analytics (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL,
    content_id VARCHAR(255) NOT NULL,
    action_type VARCHAR(50) NOT NULL,
    action_data JSONB,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    session_id VARCHAR(255),
    device_info JSONB
);

-- 创建推荐记录表
CREATE TABLE IF NOT EXISTS recommendations (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL,
    item_id VARCHAR(255) NOT NULL,
    item_type VARCHAR(50) NOT NULL,
    score FLOAT NOT NULL,
    algorithm VARCHAR(100) NOT NULL,
    context JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    clicked BOOLEAN DEFAULT FALSE,
    clicked_at TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_knowledge_nodes_type ON knowledge_nodes(node_type);
CREATE INDEX IF NOT EXISTS idx_knowledge_nodes_vector ON knowledge_nodes 
USING ivfflat (embedding vector_cosine_ops) WITH (lists = 100);

CREATE INDEX IF NOT EXISTS idx_relationships_source ON knowledge_relationships(source_node_id);
CREATE INDEX IF NOT EXISTS idx_relationships_target ON knowledge_relationships(target_node_id);
CREATE INDEX IF NOT EXISTS idx_relationships_type ON knowledge_relationships(relationship_type);

CREATE INDEX IF NOT EXISTS idx_analytics_user ON learning_analytics(user_id);
CREATE INDEX IF NOT EXISTS idx_analytics_content ON learning_analytics(content_id);
CREATE INDEX IF NOT EXISTS idx_analytics_timestamp ON learning_analytics(timestamp);

CREATE INDEX IF NOT EXISTS idx_recommendations_user ON recommendations(user_id);
CREATE INDEX IF NOT EXISTS idx_recommendations_item ON recommendations(item_id);
CREATE INDEX IF NOT EXISTS idx_recommendations_score ON recommendations(score DESC);

-- 创建向量搜索函数
CREATE OR REPLACE FUNCTION search_similar_embeddings(
    query_embedding vector(1536),
    content_type_filter VARCHAR(50) DEFAULT NULL,
    similarity_threshold FLOAT DEFAULT 0.7,
    limit_count INTEGER DEFAULT 10
)
RETURNS TABLE (
    content_id VARCHAR(255),
    content_type VARCHAR(50),
    content_text TEXT,
    similarity FLOAT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        e.content_id,
        e.content_type,
        e.content_text,
        1 - (e.embedding <=> query_embedding) AS similarity
    FROM embeddings e
    WHERE 
        (content_type_filter IS NULL OR e.content_type = content_type_filter)
        AND (1 - (e.embedding <=> query_embedding)) >= similarity_threshold
    ORDER BY e.embedding <=> query_embedding
    LIMIT limit_count;
END;
$$ LANGUAGE plpgsql;

-- 创建知识图谱搜索函数
CREATE OR REPLACE FUNCTION search_knowledge_graph(
    query_embedding vector(1536),
    node_type_filter VARCHAR(50) DEFAULT NULL,
    similarity_threshold FLOAT DEFAULT 0.7,
    limit_count INTEGER DEFAULT 10
)
RETURNS TABLE (
    node_id VARCHAR(255),
    node_type VARCHAR(50),
    title VARCHAR(500),
    description TEXT,
    similarity FLOAT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        kn.node_id,
        kn.node_type,
        kn.title,
        kn.description,
        1 - (kn.embedding <=> query_embedding) AS similarity
    FROM knowledge_nodes kn
    WHERE 
        (node_type_filter IS NULL OR kn.node_type = node_type_filter)
        AND (1 - (kn.embedding <=> query_embedding)) >= similarity_threshold
    ORDER BY kn.embedding <=> query_embedding
    LIMIT limit_count;
END;
$$ LANGUAGE plpgsql;

-- 插入示例数据
INSERT INTO knowledge_nodes (node_id, node_type, title, description, properties) VALUES
('concept_001', 'concept', '三维坐标系', '三维空间中用于定位点的坐标系统', '{"difficulty": "basic", "subject": "mathematics"}'),
('concept_002', 'concept', '向量运算', '向量的加法、减法和数量积运算', '{"difficulty": "intermediate", "subject": "mathematics"}'),
('skill_001', 'skill', '3D建模', '使用三维建模软件创建3D模型的技能', '{"difficulty": "advanced", "subject": "design"}');

INSERT INTO knowledge_relationships (source_node_id, target_node_id, relationship_type, weight) VALUES
('concept_001', 'concept_002', 'prerequisite', 0.8),
('concept_002', 'skill_001', 'enables', 0.9);

-- 创建触发器以自动更新时间戳
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_embeddings_updated_at 
    BEFORE UPDATE ON embeddings 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_knowledge_nodes_updated_at 
    BEFORE UPDATE ON knowledge_nodes 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 授予权限
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO postgres;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO postgres;
