import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@nestjs/typeorm'
import { ConfigModule } from '@nestjs/config'

import { PerformanceMetric } from './entities/performance-metric.entity'
import { BusinessMetric } from './entities/business-metric.entity'
import { HealthCheck } from './entities/health-check.entity'
import { SystemLog } from './entities/system-log.entity'
import { ErrorLog } from './entities/error-log.entity'
import { AuditLog } from './entities/audit-log.entity'
import { Alert } from './entities/alert.entity'
import { AlertRule } from './entities/alert-rule.entity'

import { PerformanceMetricsController } from './controllers/performance-metrics.controller'
import { BusinessMetricsController } from './controllers/business-metrics.controller'
import { HealthCheckController } from './controllers/health-check.controller'
import { SystemLogsController } from './controllers/system-logs.controller'
import { ErrorLogsController } from './controllers/error-logs.controller'
import { AuditLogsController } from './controllers/audit-logs.controller'
import { AlertsController } from './controllers/alerts.controller'
import { AlertRulesController } from './controllers/alert-rules.controller'

import { PerformanceMetricsService } from './services/performance-metrics.service'
import { BusinessMetricsService } from './services/business-metrics.service'
import { HealthCheckService } from './services/health-check.service'
import { SystemLogsService } from './services/system-logs.service'
import { ErrorLogsService } from './services/error-logs.service'
import { AuditLogsService } from './services/audit-logs.service'
import { AlertsService } from './services/alerts.service'
import { AlertRulesService } from './services/alert-rules.service'
import { MetricsCollectorService } from './services/metrics-collector.service'
import { LogAggregatorService } from './services/log-aggregator.service'
import { AlertManagerService } from './services/alert-manager.service'

import { UsersModule } from '../users/users.module'
import { NotificationModule } from '../notifications/notification.module'

/**
 * 监控接口模块
 * 
 * 确保系统可观测性的监控功能，包括：
 * - 性能指标监控
 * - 业务指标统计
 * - 健康检查接口
 * - 日志接口管理
 * - 错误日志记录
 * - 审计日志跟踪
 * - 告警系统
 * - 告警规则管理
 * - 指标收集服务
 * - 日志聚合服务
 * - 告警管理服务
 */
@Module({
  imports: [
    ConfigModule,
    TypeOrmModule.forFeature([
      PerformanceMetric,
      BusinessMetric,
      HealthCheck,
      SystemLog,
      ErrorLog,
      AuditLog,
      Alert,
      AlertRule
    ]),
    UsersModule,
    NotificationModule
  ],
  controllers: [
    PerformanceMetricsController,
    BusinessMetricsController,
    HealthCheckController,
    SystemLogsController,
    ErrorLogsController,
    AuditLogsController,
    AlertsController,
    AlertRulesController
  ],
  providers: [
    PerformanceMetricsService,
    BusinessMetricsService,
    HealthCheckService,
    SystemLogsService,
    ErrorLogsService,
    AuditLogsService,
    AlertsService,
    AlertRulesService,
    MetricsCollectorService,
    LogAggregatorService,
    AlertManagerService
  ],
  exports: [
    PerformanceMetricsService,
    BusinessMetricsService,
    HealthCheckService,
    SystemLogsService,
    ErrorLogsService,
    AuditLogsService,
    AlertsService,
    AlertRulesService,
    MetricsCollectorService,
    LogAggregatorService,
    AlertManagerService
  ]
})
export class MonitoringModule {}
