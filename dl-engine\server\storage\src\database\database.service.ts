/**
 * DL-Engine 数据库服务
 * 
 * 核心功能：
 * - MySQL主数据库管理 (用户数据、项目数据、业务数据、事务管理)
 * - Redis缓存系统 (会话缓存、数据缓存、分布式锁、消息队列)
 * - PostgreSQL向量库 (向量存储、相似度搜索、AI数据、分析数据)
 */

import { Injectable, OnModuleInit, OnModuleDestroy } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import { InjectDataSource } from '@nestjs/typeorm'
import { DataSource } from 'typeorm'
import { createClient, RedisClientType } from 'redis'
import { Cron, CronExpression } from '@nestjs/schedule'

export interface DatabaseStats {
  mysql: {
    connections: number
    queries: number
    slowQueries: number
    uptime: number
    version: string
  }
  redis: {
    connections: number
    memory: number
    keys: number
    hits: number
    misses: number
    uptime: number
  }
  postgresql: {
    connections: number
    queries: number
    vectorCount: number
    indexSize: number
    uptime: number
    version: string
  }
}

@Injectable()
export class DatabaseService implements OnModuleInit, OnModuleDestroy {
  private redisClient: RedisClientType
  private redisSubscriber: RedisClientType
  private redisPublisher: RedisClientType

  constructor(
    @InjectDataSource('mysql')
    private readonly mysqlDataSource: DataSource,
    @InjectDataSource('postgresql')
    private readonly postgresDataSource: DataSource,
    private readonly configService: ConfigService
  ) {}

  async onModuleInit() {
    await this.initializeRedis()
    await this.initializePostgreSQL()
    await this.runHealthChecks()
  }

  async onModuleDestroy() {
    await this.cleanup()
  }

  /**
   * 获取数据库统计信息
   */
  async getDatabaseStats(): Promise<DatabaseStats> {
    const [mysqlStats, redisStats, postgresStats] = await Promise.all([
      this.getMySQLStats(),
      this.getRedisStats(),
      this.getPostgreSQLStats()
    ])

    return {
      mysql: mysqlStats,
      redis: redisStats,
      postgresql: postgresStats
    }
  }

  /**
   * MySQL 操作方法
   */
  async executeMySQLQuery(query: string, parameters?: any[]): Promise<any> {
    return this.mysqlDataSource.query(query, parameters)
  }

  async getMySQLConnection() {
    return this.mysqlDataSource.createQueryRunner()
  }

  async beginMySQLTransaction() {
    const queryRunner = this.mysqlDataSource.createQueryRunner()
    await queryRunner.connect()
    await queryRunner.startTransaction()
    return queryRunner
  }

  /**
   * Redis 操作方法
   */
  async redisGet(key: string): Promise<string | null> {
    return this.redisClient.get(key)
  }

  async redisSet(key: string, value: string, ttl?: number): Promise<void> {
    if (ttl) {
      await this.redisClient.setEx(key, ttl, value)
    } else {
      await this.redisClient.set(key, value)
    }
  }

  async redisDel(key: string): Promise<number> {
    return this.redisClient.del(key)
  }

  async redisExists(key: string): Promise<number> {
    return this.redisClient.exists(key)
  }

  async redisIncr(key: string): Promise<number> {
    return this.redisClient.incr(key)
  }

  async redisExpire(key: string, seconds: number): Promise<boolean> {
    return this.redisClient.expire(key, seconds)
  }

  async redisHGet(key: string, field: string): Promise<string | undefined> {
    return this.redisClient.hGet(key, field)
  }

  async redisHSet(key: string, field: string, value: string): Promise<number> {
    return this.redisClient.hSet(key, field, value)
  }

  async redisHGetAll(key: string): Promise<Record<string, string>> {
    return this.redisClient.hGetAll(key)
  }

  async redisLPush(key: string, ...values: string[]): Promise<number> {
    return this.redisClient.lPush(key, values)
  }

  async redisRPop(key: string): Promise<string | null> {
    return this.redisClient.rPop(key)
  }

  async redisPublish(channel: string, message: string): Promise<number> {
    return this.redisPublisher.publish(channel, message)
  }

  async redisSubscribe(channel: string, callback: (message: string) => void): Promise<void> {
    await this.redisSubscriber.subscribe(channel, callback)
  }

  /**
   * PostgreSQL 向量操作方法
   */
  async executePostgreSQLQuery(query: string, parameters?: any[]): Promise<any> {
    return this.postgresDataSource.query(query, parameters)
  }

  async insertVector(table: string, id: string, vector: number[], metadata?: any): Promise<void> {
    const vectorString = `[${vector.join(',')}]`
    const metadataString = metadata ? JSON.stringify(metadata) : null
    
    await this.postgresDataSource.query(
      `INSERT INTO ${table} (id, vector, metadata) VALUES ($1, $2, $3) 
       ON CONFLICT (id) DO UPDATE SET vector = $2, metadata = $3`,
      [id, vectorString, metadataString]
    )
  }

  async searchSimilarVectors(
    table: string, 
    queryVector: number[], 
    limit: number = 10,
    threshold: number = 0.8
  ): Promise<any[]> {
    const vectorString = `[${queryVector.join(',')}]`
    
    return this.postgresDataSource.query(
      `SELECT id, metadata, (vector <=> $1::vector) as distance 
       FROM ${table} 
       WHERE (vector <=> $1::vector) < $2
       ORDER BY vector <=> $1::vector 
       LIMIT $3`,
      [vectorString, 1 - threshold, limit]
    )
  }

  /**
   * 分布式锁
   */
  async acquireLock(lockKey: string, ttl: number = 30): Promise<boolean> {
    const result = await this.redisClient.set(lockKey, '1', {
      NX: true,
      EX: ttl
    })
    return result === 'OK'
  }

  async releaseLock(lockKey: string): Promise<void> {
    await this.redisClient.del(lockKey)
  }

  /**
   * 缓存管理
   */
  async cacheSet(key: string, data: any, ttl: number = 3600): Promise<void> {
    const value = JSON.stringify(data)
    await this.redisClient.setEx(key, ttl, value)
  }

  async cacheGet<T>(key: string): Promise<T | null> {
    const value = await this.redisClient.get(key)
    return value ? JSON.parse(value) : null
  }

  async cacheInvalidate(pattern: string): Promise<void> {
    const keys = await this.redisClient.keys(pattern)
    if (keys.length > 0) {
      await this.redisClient.del(keys)
    }
  }

  /**
   * 定时任务 - 数据库维护
   */
  @Cron(CronExpression.EVERY_DAY_AT_2AM)
  async performDailyMaintenance() {
    console.log('开始执行数据库日常维护...')
    
    try {
      // MySQL 维护
      await this.optimizeMySQLTables()
      
      // Redis 维护
      await this.cleanupExpiredRedisKeys()
      
      // PostgreSQL 维护
      await this.vacuumPostgreSQLTables()
      
      console.log('数据库日常维护完成')
    } catch (error) {
      console.error('数据库维护失败:', error)
    }
  }

  /**
   * 定时任务 - 性能监控
   */
  @Cron(CronExpression.EVERY_5_MINUTES)
  async performHealthChecks() {
    try {
      const stats = await this.getDatabaseStats()
      
      // 检查连接数
      if (stats.mysql.connections > 80) {
        console.warn('MySQL连接数过高:', stats.mysql.connections)
      }
      
      // 检查Redis内存使用
      if (stats.redis.memory > 1024 * 1024 * 1024) { // 1GB
        console.warn('Redis内存使用过高:', stats.redis.memory)
      }
      
      // 检查慢查询
      if (stats.mysql.slowQueries > 100) {
        console.warn('MySQL慢查询过多:', stats.mysql.slowQueries)
      }
      
    } catch (error) {
      console.error('健康检查失败:', error)
    }
  }

  // 私有方法

  private async initializeRedis() {
    const redisConfig = {
      host: this.configService.get('REDIS_HOST', 'localhost'),
      port: parseInt(this.configService.get('REDIS_PORT', '6379')),
      password: this.configService.get('REDIS_PASSWORD'),
      db: parseInt(this.configService.get('REDIS_DB', '0'))
    }

    this.redisClient = createClient(redisConfig)
    this.redisSubscriber = createClient(redisConfig)
    this.redisPublisher = createClient(redisConfig)

    await Promise.all([
      this.redisClient.connect(),
      this.redisSubscriber.connect(),
      this.redisPublisher.connect()
    ])

    console.log('Redis连接已建立')
  }

  private async initializePostgreSQL() {
    // 确保pgvector扩展已安装
    try {
      await this.postgresDataSource.query('CREATE EXTENSION IF NOT EXISTS vector')
      console.log('PostgreSQL向量扩展已启用')
    } catch (error) {
      console.error('启用向量扩展失败:', error)
    }
  }

  private async runHealthChecks() {
    // 测试所有数据库连接
    try {
      await this.mysqlDataSource.query('SELECT 1')
      await this.redisClient.ping()
      await this.postgresDataSource.query('SELECT 1')
      console.log('所有数据库连接正常')
    } catch (error) {
      console.error('数据库连接检查失败:', error)
      throw error
    }
  }

  private async getMySQLStats() {
    const [status, variables] = await Promise.all([
      this.mysqlDataSource.query('SHOW STATUS'),
      this.mysqlDataSource.query('SHOW VARIABLES LIKE "version"')
    ])

    const statusMap = new Map(status.map((row: any) => [row.Variable_name, row.Value]))
    
    return {
      connections: parseInt(statusMap.get('Threads_connected') || '0'),
      queries: parseInt(statusMap.get('Queries') || '0'),
      slowQueries: parseInt(statusMap.get('Slow_queries') || '0'),
      uptime: parseInt(statusMap.get('Uptime') || '0'),
      version: variables[0]?.Value || 'unknown'
    }
  }

  private async getRedisStats() {
    const info = await this.redisClient.info()
    const lines = info.split('\r\n')
    const stats: any = {}
    
    lines.forEach(line => {
      const [key, value] = line.split(':')
      if (key && value) {
        stats[key] = value
      }
    })

    return {
      connections: parseInt(stats.connected_clients || '0'),
      memory: parseInt(stats.used_memory || '0'),
      keys: parseInt(stats.db0?.split(',')[0]?.split('=')[1] || '0'),
      hits: parseInt(stats.keyspace_hits || '0'),
      misses: parseInt(stats.keyspace_misses || '0'),
      uptime: parseInt(stats.uptime_in_seconds || '0')
    }
  }

  private async getPostgreSQLStats() {
    const [activity, version] = await Promise.all([
      this.postgresDataSource.query('SELECT count(*) as connections FROM pg_stat_activity'),
      this.postgresDataSource.query('SELECT version()')
    ])

    // 获取向量表统计
    const vectorStats = await this.postgresDataSource.query(`
      SELECT 
        schemaname,
        tablename,
        n_tup_ins as inserts,
        n_tup_upd as updates,
        n_tup_del as deletes
      FROM pg_stat_user_tables 
      WHERE tablename LIKE '%vector%'
    `)

    return {
      connections: parseInt(activity[0]?.connections || '0'),
      queries: 0, // 需要从pg_stat_statements获取
      vectorCount: vectorStats.reduce((sum: number, table: any) => sum + parseInt(table.inserts || '0'), 0),
      indexSize: 0, // 需要计算索引大小
      uptime: 0, // 需要从pg_postmaster_start_time计算
      version: version[0]?.version || 'unknown'
    }
  }

  private async optimizeMySQLTables() {
    const tables = await this.mysqlDataSource.query('SHOW TABLES')
    for (const table of tables) {
      const tableName = Object.values(table)[0]
      await this.mysqlDataSource.query(`OPTIMIZE TABLE ${tableName}`)
    }
  }

  private async cleanupExpiredRedisKeys() {
    // Redis会自动清理过期键，这里可以添加自定义清理逻辑
    const info = await this.redisClient.info('keyspace')
    console.log('Redis键空间信息:', info)
  }

  private async vacuumPostgreSQLTables() {
    await this.postgresDataSource.query('VACUUM ANALYZE')
  }

  private async cleanup() {
    await Promise.all([
      this.redisClient?.quit(),
      this.redisSubscriber?.quit(),
      this.redisPublisher?.quit()
    ])
  }
}
