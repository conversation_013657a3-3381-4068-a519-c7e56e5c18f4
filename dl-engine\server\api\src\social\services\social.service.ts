/**
 * DL-Engine 社交功能服务
 * 
 * 核心功能：
 * - 好友系统管理
 * - 群组创建和管理
 * - 消息通信系统
 * - 内容审核机制
 * - 社交活动统计
 * - 隐私控制设置
 */

import { Injectable, NotFoundException, ConflictException, ForbiddenException } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { Repository, In } from 'typeorm'
import { ConfigService } from '@nestjs/config'

import { User } from '../entities/user.entity'
import { Friendship, FriendshipStatus } from '../entities/friendship.entity'
import { Group, GroupType, GroupVisibility } from '../entities/group.entity'
import { GroupMember, MemberRole } from '../entities/group-member.entity'
import { Message, MessageType } from '../entities/message.entity'
import { SocialActivity } from '../entities/social-activity.entity'

@Injectable()
export class SocialService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Friendship)
    private readonly friendshipRepository: Repository<Friendship>,
    @InjectRepository(Group)
    private readonly groupRepository: Repository<Group>,
    @InjectRepository(GroupMember)
    private readonly groupMemberRepository: Repository<GroupMember>,
    @InjectRepository(Message)
    private readonly messageRepository: Repository<Message>,
    @InjectRepository(SocialActivity)
    private readonly activityRepository: Repository<SocialActivity>,
    private readonly configService: ConfigService
  ) {}

  /**
   * 发送好友请求
   */
  async sendFriendRequest(fromUserId: string, toUserId: string, message?: string): Promise<Friendship> {
    if (fromUserId === toUserId) {
      throw new ConflictException('不能添加自己为好友')
    }

    // 检查目标用户是否存在
    const toUser = await this.userRepository.findOne({ where: { id: toUserId } })
    if (!toUser) {
      throw new NotFoundException('用户不存在')
    }

    // 检查是否已经是好友或有待处理的请求
    const existingFriendship = await this.friendshipRepository.findOne({
      where: [
        { fromUserId, toUserId },
        { fromUserId: toUserId, toUserId: fromUserId }
      ]
    })

    if (existingFriendship) {
      if (existingFriendship.status === FriendshipStatus.ACCEPTED) {
        throw new ConflictException('已经是好友关系')
      } else if (existingFriendship.status === FriendshipStatus.PENDING) {
        throw new ConflictException('已有待处理的好友请求')
      }
    }

    // 创建好友请求
    const friendship = this.friendshipRepository.create({
      fromUserId,
      toUserId,
      status: FriendshipStatus.PENDING,
      message,
      requestedAt: new Date()
    })

    const savedFriendship = await this.friendshipRepository.save(friendship)

    // 记录社交活动
    await this.recordSocialActivity(fromUserId, 'friend_request_sent', {
      targetUserId: toUserId,
      friendshipId: savedFriendship.id
    })

    // 发送通知
    await this.sendFriendRequestNotification(savedFriendship)

    return savedFriendship
  }

  /**
   * 接受好友请求
   */
  async acceptFriendRequest(friendshipId: string, userId: string): Promise<Friendship> {
    const friendship = await this.friendshipRepository.findOne({
      where: { id: friendshipId },
      relations: ['fromUser', 'toUser']
    })

    if (!friendship) {
      throw new NotFoundException('好友请求不存在')
    }

    if (friendship.toUserId !== userId) {
      throw new ForbiddenException('无权处理此好友请求')
    }

    if (friendship.status !== FriendshipStatus.PENDING) {
      throw new ConflictException('好友请求已处理')
    }

    // 更新好友关系状态
    friendship.status = FriendshipStatus.ACCEPTED
    friendship.acceptedAt = new Date()

    const updatedFriendship = await this.friendshipRepository.save(friendship)

    // 记录社交活动
    await this.recordSocialActivity(userId, 'friend_request_accepted', {
      targetUserId: friendship.fromUserId,
      friendshipId: friendship.id
    })

    await this.recordSocialActivity(friendship.fromUserId, 'friend_added', {
      targetUserId: userId,
      friendshipId: friendship.id
    })

    return updatedFriendship
  }

  /**
   * 拒绝好友请求
   */
  async rejectFriendRequest(friendshipId: string, userId: string): Promise<void> {
    const friendship = await this.friendshipRepository.findOne({
      where: { id: friendshipId }
    })

    if (!friendship) {
      throw new NotFoundException('好友请求不存在')
    }

    if (friendship.toUserId !== userId) {
      throw new ForbiddenException('无权处理此好友请求')
    }

    if (friendship.status !== FriendshipStatus.PENDING) {
      throw new ConflictException('好友请求已处理')
    }

    // 更新状态为拒绝
    friendship.status = FriendshipStatus.REJECTED
    friendship.rejectedAt = new Date()

    await this.friendshipRepository.save(friendship)

    // 记录社交活动
    await this.recordSocialActivity(userId, 'friend_request_rejected', {
      targetUserId: friendship.fromUserId,
      friendshipId: friendship.id
    })
  }

  /**
   * 获取好友列表
   */
  async getFriends(userId: string, options: any = {}): Promise<{
    friends: any[]
    total: number
    online: number
  }> {
    const { page = 1, limit = 50, status = 'online' } = options

    const queryBuilder = this.friendshipRepository.createQueryBuilder('friendship')
      .leftJoinAndSelect('friendship.fromUser', 'fromUser')
      .leftJoinAndSelect('friendship.toUser', 'toUser')
      .leftJoinAndSelect('fromUser.profile', 'fromProfile')
      .leftJoinAndSelect('toUser.profile', 'toProfile')
      .where('friendship.status = :status', { status: FriendshipStatus.ACCEPTED })
      .andWhere('(friendship.fromUserId = :userId OR friendship.toUserId = :userId)', { userId })

    if (status === 'online') {
      queryBuilder.andWhere(
        '(fromUser.isOnline = true OR toUser.isOnline = true)'
      )
    }

    const offset = (page - 1) * limit
    queryBuilder.skip(offset).take(limit)

    const [friendships, total] = await queryBuilder.getManyAndCount()

    const friends = friendships.map(friendship => {
      const friend = friendship.fromUserId === userId ? friendship.toUser : friendship.fromUser
      return {
        id: friend.id,
        username: friend.username,
        displayName: friend.displayName,
        avatarUrl: friend.avatarUrl,
        isOnline: friend.isOnline,
        lastActiveAt: friend.lastActiveAt,
        profile: friend.profile,
        friendshipId: friendship.id,
        friendsSince: friendship.acceptedAt
      }
    })

    const online = friends.filter(f => f.isOnline).length

    return { friends, total, online }
  }

  /**
   * 创建群组
   */
  async createGroup(creatorId: string, groupData: any): Promise<Group> {
    // 验证群组数据
    this.validateGroupData(groupData)

    // 创建群组
    const group = this.groupRepository.create({
      name: groupData.name,
      description: groupData.description,
      type: groupData.type || GroupType.GENERAL,
      visibility: groupData.visibility || GroupVisibility.PRIVATE,
      creatorId,
      maxMembers: groupData.maxMembers || 100,
      settings: {
        allowInvites: groupData.allowInvites !== false,
        requireApproval: groupData.requireApproval === true,
        allowFileSharing: groupData.allowFileSharing !== false,
        allowVoiceChat: groupData.allowVoiceChat !== false,
        allowVideoChat: groupData.allowVideoChat !== false
      },
      statistics: {
        memberCount: 1,
        messageCount: 0,
        activeMembers: 1,
        lastActivity: new Date()
      }
    })

    const savedGroup = await this.groupRepository.save(group)

    // 添加创建者为管理员
    await this.groupMemberRepository.save({
      groupId: savedGroup.id,
      userId: creatorId,
      role: MemberRole.ADMIN,
      joinedAt: new Date(),
      invitedBy: creatorId
    })

    // 记录社交活动
    await this.recordSocialActivity(creatorId, 'group_created', {
      groupId: savedGroup.id,
      groupName: savedGroup.name
    })

    return savedGroup
  }

  /**
   * 邀请用户加入群组
   */
  async inviteToGroup(groupId: string, inviterUserId: string, inviteeUserId: string): Promise<void> {
    // 检查群组是否存在
    const group = await this.groupRepository.findOne({ where: { id: groupId } })
    if (!group) {
      throw new NotFoundException('群组不存在')
    }

    // 检查邀请者权限
    const inviterMember = await this.groupMemberRepository.findOne({
      where: { groupId, userId: inviterUserId }
    })

    if (!inviterMember || !this.canInviteMembers(inviterMember.role)) {
      throw new ForbiddenException('无权邀请成员')
    }

    // 检查被邀请用户是否已在群组中
    const existingMember = await this.groupMemberRepository.findOne({
      where: { groupId, userId: inviteeUserId }
    })

    if (existingMember) {
      throw new ConflictException('用户已在群组中')
    }

    // 检查群组成员数量限制
    const memberCount = await this.groupMemberRepository.count({ where: { groupId } })
    if (memberCount >= group.maxMembers) {
      throw new ConflictException('群组成员已满')
    }

    // 添加成员
    await this.groupMemberRepository.save({
      groupId,
      userId: inviteeUserId,
      role: MemberRole.MEMBER,
      joinedAt: new Date(),
      invitedBy: inviterUserId
    })

    // 更新群组统计
    await this.updateGroupStatistics(groupId)

    // 记录社交活动
    await this.recordSocialActivity(inviterUserId, 'group_member_invited', {
      groupId,
      targetUserId: inviteeUserId
    })
  }

  /**
   * 发送消息
   */
  async sendMessage(senderId: string, messageData: any): Promise<Message> {
    // 验证消息数据
    this.validateMessageData(messageData)

    // 检查发送权限
    await this.checkMessagePermission(senderId, messageData)

    // 内容审核
    const moderatedContent = await this.moderateContent(messageData.content)

    // 创建消息
    const message = this.messageRepository.create({
      senderId,
      recipientId: messageData.recipientId,
      groupId: messageData.groupId,
      type: messageData.type || MessageType.TEXT,
      content: moderatedContent.content,
      metadata: {
        ...messageData.metadata,
        moderated: moderatedContent.moderated,
        originalContent: moderatedContent.moderated ? messageData.content : undefined
      }
    })

    const savedMessage = await this.messageRepository.save(message)

    // 更新相关统计
    if (messageData.groupId) {
      await this.updateGroupMessageCount(messageData.groupId)
    }

    // 记录社交活动
    await this.recordSocialActivity(senderId, 'message_sent', {
      messageId: savedMessage.id,
      recipientId: messageData.recipientId,
      groupId: messageData.groupId,
      messageType: messageData.type
    })

    return savedMessage
  }

  /**
   * 获取消息历史
   */
  async getMessages(userId: string, query: any): Promise<{
    messages: Message[]
    total: number
    hasMore: boolean
  }> {
    const { recipientId, groupId, page = 1, limit = 50, before } = query

    const queryBuilder = this.messageRepository.createQueryBuilder('message')
      .leftJoinAndSelect('message.sender', 'sender')
      .leftJoinAndSelect('message.recipient', 'recipient')

    if (groupId) {
      // 群组消息
      queryBuilder.where('message.groupId = :groupId', { groupId })
      
      // 检查用户是否在群组中
      const membership = await this.groupMemberRepository.findOne({
        where: { groupId, userId }
      })
      
      if (!membership) {
        throw new ForbiddenException('无权查看群组消息')
      }
    } else if (recipientId) {
      // 私聊消息
      queryBuilder.where(
        '(message.senderId = :userId AND message.recipientId = :recipientId) OR ' +
        '(message.senderId = :recipientId AND message.recipientId = :userId)',
        { userId, recipientId }
      )
    } else {
      throw new BadRequestException('必须指定 recipientId 或 groupId')
    }

    if (before) {
      queryBuilder.andWhere('message.createdAt < :before', { before: new Date(before) })
    }

    queryBuilder.orderBy('message.createdAt', 'DESC')

    const offset = (page - 1) * limit
    queryBuilder.skip(offset).take(limit + 1) // 多取一条判断是否还有更多

    const messages = await queryBuilder.getMany()
    const hasMore = messages.length > limit

    if (hasMore) {
      messages.pop() // 移除多取的那条
    }

    return {
      messages: messages.reverse(), // 按时间正序返回
      total: messages.length,
      hasMore
    }
  }

  /**
   * 获取社交活动动态
   */
  async getSocialFeed(userId: string, options: any = {}): Promise<{
    activities: SocialActivity[]
    total: number
  }> {
    const { page = 1, limit = 20, type } = options

    // 获取用户的好友列表
    const friends = await this.getFriends(userId)
    const friendIds = friends.friends.map(f => f.id)

    const queryBuilder = this.activityRepository.createQueryBuilder('activity')
      .leftJoinAndSelect('activity.user', 'user')
      .leftJoinAndSelect('activity.targetUser', 'targetUser')
      .where('activity.userId IN (:...userIds)', { userIds: [userId, ...friendIds] })

    if (type) {
      queryBuilder.andWhere('activity.type = :type', { type })
    }

    queryBuilder.orderBy('activity.createdAt', 'DESC')

    const offset = (page - 1) * limit
    queryBuilder.skip(offset).take(limit)

    const [activities, total] = await queryBuilder.getManyAndCount()

    return { activities, total }
  }

  /**
   * 验证群组数据
   */
  private validateGroupData(groupData: any): void {
    if (!groupData.name || groupData.name.trim().length === 0) {
      throw new BadRequestException('群组名称不能为空')
    }

    if (groupData.name.length > 50) {
      throw new BadRequestException('群组名称不能超过50个字符')
    }

    if (groupData.description && groupData.description.length > 500) {
      throw new BadRequestException('群组描述不能超过500个字符')
    }
  }

  /**
   * 验证消息数据
   */
  private validateMessageData(messageData: any): void {
    if (!messageData.content || messageData.content.trim().length === 0) {
      throw new BadRequestException('消息内容不能为空')
    }

    if (messageData.content.length > 2000) {
      throw new BadRequestException('消息内容不能超过2000个字符')
    }

    if (!messageData.recipientId && !messageData.groupId) {
      throw new BadRequestException('必须指定接收者或群组')
    }
  }

  /**
   * 检查是否可以邀请成员
   */
  private canInviteMembers(role: MemberRole): boolean {
    return role === MemberRole.ADMIN || role === MemberRole.MODERATOR
  }

  /**
   * 检查消息发送权限
   */
  private async checkMessagePermission(senderId: string, messageData: any): Promise<void> {
    if (messageData.groupId) {
      // 检查是否在群组中
      const membership = await this.groupMemberRepository.findOne({
        where: { groupId: messageData.groupId, userId: senderId }
      })

      if (!membership) {
        throw new ForbiddenException('不在群组中，无法发送消息')
      }
    } else if (messageData.recipientId) {
      // 检查是否是好友关系（可选）
      const friendship = await this.friendshipRepository.findOne({
        where: [
          { fromUserId: senderId, toUserId: messageData.recipientId, status: FriendshipStatus.ACCEPTED },
          { fromUserId: messageData.recipientId, toUserId: senderId, status: FriendshipStatus.ACCEPTED }
        ]
      })

      // 这里可以根据业务需求决定是否必须是好友才能发消息
      // if (!friendship) {
      //   throw new ForbiddenException('只能向好友发送消息')
      // }
    }
  }

  /**
   * 内容审核
   */
  private async moderateContent(content: string): Promise<{
    content: string
    moderated: boolean
  }> {
    // 这里应该集成内容审核服务
    // 暂时只做简单的敏感词过滤
    const sensitiveWords = ['spam', 'abuse', 'inappropriate']
    let moderated = false
    let moderatedContent = content

    for (const word of sensitiveWords) {
      if (content.toLowerCase().includes(word)) {
        moderatedContent = moderatedContent.replace(new RegExp(word, 'gi'), '***')
        moderated = true
      }
    }

    return { content: moderatedContent, moderated }
  }

  /**
   * 更新群组统计
   */
  private async updateGroupStatistics(groupId: string): Promise<void> {
    const memberCount = await this.groupMemberRepository.count({ where: { groupId } })
    
    await this.groupRepository.update(groupId, {
      'statistics.memberCount': memberCount,
      'statistics.lastActivity': new Date()
    })
  }

  /**
   * 更新群组消息数量
   */
  private async updateGroupMessageCount(groupId: string): Promise<void> {
    await this.groupRepository.update(groupId, {
      'statistics.messageCount': () => 'statistics.messageCount + 1',
      'statistics.lastActivity': new Date()
    })
  }

  /**
   * 记录社交活动
   */
  private async recordSocialActivity(
    userId: string,
    type: string,
    data: any
  ): Promise<void> {
    const activity = this.activityRepository.create({
      userId,
      type,
      data,
      createdAt: new Date()
    })

    await this.activityRepository.save(activity)
  }

  /**
   * 发送好友请求通知
   */
  private async sendFriendRequestNotification(friendship: Friendship): Promise<void> {
    // 这里应该集成通知服务
    console.log(`发送好友请求通知: ${friendship.toUserId}`)
  }
}
