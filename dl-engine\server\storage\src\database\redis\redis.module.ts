import { Module, Global } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { RedisService } from './redis.service';
import { RedisController } from './redis.controller';
import { CacheService } from './cache.service';
import { SessionService } from './session.service';
import { LockService } from './lock.service';
import { QueueService } from './queue.service';
import { PubSubService } from './pubsub.service';

@Global()
@Module({
  imports: [ConfigModule],
  providers: [
    {
      provide: 'REDIS_CLIENT',
      useFactory: async (configService: ConfigService) => {
        const Redis = require('redis');
        
        const isCluster = configService.get('REDIS_CLUSTER', false);
        
        if (isCluster) {
          // 集群模式
          const clusterNodes = configService.get('REDIS_CLUSTER_NODES', 'localhost:6379').split(',');
          const client = Redis.createCluster({
            rootNodes: clusterNodes.map(node => {
              const [host, port] = node.split(':');
              return { url: `redis://${host}:${port || 6379}` };
            }),
            defaults: {
              password: configService.get('REDIS_PASSWORD'),
              database: configService.get('REDIS_DATABASE', 0),
            },
          });
          
          await client.connect();
          return client;
        } else {
          // 单机模式
          const client = Redis.createClient({
            url: `redis://${configService.get('REDIS_HOST', 'localhost')}:${configService.get('REDIS_PORT', 6379)}`,
            password: configService.get('REDIS_PASSWORD'),
            database: configService.get('REDIS_DATABASE', 0),
            socket: {
              connectTimeout: 10000,
              lazyConnect: true,
            },
            retry_strategy: (options) => {
              if (options.error && options.error.code === 'ECONNREFUSED') {
                return new Error('Redis server connection refused');
              }
              if (options.total_retry_time > 1000 * 60 * 60) {
                return new Error('Retry time exhausted');
              }
              if (options.attempt > 10) {
                return undefined;
              }
              return Math.min(options.attempt * 100, 3000);
            },
          });
          
          await client.connect();
          return client;
        }
      },
      inject: [ConfigService],
    },
    {
      provide: 'REDIS_SUBSCRIBER',
      useFactory: async (configService: ConfigService) => {
        const Redis = require('redis');
        
        const subscriber = Redis.createClient({
          url: `redis://${configService.get('REDIS_HOST', 'localhost')}:${configService.get('REDIS_PORT', 6379)}`,
          password: configService.get('REDIS_PASSWORD'),
          database: configService.get('REDIS_DATABASE', 0),
        });
        
        await subscriber.connect();
        return subscriber;
      },
      inject: [ConfigService],
    },
    {
      provide: 'REDIS_PUBLISHER',
      useFactory: async (configService: ConfigService) => {
        const Redis = require('redis');
        
        const publisher = Redis.createClient({
          url: `redis://${configService.get('REDIS_HOST', 'localhost')}:${configService.get('REDIS_PORT', 6379)}`,
          password: configService.get('REDIS_PASSWORD'),
          database: configService.get('REDIS_DATABASE', 0),
        });
        
        await publisher.connect();
        return publisher;
      },
      inject: [ConfigService],
    },
    RedisService,
    CacheService,
    SessionService,
    LockService,
    QueueService,
    PubSubService,
  ],
  controllers: [RedisController],
  exports: [
    'REDIS_CLIENT',
    'REDIS_SUBSCRIBER', 
    'REDIS_PUBLISHER',
    RedisService,
    CacheService,
    SessionService,
    LockService,
    QueueService,
    PubSubService,
  ],
})
export class RedisModule {}
