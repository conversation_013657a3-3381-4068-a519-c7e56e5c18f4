import { useState, useEffect, useCallback } from 'react'
import { message } from 'antd'
import { useI18n } from './useI18n'

// PWA安装事件接口
interface BeforeInstallPromptEvent extends Event {
  readonly platforms: string[]
  readonly userChoice: Promise<{
    outcome: 'accepted' | 'dismissed'
    platform: string
  }>
  prompt(): Promise<void>
}

// PWA Hook
export const usePWA = () => {
  const { t } = useI18n()
  const [isInstallable, setIsInstallable] = useState(false)
  const [isInstalled, setIsInstalled] = useState(false)
  const [isOnline, setIsOnline] = useState(navigator.onLine)
  const [installPrompt, setInstallPrompt] = useState<BeforeInstallPromptEvent | null>(null)
  const [updateAvailable, setUpdateAvailable] = useState(false)

  // 检查是否已安装PWA
  const checkIfInstalled = useCallback(() => {
    // 检查是否在独立模式下运行
    const isStandalone = window.matchMedia('(display-mode: standalone)').matches
    // 检查是否在iOS Safari的独立模式下
    const isIOSStandalone = (window.navigator as any).standalone === true
    // 检查是否在Android Chrome的独立模式下
    const isAndroidStandalone = window.matchMedia('(display-mode: standalone)').matches
    
    setIsInstalled(isStandalone || isIOSStandalone || isAndroidStandalone)
  }, [])

  // 显示安装提示
  const showInstallPrompt = useCallback(async () => {
    if (!installPrompt) {
      message.warning(t('pwa.installNotAvailable'))
      return
    }

    try {
      // 显示安装提示
      await installPrompt.prompt()
      
      // 等待用户选择
      const choiceResult = await installPrompt.userChoice
      
      if (choiceResult.outcome === 'accepted') {
        message.success(t('pwa.installSuccess'))
        setIsInstallable(false)
        setInstallPrompt(null)
      } else {
        message.info(t('pwa.installCancelled'))
      }
    } catch (error) {
      console.error('PWA安装失败:', error)
      message.error(t('pwa.installError'))
    }
  }, [installPrompt, t])

  // 检查更新
  const checkForUpdates = useCallback(async () => {
    if ('serviceWorker' in navigator) {
      try {
        const registration = await navigator.serviceWorker.getRegistration()
        if (registration) {
          await registration.update()
        }
      } catch (error) {
        console.error('检查更新失败:', error)
      }
    }
  }, [])

  // 应用更新
  const applyUpdate = useCallback(async () => {
    if ('serviceWorker' in navigator) {
      try {
        const registration = await navigator.serviceWorker.getRegistration()
        if (registration && registration.waiting) {
          // 发送消息给等待中的Service Worker
          registration.waiting.postMessage({ type: 'SKIP_WAITING' })
          
          // 监听控制器变化
          navigator.serviceWorker.addEventListener('controllerchange', () => {
            window.location.reload()
          })
        }
      } catch (error) {
        console.error('应用更新失败:', error)
        message.error(t('pwa.updateError'))
      }
    }
  }, [t])

  // 获取网络状态
  const getNetworkStatus = useCallback(() => {
    return {
      online: navigator.onLine,
      connection: (navigator as any).connection || (navigator as any).mozConnection || (navigator as any).webkitConnection,
      effectiveType: ((navigator as any).connection || {}).effectiveType || 'unknown'
    }
  }, [])

  // 缓存重要资源
  const cacheImportantResources = useCallback(async (urls: string[]) => {
    if ('caches' in window) {
      try {
        const cache = await caches.open('dl-engine-important')
        await cache.addAll(urls)
        message.success(t('pwa.cacheSuccess'))
      } catch (error) {
        console.error('缓存资源失败:', error)
        message.error(t('pwa.cacheError'))
      }
    }
  }, [t])

  // 清理缓存
  const clearCache = useCallback(async () => {
    if ('caches' in window) {
      try {
        const cacheNames = await caches.keys()
        await Promise.all(
          cacheNames.map(cacheName => caches.delete(cacheName))
        )
        message.success(t('pwa.clearCacheSuccess'))
      } catch (error) {
        console.error('清理缓存失败:', error)
        message.error(t('pwa.clearCacheError'))
      }
    }
  }, [t])

  // 获取缓存大小
  const getCacheSize = useCallback(async () => {
    if ('caches' in window && 'storage' in navigator && 'estimate' in navigator.storage) {
      try {
        const estimate = await navigator.storage.estimate()
        return {
          used: estimate.usage || 0,
          available: estimate.quota || 0,
          percentage: estimate.usage && estimate.quota ? (estimate.usage / estimate.quota) * 100 : 0
        }
      } catch (error) {
        console.error('获取缓存大小失败:', error)
        return null
      }
    }
    return null
  }, [])

  useEffect(() => {
    // 检查是否已安装
    checkIfInstalled()

    // 监听安装提示事件
    const handleBeforeInstallPrompt = (e: Event) => {
      e.preventDefault()
      setInstallPrompt(e as BeforeInstallPromptEvent)
      setIsInstallable(true)
    }

    // 监听应用安装事件
    const handleAppInstalled = () => {
      setIsInstalled(true)
      setIsInstallable(false)
      setInstallPrompt(null)
      message.success(t('pwa.appInstalled'))
    }

    // 监听网络状态变化
    const handleOnline = () => {
      setIsOnline(true)
      message.success(t('pwa.backOnline'))
    }

    const handleOffline = () => {
      setIsOnline(false)
      message.warning(t('pwa.goingOffline'))
    }

    // 监听Service Worker更新
    const handleServiceWorkerUpdate = () => {
      setUpdateAvailable(true)
      message.info({
        content: t('pwa.updateAvailable'),
        key: 'sw-update',
        duration: 0,
        onClick: applyUpdate
      })
    }

    // 添加事件监听器
    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
    window.addEventListener('appinstalled', handleAppInstalled)
    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    // Service Worker事件监听
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.addEventListener('message', (event) => {
        if (event.data && event.data.type === 'SW_UPDATE_AVAILABLE') {
          handleServiceWorkerUpdate()
        }
      })

      // 检查现有的Service Worker
      navigator.serviceWorker.getRegistration().then((registration) => {
        if (registration) {
          registration.addEventListener('updatefound', () => {
            const newWorker = registration.installing
            if (newWorker) {
              newWorker.addEventListener('statechange', () => {
                if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                  handleServiceWorkerUpdate()
                }
              })
            }
          })
        }
      })
    }

    // 清理函数
    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
      window.removeEventListener('appinstalled', handleAppInstalled)
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [checkIfInstalled, t, applyUpdate])

  return {
    isInstallable,
    isInstalled,
    isOnline,
    updateAvailable,
    showInstallPrompt,
    checkForUpdates,
    applyUpdate,
    getNetworkStatus,
    cacheImportantResources,
    clearCache,
    getCacheSize
  }
}
