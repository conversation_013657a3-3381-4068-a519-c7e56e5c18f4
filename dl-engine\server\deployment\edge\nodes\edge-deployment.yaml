# DL-Engine 边缘计算节点部署配置
# 支持就近访问和内容分发的边缘计算架构

apiVersion: v1
kind: Namespace
metadata:
  name: dl-engine-edge
  labels:
    name: dl-engine-edge
    app.kubernetes.io/name: dl-engine
    app.kubernetes.io/component: edge

---
# 边缘节点配置映射
apiVersion: v1
kind: ConfigMap
metadata:
  name: edge-node-config
  namespace: dl-engine-edge
  labels:
    app.kubernetes.io/name: dl-engine
    app.kubernetes.io/component: edge-config
data:
  # 边缘节点配置
  edge.yaml: |
    edge:
      # 节点标识
      nodeId: "${NODE_ID}"
      region: "${EDGE_REGION}"
      zone: "${EDGE_ZONE}"
      
      # 网络配置
      network:
        listenPort: 3030
        healthPort: 8080
        metricsPort: 9090
        
      # 缓存配置
      cache:
        enabled: true
        maxSize: "10GB"
        ttl: 3600
        types:
          - "image/*"
          - "video/*"
          - "audio/*"
          - "application/octet-stream"
          - "model/gltf+json"
          - "model/gltf-binary"
      
      # 代理配置
      proxy:
        upstream:
          - "http://dl-engine-gateway.dl-engine.svc.cluster.local:3030"
        timeout: 30s
        retries: 3
        
      # 负载均衡配置
      loadBalancer:
        algorithm: "round_robin"
        healthCheck:
          enabled: true
          interval: 10s
          timeout: 5s
          path: "/health"
        
      # 监控配置
      monitoring:
        enabled: true
        prometheus:
          enabled: true
          path: "/metrics"
        logging:
          level: "info"
          format: "json"

  # Nginx配置
  nginx.conf: |
    user nginx;
    worker_processes auto;
    error_log /var/log/nginx/error.log warn;
    pid /var/run/nginx.pid;
    
    events {
        worker_connections 1024;
        use epoll;
        multi_accept on;
    }
    
    http {
        include /etc/nginx/mime.types;
        default_type application/octet-stream;
        
        # 日志格式
        log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                        '$status $body_bytes_sent "$http_referer" '
                        '"$http_user_agent" "$http_x_forwarded_for" '
                        'rt=$request_time uct="$upstream_connect_time" '
                        'uht="$upstream_header_time" urt="$upstream_response_time"';
        
        access_log /var/log/nginx/access.log main;
        
        # 基础配置
        sendfile on;
        tcp_nopush on;
        tcp_nodelay on;
        keepalive_timeout 65;
        types_hash_max_size 2048;
        client_max_body_size 100M;
        
        # Gzip压缩
        gzip on;
        gzip_vary on;
        gzip_min_length 1024;
        gzip_proxied any;
        gzip_comp_level 6;
        gzip_types
            text/plain
            text/css
            text/xml
            text/javascript
            application/json
            application/javascript
            application/xml+rss
            application/atom+xml
            image/svg+xml;
        
        # 缓存配置
        proxy_cache_path /var/cache/nginx levels=1:2 keys_zone=edge_cache:10m max_size=10g inactive=60m use_temp_path=off;
        
        # 上游服务器
        upstream dl_engine_backend {
            least_conn;
            server dl-engine-gateway.dl-engine.svc.cluster.local:3030 max_fails=3 fail_timeout=30s;
            keepalive 32;
        }
        
        # 健康检查
        server {
            listen 8080;
            location /health {
                access_log off;
                return 200 "healthy\n";
                add_header Content-Type text/plain;
            }
            
            location /metrics {
                access_log off;
                stub_status on;
            }
        }
        
        # 主服务器
        server {
            listen 3030;
            server_name _;
            
            # 静态资源缓存
            location ~* \.(jpg|jpeg|png|gif|ico|css|js|woff|woff2|ttf|eot|svg)$ {
                proxy_cache edge_cache;
                proxy_cache_valid 200 1h;
                proxy_cache_valid 404 1m;
                proxy_cache_use_stale error timeout updating http_500 http_502 http_503 http_504;
                proxy_cache_lock on;
                
                add_header X-Cache-Status $upstream_cache_status;
                expires 1h;
                
                proxy_pass http://dl_engine_backend;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
            }
            
            # 3D模型文件缓存
            location ~* \.(gltf|glb|fbx|obj|dae|3ds|blend)$ {
                proxy_cache edge_cache;
                proxy_cache_valid 200 24h;
                proxy_cache_valid 404 1m;
                proxy_cache_use_stale error timeout updating http_500 http_502 http_503 http_504;
                proxy_cache_lock on;
                
                add_header X-Cache-Status $upstream_cache_status;
                expires 24h;
                
                proxy_pass http://dl_engine_backend;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
            }
            
            # 媒体文件缓存
            location ~* \.(mp4|mp3|wav|ogg|webm|avi|mov)$ {
                proxy_cache edge_cache;
                proxy_cache_valid 200 12h;
                proxy_cache_valid 404 1m;
                proxy_cache_use_stale error timeout updating http_500 http_502 http_503 http_504;
                proxy_cache_lock on;
                
                add_header X-Cache-Status $upstream_cache_status;
                expires 12h;
                
                proxy_pass http://dl_engine_backend;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
            }
            
            # API请求不缓存
            location /api/ {
                proxy_pass http://dl_engine_backend;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_set_header Connection "";
                proxy_http_version 1.1;
                
                # 超时配置
                proxy_connect_timeout 5s;
                proxy_send_timeout 30s;
                proxy_read_timeout 30s;
            }
            
            # WebSocket代理
            location /ws/ {
                proxy_pass http://dl_engine_backend;
                proxy_http_version 1.1;
                proxy_set_header Upgrade $http_upgrade;
                proxy_set_header Connection "upgrade";
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                
                # WebSocket超时配置
                proxy_connect_timeout 5s;
                proxy_send_timeout 3600s;
                proxy_read_timeout 3600s;
            }
            
            # 默认代理
            location / {
                proxy_pass http://dl_engine_backend;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_set_header Connection "";
                proxy_http_version 1.1;
                
                # 超时配置
                proxy_connect_timeout 5s;
                proxy_send_timeout 30s;
                proxy_read_timeout 30s;
            }
        }
    }

---
# 边缘节点部署
apiVersion: apps/v1
kind: Deployment
metadata:
  name: dl-engine-edge-node
  namespace: dl-engine-edge
  labels:
    app.kubernetes.io/name: dl-engine
    app.kubernetes.io/component: edge-node
    app.kubernetes.io/version: "1.0.0"
spec:
  replicas: 2
  selector:
    matchLabels:
      app.kubernetes.io/name: dl-engine
      app.kubernetes.io/component: edge-node
  template:
    metadata:
      labels:
        app.kubernetes.io/name: dl-engine
        app.kubernetes.io/component: edge-node
        app.kubernetes.io/version: "1.0.0"
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9090"
        prometheus.io/path: "/metrics"
    spec:
      containers:
        - name: nginx
          image: nginx:1.21-alpine
          ports:
            - containerPort: 3030
              name: http
              protocol: TCP
            - containerPort: 8080
              name: health
              protocol: TCP
            - containerPort: 9090
              name: metrics
              protocol: TCP
          env:
            - name: NODE_ID
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: EDGE_REGION
              value: "default"
            - name: EDGE_ZONE
              value: "zone-a"
          volumeMounts:
            - name: nginx-config
              mountPath: /etc/nginx/nginx.conf
              subPath: nginx.conf
            - name: cache-volume
              mountPath: /var/cache/nginx
            - name: log-volume
              mountPath: /var/log/nginx
          resources:
            limits:
              cpu: 1000m
              memory: 1Gi
            requests:
              cpu: 500m
              memory: 512Mi
          livenessProbe:
            httpGet:
              path: /health
              port: 8080
            initialDelaySeconds: 10
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3
          readinessProbe:
            httpGet:
              path: /health
              port: 8080
            initialDelaySeconds: 5
            periodSeconds: 5
            timeoutSeconds: 3
            failureThreshold: 3
      volumes:
        - name: nginx-config
          configMap:
            name: edge-node-config
        - name: cache-volume
          emptyDir:
            sizeLimit: 10Gi
        - name: log-volume
          emptyDir: {}
      nodeSelector:
        kubernetes.io/arch: amd64
      tolerations:
        - key: "edge-node"
          operator: "Equal"
          value: "true"
          effect: "NoSchedule"

---
# 边缘节点服务
apiVersion: v1
kind: Service
metadata:
  name: dl-engine-edge-service
  namespace: dl-engine-edge
  labels:
    app.kubernetes.io/name: dl-engine
    app.kubernetes.io/component: edge-service
spec:
  type: LoadBalancer
  ports:
    - port: 80
      targetPort: 3030
      protocol: TCP
      name: http
    - port: 8080
      targetPort: 8080
      protocol: TCP
      name: health
  selector:
    app.kubernetes.io/name: dl-engine
    app.kubernetes.io/component: edge-node

---
# 水平Pod自动扩缩容
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: dl-engine-edge-hpa
  namespace: dl-engine-edge
  labels:
    app.kubernetes.io/name: dl-engine
    app.kubernetes.io/component: edge-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: dl-engine-edge-node
  minReplicas: 2
  maxReplicas: 10
  metrics:
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 70
    - type: Resource
      resource:
        name: memory
        target:
          type: Utilization
          averageUtilization: 80
