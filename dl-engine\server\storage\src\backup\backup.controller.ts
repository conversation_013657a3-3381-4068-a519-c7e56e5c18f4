import { Controller, Get, Post, Put, Delete, Body, Param, Query, Logger } from '@nestjs/common';
import { BackupService } from './backup.service';
import { MigrationService } from './migration.service';

@Controller('backup')
export class BackupController {
  private readonly logger = new Logger(BackupController.name);

  constructor(
    private backupService: BackupService,
    private migrationService: MigrationService,
  ) {}

  // 备份管理
  @Post('full')
  async createFullBackup() {
    const jobId = await this.backupService.createFullBackup();
    return { jobId };
  }

  @Post('incremental')
  async createIncrementalBackup(@Body() { lastBackupId }: { lastBackupId?: string }) {
    const jobId = await this.backupService.createIncrementalBackup(lastBackupId);
    return { jobId };
  }

  @Get('jobs')
  async getBackupJobs(@Query('limit') limit?: string) {
    const jobs = this.backupService.getBackupJobs(limit ? parseInt(limit) : undefined);
    return { jobs };
  }

  @Get('jobs/:jobId')
  async getBackupJob(@Param('jobId') jobId: string) {
    const job = this.backupService.getBackupJob(jobId);
    if (!job) {
      return { error: 'Backup job not found' };
    }
    return { job };
  }

  @Post('restore/:jobId')
  async restoreBackup(
    @Param('jobId') jobId: string,
    @Body() options: {
      databases?: string[];
      buckets?: string[];
      overwrite?: boolean;
      targetPrefix?: string;
    }
  ) {
    await this.backupService.restoreBackup(jobId, options);
    return { success: true };
  }

  @Delete('jobs/:jobId')
  async deleteBackup(@Param('jobId') jobId: string) {
    await this.backupService.deleteBackup(jobId);
    return { success: true };
  }

  @Post('verify/:jobId')
  async verifyBackup(@Param('jobId') jobId: string) {
    const job = this.backupService.getBackupJob(jobId);
    if (!job) {
      return { error: 'Backup job not found' };
    }

    const isValid = await this.backupService.verifyBackupIntegrity(job.location, job.checksum);
    return { isValid };
  }

  @Get('stats')
  async getBackupStats() {
    return await this.backupService.getBackupStats();
  }

  // 迁移管理
  @Post('migration/plans')
  async createMigrationPlan(@Body() plan: any) {
    const planId = await this.migrationService.createMigrationPlan(plan);
    return { planId };
  }

  @Get('migration/plans')
  async getMigrationPlans() {
    const plans = this.migrationService.getMigrationPlans();
    return { plans };
  }

  @Post('migration/plans/:planId/execute')
  async executeMigrationPlan(
    @Param('planId') planId: string,
    @Body() options: {
      dryRun?: boolean;
      skipValidation?: boolean;
      continueOnError?: boolean;
    }
  ) {
    const executionId = await this.migrationService.executeMigrationPlan(planId, options);
    return { executionId };
  }

  @Get('migration/executions')
  async getMigrationExecutions(@Query('planId') planId?: string) {
    const executions = this.migrationService.getMigrationExecutions(planId);
    return { executions };
  }

  @Get('migration/executions/:executionId')
  async getMigrationExecution(@Param('executionId') executionId: string) {
    const execution = this.migrationService.getMigrationExecution(executionId);
    if (!execution) {
      return { error: 'Migration execution not found' };
    }
    return { execution };
  }

  @Post('migration/executions/:executionId/rollback')
  async rollbackMigration(@Param('executionId') executionId: string) {
    await this.migrationService.rollbackMigration(executionId);
    return { success: true };
  }

  @Post('migration/sync')
  async syncData(@Body() config: any) {
    const result = await this.migrationService.syncData(config);
    return result;
  }

  @Post('migration/upgrade')
  async upgradeVersion(
    @Body() {
      fromVersion,
      toVersion,
      options,
    }: {
      fromVersion: string;
      toVersion: string;
      options?: {
        backupFirst?: boolean;
        validateAfter?: boolean;
        rollbackOnFailure?: boolean;
      };
    }
  ) {
    const executionId = await this.migrationService.upgradeVersion(fromVersion, toVersion, options);
    return { executionId };
  }

  @Post('migration/consistency-check')
  async checkDataConsistency(@Body() { sources }: { sources: any[] }) {
    const result = await this.migrationService.checkDataConsistency(sources);
    return result;
  }
}
