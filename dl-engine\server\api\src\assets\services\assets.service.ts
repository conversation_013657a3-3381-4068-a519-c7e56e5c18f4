/**
 * DL-Engine 资产管理服务
 * 
 * 核心功能：
 * - 文件上传和存储
 * - 资产分类和标签
 * - 搜索索引和检索
 * - 使用统计和分析
 * - 版本管理和历史
 * - 权限控制和分享
 */

import { Injectable, NotFoundException, BadRequestException, ForbiddenException } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { Repository, Like, In } from 'typeorm'
import { ConfigService } from '@nestjs/config'

import { Asset, AssetType, AssetStatus } from '../entities/asset.entity'
import { AssetCategory } from '../entities/asset-category.entity'
import { AssetTag } from '../entities/asset-tag.entity'
import { AssetUsage } from '../entities/asset-usage.entity'
import { AssetVersion } from '../entities/asset-version.entity'

@Injectable()
export class AssetsService {
  constructor(
    @InjectRepository(Asset)
    private readonly assetRepository: Repository<Asset>,
    @InjectRepository(AssetCategory)
    private readonly categoryRepository: Repository<AssetCategory>,
    @InjectRepository(AssetTag)
    private readonly tagRepository: Repository<AssetTag>,
    @InjectRepository(AssetUsage)
    private readonly usageRepository: Repository<AssetUsage>,
    @InjectRepository(AssetVersion)
    private readonly versionRepository: Repository<AssetVersion>,
    private readonly configService: ConfigService
  ) {}

  /**
   * 上传资产文件
   */
  async uploadAsset(
    file: Express.Multer.File,
    uploadData: any,
    userId: string
  ): Promise<Asset> {
    // 验证文件
    this.validateFile(file)

    // 检测文件类型
    const assetType = this.detectAssetType(file)

    // 生成文件路径
    const filePath = await this.generateFilePath(file, userId)

    // 上传文件到存储服务
    const fileUrl = await this.uploadFileToStorage(file, filePath)

    // 提取文件元数据
    const metadata = await this.extractFileMetadata(file, assetType)

    // 创建资产记录
    const asset = this.assetRepository.create({
      name: uploadData.name || file.originalname,
      description: uploadData.description,
      type: assetType,
      status: AssetStatus.PROCESSING,
      ownerId: userId,
      projectId: uploadData.projectId,
      categoryId: uploadData.categoryId,
      originalName: file.originalname,
      fileName: filePath,
      fileUrl,
      fileSize: file.size,
      mimeType: file.mimetype,
      metadata,
      statistics: {
        downloads: 0,
        views: 0,
        uses: 0,
        likes: 0,
        shares: 0,
        comments: 0,
        avgRating: 0,
        totalRatings: 0,
        lastAccessed: new Date()
      }
    })

    const savedAsset = await this.assetRepository.save(asset)

    // 处理标签
    if (uploadData.tags && uploadData.tags.length > 0) {
      await this.addTagsToAsset(savedAsset.id, uploadData.tags)
    }

    // 异步处理文件（生成缩略图、转换格式等）
    this.processAssetAsync(savedAsset.id)

    return savedAsset
  }

  /**
   * 搜索资产
   */
  async searchAssets(query: any, userId?: string): Promise<{
    assets: Asset[]
    total: number
    facets: any
  }> {
    const {
      search,
      type,
      category,
      tags,
      projectId,
      ownerId,
      status = AssetStatus.ACTIVE,
      sortBy = 'updatedAt',
      sortOrder = 'DESC',
      page = 1,
      limit = 20
    } = query

    const queryBuilder = this.assetRepository.createQueryBuilder('asset')
      .leftJoinAndSelect('asset.owner', 'owner')
      .leftJoinAndSelect('asset.category', 'category')
      .leftJoinAndSelect('asset.tags', 'tags')
      .leftJoinAndSelect('asset.project', 'project')

    // 基础过滤
    queryBuilder.andWhere('asset.status = :status', { status })

    // 权限过滤
    if (userId) {
      queryBuilder.andWhere(
        '(asset.ownerId = :userId OR asset.isPublic = true OR project.visibility = :publicVisibility)',
        { userId, publicVisibility: 'public' }
      )
    } else {
      queryBuilder.andWhere('asset.isPublic = true')
    }

    // 搜索条件
    if (search) {
      queryBuilder.andWhere(
        '(asset.name LIKE :search OR asset.description LIKE :search OR tags.name LIKE :search)',
        { search: `%${search}%` }
      )
    }

    // 类型过滤
    if (type) {
      if (Array.isArray(type)) {
        queryBuilder.andWhere('asset.type IN (:...types)', { types: type })
      } else {
        queryBuilder.andWhere('asset.type = :type', { type })
      }
    }

    // 分类过滤
    if (category) {
      queryBuilder.andWhere('asset.categoryId = :category', { category })
    }

    // 标签过滤
    if (tags && tags.length > 0) {
      queryBuilder.andWhere('tags.id IN (:...tags)', { tags })
    }

    // 项目过滤
    if (projectId) {
      queryBuilder.andWhere('asset.projectId = :projectId', { projectId })
    }

    // 所有者过滤
    if (ownerId) {
      queryBuilder.andWhere('asset.ownerId = :ownerId', { ownerId })
    }

    // 排序
    queryBuilder.orderBy(`asset.${sortBy}`, sortOrder)

    // 分页
    const offset = (page - 1) * limit
    queryBuilder.skip(offset).take(limit)

    const [assets, total] = await queryBuilder.getManyAndCount()

    // 生成搜索面板数据
    const facets = await this.generateSearchFacets(query, userId)

    return { assets, total, facets }
  }

  /**
   * 获取资产详情
   */
  async getAssetById(id: string, userId?: string): Promise<Asset> {
    const asset = await this.assetRepository.findOne({
      where: { id },
      relations: ['owner', 'category', 'tags', 'project', 'versions', 'usages']
    })

    if (!asset) {
      throw new NotFoundException('资产不存在')
    }

    // 检查访问权限
    await this.checkAssetAccess(asset, userId, 'read')

    // 记录访问统计
    if (userId && userId !== asset.ownerId) {
      await this.recordAssetView(id, userId)
    }

    return asset
  }

  /**
   * 更新资产信息
   */
  async updateAsset(id: string, updateData: any, userId: string): Promise<Asset> {
    const asset = await this.assetRepository.findOne({ where: { id } })

    if (!asset) {
      throw new NotFoundException('资产不存在')
    }

    // 检查编辑权限
    await this.checkAssetAccess(asset, userId, 'write')

    // 更新基本信息
    Object.assign(asset, {
      name: updateData.name || asset.name,
      description: updateData.description || asset.description,
      categoryId: updateData.categoryId || asset.categoryId,
      isPublic: updateData.isPublic !== undefined ? updateData.isPublic : asset.isPublic,
      updatedAt: new Date()
    })

    const updatedAsset = await this.assetRepository.save(asset)

    // 更新标签
    if (updateData.tags !== undefined) {
      await this.updateAssetTags(id, updateData.tags)
    }

    return updatedAsset
  }

  /**
   * 删除资产
   */
  async deleteAsset(id: string, userId: string): Promise<void> {
    const asset = await this.assetRepository.findOne({ where: { id } })

    if (!asset) {
      throw new NotFoundException('资产不存在')
    }

    // 检查删除权限
    await this.checkAssetAccess(asset, userId, 'delete')

    // 检查是否被使用
    const usageCount = await this.usageRepository.count({ where: { assetId: id } })
    if (usageCount > 0) {
      throw new BadRequestException('资产正在被使用，无法删除')
    }

    // 软删除
    asset.status = AssetStatus.DELETED
    asset.deletedAt = new Date()
    await this.assetRepository.save(asset)

    // 删除文件
    await this.deleteFileFromStorage(asset.fileUrl)
  }

  /**
   * 记录资产使用
   */
  async recordAssetUsage(assetId: string, usageData: any): Promise<AssetUsage> {
    const asset = await this.assetRepository.findOne({ where: { id: assetId } })

    if (!asset) {
      throw new NotFoundException('资产不存在')
    }

    // 创建使用记录
    const usage = this.usageRepository.create({
      assetId,
      userId: usageData.userId,
      projectId: usageData.projectId,
      sceneId: usageData.sceneId,
      usageType: usageData.type || 'reference',
      context: usageData.context,
      metadata: usageData.metadata
    })

    const savedUsage = await this.usageRepository.save(usage)

    // 更新资产使用统计
    asset.updateStatistics('uses')
    await this.assetRepository.save(asset)

    return savedUsage
  }

  /**
   * 获取资产使用统计
   */
  async getAssetStatistics(assetId: string, userId: string): Promise<any> {
    const asset = await this.assetRepository.findOne({ where: { id: assetId } })

    if (!asset) {
      throw new NotFoundException('资产不存在')
    }

    // 检查访问权限
    await this.checkAssetAccess(asset, userId, 'read')

    // 获取详细使用统计
    const usages = await this.usageRepository.find({
      where: { assetId },
      relations: ['user', 'project', 'scene']
    })

    const usageByType = usages.reduce((acc, usage) => {
      acc[usage.usageType] = (acc[usage.usageType] || 0) + 1
      return acc
    }, {})

    const usageByProject = usages.reduce((acc, usage) => {
      if (usage.project) {
        acc[usage.project.name] = (acc[usage.project.name] || 0) + 1
      }
      return acc
    }, {})

    return {
      basic: asset.statistics,
      usageByType,
      usageByProject,
      recentUsages: usages.slice(0, 10).map(u => ({
        user: u.user?.displayName,
        project: u.project?.name,
        scene: u.scene?.name,
        type: u.usageType,
        usedAt: u.createdAt
      }))
    }
  }

  /**
   * 验证文件
   */
  private validateFile(file: Express.Multer.File): void {
    const maxSize = this.configService.get('ASSET_MAX_SIZE', 100 * 1024 * 1024) // 100MB
    const allowedTypes = this.configService.get('ASSET_ALLOWED_TYPES', [
      'image/jpeg', 'image/png', 'image/webp', 'image/gif',
      'model/gltf-binary', 'model/gltf+json',
      'audio/mpeg', 'audio/wav', 'audio/ogg',
      'video/mp4', 'video/webm',
      'application/json', 'text/plain'
    ])

    if (file.size > maxSize) {
      throw new BadRequestException(`文件大小不能超过 ${maxSize / 1024 / 1024}MB`)
    }

    if (!allowedTypes.includes(file.mimetype)) {
      throw new BadRequestException('不支持的文件类型')
    }
  }

  /**
   * 检测资产类型
   */
  private detectAssetType(file: Express.Multer.File): AssetType {
    const mimeType = file.mimetype

    if (mimeType.startsWith('image/')) return AssetType.IMAGE
    if (mimeType.startsWith('audio/')) return AssetType.AUDIO
    if (mimeType.startsWith('video/')) return AssetType.VIDEO
    if (mimeType.includes('gltf') || mimeType.includes('model')) return AssetType.MODEL_3D
    if (mimeType === 'application/json') return AssetType.DATA
    if (mimeType.startsWith('text/')) return AssetType.DOCUMENT

    return AssetType.OTHER
  }

  /**
   * 生成文件路径
   */
  private async generateFilePath(file: Express.Multer.File, userId: string): Promise<string> {
    const timestamp = Date.now()
    const extension = file.originalname.split('.').pop()
    const hash = this.generateFileHash(file.buffer)
    
    return `assets/${userId}/${timestamp}-${hash}.${extension}`
  }

  /**
   * 生成文件哈希
   */
  private generateFileHash(buffer: Buffer): string {
    const crypto = require('crypto')
    return crypto.createHash('md5').update(buffer).digest('hex').substring(0, 8)
  }

  /**
   * 上传文件到存储服务
   */
  private async uploadFileToStorage(file: Express.Multer.File, filePath: string): Promise<string> {
    // 这里应该集成实际的存储服务（如 Minio、AWS S3 等）
    // 暂时返回一个模拟的 URL
    return `https://storage.dl-engine.com/${filePath}`
  }

  /**
   * 从存储服务删除文件
   */
  private async deleteFileFromStorage(fileUrl: string): Promise<void> {
    // 这里应该调用存储服务的删除API
    console.log(`删除文件: ${fileUrl}`)
  }

  /**
   * 提取文件元数据
   */
  private async extractFileMetadata(file: Express.Multer.File, type: AssetType): Promise<any> {
    const metadata: any = {
      originalName: file.originalname,
      size: file.size,
      mimeType: file.mimetype,
      uploadedAt: new Date()
    }

    // 根据文件类型提取特定元数据
    switch (type) {
      case AssetType.IMAGE:
        // 提取图像尺寸、颜色信息等
        metadata.dimensions = await this.extractImageDimensions(file.buffer)
        break
      case AssetType.MODEL_3D:
        // 提取3D模型信息
        metadata.modelInfo = await this.extract3DModelInfo(file.buffer)
        break
      case AssetType.AUDIO:
        // 提取音频信息
        metadata.audioInfo = await this.extractAudioInfo(file.buffer)
        break
    }

    return metadata
  }

  /**
   * 提取图像尺寸
   */
  private async extractImageDimensions(buffer: Buffer): Promise<any> {
    // 这里应该使用图像处理库提取尺寸信息
    return { width: 1920, height: 1080 }
  }

  /**
   * 提取3D模型信息
   */
  private async extract3DModelInfo(buffer: Buffer): Promise<any> {
    // 这里应该解析3D模型文件
    return { vertices: 1000, faces: 500, materials: 2 }
  }

  /**
   * 提取音频信息
   */
  private async extractAudioInfo(buffer: Buffer): Promise<any> {
    // 这里应该使用音频处理库提取信息
    return { duration: 120, bitrate: 320, sampleRate: 44100 }
  }

  /**
   * 异步处理资产
   */
  private async processAssetAsync(assetId: string): Promise<void> {
    // 在后台处理资产（生成缩略图、转换格式等）
    setTimeout(async () => {
      try {
        await this.assetRepository.update(assetId, {
          status: AssetStatus.ACTIVE,
          processedAt: new Date()
        })
      } catch (error) {
        await this.assetRepository.update(assetId, {
          status: AssetStatus.FAILED
        })
      }
    }, 1000)
  }

  /**
   * 检查资产访问权限
   */
  private async checkAssetAccess(asset: Asset, userId: string, action: string): Promise<void> {
    // 所有者拥有所有权限
    if (asset.ownerId === userId) return

    // 公开资产的读取权限
    if (action === 'read' && asset.isPublic) return

    // 其他情况需要特定权限
    throw new ForbiddenException(`无权限执行此操作: ${action}`)
  }

  /**
   * 记录资产访问
   */
  private async recordAssetView(assetId: string, userId: string): Promise<void> {
    // 更新访问统计
    await this.assetRepository.update(assetId, {
      'statistics.views': () => 'statistics.views + 1',
      'statistics.lastAccessed': new Date()
    })
  }

  /**
   * 生成搜索面板数据
   */
  private async generateSearchFacets(query: any, userId?: string): Promise<any> {
    // 生成搜索面板数据（类型、分类、标签等的统计）
    return {
      types: await this.getTypeFacets(query, userId),
      categories: await this.getCategoryFacets(query, userId),
      tags: await this.getTagFacets(query, userId)
    }
  }

  /**
   * 获取类型面板数据
   */
  private async getTypeFacets(query: any, userId?: string): Promise<any[]> {
    // 实现类型统计
    return []
  }

  /**
   * 获取分类面板数据
   */
  private async getCategoryFacets(query: any, userId?: string): Promise<any[]> {
    // 实现分类统计
    return []
  }

  /**
   * 获取标签面板数据
   */
  private async getTagFacets(query: any, userId?: string): Promise<any[]> {
    // 实现标签统计
    return []
  }

  /**
   * 添加标签到资产
   */
  private async addTagsToAsset(assetId: string, tags: string[]): Promise<void> {
    // 实现标签关联
  }

  /**
   * 更新资产标签
   */
  private async updateAssetTags(assetId: string, tags: string[]): Promise<void> {
    // 实现标签更新
  }
}
