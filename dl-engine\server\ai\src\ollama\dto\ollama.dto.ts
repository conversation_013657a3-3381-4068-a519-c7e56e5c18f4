import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ber, IsArray, <PERSON>, <PERSON> } from 'class-validator'

export class GenerateTextDto {
  @IsString()
  model: string

  @IsString()
  prompt: string

  @IsOptional()
  @IsString()
  system?: string

  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(2)
  temperature?: number

  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1)
  topP?: number

  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(100)
  topK?: number

  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(8192)
  maxTokens?: number

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  stop?: string[]
}

export class GenerateEmbeddingDto {
  @IsString()
  model: string

  @IsString()
  text: string
}

export class ChatDto {
  @IsString()
  model: string

  @IsArray()
  messages: Array<{
    role: 'system' | 'user' | 'assistant'
    content: string
  }>

  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(2)
  temperature?: number

  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1)
  topP?: number

  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(100)
  topK?: number

  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(8192)
  maxTokens?: number
}
