/**
 * DL-Engine 共享工具库
 * 提供通用的工具函数和类型定义
 */

// 导出所有工具模块
export * from './validation'
export * from './formatting'
export * from './encryption'
export * from './storage'
export * from './network'
export * from './math'
export * from './geometry'
export * from './color'
export * from './file'
export * from './date'
export * from './string'
export * from './array'
export * from './object'
export * from './performance'
export * from './device'
export * from './browser'
export * from './education'
export * from './3d'

// 通用常量
export const CONSTANTS = {
  // 应用信息
  APP_NAME: 'DL-Engine',
  APP_VERSION: '1.0.0',
  APP_DESCRIPTION: '数字化学习引擎',
  
  // API配置
  API_TIMEOUT: 30000,
  API_RETRY_COUNT: 3,
  API_RETRY_DELAY: 1000,
  
  // 文件限制
  MAX_FILE_SIZE: 100 * 1024 * 1024, // 100MB
  MAX_IMAGE_SIZE: 10 * 1024 * 1024,  // 10MB
  MAX_VIDEO_SIZE: 500 * 1024 * 1024, // 500MB
  MAX_MODEL_SIZE: 50 * 1024 * 1024,  // 50MB
  
  // 支持的文件类型
  SUPPORTED_IMAGE_TYPES: ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'],
  SUPPORTED_VIDEO_TYPES: ['mp4', 'webm', 'ogg', 'avi', 'mov'],
  SUPPORTED_AUDIO_TYPES: ['mp3', 'wav', 'ogg', 'aac', 'm4a'],
  SUPPORTED_MODEL_TYPES: ['gltf', 'glb', 'fbx', 'obj', 'dae', '3ds'],
  SUPPORTED_DOCUMENT_TYPES: ['pdf', 'doc', 'docx', 'ppt', 'pptx', 'xls', 'xlsx'],
  
  // 教育相关常量
  MAX_STUDENTS_PER_CLASS: 50,
  MAX_CONCURRENT_SESSIONS: 100,
  DEFAULT_SESSION_DURATION: 45 * 60 * 1000, // 45分钟
  
  // 3D渲染常量
  DEFAULT_CAMERA_FOV: 75,
  DEFAULT_CAMERA_NEAR: 0.1,
  DEFAULT_CAMERA_FAR: 1000,
  MAX_TEXTURE_SIZE: 2048,
  MAX_GEOMETRY_VERTICES: 100000,
  
  // 网络常量
  WEBSOCKET_HEARTBEAT_INTERVAL: 30000,
  WEBRTC_ICE_SERVERS: [
    { urls: 'stun:stun.l.google.com:19302' },
    { urls: 'stun:stun1.l.google.com:19302' }
  ],
  
  // 缓存常量
  CACHE_TTL: 3600 * 1000, // 1小时
  CACHE_MAX_SIZE: 1000,
  
  // 本地化
  DEFAULT_LOCALE: 'zh-CN',
  SUPPORTED_LOCALES: ['zh-CN', 'en-US'],
  
  // 主题
  DEFAULT_THEME: 'light',
  SUPPORTED_THEMES: ['light', 'dark', 'auto']
} as const

// 错误代码
export const ERROR_CODES = {
  // 通用错误
  UNKNOWN_ERROR: 'UNKNOWN_ERROR',
  NETWORK_ERROR: 'NETWORK_ERROR',
  TIMEOUT_ERROR: 'TIMEOUT_ERROR',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  
  // 认证错误
  AUTH_REQUIRED: 'AUTH_REQUIRED',
  AUTH_INVALID: 'AUTH_INVALID',
  AUTH_EXPIRED: 'AUTH_EXPIRED',
  AUTH_FORBIDDEN: 'AUTH_FORBIDDEN',
  
  // 文件错误
  FILE_TOO_LARGE: 'FILE_TOO_LARGE',
  FILE_TYPE_NOT_SUPPORTED: 'FILE_TYPE_NOT_SUPPORTED',
  FILE_UPLOAD_FAILED: 'FILE_UPLOAD_FAILED',
  FILE_NOT_FOUND: 'FILE_NOT_FOUND',
  
  // 3D相关错误
  MODEL_LOAD_FAILED: 'MODEL_LOAD_FAILED',
  TEXTURE_LOAD_FAILED: 'TEXTURE_LOAD_FAILED',
  WEBGL_NOT_SUPPORTED: 'WEBGL_NOT_SUPPORTED',
  WEBXR_NOT_SUPPORTED: 'WEBXR_NOT_SUPPORTED',
  
  // 教育业务错误
  CLASS_FULL: 'CLASS_FULL',
  SESSION_EXPIRED: 'SESSION_EXPIRED',
  COURSE_NOT_FOUND: 'COURSE_NOT_FOUND',
  PERMISSION_DENIED: 'PERMISSION_DENIED'
} as const

// 事件类型
export const EVENT_TYPES = {
  // 应用事件
  APP_INIT: 'app:init',
  APP_READY: 'app:ready',
  APP_ERROR: 'app:error',
  
  // 用户事件
  USER_LOGIN: 'user:login',
  USER_LOGOUT: 'user:logout',
  USER_UPDATE: 'user:update',
  
  // 场景事件
  SCENE_LOAD: 'scene:load',
  SCENE_READY: 'scene:ready',
  SCENE_UPDATE: 'scene:update',
  SCENE_DESTROY: 'scene:destroy',
  
  // 网络事件
  NETWORK_ONLINE: 'network:online',
  NETWORK_OFFLINE: 'network:offline',
  NETWORK_SLOW: 'network:slow',
  
  // 协作事件
  COLLABORATION_JOIN: 'collaboration:join',
  COLLABORATION_LEAVE: 'collaboration:leave',
  COLLABORATION_UPDATE: 'collaboration:update',
  
  // 学习事件
  LEARNING_START: 'learning:start',
  LEARNING_PROGRESS: 'learning:progress',
  LEARNING_COMPLETE: 'learning:complete',
  LEARNING_PAUSE: 'learning:pause'
} as const

// 通用类型定义
export type Locale = typeof CONSTANTS.SUPPORTED_LOCALES[number]
export type Theme = typeof CONSTANTS.SUPPORTED_THEMES[number]
export type ErrorCode = typeof ERROR_CODES[keyof typeof ERROR_CODES]
export type EventType = typeof EVENT_TYPES[keyof typeof EVENT_TYPES]

// 基础接口
export interface BaseEntity {
  id: string
  createdAt: Date
  updatedAt: Date
  version?: number
}

export interface User extends BaseEntity {
  username: string
  email: string
  phone?: string
  avatar?: string
  role: 'student' | 'teacher' | 'admin'
  profile: UserProfile
  preferences: UserPreferences
}

export interface UserProfile {
  firstName: string
  lastName: string
  displayName: string
  bio?: string
  location?: string
  website?: string
  socialLinks?: Record<string, string>
}

export interface UserPreferences {
  locale: Locale
  theme: Theme
  notifications: NotificationSettings
  privacy: PrivacySettings
  accessibility: AccessibilitySettings
}

export interface NotificationSettings {
  email: boolean
  push: boolean
  sms: boolean
  inApp: boolean
  types: string[]
}

export interface PrivacySettings {
  profileVisibility: 'public' | 'friends' | 'private'
  showOnlineStatus: boolean
  allowDirectMessages: boolean
  shareAnalytics: boolean
}

export interface AccessibilitySettings {
  highContrast: boolean
  largeText: boolean
  reduceMotion: boolean
  screenReader: boolean
  keyboardNavigation: boolean
}

// API响应接口
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: {
    code: ErrorCode
    message: string
    details?: any
  }
  meta?: {
    total?: number
    page?: number
    limit?: number
    hasMore?: boolean
  }
}

// 分页接口
export interface PaginationParams {
  page: number
  limit: number
  sort?: string
  order?: 'asc' | 'desc'
  search?: string
  filters?: Record<string, any>
}

// 文件接口
export interface FileInfo {
  id: string
  name: string
  type: string
  size: number
  url: string
  thumbnailUrl?: string
  metadata?: Record<string, any>
  uploadedAt: Date
  uploadedBy: string
}

// 3D相关接口
export interface Vector3 {
  x: number
  y: number
  z: number
}

export interface Quaternion {
  x: number
  y: number
  z: number
  w: number
}

export interface Transform {
  position: Vector3
  rotation: Quaternion
  scale: Vector3
}

export interface Material {
  id: string
  name: string
  type: 'basic' | 'standard' | 'physical' | 'custom'
  properties: Record<string, any>
  textures?: Record<string, string>
}

export interface Geometry {
  id: string
  type: 'box' | 'sphere' | 'plane' | 'cylinder' | 'custom'
  parameters: Record<string, any>
  vertices?: number[]
  indices?: number[]
  normals?: number[]
  uvs?: number[]
}

// 教育相关接口
export interface Course extends BaseEntity {
  title: string
  description: string
  instructor: string
  category: string
  level: 'beginner' | 'intermediate' | 'advanced'
  duration: number
  thumbnail?: string
  tags: string[]
  isPublished: boolean
  enrollmentCount: number
  rating: number
  lessons: Lesson[]
}

export interface Lesson extends BaseEntity {
  title: string
  description: string
  type: 'video' | 'interactive' | '3d' | 'quiz' | 'assignment'
  content: any
  duration: number
  order: number
  isRequired: boolean
  prerequisites: string[]
}

export interface LearningProgress {
  userId: string
  courseId: string
  lessonId?: string
  progress: number
  status: 'not_started' | 'in_progress' | 'completed' | 'failed'
  startedAt?: Date
  completedAt?: Date
  timeSpent: number
  score?: number
}

// 协作相关接口
export interface CollaborationSession extends BaseEntity {
  name: string
  description?: string
  type: 'class' | 'meeting' | 'workshop' | 'presentation'
  hostId: string
  participants: Participant[]
  maxParticipants: number
  isActive: boolean
  startTime: Date
  endTime?: Date
  settings: SessionSettings
}

export interface Participant {
  userId: string
  role: 'host' | 'presenter' | 'participant' | 'observer'
  joinedAt: Date
  isOnline: boolean
  permissions: string[]
}

export interface SessionSettings {
  allowScreenShare: boolean
  allowVoiceChat: boolean
  allowVideoChat: boolean
  allowTextChat: boolean
  allowFileShare: boolean
  recordSession: boolean
  requireApproval: boolean
}
