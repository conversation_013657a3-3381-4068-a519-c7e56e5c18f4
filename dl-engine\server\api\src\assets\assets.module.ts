import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@nestjs/typeorm'
import { ConfigModule } from '@nestjs/config'

import { Asset } from './entities/asset.entity'
import { AssetCategory } from './entities/asset-category.entity'
import { AssetTag } from './entities/asset-tag.entity'
import { AssetVersion } from './entities/asset-version.entity'
import { AssetUsage } from './entities/asset-usage.entity'
import { AssetShare } from './entities/asset-share.entity'
import { AssetComment } from './entities/asset-comment.entity'
import { AssetRating } from './entities/asset-rating.entity'
import { AssetCollection } from './entities/asset-collection.entity'

import { AssetsController } from './controllers/assets.controller'
import { AssetCategoriesController } from './controllers/asset-categories.controller'
import { AssetTagsController } from './controllers/asset-tags.controller'
import { AssetVersionsController } from './controllers/asset-versions.controller'
import { AssetUsageController } from './controllers/asset-usage.controller'
import { AssetSharesController } from './controllers/asset-shares.controller'
import { AssetCommentsController } from './controllers/asset-comments.controller'
import { AssetRatingsController } from './controllers/asset-ratings.controller'
import { AssetCollectionsController } from './controllers/asset-collections.controller'

import { AssetsService } from './services/assets.service'
import { AssetCategoriesService } from './services/asset-categories.service'
import { AssetTagsService } from './services/asset-tags.service'
import { AssetVersionsService } from './services/asset-versions.service'
import { AssetUsageService } from './services/asset-usage.service'
import { AssetSharesService } from './services/asset-shares.service'
import { AssetCommentsService } from './services/asset-comments.service'
import { AssetRatingsService } from './services/asset-ratings.service'
import { AssetCollectionsService } from './services/asset-collections.service'
import { AssetValidationService } from './services/asset-validation.service'
import { AssetProcessingService } from './services/asset-processing.service'
import { AssetSearchService } from './services/asset-search.service'

import { UsersModule } from '../users/users.module'
import { NotificationModule } from '../notifications/notification.module'
import { StorageModule } from '../storage/storage.module'

/**
 * 资产管理模块
 * 
 * 功能包括：
 * - 文件上传和管理
 * - 资产分类和标签
 * - 版本控制
 * - 使用统计
 * - 资产分享和评论
 * - 资产评分和收藏
 * - 搜索和索引
 * - 文件处理和转换
 */
@Module({
  imports: [
    ConfigModule,
    TypeOrmModule.forFeature([
      Asset,
      AssetCategory,
      AssetTag,
      AssetVersion,
      AssetUsage,
      AssetShare,
      AssetComment,
      AssetRating,
      AssetCollection
    ]),
    UsersModule,
    NotificationModule,
    StorageModule
  ],
  controllers: [
    AssetsController,
    AssetCategoriesController,
    AssetTagsController,
    AssetVersionsController,
    AssetUsageController,
    AssetSharesController,
    AssetCommentsController,
    AssetRatingsController,
    AssetCollectionsController
  ],
  providers: [
    AssetsService,
    AssetCategoriesService,
    AssetTagsService,
    AssetVersionsService,
    AssetUsageService,
    AssetSharesService,
    AssetCommentsService,
    AssetRatingsService,
    AssetCollectionsService,
    AssetValidationService,
    AssetProcessingService,
    AssetSearchService
  ],
  exports: [
    AssetsService,
    AssetCategoriesService,
    AssetTagsService,
    AssetVersionsService,
    AssetUsageService,
    AssetSharesService,
    AssetCommentsService,
    AssetRatingsService,
    AssetCollectionsService,
    AssetProcessingService,
    AssetSearchService
  ]
})
export class AssetsModule {}
