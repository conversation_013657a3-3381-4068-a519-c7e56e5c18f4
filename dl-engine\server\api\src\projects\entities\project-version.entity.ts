import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index
} from 'typeorm'
import { User } from '../../users/entities/user.entity'
import { Project } from './project.entity'

/**
 * 版本类型枚举
 */
export enum VersionType {
  MAJOR = 'major',           // 主版本
  MINOR = 'minor',           // 次版本
  PATCH = 'patch',           // 补丁版本
  SNAPSHOT = 'snapshot',     // 快照版本
  RELEASE = 'release',       // 发布版本
  HOTFIX = 'hotfix'         // 热修复版本
}

/**
 * 版本状态枚举
 */
export enum VersionStatus {
  DRAFT = 'draft',           // 草稿
  PUBLISHED = 'published',   // 已发布
  ARCHIVED = 'archived',     // 已归档
  DELETED = 'deleted'        // 已删除
}

/**
 * 项目版本实体
 * 
 * 管理项目的版本控制和历史记录
 */
@Entity('project_versions')
@Index(['projectId', 'version'])
@Index(['projectId', 'createdAt'])
@Index(['status'])
export class ProjectVersion {
  @PrimaryGeneratedColumn('uuid')
  id: string

  /**
   * 项目ID
   */
  @Column({ type: 'uuid' })
  projectId: string

  /**
   * 版本号
   */
  @Column({ type: 'varchar', length: 50 })
  version: string

  /**
   * 版本名称
   */
  @Column({ type: 'varchar', length: 200, nullable: true })
  name?: string

  /**
   * 版本描述
   */
  @Column({ type: 'text', nullable: true })
  description?: string

  /**
   * 版本类型
   */
  @Column({
    type: 'enum',
    enum: VersionType,
    default: VersionType.MINOR
  })
  type: VersionType

  /**
   * 版本状态
   */
  @Column({
    type: 'enum',
    enum: VersionStatus,
    default: VersionStatus.DRAFT
  })
  status: VersionStatus

  /**
   * 创建者ID
   */
  @Column({ type: 'uuid' })
  createdById: string

  /**
   * 父版本ID
   */
  @Column({ type: 'uuid', nullable: true })
  parentVersionId?: string

  /**
   * 是否为当前版本
   */
  @Column({ type: 'boolean', default: false })
  isCurrent: boolean

  /**
   * 是否为发布版本
   */
  @Column({ type: 'boolean', default: false })
  isRelease: boolean

  /**
   * 版本标签
   */
  @Column({ type: 'json', nullable: true })
  tags?: string[]

  /**
   * 变更日志
   */
  @Column({ type: 'json', nullable: true })
  changelog?: Array<{
    type: 'added' | 'changed' | 'deprecated' | 'removed' | 'fixed' | 'security'
    description: string
    author?: string
    timestamp?: Date
  }>

  /**
   * 文件变更信息
   */
  @Column({ type: 'json', nullable: true })
  fileChanges?: {
    added: string[]
    modified: string[]
    deleted: string[]
    renamed: Array<{ from: string; to: string }>
    totalFiles: number
    totalSize: number
  }

  /**
   * 版本元数据
   */
  @Column({ type: 'json', nullable: true })
  metadata?: {
    buildNumber: number
    commitHash?: string
    branch?: string
    buildTime: Date
    buildEnvironment: string
    dependencies: Record<string, string>
    features: string[]
    bugFixes: string[]
    knownIssues: string[]
    compatibility: {
      minEngineVersion: string
      maxEngineVersion?: string
      platforms: string[]
      browsers?: string[]
    }
  }

  /**
   * 存储信息
   */
  @Column({ type: 'json', nullable: true })
  storage?: {
    path: string
    size: number
    checksum: string
    compressionType?: string
    encryptionType?: string
    backupPath?: string
  }

  /**
   * 统计信息
   */
  @Column({ type: 'json', nullable: true })
  statistics?: {
    downloads: number
    views: number
    forks: number
    issues: number
    feedback: {
      positive: number
      negative: number
      neutral: number
    }
    performance: {
      loadTime: number
      memoryUsage: number
      renderTime: number
    }
  }

  /**
   * 发布时间
   */
  @Column({ type: 'timestamp', nullable: true })
  publishedAt?: Date

  /**
   * 归档时间
   */
  @Column({ type: 'timestamp', nullable: true })
  archivedAt?: Date

  /**
   * 创建时间
   */
  @CreateDateColumn()
  createdAt: Date

  /**
   * 更新时间
   */
  @UpdateDateColumn()
  updatedAt: Date

  // 关联关系
  @ManyToOne(() => Project, project => project.versions, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'projectId' })
  project: Project

  @ManyToOne(() => User)
  @JoinColumn({ name: 'createdById' })
  createdBy: User

  @ManyToOne(() => ProjectVersion, { nullable: true })
  @JoinColumn({ name: 'parentVersionId' })
  parentVersion?: ProjectVersion

  /**
   * 解析版本号
   */
  parseVersion(): { major: number; minor: number; patch: number } {
    const parts = this.version.split('.').map(Number)
    return {
      major: parts[0] || 0,
      minor: parts[1] || 0,
      patch: parts[2] || 0
    }
  }

  /**
   * 比较版本号
   */
  compareVersion(other: ProjectVersion): number {
    const thisVersion = this.parseVersion()
    const otherVersion = other.parseVersion()

    if (thisVersion.major !== otherVersion.major) {
      return thisVersion.major - otherVersion.major
    }
    if (thisVersion.minor !== otherVersion.minor) {
      return thisVersion.minor - otherVersion.minor
    }
    return thisVersion.patch - otherVersion.patch
  }

  /**
   * 检查是否为更新版本
   */
  isNewerThan(other: ProjectVersion): boolean {
    return this.compareVersion(other) > 0
  }

  /**
   * 检查是否为较旧版本
   */
  isOlderThan(other: ProjectVersion): boolean {
    return this.compareVersion(other) < 0
  }

  /**
   * 检查是否为相同版本
   */
  isSameVersion(other: ProjectVersion): boolean {
    return this.compareVersion(other) === 0
  }

  /**
   * 生成下一个版本号
   */
  static generateNextVersion(currentVersion: string, type: VersionType): string {
    const parts = currentVersion.split('.').map(Number)
    let [major, minor, patch] = [parts[0] || 0, parts[1] || 0, parts[2] || 0]

    switch (type) {
      case VersionType.MAJOR:
        major++
        minor = 0
        patch = 0
        break
      case VersionType.MINOR:
        minor++
        patch = 0
        break
      case VersionType.PATCH:
      case VersionType.HOTFIX:
        patch++
        break
      case VersionType.SNAPSHOT:
        return `${major}.${minor}.${patch}-SNAPSHOT-${Date.now()}`
      case VersionType.RELEASE:
        // 发布版本不改变版本号，只改变状态
        break
    }

    return `${major}.${minor}.${patch}`
  }

  /**
   * 检查是否可以发布
   */
  canPublish(): boolean {
    return this.status === VersionStatus.DRAFT && this.type !== VersionType.SNAPSHOT
  }

  /**
   * 检查是否可以归档
   */
  canArchive(): boolean {
    return this.status === VersionStatus.PUBLISHED && !this.isCurrent
  }

  /**
   * 检查是否可以删除
   */
  canDelete(): boolean {
    return this.status !== VersionStatus.DELETED && !this.isCurrent && !this.isRelease
  }

  /**
   * 获取版本摘要
   */
  getSummary(): string {
    const changes = this.changelog?.length || 0
    const files = this.fileChanges?.totalFiles || 0
    return `版本 ${this.version}：${changes} 项变更，${files} 个文件`
  }

  /**
   * 更新统计信息
   */
  updateStatistics(field: keyof ProjectVersion['statistics'], increment = 1): void {
    if (!this.statistics) {
      this.statistics = {
        downloads: 0,
        views: 0,
        forks: 0,
        issues: 0,
        feedback: { positive: 0, negative: 0, neutral: 0 },
        performance: { loadTime: 0, memoryUsage: 0, renderTime: 0 }
      }
    }

    if (typeof this.statistics[field] === 'number') {
      this.statistics[field] += increment
    }
  }

  /**
   * 添加变更日志条目
   */
  addChangelogEntry(
    type: 'added' | 'changed' | 'deprecated' | 'removed' | 'fixed' | 'security',
    description: string,
    author?: string
  ): void {
    if (!this.changelog) {
      this.changelog = []
    }

    this.changelog.push({
      type,
      description,
      author,
      timestamp: new Date()
    })
  }

  /**
   * 获取兼容性信息
   */
  getCompatibilityInfo(): string {
    if (!this.metadata?.compatibility) return '兼容性信息未知'

    const { minEngineVersion, maxEngineVersion, platforms } = this.metadata.compatibility
    let info = `引擎版本: ${minEngineVersion}`
    
    if (maxEngineVersion) {
      info += ` - ${maxEngineVersion}`
    } else {
      info += '+'
    }

    if (platforms.length > 0) {
      info += `，平台: ${platforms.join(', ')}`
    }

    return info
  }
}
