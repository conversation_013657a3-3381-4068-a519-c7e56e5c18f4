/*
DL-Engine Scaling Manager Service
数字化学习引擎 - 扩缩容管理服务

实现自动扩容、负载监控和实例迁移功能
*/

import { Application } from '@feathersjs/feathers'
import { InstanceID } from '@ir-engine/common/src/schemas/networking/instance.schema'
import { LocationID } from '@ir-engine/common/src/schemas/social/location.schema'
import logger from '@ir-engine/server-core/src/ServerLogger'

/**
 * 扩缩容策略枚举
 */
export enum ScalingStrategy {
  MANUAL = 'manual',
  AUTO_CPU = 'auto_cpu',
  AUTO_MEMORY = 'auto_memory',
  AUTO_USERS = 'auto_users',
  AUTO_HYBRID = 'auto_hybrid'
}

/**
 * 实例负载指标
 */
interface InstanceLoadMetrics {
  instanceId: InstanceID
  cpuUsage: number          // CPU 使用率 (0-100)
  memoryUsage: number       // 内存使用率 (0-100)
  networkUsage: number      // 网络使用率 (0-100)
  userCount: number         // 当前用户数
  maxUsers: number          // 最大用户数
  responseTime: number      // 响应时间 (ms)
  errorRate: number         // 错误率 (0-1)
  timestamp: number
}

/**
 * 扩缩容配置
 */
interface ScalingConfig {
  strategy: ScalingStrategy
  minInstances: number      // 最小实例数
  maxInstances: number      // 最大实例数
  targetCpuUsage: number    // 目标 CPU 使用率
  targetMemoryUsage: number // 目标内存使用率
  targetUserRatio: number   // 目标用户比例
  scaleUpThreshold: number  // 扩容阈值
  scaleDownThreshold: number // 缩容阈值
  cooldownPeriod: number    // 冷却期 (秒)
  evaluationPeriod: number  // 评估周期 (秒)
}

/**
 * 扩缩容决策
 */
interface ScalingDecision {
  action: 'scale_up' | 'scale_down' | 'migrate' | 'none'
  reason: string
  targetInstances: number
  priority: number
  estimatedTime: number
}

/**
 * 实例迁移计划
 */
interface MigrationPlan {
  sourceInstanceId: InstanceID
  targetInstanceId?: InstanceID
  affectedUsers: string[]
  estimatedDowntime: number
  migrationSteps: MigrationStep[]
}

/**
 * 迁移步骤
 */
interface MigrationStep {
  step: number
  action: string
  description: string
  estimatedTime: number
}

/**
 * 扩缩容管理器类
 */
export class ScalingManagerService {
  private app: Application
  private scalingConfigs: Map<LocationID, ScalingConfig> = new Map()
  private instanceMetrics: Map<InstanceID, InstanceLoadMetrics> = new Map()
  private lastScalingActions: Map<LocationID, number> = new Map()
  private monitoringInterval: NodeJS.Timeout | null = null

  constructor(app: Application) {
    this.app = app
    this.initializeScalingManager()
  }

  /**
   * 设置位置的扩缩容配置
   */
  async setScalingConfig(locationId: LocationID, config: ScalingConfig): Promise<void> {
    try {
      // 验证配置
      this.validateScalingConfig(config)
      
      // 保存配置
      this.scalingConfigs.set(locationId, config)
      
      logger.info('扩缩容配置已设置', { locationId, config })

    } catch (error) {
      logger.error('设置扩缩容配置失败', { error, locationId })
      throw error
    }
  }

  /**
   * 获取位置的扩缩容配置
   */
  getScalingConfig(locationId: LocationID): ScalingConfig | null {
    return this.scalingConfigs.get(locationId) || null
  }

  /**
   * 更新实例负载指标
   */
  async updateInstanceMetrics(instanceId: InstanceID, metrics: InstanceLoadMetrics): Promise<void> {
    try {
      // 更新指标
      this.instanceMetrics.set(instanceId, {
        ...metrics,
        timestamp: Date.now()
      })

      // 触发扩缩容评估
      await this.evaluateScaling(instanceId)

    } catch (error) {
      logger.error('更新实例指标失败', { error, instanceId })
    }
  }

  /**
   * 手动扩容
   */
  async scaleUp(locationId: LocationID, targetInstances: number): Promise<void> {
    try {
      const currentInstances = await this.getCurrentInstanceCount(locationId)
      const instancesToCreate = targetInstances - currentInstances

      if (instancesToCreate <= 0) {
        logger.warn('无需扩容', { locationId, currentInstances, targetInstances })
        return
      }

      logger.info('开始手动扩容', { locationId, currentInstances, targetInstances })

      // 创建新实例
      for (let i = 0; i < instancesToCreate; i++) {
        await this.createNewInstance(locationId)
      }

      // 记录扩容操作
      this.lastScalingActions.set(locationId, Date.now())

      logger.info('手动扩容完成', { locationId, createdInstances: instancesToCreate })

    } catch (error) {
      logger.error('手动扩容失败', { error, locationId })
      throw error
    }
  }

  /**
   * 手动缩容
   */
  async scaleDown(locationId: LocationID, targetInstances: number): Promise<void> {
    try {
      const currentInstances = await this.getCurrentInstanceCount(locationId)
      const instancesToRemove = currentInstances - targetInstances

      if (instancesToRemove <= 0) {
        logger.warn('无需缩容', { locationId, currentInstances, targetInstances })
        return
      }

      logger.info('开始手动缩容', { locationId, currentInstances, targetInstances })

      // 选择要移除的实例
      const instancesToTerminate = await this.selectInstancesForTermination(locationId, instancesToRemove)

      // 终止实例
      for (const instanceId of instancesToTerminate) {
        await this.terminateInstance(instanceId)
      }

      // 记录缩容操作
      this.lastScalingActions.set(locationId, Date.now())

      logger.info('手动缩容完成', { locationId, terminatedInstances: instancesToRemove })

    } catch (error) {
      logger.error('手动缩容失败', { error, locationId })
      throw error
    }
  }

  /**
   * 实例迁移
   */
  async migrateInstance(sourceInstanceId: InstanceID, targetInstanceId?: InstanceID): Promise<void> {
    try {
      logger.info('开始实例迁移', { sourceInstanceId, targetInstanceId })

      // 创建迁移计划
      const migrationPlan = await this.createMigrationPlan(sourceInstanceId, targetInstanceId)

      // 执行迁移
      await this.executeMigration(migrationPlan)

      logger.info('实例迁移完成', { sourceInstanceId, targetInstanceId })

    } catch (error) {
      logger.error('实例迁移失败', { error, sourceInstanceId, targetInstanceId })
      throw error
    }
  }

  /**
   * 获取位置的负载统计
   */
  async getLocationLoadStats(locationId: LocationID): Promise<any> {
    const instances = await this.getLocationInstances(locationId)
    const metrics = instances.map(instance => this.instanceMetrics.get(instance.id)).filter(Boolean)

    if (metrics.length === 0) {
      return {
        instanceCount: 0,
        totalUsers: 0,
        avgCpuUsage: 0,
        avgMemoryUsage: 0,
        avgResponseTime: 0
      }
    }

    return {
      instanceCount: instances.length,
      totalUsers: metrics.reduce((sum, m) => sum + m!.userCount, 0),
      avgCpuUsage: metrics.reduce((sum, m) => sum + m!.cpuUsage, 0) / metrics.length,
      avgMemoryUsage: metrics.reduce((sum, m) => sum + m!.memoryUsage, 0) / metrics.length,
      avgResponseTime: metrics.reduce((sum, m) => sum + m!.responseTime, 0) / metrics.length,
      maxUsers: metrics.reduce((sum, m) => sum + m!.maxUsers, 0)
    }
  }

  /**
   * 评估扩缩容需求
   */
  private async evaluateScaling(instanceId: InstanceID): Promise<void> {
    try {
      // 获取实例信息
      const instance = await this.app.service('instance').get(instanceId)
      if (!instance || !instance.locationId) return

      const locationId = instance.locationId
      const config = this.scalingConfigs.get(locationId)
      if (!config || config.strategy === ScalingStrategy.MANUAL) return

      // 检查冷却期
      const lastAction = this.lastScalingActions.get(locationId) || 0
      const timeSinceLastAction = (Date.now() - lastAction) / 1000
      if (timeSinceLastAction < config.cooldownPeriod) return

      // 获取扩缩容决策
      const decision = await this.makeScalingDecision(locationId, config)

      // 执行决策
      await this.executeScalingDecision(locationId, decision)

    } catch (error) {
      logger.error('扩缩容评估失败', { error, instanceId })
    }
  }

  /**
   * 制定扩缩容决策
   */
  private async makeScalingDecision(locationId: LocationID, config: ScalingConfig): Promise<ScalingDecision> {
    const stats = await this.getLocationLoadStats(locationId)
    const currentInstances = stats.instanceCount

    // 默认决策
    let decision: ScalingDecision = {
      action: 'none',
      reason: '负载正常',
      targetInstances: currentInstances,
      priority: 0,
      estimatedTime: 0
    }

    // 根据策略评估
    switch (config.strategy) {
      case ScalingStrategy.AUTO_CPU:
        decision = this.evaluateCpuBasedScaling(stats, config, currentInstances)
        break
      case ScalingStrategy.AUTO_MEMORY:
        decision = this.evaluateMemoryBasedScaling(stats, config, currentInstances)
        break
      case ScalingStrategy.AUTO_USERS:
        decision = this.evaluateUserBasedScaling(stats, config, currentInstances)
        break
      case ScalingStrategy.AUTO_HYBRID:
        decision = this.evaluateHybridScaling(stats, config, currentInstances)
        break
    }

    // 检查实例数量限制
    decision.targetInstances = Math.max(config.minInstances, 
      Math.min(config.maxInstances, decision.targetInstances))

    return decision
  }

  /**
   * 基于 CPU 的扩缩容评估
   */
  private evaluateCpuBasedScaling(stats: any, config: ScalingConfig, currentInstances: number): ScalingDecision {
    if (stats.avgCpuUsage > config.scaleUpThreshold) {
      return {
        action: 'scale_up',
        reason: `CPU 使用率过高: ${stats.avgCpuUsage.toFixed(1)}%`,
        targetInstances: currentInstances + 1,
        priority: 8,
        estimatedTime: 60
      }
    } else if (stats.avgCpuUsage < config.scaleDownThreshold && currentInstances > config.minInstances) {
      return {
        action: 'scale_down',
        reason: `CPU 使用率过低: ${stats.avgCpuUsage.toFixed(1)}%`,
        targetInstances: currentInstances - 1,
        priority: 3,
        estimatedTime: 30
      }
    }

    return {
      action: 'none',
      reason: 'CPU 使用率正常',
      targetInstances: currentInstances,
      priority: 0,
      estimatedTime: 0
    }
  }

  /**
   * 基于内存的扩缩容评估
   */
  private evaluateMemoryBasedScaling(stats: any, config: ScalingConfig, currentInstances: number): ScalingDecision {
    if (stats.avgMemoryUsage > config.scaleUpThreshold) {
      return {
        action: 'scale_up',
        reason: `内存使用率过高: ${stats.avgMemoryUsage.toFixed(1)}%`,
        targetInstances: currentInstances + 1,
        priority: 9,
        estimatedTime: 60
      }
    } else if (stats.avgMemoryUsage < config.scaleDownThreshold && currentInstances > config.minInstances) {
      return {
        action: 'scale_down',
        reason: `内存使用率过低: ${stats.avgMemoryUsage.toFixed(1)}%`,
        targetInstances: currentInstances - 1,
        priority: 3,
        estimatedTime: 30
      }
    }

    return {
      action: 'none',
      reason: '内存使用率正常',
      targetInstances: currentInstances,
      priority: 0,
      estimatedTime: 0
    }
  }

  /**
   * 基于用户数的扩缩容评估
   */
  private evaluateUserBasedScaling(stats: any, config: ScalingConfig, currentInstances: number): ScalingDecision {
    const userRatio = stats.totalUsers / stats.maxUsers
    
    if (userRatio > config.targetUserRatio) {
      return {
        action: 'scale_up',
        reason: `用户比例过高: ${(userRatio * 100).toFixed(1)}%`,
        targetInstances: currentInstances + 1,
        priority: 7,
        estimatedTime: 60
      }
    } else if (userRatio < config.scaleDownThreshold && currentInstances > config.minInstances) {
      return {
        action: 'scale_down',
        reason: `用户比例过低: ${(userRatio * 100).toFixed(1)}%`,
        targetInstances: currentInstances - 1,
        priority: 4,
        estimatedTime: 30
      }
    }

    return {
      action: 'none',
      reason: '用户比例正常',
      targetInstances: currentInstances,
      priority: 0,
      estimatedTime: 0
    }
  }

  /**
   * 混合策略扩缩容评估
   */
  private evaluateHybridScaling(stats: any, config: ScalingConfig, currentInstances: number): ScalingDecision {
    const cpuDecision = this.evaluateCpuBasedScaling(stats, config, currentInstances)
    const memoryDecision = this.evaluateMemoryBasedScaling(stats, config, currentInstances)
    const userDecision = this.evaluateUserBasedScaling(stats, config, currentInstances)

    // 选择优先级最高的决策
    const decisions = [cpuDecision, memoryDecision, userDecision]
    return decisions.reduce((prev, current) => 
      current.priority > prev.priority ? current : prev
    )
  }

  /**
   * 执行扩缩容决策
   */
  private async executeScalingDecision(locationId: LocationID, decision: ScalingDecision): Promise<void> {
    if (decision.action === 'none') return

    logger.info('执行扩缩容决策', { locationId, decision })

    try {
      switch (decision.action) {
        case 'scale_up':
          await this.scaleUp(locationId, decision.targetInstances)
          break
        case 'scale_down':
          await this.scaleDown(locationId, decision.targetInstances)
          break
        case 'migrate':
          // 实现迁移逻辑
          break
      }
    } catch (error) {
      logger.error('执行扩缩容决策失败', { error, locationId, decision })
    }
  }

  /**
   * 验证扩缩容配置
   */
  private validateScalingConfig(config: ScalingConfig): void {
    if (config.minInstances < 0 || config.maxInstances < config.minInstances) {
      throw new Error('实例数量配置无效')
    }
    if (config.scaleUpThreshold <= config.scaleDownThreshold) {
      throw new Error('扩缩容阈值配置无效')
    }
    if (config.cooldownPeriod < 0 || config.evaluationPeriod < 0) {
      throw new Error('时间配置无效')
    }
  }

  /**
   * 获取当前实例数量
   */
  private async getCurrentInstanceCount(locationId: LocationID): Promise<number> {
    const instances = await this.getLocationInstances(locationId)
    return instances.length
  }

  /**
   * 获取位置的所有实例
   */
  private async getLocationInstances(locationId: LocationID): Promise<any[]> {
    const result = await this.app.service('instance').find({
      query: {
        locationId,
        ended: false
      }
    })
    return result.data || []
  }

  /**
   * 创建新实例
   */
  private async createNewInstance(locationId: LocationID): Promise<void> {
    const instanceManager = this.app.get('instanceManager')
    if (instanceManager) {
      await instanceManager.createInstance({ locationId })
    }
  }

  /**
   * 选择要终止的实例
   */
  private async selectInstancesForTermination(locationId: LocationID, count: number): Promise<InstanceID[]> {
    const instances = await this.getLocationInstances(locationId)
    
    // 按用户数排序，优先终止用户较少的实例
    instances.sort((a, b) => {
      const metricsA = this.instanceMetrics.get(a.id)
      const metricsB = this.instanceMetrics.get(b.id)
      const usersA = metricsA?.userCount || 0
      const usersB = metricsB?.userCount || 0
      return usersA - usersB
    })

    return instances.slice(0, count).map(instance => instance.id)
  }

  /**
   * 终止实例
   */
  private async terminateInstance(instanceId: InstanceID): Promise<void> {
    const instanceManager = this.app.get('instanceManager')
    if (instanceManager) {
      await instanceManager.destroyInstance(instanceId)
    }
  }

  /**
   * 创建迁移计划
   */
  private async createMigrationPlan(sourceInstanceId: InstanceID, targetInstanceId?: InstanceID): Promise<MigrationPlan> {
    // 实现迁移计划创建逻辑
    return {
      sourceInstanceId,
      targetInstanceId,
      affectedUsers: [],
      estimatedDowntime: 30,
      migrationSteps: []
    }
  }

  /**
   * 执行迁移
   */
  private async executeMigration(plan: MigrationPlan): Promise<void> {
    // 实现迁移执行逻辑
  }

  /**
   * 初始化扩缩容管理器
   */
  private initializeScalingManager(): void {
    // 启动监控
    this.monitoringInterval = setInterval(() => {
      this.performPeriodicEvaluation()
    }, 30000) // 每30秒评估一次

    logger.info('扩缩容管理器初始化完成')
  }

  /**
   * 定期评估
   */
  private async performPeriodicEvaluation(): Promise<void> {
    try {
      // 清理过期指标
      this.cleanupExpiredMetrics()
      
      // 对所有配置的位置进行评估
      for (const [locationId, config] of this.scalingConfigs) {
        if (config.strategy !== ScalingStrategy.MANUAL) {
          const decision = await this.makeScalingDecision(locationId, config)
          await this.executeScalingDecision(locationId, decision)
        }
      }
    } catch (error) {
      logger.error('定期评估失败', { error })
    }
  }

  /**
   * 清理过期指标
   */
  private cleanupExpiredMetrics(): void {
    const now = Date.now()
    const expireTime = 5 * 60 * 1000 // 5分钟

    for (const [instanceId, metrics] of this.instanceMetrics) {
      if (now - metrics.timestamp > expireTime) {
        this.instanceMetrics.delete(instanceId)
      }
    }
  }
}

// 导出服务
export default (app: Application): void => {
  const scalingManager = new ScalingManagerService(app)
  app.set('scalingManager', scalingManager)
}
