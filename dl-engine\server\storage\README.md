# DL-Engine 存储与AI智能服务

## 概述

DL-Engine存储与AI智能服务是一个完整的存储解决方案，提供多数据库支持、对象存储、媒体处理、备份迁移等功能。

## 架构特色

### 🗄️ 多数据库融合架构
- **MySQL**: 主数据库，处理事务性数据
- **Redis**: 高性能缓存和会话管理
- **PostgreSQL**: 向量数据库，支持AI功能
- **Minio**: 分布式对象存储

### 🚀 高可用性设计
- 分布式事务支持
- 多级缓存策略
- 自动故障恢复
- 负载均衡和水平扩展

### 🤖 智能化功能
- 向量搜索和相似度计算
- 知识图谱构建和分析
- 学习路径推荐
- 内容智能分类

### 🔒 安全性保障
- 细粒度权限控制
- 数据加密传输和存储
- 访问日志和审计
- 临时访问令牌机制

### ⚡ 性能优化
- 分片上传和断点续传
- CDN加速和缓存优化
- 数据压缩和格式转换
- 连接池和资源管理

## 服务模块

### 1. 数据库服务 (Database Services)

#### MySQL主数据库
- **实体管理**: 用户、项目、场景、资产、课程等
- **事务管理**: 分布式事务、Saga模式、嵌套事务
- **数据迁移**: 版本管理、一致性检查、备份恢复
- **连接管理**: 连接池、健康检查、性能监控

#### Redis缓存系统
- **缓存服务**: 多级缓存、标签管理、压缩、统计
- **会话管理**: 用户会话、滑动过期、设备管理
- **分布式锁**: 可重入锁、锁续期、批量操作
- **队列服务**: 优先级队列、延迟任务、重试机制
- **发布订阅**: 消息广播、请求响应模式

#### PostgreSQL向量库
- **向量存储**: 多种向量类型和模型支持
- **嵌入处理**: 批处理、状态管理、质量评估
- **文档管理**: 文档解析、分块、索引
- **知识图谱**: 节点关系、学习分析

### 2. 对象存储服务 (Object Storage)

#### Minio存储
- **文件上传**: 单文件、分片上传、批量上传
- **存储管理**: 存储桶策略、生命周期、配额管理
- **访问控制**: 权限管理、临时访问、预签名URL
- **CDN集成**: 多提供商支持、缓存管理、性能优化

### 3. 媒体处理服务 (Media Processing)

#### 图像处理
- **基础操作**: 调整大小、裁剪、旋转、格式转换
- **图像增强**: 亮度、对比度、饱和度、锐化
- **特效处理**: 滤镜、水印、特效、批量处理
- **缩略图生成**: 多尺寸、自定义参数

#### 视频处理
- **视频转码**: 多格式支持、质量控制、编码优化
- **缩略图提取**: 关键帧提取、时间点截图
- **流媒体**: HLS/DASH流生成、多码率支持
- **视频分析**: 元数据提取、质量检测

#### 音频处理
- **音频转码**: 格式转换、质量调整、压缩优化
- **音频编辑**: 裁剪、混合、拼接、特效
- **波形生成**: 可视化波形图生成
- **音频分析**: 频谱分析、音量检测

### 4. 备份与迁移 (Backup & Migration)

#### 数据备份
- **备份策略**: 全量备份、增量备份、定时备份
- **数据压缩**: 高效压缩算法、加密存储
- **远程备份**: 云端存储、多地备份
- **恢复验证**: 完整性校验、恢复测试

#### 数据迁移
- **迁移计划**: 版本升级、数据同步、一致性检查
- **迁移执行**: 步骤管理、进度跟踪、错误处理
- **回滚支持**: 自动回滚、状态恢复

## 快速开始

### 环境要求
- Node.js 18+
- MySQL 8.0+
- Redis 6.0+
- PostgreSQL 14+ (with pgvector)
- Minio Server
- FFmpeg (媒体处理)

### 安装依赖
```bash
npm install
```

### 环境配置
创建 `.env` 文件：
```env
# 数据库配置
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USERNAME=root
MYSQL_PASSWORD=password
MYSQL_DATABASE=dl_engine

REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

POSTGRESQL_HOST=localhost
POSTGRESQL_PORT=5432
POSTGRESQL_USERNAME=postgres
POSTGRESQL_PASSWORD=password
POSTGRESQL_DATABASE=dl_engine_vectors

# 对象存储配置
MINIO_ENDPOINT=localhost
MINIO_PORT=9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin

# 服务配置
STORAGE_PORT=3001
```

### 启动服务
```bash
# 开发模式
npm run start:dev

# 生产模式
npm run build
npm run start:prod
```

## API文档

服务启动后，访问 `http://localhost:3001/api/docs` 查看完整的API文档。

### 主要API端点

#### 存储管理
- `GET /storage/health` - 健康检查
- `GET /storage/metrics` - 存储指标
- `POST /storage/optimize` - 存储优化
- `POST /storage/cleanup` - 存储清理

#### 数据库操作
- `GET /mysql/health` - MySQL健康检查
- `GET /redis/ping` - Redis连接测试
- `GET /postgresql/health` - PostgreSQL健康检查

#### 文件上传
- `POST /minio/upload` - 单文件上传
- `POST /minio/upload/batch` - 批量上传
- `POST /minio/upload/chunk/init` - 初始化分片上传

#### 媒体处理
- `POST /media/upload` - 媒体文件上传
- `GET /media/files` - 获取媒体文件列表
- `POST /media/images/:fileId/thumbnails` - 生成图像缩略图

#### 备份管理
- `POST /backup/full` - 创建完整备份
- `GET /backup/jobs` - 获取备份任务列表
- `POST /backup/restore/:jobId` - 恢复备份

## 性能指标

### 处理能力
- **文件上传**: 支持TB级文件，断点续传
- **媒体处理**: 并发处理，队列管理
- **数据库**: 高并发读写，连接池优化
- **缓存**: 毫秒级响应，高命中率

### 可扩展性
- **水平扩展**: 支持多实例部署
- **负载均衡**: 自动负载分配
- **存储扩展**: 分布式存储集群
- **处理扩展**: 队列工作节点扩展

## 监控与运维

### 健康检查
- 服务状态监控
- 数据库连接检查
- 存储空间监控
- 队列状态监控

### 日志管理
- 结构化日志输出
- 错误日志收集
- 性能日志分析
- 访问日志审计

### 性能监控
- 响应时间统计
- 吞吐量监控
- 资源使用率
- 错误率统计

## 开发指南

### 项目结构
```
src/
├── database/           # 数据库服务
│   ├── mysql/         # MySQL服务
│   ├── redis/         # Redis服务
│   └── postgresql/    # PostgreSQL服务
├── minio/             # 对象存储服务
├── media/             # 媒体处理服务
├── backup/            # 备份迁移服务
├── storage.module.ts  # 主模块
├── storage.service.ts # 主服务
└── main.ts           # 入口文件
```

### 扩展开发
1. 创建新的服务模块
2. 实现服务接口
3. 注册到主模块
4. 添加API端点
5. 编写测试用例

## 许可证

MIT License
