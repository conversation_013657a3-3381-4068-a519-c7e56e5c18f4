import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosInstance } from 'axios';

export interface OllamaModel {
  name: string;
  size: number;
  digest: string;
  details: {
    format: string;
    family: string;
    families: string[];
    parameter_size: string;
    quantization_level: string;
  };
  loaded: boolean;
  modified_at: string;
}

export interface GenerateOptions {
  model: string;
  prompt?: string;
  messages?: Array<{ role: string; content: string }>;
  system?: string;
  template?: string;
  context?: number[];
  stream?: boolean;
  raw?: boolean;
  format?: 'json';
  options?: {
    temperature?: number;
    top_k?: number;
    top_p?: number;
    repeat_last_n?: number;
    repeat_penalty?: number;
    presence_penalty?: number;
    frequency_penalty?: number;
    mirostat?: number;
    mirostat_eta?: number;
    mirostat_tau?: number;
    num_ctx?: number;
    num_gqa?: number;
    num_gpu?: number;
    num_thread?: number;
    num_predict?: number;
    tfs_z?: number;
    typical_p?: number;
    seed?: number;
    stop?: string[];
  };
}

export interface GenerateResponse {
  model: string;
  created_at: string;
  response?: string;
  message?: { role: string; content: string };
  done: boolean;
  context?: number[];
  total_duration?: number;
  load_duration?: number;
  prompt_eval_count?: number;
  prompt_eval_duration?: number;
  eval_count?: number;
  eval_duration?: number;
}

export interface EmbeddingOptions {
  model: string;
  prompt: string;
  options?: {
    temperature?: number;
    seed?: number;
  };
}

export interface EmbeddingResponse {
  embedding: number[];
}

@Injectable()
export class OllamaService implements OnModuleInit {
  private readonly logger = new Logger(OllamaService.name);
  private readonly client: AxiosInstance;
  private readonly baseUrl: string;
  private readonly models = new Map<string, OllamaModel>();

  constructor(private configService: ConfigService) {
    this.baseUrl = this.configService.get('OLLAMA_BASE_URL', 'http://localhost:11434');
    
    this.client = axios.create({
      baseURL: this.baseUrl,
      timeout: this.configService.get('OLLAMA_TIMEOUT', 300000), // 5分钟
      headers: {
        'Content-Type': 'application/json',
      },
    });
  }

  async onModuleInit() {
    try {
      await this.checkConnection();
      await this.loadAvailableModels();
      this.logger.log('Ollama service initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize Ollama service:', error);
    }
  }

  /**
   * 检查Ollama连接
   */
  async checkConnection(): Promise<boolean> {
    try {
      const response = await this.client.get('/api/version');
      this.logger.log(`Connected to Ollama version: ${response.data.version}`);
      return true;
    } catch (error) {
      this.logger.error('Failed to connect to Ollama:', error);
      return false;
    }
  }

  /**
   * 获取可用模型列表
   */
  async getAvailableModels(): Promise<OllamaModel[]> {
    try {
      const response = await this.client.get('/api/tags');
      const models = response.data.models || [];
      
      // 更新本地模型缓存
      this.models.clear();
      for (const model of models) {
        this.models.set(model.name, {
          ...model,
          loaded: false, // 需要单独检查加载状态
        });
      }

      return Array.from(this.models.values());
    } catch (error) {
      this.logger.error('Failed to get available models:', error);
      return [];
    }
  }

  /**
   * 拉取模型
   */
  async pullModel(
    modelName: string,
    onProgress?: (progress: { status: string; digest?: string; total?: number; completed?: number }) => void
  ): Promise<void> {
    try {
      this.logger.log(`Pulling model: ${modelName}`);
      
      const response = await this.client.post('/api/pull', 
        { name: modelName },
        { 
          responseType: 'stream',
          timeout: 0, // 无超时限制
        }
      );

      return new Promise((resolve, reject) => {
        let buffer = '';
        
        response.data.on('data', (chunk: Buffer) => {
          buffer += chunk.toString();
          const lines = buffer.split('\n');
          buffer = lines.pop() || '';
          
          for (const line of lines) {
            if (line.trim()) {
              try {
                const progress = JSON.parse(line);
                if (onProgress) {
                  onProgress(progress);
                }
                
                if (progress.status === 'success') {
                  this.logger.log(`Model pulled successfully: ${modelName}`);
                  resolve();
                }
              } catch (error) {
                // 忽略JSON解析错误
              }
            }
          }
        });

        response.data.on('end', () => {
          resolve();
        });

        response.data.on('error', (error: Error) => {
          this.logger.error(`Failed to pull model ${modelName}:`, error);
          reject(error);
        });
      });
    } catch (error) {
      this.logger.error(`Failed to pull model ${modelName}:`, error);
      throw error;
    }
  }

  /**
   * 删除模型
   */
  async deleteModel(modelName: string): Promise<void> {
    try {
      await this.client.delete('/api/delete', {
        data: { name: modelName }
      });
      
      this.models.delete(modelName);
      this.logger.log(`Model deleted: ${modelName}`);
    } catch (error) {
      this.logger.error(`Failed to delete model ${modelName}:`, error);
      throw error;
    }
  }

  /**
   * 生成文本
   */
  async generateText(
    prompt: string,
    options: {
      model?: string;
      system?: string;
      context?: number[];
      stream?: boolean;
      maxTokens?: number;
      temperature?: number;
      topP?: number;
      topK?: number;
      repeatPenalty?: number;
      seed?: number;
      stop?: string[];
    } = {}
  ): Promise<string | AsyncIterable<string>> {
    try {
      const generateOptions: GenerateOptions = {
        model: options.model || this.configService.get('OLLAMA_DEFAULT_MODEL', 'llama2'),
        prompt,
        system: options.system,
        context: options.context,
        stream: options.stream || false,
        options: {
          temperature: options.temperature,
          top_p: options.topP,
          top_k: options.topK,
          repeat_penalty: options.repeatPenalty,
          num_predict: options.maxTokens,
          seed: options.seed,
          stop: options.stop,
        },
      };

      if (options.stream) {
        return this.generateStreamingText(generateOptions);
      } else {
        return this.generateCompleteText(generateOptions);
      }
    } catch (error) {
      this.logger.error('Failed to generate text:', error);
      throw error;
    }
  }

  /**
   * 聊天对话
   */
  async chat(
    messages: Array<{ role: 'system' | 'user' | 'assistant'; content: string }>,
    options: {
      model?: string;
      stream?: boolean;
      maxTokens?: number;
      temperature?: number;
      topP?: number;
      topK?: number;
      repeatPenalty?: number;
      seed?: number;
      stop?: string[];
    } = {}
  ): Promise<string | AsyncIterable<string>> {
    try {
      const generateOptions: GenerateOptions = {
        model: options.model || this.configService.get('OLLAMA_DEFAULT_MODEL', 'llama2'),
        messages,
        stream: options.stream || false,
        options: {
          temperature: options.temperature,
          top_p: options.topP,
          top_k: options.topK,
          repeat_penalty: options.repeatPenalty,
          num_predict: options.maxTokens,
          seed: options.seed,
          stop: options.stop,
        },
      };

      if (options.stream) {
        return this.chatStreaming(generateOptions);
      } else {
        return this.chatComplete(generateOptions);
      }
    } catch (error) {
      this.logger.error('Failed to chat:', error);
      throw error;
    }
  }

  /**
   * 生成嵌入向量
   */
  async generateEmbedding(
    text: string,
    options: {
      model?: string;
      temperature?: number;
      seed?: number;
    } = {}
  ): Promise<number[]> {
    try {
      const embeddingOptions: EmbeddingOptions = {
        model: options.model || this.configService.get('OLLAMA_EMBEDDING_MODEL', 'nomic-embed-text'),
        prompt: text,
        options: {
          temperature: options.temperature,
          seed: options.seed,
        },
      };

      const response = await this.client.post('/api/embeddings', embeddingOptions);
      return response.data.embedding;
    } catch (error) {
      this.logger.error('Failed to generate embedding:', error);
      throw error;
    }
  }

  /**
   * 获取模型信息
   */
  async getModelInfo(modelName: string): Promise<any> {
    try {
      const response = await this.client.post('/api/show', { name: modelName });
      return response.data;
    } catch (error) {
      this.logger.error(`Failed to get model info for ${modelName}:`, error);
      throw error;
    }
  }

  /**
   * 检查模型是否存在
   */
  async modelExists(modelName: string): Promise<boolean> {
    try {
      await this.getModelInfo(modelName);
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * 获取运行状态
   */
  async getStatus(): Promise<{
    connected: boolean;
    models: number;
    version?: string;
    memory?: any;
  }> {
    try {
      const connected = await this.checkConnection();
      const models = await this.getAvailableModels();
      
      return {
        connected,
        models: models.length,
        version: await this.getVersion(),
      };
    } catch (error) {
      return {
        connected: false,
        models: 0,
      };
    }
  }

  private async generateCompleteText(options: GenerateOptions): Promise<string> {
    const response = await this.client.post('/api/generate', options);
    return response.data.response;
  }

  private async generateStreamingText(options: GenerateOptions): Promise<AsyncIterable<string>> {
    const response = await this.client.post('/api/generate', options, {
      responseType: 'stream',
    });

    return this.createStreamIterator(response.data);
  }

  private async chatComplete(options: GenerateOptions): Promise<string> {
    const response = await this.client.post('/api/chat', options);
    return response.data.message.content;
  }

  private async chatStreaming(options: GenerateOptions): Promise<AsyncIterable<string>> {
    const response = await this.client.post('/api/chat', options, {
      responseType: 'stream',
    });

    return this.createChatStreamIterator(response.data);
  }

  private async *createStreamIterator(stream: any): AsyncIterable<string> {
    let buffer = '';
    
    for await (const chunk of stream) {
      buffer += chunk.toString();
      const lines = buffer.split('\n');
      buffer = lines.pop() || '';
      
      for (const line of lines) {
        if (line.trim()) {
          try {
            const data = JSON.parse(line);
            if (data.response) {
              yield data.response;
            }
          } catch (error) {
            // 忽略JSON解析错误
          }
        }
      }
    }
  }

  private async *createChatStreamIterator(stream: any): AsyncIterable<string> {
    let buffer = '';
    
    for await (const chunk of stream) {
      buffer += chunk.toString();
      const lines = buffer.split('\n');
      buffer = lines.pop() || '';
      
      for (const line of lines) {
        if (line.trim()) {
          try {
            const data = JSON.parse(line);
            if (data.message?.content) {
              yield data.message.content;
            }
          } catch (error) {
            // 忽略JSON解析错误
          }
        }
      }
    }
  }

  private async loadAvailableModels(): Promise<void> {
    await this.getAvailableModels();
  }

  private async getVersion(): Promise<string> {
    try {
      const response = await this.client.get('/api/version');
      return response.data.version;
    } catch (error) {
      return 'unknown';
    }
  }
}
