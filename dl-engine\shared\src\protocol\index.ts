/**
 * DL-Engine 通信协议
 * 定义客户端和服务端之间的通信协议
 */

// 消息类型枚举
export enum MessageType {
  // 连接管理
  CONNECT = 'connect',
  DISCONNECT = 'disconnect',
  HEARTBEAT = 'heartbeat',
  
  // 认证相关
  AUTH_REQUEST = 'auth:request',
  AUTH_RESPONSE = 'auth:response',
  AUTH_REFRESH = 'auth:refresh',
  AUTH_LOGOUT = 'auth:logout',
  
  // 用户相关
  USER_JOIN = 'user:join',
  USER_LEAVE = 'user:leave',
  USER_UPDATE = 'user:update',
  USER_STATUS = 'user:status',
  
  // 场景相关
  SCENE_JOIN = 'scene:join',
  SCENE_LEAVE = 'scene:leave',
  SCENE_UPDATE = 'scene:update',
  SCENE_SYNC = 'scene:sync',
  SCENE_OBJECT_ADD = 'scene:object:add',
  SCENE_OBJECT_UPDATE = 'scene:object:update',
  SCENE_OBJECT_REMOVE = 'scene:object:remove',
  
  // 协作相关
  COLLABORATION_START = 'collaboration:start',
  COLLABORATION_END = 'collaboration:end',
  COLLABORATION_UPDATE = 'collaboration:update',
  COLLABORATION_CURSOR = 'collaboration:cursor',
  COLLABORATION_SELECTION = 'collaboration:selection',
  
  // 聊天相关
  CHAT_MESSAGE = 'chat:message',
  CHAT_TYPING = 'chat:typing',
  CHAT_HISTORY = 'chat:history',
  
  // 语音视频
  VOICE_START = 'voice:start',
  VOICE_STOP = 'voice:stop',
  VOICE_DATA = 'voice:data',
  VIDEO_START = 'video:start',
  VIDEO_STOP = 'video:stop',
  VIDEO_DATA = 'video:data',
  
  // 屏幕共享
  SCREEN_SHARE_START = 'screen:share:start',
  SCREEN_SHARE_STOP = 'screen:share:stop',
  SCREEN_SHARE_DATA = 'screen:share:data',
  
  // 文件传输
  FILE_UPLOAD_START = 'file:upload:start',
  FILE_UPLOAD_CHUNK = 'file:upload:chunk',
  FILE_UPLOAD_COMPLETE = 'file:upload:complete',
  FILE_DOWNLOAD = 'file:download',
  
  // 学习相关
  LEARNING_START = 'learning:start',
  LEARNING_PROGRESS = 'learning:progress',
  LEARNING_COMPLETE = 'learning:complete',
  LEARNING_QUIZ_SUBMIT = 'learning:quiz:submit',
  LEARNING_QUIZ_RESULT = 'learning:quiz:result',
  
  // 系统消息
  SYSTEM_NOTIFICATION = 'system:notification',
  SYSTEM_ERROR = 'system:error',
  SYSTEM_MAINTENANCE = 'system:maintenance'
}

// 基础消息接口
export interface BaseMessage {
  id: string
  type: MessageType
  timestamp: number
  senderId?: string
  targetId?: string
  sessionId?: string
}

// 认证消息
export interface AuthRequestMessage extends BaseMessage {
  type: MessageType.AUTH_REQUEST
  payload: {
    token?: string
    username?: string
    password?: string
    phone?: string
    verificationCode?: string
  }
}

export interface AuthResponseMessage extends BaseMessage {
  type: MessageType.AUTH_RESPONSE
  payload: {
    success: boolean
    token?: string
    refreshToken?: string
    user?: any
    error?: string
  }
}

// 用户消息
export interface UserJoinMessage extends BaseMessage {
  type: MessageType.USER_JOIN
  payload: {
    user: {
      id: string
      username: string
      avatar?: string
      role: string
    }
    sessionId: string
  }
}

export interface UserUpdateMessage extends BaseMessage {
  type: MessageType.USER_UPDATE
  payload: {
    userId: string
    updates: {
      position?: { x: number; y: number; z: number }
      rotation?: { x: number; y: number; z: number; w: number }
      animation?: string
      status?: string
    }
  }
}

// 场景消息
export interface SceneJoinMessage extends BaseMessage {
  type: MessageType.SCENE_JOIN
  payload: {
    sceneId: string
    userId: string
    spawnPoint?: { x: number; y: number; z: number }
  }
}

export interface SceneUpdateMessage extends BaseMessage {
  type: MessageType.SCENE_UPDATE
  payload: {
    sceneId: string
    updates: {
      objects?: any[]
      lighting?: any
      environment?: any
      physics?: any
    }
  }
}

export interface SceneObjectUpdateMessage extends BaseMessage {
  type: MessageType.SCENE_OBJECT_UPDATE
  payload: {
    objectId: string
    transform?: {
      position: { x: number; y: number; z: number }
      rotation: { x: number; y: number; z: number; w: number }
      scale: { x: number; y: number; z: number }
    }
    properties?: Record<string, any>
    animation?: {
      name: string
      duration: number
      loop: boolean
    }
  }
}

// 协作消息
export interface CollaborationUpdateMessage extends BaseMessage {
  type: MessageType.COLLABORATION_UPDATE
  payload: {
    operation: 'create' | 'update' | 'delete'
    objectType: string
    objectId: string
    data: any
    userId: string
    timestamp: number
  }
}

export interface CollaborationCursorMessage extends BaseMessage {
  type: MessageType.COLLABORATION_CURSOR
  payload: {
    userId: string
    position: { x: number; y: number; z?: number }
    visible: boolean
  }
}

// 聊天消息
export interface ChatMessage extends BaseMessage {
  type: MessageType.CHAT_MESSAGE
  payload: {
    content: string
    messageType: 'text' | 'image' | 'file' | 'emoji'
    metadata?: {
      fileName?: string
      fileSize?: number
      imageUrl?: string
      emojiCode?: string
    }
    replyTo?: string
    mentions?: string[]
  }
}

// 语音视频消息
export interface VoiceDataMessage extends BaseMessage {
  type: MessageType.VOICE_DATA
  payload: {
    audioData: ArrayBuffer
    sampleRate: number
    channels: number
    duration: number
  }
}

export interface VideoDataMessage extends BaseMessage {
  type: MessageType.VIDEO_DATA
  payload: {
    videoData: ArrayBuffer
    width: number
    height: number
    format: string
    timestamp: number
  }
}

// 文件传输消息
export interface FileUploadStartMessage extends BaseMessage {
  type: MessageType.FILE_UPLOAD_START
  payload: {
    fileName: string
    fileSize: number
    fileType: string
    chunkSize: number
    totalChunks: number
    checksum: string
  }
}

export interface FileUploadChunkMessage extends BaseMessage {
  type: MessageType.FILE_UPLOAD_CHUNK
  payload: {
    uploadId: string
    chunkIndex: number
    chunkData: ArrayBuffer
    checksum: string
  }
}

// 学习消息
export interface LearningProgressMessage extends BaseMessage {
  type: MessageType.LEARNING_PROGRESS
  payload: {
    courseId: string
    lessonId: string
    progress: number
    timeSpent: number
    interactions: Array<{
      type: string
      timestamp: number
      data: any
    }>
  }
}

export interface LearningQuizSubmitMessage extends BaseMessage {
  type: MessageType.LEARNING_QUIZ_SUBMIT
  payload: {
    quizId: string
    answers: Array<{
      questionId: string
      answer: any
      timeSpent: number
    }>
    totalTime: number
  }
}

// 系统消息
export interface SystemNotificationMessage extends BaseMessage {
  type: MessageType.SYSTEM_NOTIFICATION
  payload: {
    level: 'info' | 'warning' | 'error' | 'success'
    title: string
    content: string
    actions?: Array<{
      label: string
      action: string
      data?: any
    }>
    autoClose?: number
  }
}

// 消息联合类型
export type Message = 
  | AuthRequestMessage
  | AuthResponseMessage
  | UserJoinMessage
  | UserUpdateMessage
  | SceneJoinMessage
  | SceneUpdateMessage
  | SceneObjectUpdateMessage
  | CollaborationUpdateMessage
  | CollaborationCursorMessage
  | ChatMessage
  | VoiceDataMessage
  | VideoDataMessage
  | FileUploadStartMessage
  | FileUploadChunkMessage
  | LearningProgressMessage
  | LearningQuizSubmitMessage
  | SystemNotificationMessage

// 消息工厂函数
export class MessageFactory {
  private static generateId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  }

  static createAuthRequest(payload: AuthRequestMessage['payload']): AuthRequestMessage {
    return {
      id: this.generateId(),
      type: MessageType.AUTH_REQUEST,
      timestamp: Date.now(),
      payload
    }
  }

  static createUserUpdate(
    senderId: string,
    updates: UserUpdateMessage['payload']['updates']
  ): UserUpdateMessage {
    return {
      id: this.generateId(),
      type: MessageType.USER_UPDATE,
      timestamp: Date.now(),
      senderId,
      payload: {
        userId: senderId,
        updates
      }
    }
  }

  static createSceneObjectUpdate(
    senderId: string,
    objectId: string,
    updates: Partial<SceneObjectUpdateMessage['payload']>
  ): SceneObjectUpdateMessage {
    return {
      id: this.generateId(),
      type: MessageType.SCENE_OBJECT_UPDATE,
      timestamp: Date.now(),
      senderId,
      payload: {
        objectId,
        ...updates
      }
    }
  }

  static createChatMessage(
    senderId: string,
    content: string,
    messageType: ChatMessage['payload']['messageType'] = 'text',
    metadata?: ChatMessage['payload']['metadata']
  ): ChatMessage {
    return {
      id: this.generateId(),
      type: MessageType.CHAT_MESSAGE,
      timestamp: Date.now(),
      senderId,
      payload: {
        content,
        messageType,
        metadata
      }
    }
  }

  static createSystemNotification(
    level: SystemNotificationMessage['payload']['level'],
    title: string,
    content: string,
    actions?: SystemNotificationMessage['payload']['actions']
  ): SystemNotificationMessage {
    return {
      id: this.generateId(),
      type: MessageType.SYSTEM_NOTIFICATION,
      timestamp: Date.now(),
      payload: {
        level,
        title,
        content,
        actions
      }
    }
  }
}

// 消息验证器
export class MessageValidator {
  static validate(message: any): message is Message {
    if (!message || typeof message !== 'object') {
      return false
    }

    const { id, type, timestamp } = message
    
    if (!id || typeof id !== 'string') {
      return false
    }

    if (!type || !Object.values(MessageType).includes(type)) {
      return false
    }

    if (!timestamp || typeof timestamp !== 'number') {
      return false
    }

    return true
  }

  static validatePayload(message: Message): boolean {
    switch (message.type) {
      case MessageType.AUTH_REQUEST:
        return this.validateAuthRequest(message as AuthRequestMessage)
      case MessageType.CHAT_MESSAGE:
        return this.validateChatMessage(message as ChatMessage)
      case MessageType.USER_UPDATE:
        return this.validateUserUpdate(message as UserUpdateMessage)
      default:
        return true
    }
  }

  private static validateAuthRequest(message: AuthRequestMessage): boolean {
    const { payload } = message
    return payload && (
      (payload.token && typeof payload.token === 'string') ||
      (payload.username && payload.password) ||
      (payload.phone && payload.verificationCode)
    )
  }

  private static validateChatMessage(message: ChatMessage): boolean {
    const { payload } = message
    return payload && 
           payload.content && 
           typeof payload.content === 'string' &&
           payload.messageType &&
           ['text', 'image', 'file', 'emoji'].includes(payload.messageType)
  }

  private static validateUserUpdate(message: UserUpdateMessage): boolean {
    const { payload } = message
    return payload && 
           payload.userId && 
           typeof payload.userId === 'string' &&
           payload.updates &&
           typeof payload.updates === 'object'
  }
}
