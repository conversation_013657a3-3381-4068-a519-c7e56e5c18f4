import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
  Index
} from 'typeorm'
import { User } from '../../users/entities/user.entity'
import { Project } from '../../projects/entities/project.entity'
import { SceneVersion } from './scene-version.entity'
import { SceneComponent } from './scene-component.entity'
import { SceneAsset } from './scene-asset.entity'
import { SceneShare } from './scene-share.entity'
import { SceneComment } from './scene-comment.entity'
import { SceneBookmark } from './scene-bookmark.entity'

/**
 * 场景状态枚举
 */
export enum SceneStatus {
  DRAFT = 'draft',           // 草稿
  PUBLISHED = 'published',   // 已发布
  ARCHIVED = 'archived',     // 已归档
  DELETED = 'deleted'        // 已删除
}

/**
 * 场景类型枚举
 */
export enum SceneType {
  EDUCATIONAL = 'educational',   // 教育场景
  SIMULATION = 'simulation',     // 仿真场景
  GAME = 'game',                // 游戏场景
  PRESENTATION = 'presentation', // 演示场景
  TRAINING = 'training',        // 培训场景
  EXHIBITION = 'exhibition',    // 展览场景
  EXPERIMENT = 'experiment',    // 实验场景
  OTHER = 'other'               // 其他
}

/**
 * 渲染质量枚举
 */
export enum RenderQuality {
  LOW = 'low',               // 低质量
  MEDIUM = 'medium',         // 中等质量
  HIGH = 'high',             // 高质量
  ULTRA = 'ultra'            // 超高质量
}

/**
 * 场景实体
 * 
 * 存储3D场景的基本信息和配置
 */
@Entity('scenes')
@Index(['projectId', 'status'])
@Index(['creatorId', 'status'])
@Index(['type', 'status'])
@Index(['createdAt'])
export class Scene {
  @PrimaryGeneratedColumn('uuid')
  id: string

  /**
   * 场景名称
   */
  @Column({ type: 'varchar', length: 200 })
  name: string

  /**
   * 场景描述
   */
  @Column({ type: 'text', nullable: true })
  description?: string

  /**
   * 场景类型
   */
  @Column({
    type: 'enum',
    enum: SceneType,
    default: SceneType.EDUCATIONAL
  })
  type: SceneType

  /**
   * 场景状态
   */
  @Column({
    type: 'enum',
    enum: SceneStatus,
    default: SceneStatus.DRAFT
  })
  status: SceneStatus

  /**
   * 所属项目ID
   */
  @Column({ type: 'uuid' })
  projectId: string

  /**
   * 创建者ID
   */
  @Column({ type: 'uuid' })
  creatorId: string

  /**
   * 场景缩略图URL
   */
  @Column({ type: 'text', nullable: true })
  thumbnailUrl?: string

  /**
   * 场景预览图URL列表
   */
  @Column({ type: 'json', nullable: true })
  previewImages?: string[]

  /**
   * 场景数据文件路径
   */
  @Column({ type: 'varchar', length: 500, nullable: true })
  sceneDataPath?: string

  /**
   * 场景大小（字节）
   */
  @Column({ type: 'bigint', default: 0 })
  size: number

  /**
   * 场景版本号
   */
  @Column({ type: 'varchar', length: 50, default: '1.0.0' })
  version: string

  /**
   * 场景配置
   */
  @Column({ type: 'json', nullable: true })
  configuration?: {
    // 渲染设置
    rendering: {
      quality: RenderQuality
      shadows: boolean
      reflections: boolean
      antiAliasing: boolean
      postProcessing: boolean
      maxLights: number
      maxTextures: number
    }
    // 物理设置
    physics: {
      enabled: boolean
      gravity: { x: number; y: number; z: number }
      timeStep: number
      maxSubSteps: number
    }
    // 音频设置
    audio: {
      enabled: boolean
      masterVolume: number
      spatialAudio: boolean
      maxSources: number
    }
    // 交互设置
    interaction: {
      enabled: boolean
      multiTouch: boolean
      gestureRecognition: boolean
      voiceControl: boolean
    }
    // VR/AR设置
    xr: {
      vrEnabled: boolean
      arEnabled: boolean
      handTracking: boolean
      eyeTracking: boolean
      roomScale: boolean
    }
  }

  /**
   * 场景元数据
   */
  @Column({ type: 'json', nullable: true })
  metadata?: {
    // 场景统计
    statistics: {
      vertices: number
      triangles: number
      materials: number
      textures: number
      lights: number
      cameras: number
      animations: number
    }
    // 性能信息
    performance: {
      targetFPS: number
      minFPS: number
      maxFPS: number
      avgRenderTime: number
      memoryUsage: number
    }
    // 兼容性信息
    compatibility: {
      minWebGLVersion: string
      requiredExtensions: string[]
      supportedPlatforms: string[]
      minRAM: number
      minVRAM: number
    }
    // 教育信息
    education?: {
      subject: string
      gradeLevel: string[]
      learningObjectives: string[]
      difficulty: 'beginner' | 'intermediate' | 'advanced'
      duration: number // 预计学习时间（分钟）
      interactiveElements: string[]
      assessmentPoints: string[]
    }
  }

  /**
   * 场景环境设置
   */
  @Column({ type: 'json', nullable: true })
  environment?: {
    // 天空盒设置
    skybox: {
      type: 'color' | 'gradient' | 'cubemap' | 'hdri'
      color?: string
      gradient?: { top: string; bottom: string }
      texture?: string
      rotation: number
      exposure: number
    }
    // 光照设置
    lighting: {
      ambientLight: {
        color: string
        intensity: number
      }
      directionalLight: {
        color: string
        intensity: number
        direction: { x: number; y: number; z: number }
        castShadows: boolean
      }
      fog?: {
        enabled: boolean
        color: string
        near: number
        far: number
        density?: number
      }
    }
    // 后处理效果
    postProcessing: {
      bloom: boolean
      toneMappingExposure: number
      colorGrading: {
        contrast: number
        brightness: number
        saturation: number
        hue: number
      }
    }
  }

  /**
   * 场景统计信息
   */
  @Column({ type: 'json', nullable: true })
  statistics?: {
    views: number
    downloads: number
    shares: number
    likes: number
    comments: number
    bookmarks: number
    forks: number
    avgRating: number
    totalRatings: number
    lastActivity: Date
  }

  /**
   * 最后渲染时间
   */
  @Column({ type: 'timestamp', nullable: true })
  lastRenderedAt?: Date

  /**
   * 发布时间
   */
  @Column({ type: 'timestamp', nullable: true })
  publishedAt?: Date

  /**
   * 归档时间
   */
  @Column({ type: 'timestamp', nullable: true })
  archivedAt?: Date

  /**
   * 创建时间
   */
  @CreateDateColumn()
  createdAt: Date

  /**
   * 更新时间
   */
  @UpdateDateColumn()
  updatedAt: Date

  // 关联关系
  @ManyToOne(() => Project)
  @JoinColumn({ name: 'projectId' })
  project: Project

  @ManyToOne(() => User)
  @JoinColumn({ name: 'creatorId' })
  creator: User

  @OneToMany(() => SceneVersion, version => version.scene, { cascade: true })
  versions: SceneVersion[]

  @OneToMany(() => SceneComponent, component => component.scene, { cascade: true })
  components: SceneComponent[]

  @OneToMany(() => SceneAsset, asset => asset.scene, { cascade: true })
  assets: SceneAsset[]

  @OneToMany(() => SceneShare, share => share.scene, { cascade: true })
  shares: SceneShare[]

  @OneToMany(() => SceneComment, comment => comment.scene, { cascade: true })
  comments: SceneComment[]

  @OneToMany(() => SceneBookmark, bookmark => bookmark.scene, { cascade: true })
  bookmarks: SceneBookmark[]

  /**
   * 检查场景是否已发布
   */
  isPublished(): boolean {
    return this.status === SceneStatus.PUBLISHED
  }

  /**
   * 检查场景是否可编辑
   */
  canEdit(): boolean {
    return this.status !== SceneStatus.ARCHIVED && this.status !== SceneStatus.DELETED
  }

  /**
   * 检查场景是否可删除
   */
  canDelete(): boolean {
    return this.status !== SceneStatus.DELETED
  }

  /**
   * 更新统计信息
   */
  updateStatistics(field: keyof Scene['statistics'], increment = 1): void {
    if (!this.statistics) {
      this.statistics = {
        views: 0,
        downloads: 0,
        shares: 0,
        likes: 0,
        comments: 0,
        bookmarks: 0,
        forks: 0,
        avgRating: 0,
        totalRatings: 0,
        lastActivity: new Date()
      }
    }

    if (typeof this.statistics[field] === 'number') {
      this.statistics[field] += increment
    }
    this.statistics.lastActivity = new Date()
  }

  /**
   * 计算场景复杂度评分
   */
  getComplexityScore(): number {
    if (!this.metadata?.statistics) return 0

    const stats = this.metadata.statistics
    let score = 0

    // 基于几何复杂度
    score += Math.min(stats.triangles / 10000, 5) // 最多5分
    score += Math.min(stats.materials / 50, 3)    // 最多3分
    score += Math.min(stats.textures / 20, 2)     // 最多2分

    return Math.min(score, 10) // 总分不超过10分
  }

  /**
   * 获取推荐的渲染质量
   */
  getRecommendedQuality(): RenderQuality {
    const complexity = this.getComplexityScore()
    
    if (complexity <= 3) return RenderQuality.HIGH
    if (complexity <= 6) return RenderQuality.MEDIUM
    return RenderQuality.LOW
  }

  /**
   * 检查是否支持VR
   */
  supportsVR(): boolean {
    return this.configuration?.xr?.vrEnabled || false
  }

  /**
   * 检查是否支持AR
   */
  supportsAR(): boolean {
    return this.configuration?.xr?.arEnabled || false
  }

  /**
   * 获取场景URL友好的slug
   */
  getSlug(): string {
    return this.name
      .toLowerCase()
      .replace(/[^a-z0-9\u4e00-\u9fa5]/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '')
  }
}
