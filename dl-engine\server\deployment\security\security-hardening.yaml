# DL-Engine 安全加固配置
# 包括网络安全、应用安全、数据安全和访问控制

apiVersion: v1
kind: ConfigMap
metadata:
  name: security-config
  namespace: dl-engine
  labels:
    app.kubernetes.io/name: dl-engine
    app.kubernetes.io/component: security
data:
  # 安全策略配置
  security-policy.yaml: |
    # 密码策略
    password:
      minLength: 8
      requireUppercase: true
      requireLowercase: true
      requireNumbers: true
      requireSpecialChars: true
      maxAge: 90 # 天
      historyCount: 5
      lockoutThreshold: 5
      lockoutDuration: 30 # 分钟
    
    # 会话策略
    session:
      timeout: 3600 # 秒
      maxConcurrent: 3
      requireReauth: true
      secureOnly: true
      sameSite: "strict"
    
    # API安全
    api:
      rateLimit:
        window: 900 # 15分钟
        maxRequests: 1000
        burstLimit: 100
      cors:
        allowedOrigins:
          - "https://dl-engine.org"
          - "https://*.dl-engine.org"
        allowedMethods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
        allowedHeaders: ["Authorization", "Content-Type", "X-Requested-With"]
        maxAge: 86400
      
    # 文件上传安全
    upload:
      maxSize: 104857600 # 100MB
      allowedTypes:
        - "image/jpeg"
        - "image/png"
        - "image/gif"
        - "image/webp"
        - "video/mp4"
        - "video/webm"
        - "audio/mp3"
        - "audio/wav"
        - "model/gltf+json"
        - "model/gltf-binary"
        - "application/pdf"
      scanVirus: true
      quarantineSuspicious: true
    
    # 数据加密
    encryption:
      algorithm: "AES-256-GCM"
      keyRotation: 30 # 天
      saltRounds: 12
      
    # 审计日志
    audit:
      enabled: true
      events:
        - "login"
        - "logout"
        - "password_change"
        - "permission_change"
        - "data_access"
        - "data_modification"
        - "file_upload"
        - "admin_action"
      retention: 365 # 天

  # 网络安全配置
  network-security.conf: |
    # Nginx安全配置
    
    # 隐藏服务器信息
    server_tokens off;
    more_set_headers "Server: DL-Engine";
    
    # 安全头部
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Permissions-Policy "geolocation=(), microphone=(), camera=()" always;
    
    # HSTS
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
    
    # CSP
    add_header Content-Security-Policy "
      default-src 'self';
      script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net;
      style-src 'self' 'unsafe-inline' https://fonts.googleapis.com;
      font-src 'self' https://fonts.gstatic.com;
      img-src 'self' data: https:;
      media-src 'self' blob:;
      connect-src 'self' wss: https:;
      worker-src 'self' blob:;
      frame-src 'none';
      object-src 'none';
      base-uri 'self';
      form-action 'self';
    " always;
    
    # 限制请求大小
    client_max_body_size 100M;
    client_body_buffer_size 128k;
    client_header_buffer_size 1k;
    large_client_header_buffers 4 4k;
    
    # 超时设置
    client_body_timeout 12;
    client_header_timeout 12;
    keepalive_timeout 15;
    send_timeout 10;
    
    # 限制连接
    limit_conn_zone $binary_remote_addr zone=conn_limit_per_ip:10m;
    limit_req_zone $binary_remote_addr zone=req_limit_per_ip:10m rate=10r/s;
    
    # 应用限制
    limit_conn conn_limit_per_ip 20;
    limit_req zone=req_limit_per_ip burst=20 nodelay;
    
    # 禁止访问敏感文件
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    location ~ ~$ {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # 防止SQL注入和XSS
    if ($args ~* "(\<|%3C).*script.*(\>|%3E)") { return 403; }
    if ($args ~* "GLOBALS(=|\[|\%[0-9A-Z]{0,2})") { return 403; }
    if ($args ~* "_REQUEST(=|\[|\%[0-9A-Z]{0,2})") { return 403; }
    if ($args ~* "proc/self/environ") { return 403; }
    if ($args ~* "mosConfig_[a-zA-Z_]{1,21}(=|\%3D)") { return 403; }
    if ($args ~* "base64_(en|de)code\(.*\)") { return 403; }
    if ($args ~* "(<|%3C)([^s]*s)+cript.*(>|%3E)") { return 403; }
    if ($args ~* "(<|%3C)([^e]*e)+mbed.*(>|%3E)") { return 403; }
    if ($args ~* "(<|%3C)([^o]*o)+bject.*(>|%3E)") { return 403; }
    if ($args ~* "(<|%3C)([^i]*i)+frame.*(>|%3E)") { return 403; }

---
# Pod安全策略
apiVersion: policy/v1beta1
kind: PodSecurityPolicy
metadata:
  name: dl-engine-psp
  namespace: dl-engine
spec:
  privileged: false
  allowPrivilegeEscalation: false
  requiredDropCapabilities:
    - ALL
  volumes:
    - 'configMap'
    - 'emptyDir'
    - 'projected'
    - 'secret'
    - 'downwardAPI'
    - 'persistentVolumeClaim'
  runAsUser:
    rule: 'MustRunAsNonRoot'
  seLinux:
    rule: 'RunAsAny'
  fsGroup:
    rule: 'RunAsAny'

---
# 网络策略
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: dl-engine-network-policy
  namespace: dl-engine
spec:
  podSelector:
    matchLabels:
      app.kubernetes.io/name: dl-engine
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: dl-engine-edge
    - namespaceSelector:
        matchLabels:
          name: dl-engine-monitoring
    ports:
    - protocol: TCP
      port: 3030
    - protocol: TCP
      port: 3031
    - protocol: TCP
      port: 3032
    - protocol: TCP
      port: 8080
  egress:
  - to:
    - namespaceSelector:
        matchLabels:
          name: kube-system
    ports:
    - protocol: TCP
      port: 53
    - protocol: UDP
      port: 53
  - to:
    - namespaceSelector:
        matchLabels:
          name: dl-engine-data
    ports:
    - protocol: TCP
      port: 3306
    - protocol: TCP
      port: 6379
    - protocol: TCP
      port: 5432

---
# RBAC配置
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: dl-engine
  name: dl-engine-role
rules:
- apiGroups: [""]
  resources: ["pods", "services", "configmaps", "secrets"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["apps"]
  resources: ["deployments", "replicasets"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["agones.dev"]
  resources: ["gameservers", "fleets"]
  verbs: ["get", "list", "watch", "create", "update", "patch"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: dl-engine-rolebinding
  namespace: dl-engine
subjects:
- kind: ServiceAccount
  name: dl-engine
  namespace: dl-engine
roleRef:
  kind: Role
  name: dl-engine-role
  apiGroup: rbac.authorization.k8s.io

---
# 安全扫描作业
apiVersion: batch/v1
kind: CronJob
metadata:
  name: security-scan
  namespace: dl-engine
spec:
  schedule: "0 2 * * *" # 每天凌晨2点执行
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: security-scanner
            image: aquasec/trivy:latest
            command:
            - /bin/sh
            - -c
            - |
              echo "开始安全扫描..."
              
              # 扫描容器镜像
              trivy image --exit-code 1 --severity HIGH,CRITICAL dl-engine/gateway:latest
              trivy image --exit-code 1 --severity HIGH,CRITICAL dl-engine/auth:latest
              trivy image --exit-code 1 --severity HIGH,CRITICAL dl-engine/api:latest
              trivy image --exit-code 1 --severity HIGH,CRITICAL dl-engine/instance:latest
              
              # 扫描Kubernetes配置
              trivy k8s --report summary cluster
              
              echo "安全扫描完成"
            env:
            - name: TRIVY_DB_REPOSITORY
              value: "ghcr.io/aquasecurity/trivy-db"
            volumeMounts:
            - name: docker-socket
              mountPath: /var/run/docker.sock
          volumes:
          - name: docker-socket
            hostPath:
              path: /var/run/docker.sock
          restartPolicy: OnFailure

---
# 密钥轮换作业
apiVersion: batch/v1
kind: CronJob
metadata:
  name: key-rotation
  namespace: dl-engine
spec:
  schedule: "0 3 1 * *" # 每月1号凌晨3点执行
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: key-rotator
            image: dl-engine/security-tools:latest
            command:
            - /bin/sh
            - -c
            - |
              echo "开始密钥轮换..."
              
              # 生成新的JWT密钥
              NEW_JWT_SECRET=$(openssl rand -base64 64)
              
              # 更新Kubernetes Secret
              kubectl patch secret dl-engine-secret -p='{"data":{"JWT_SECRET":"'$(echo -n $NEW_JWT_SECRET | base64)'"}}'
              
              # 生成新的加密密钥
              NEW_ENCRYPTION_KEY=$(openssl rand -base64 32)
              kubectl patch secret dl-engine-secret -p='{"data":{"ENCRYPTION_KEY":"'$(echo -n $NEW_ENCRYPTION_KEY | base64)'"}}'
              
              # 重启相关服务以应用新密钥
              kubectl rollout restart deployment/dl-engine-gateway
              kubectl rollout restart deployment/dl-engine-auth
              kubectl rollout restart deployment/dl-engine-api
              
              echo "密钥轮换完成"
            env:
            - name: KUBECONFIG
              value: /etc/kubeconfig/config
            volumeMounts:
            - name: kubeconfig
              mountPath: /etc/kubeconfig
          volumes:
          - name: kubeconfig
            secret:
              secretName: kubeconfig
          restartPolicy: OnFailure
          serviceAccountName: dl-engine-admin

---
# 入侵检测系统配置
apiVersion: v1
kind: ConfigMap
metadata:
  name: ids-config
  namespace: dl-engine
data:
  falco.yaml: |
    rules_file:
      - /etc/falco/falco_rules.yaml
      - /etc/falco/falco_rules.local.yaml
      - /etc/falco/k8s_audit_rules.yaml
      - /etc/falco/rules.d
    
    time_format_iso_8601: true
    json_output: true
    json_include_output_property: true
    
    log_stderr: true
    log_syslog: true
    log_level: info
    
    priority: debug
    
    buffered_outputs: false
    
    outputs:
      rate: 1
      max_burst: 1000
    
    syslog_output:
      enabled: true
    
    file_output:
      enabled: true
      keep_alive: false
      filename: /var/log/falco.log
    
    stdout_output:
      enabled: true
    
    webserver:
      enabled: true
      listen_port: 8765
      k8s_healthz_endpoint: /healthz
      ssl_enabled: false
      ssl_certificate: /etc/ssl/falco/falco.pem
    
    grpc:
      enabled: false
      bind_address: "0.0.0.0:5060"
      threadiness: 8
    
    grpc_output:
      enabled: false

  custom_rules.yaml: |
    # DL-Engine 自定义安全规则
    
    # 检测异常网络连接
    - rule: Unexpected outbound connection
      desc: Detect unexpected outbound connections from DL-Engine pods
      condition: >
        outbound and
        container.image.repository contains "dl-engine" and
        not fd.sport in (80, 443, 3306, 6379, 5432, 9200, 11434)
      output: >
        Unexpected outbound connection from DL-Engine
        (command=%proc.cmdline connection=%fd.name user=%user.name container=%container.name)
      priority: WARNING
    
    # 检测特权容器
    - rule: Privileged container started
      desc: Detect privileged container in DL-Engine namespace
      condition: >
        spawned_process and
        k8s.ns.name = "dl-engine" and
        container.privileged = true
      output: >
        Privileged container started in DL-Engine namespace
        (user=%user.name command=%proc.cmdline container=%container.name)
      priority: CRITICAL
    
    # 检测敏感文件访问
    - rule: Sensitive file access
      desc: Detect access to sensitive files
      condition: >
        open_read and
        container.image.repository contains "dl-engine" and
        (fd.name startswith "/etc/passwd" or
         fd.name startswith "/etc/shadow" or
         fd.name startswith "/etc/ssh/" or
         fd.name contains "id_rsa" or
         fd.name contains "id_dsa")
      output: >
        Sensitive file accessed in DL-Engine container
        (file=%fd.name command=%proc.cmdline container=%container.name)
      priority: HIGH
    
    # 检测异常进程执行
    - rule: Unexpected process in container
      desc: Detect unexpected process execution in DL-Engine containers
      condition: >
        spawned_process and
        container.image.repository contains "dl-engine" and
        not proc.name in (node, npm, nginx, mysql, redis-server, postgres)
      output: >
        Unexpected process in DL-Engine container
        (command=%proc.cmdline container=%container.name user=%user.name)
      priority: WARNING

---
# Falco部署
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: falco
  namespace: dl-engine
spec:
  selector:
    matchLabels:
      app: falco
  template:
    metadata:
      labels:
        app: falco
    spec:
      serviceAccount: falco
      hostNetwork: true
      hostPID: true
      containers:
      - name: falco
        image: falcosecurity/falco:latest
        securityContext:
          privileged: true
        args:
          - /usr/bin/falco
          - --cri=/run/containerd/containerd.sock
          - --k8s-api=https://kubernetes.default:443
          - --k8s-api-cert=/var/run/secrets/kubernetes.io/serviceaccount/ca.crt
          - --k8s-api-token=/var/run/secrets/kubernetes.io/serviceaccount/token
        env:
        - name: FALCO_K8S_NODE_NAME
          valueFrom:
            fieldRef:
              fieldPath: spec.nodeName
        volumeMounts:
        - mountPath: /host/var/run/docker.sock
          name: docker-socket
        - mountPath: /host/run/containerd/containerd.sock
          name: containerd-socket
        - mountPath: /host/dev
          name: dev-fs
        - mountPath: /host/proc
          name: proc-fs
          readOnly: true
        - mountPath: /host/boot
          name: boot-fs
          readOnly: true
        - mountPath: /host/lib/modules
          name: lib-modules
          readOnly: true
        - mountPath: /host/usr
          name: usr-fs
          readOnly: true
        - mountPath: /etc/falco
          name: falco-config
      volumes:
      - name: docker-socket
        hostPath:
          path: /var/run/docker.sock
      - name: containerd-socket
        hostPath:
          path: /run/containerd/containerd.sock
      - name: dev-fs
        hostPath:
          path: /dev
      - name: proc-fs
        hostPath:
          path: /proc
      - name: boot-fs
        hostPath:
          path: /boot
      - name: lib-modules
        hostPath:
          path: /lib/modules
      - name: usr-fs
        hostPath:
          path: /usr
      - name: falco-config
        configMap:
          name: ids-config
