import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { LiveStreamService } from './live-stream.service';
import { VodService } from './vod.service';
import { RTMPService } from './rtmp.service';
import { TranscodingService } from './transcoding.service';
import { RecordingService } from './recording.service';
import { StreamAnalyticsService } from './stream-analytics.service';

export interface StreamConfig {
  id: string;
  name: string;
  type: 'live' | 'vod';
  status: 'inactive' | 'starting' | 'active' | 'stopping' | 'error';
  input: {
    type: 'rtmp' | 'webrtc' | 'file' | 'url';
    url: string;
    protocol?: string;
    codec?: string;
    bitrate?: number;
    resolution?: string;
    framerate?: number;
  };
  outputs: Array<{
    id: string;
    name: string;
    type: 'hls' | 'dash' | 'rtmp' | 'webrtc';
    url: string;
    quality: string;
    bitrate: number;
    resolution: string;
    enabled: boolean;
  }>;
  recording?: {
    enabled: boolean;
    format: 'mp4' | 'flv' | 'ts';
    segmentDuration: number;
    maxDuration: number;
    storage: {
      type: 'local' | 'minio' | 's3';
      bucket?: string;
      path: string;
    };
  };
  transcoding?: {
    enabled: boolean;
    profiles: Array<{
      name: string;
      codec: string;
      bitrate: number;
      resolution: string;
      framerate: number;
    }>;
  };
  security?: {
    authentication: boolean;
    encryption: boolean;
    allowedOrigins: string[];
    tokenExpiry: number;
  };
  analytics?: {
    enabled: boolean;
    metrics: string[];
    retention: number;
  };
  createdAt: Date;
  updatedAt: Date;
  metadata?: Record<string, any>;
}

export interface StreamStats {
  streamId: string;
  status: string;
  viewers: {
    current: number;
    peak: number;
    total: number;
  };
  bandwidth: {
    input: number;
    output: number;
    total: number;
  };
  quality: {
    bitrate: number;
    framerate: number;
    resolution: string;
    dropRate: number;
  };
  duration: number;
  startTime: Date;
  lastUpdate: Date;
}

@Injectable()
export class StreamingService {
  private readonly logger = new Logger(StreamingService.name);
  private readonly streams = new Map<string, StreamConfig>();

  constructor(
    private configService: ConfigService,
    private liveStreamService: LiveStreamService,
    private vodService: VodService,
    private rtmpService: RTMPService,
    private transcodingService: TranscodingService,
    private recordingService: RecordingService,
    private streamAnalyticsService: StreamAnalyticsService,
  ) {}

  /**
   * 创建流配置
   */
  async createStream(config: Omit<StreamConfig, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    const streamId = this.generateStreamId();
    const streamConfig: StreamConfig = {
      id: streamId,
      ...config,
      status: 'inactive',
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    this.streams.set(streamId, streamConfig);

    // 根据类型初始化相应服务
    if (config.type === 'live') {
      await this.liveStreamService.initializeStream(streamConfig);
    } else {
      await this.vodService.initializeStream(streamConfig);
    }

    this.logger.log(`Stream created: ${streamId} (${config.type})`);
    return streamId;
  }

  /**
   * 启动流
   */
  async startStream(streamId: string): Promise<void> {
    const stream = this.streams.get(streamId);
    if (!stream) {
      throw new Error(`Stream not found: ${streamId}`);
    }

    try {
      stream.status = 'starting';
      stream.updatedAt = new Date();

      if (stream.type === 'live') {
        await this.liveStreamService.startStream(streamId);
      } else {
        await this.vodService.startStream(streamId);
      }

      // 启动转码（如果启用）
      if (stream.transcoding?.enabled) {
        await this.transcodingService.startTranscoding(streamId, stream.transcoding.profiles);
      }

      // 启动录制（如果启用）
      if (stream.recording?.enabled) {
        await this.recordingService.startRecording(streamId, stream.recording);
      }

      // 启动分析（如果启用）
      if (stream.analytics?.enabled) {
        await this.streamAnalyticsService.startAnalytics(streamId, stream.analytics);
      }

      stream.status = 'active';
      this.logger.log(`Stream started: ${streamId}`);
    } catch (error) {
      stream.status = 'error';
      this.logger.error(`Failed to start stream ${streamId}:`, error);
      throw error;
    }
  }

  /**
   * 停止流
   */
  async stopStream(streamId: string): Promise<void> {
    const stream = this.streams.get(streamId);
    if (!stream) {
      throw new Error(`Stream not found: ${streamId}`);
    }

    try {
      stream.status = 'stopping';
      stream.updatedAt = new Date();

      // 停止分析
      if (stream.analytics?.enabled) {
        await this.streamAnalyticsService.stopAnalytics(streamId);
      }

      // 停止录制
      if (stream.recording?.enabled) {
        await this.recordingService.stopRecording(streamId);
      }

      // 停止转码
      if (stream.transcoding?.enabled) {
        await this.transcodingService.stopTranscoding(streamId);
      }

      // 停止流
      if (stream.type === 'live') {
        await this.liveStreamService.stopStream(streamId);
      } else {
        await this.vodService.stopStream(streamId);
      }

      stream.status = 'inactive';
      this.logger.log(`Stream stopped: ${streamId}`);
    } catch (error) {
      stream.status = 'error';
      this.logger.error(`Failed to stop stream ${streamId}:`, error);
      throw error;
    }
  }

  /**
   * 获取流配置
   */
  getStream(streamId: string): StreamConfig | null {
    return this.streams.get(streamId) || null;
  }

  /**
   * 获取所有流
   */
  getStreams(filter?: {
    type?: 'live' | 'vod';
    status?: string;
    limit?: number;
    offset?: number;
  }): StreamConfig[] {
    let streams = Array.from(this.streams.values());

    if (filter?.type) {
      streams = streams.filter(stream => stream.type === filter.type);
    }

    if (filter?.status) {
      streams = streams.filter(stream => stream.status === filter.status);
    }

    // 排序
    streams.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());

    // 分页
    if (filter?.offset) {
      streams = streams.slice(filter.offset);
    }
    if (filter?.limit) {
      streams = streams.slice(0, filter.limit);
    }

    return streams;
  }

  /**
   * 更新流配置
   */
  async updateStream(streamId: string, updates: Partial<StreamConfig>): Promise<void> {
    const stream = this.streams.get(streamId);
    if (!stream) {
      throw new Error(`Stream not found: ${streamId}`);
    }

    // 更新配置
    Object.assign(stream, updates, { updatedAt: new Date() });

    // 如果流正在运行，需要重新配置
    if (stream.status === 'active') {
      await this.reconfigureStream(streamId);
    }

    this.logger.log(`Stream updated: ${streamId}`);
  }

  /**
   * 删除流
   */
  async deleteStream(streamId: string): Promise<void> {
    const stream = this.streams.get(streamId);
    if (!stream) {
      throw new Error(`Stream not found: ${streamId}`);
    }

    // 如果流正在运行，先停止
    if (stream.status === 'active') {
      await this.stopStream(streamId);
    }

    // 清理资源
    if (stream.type === 'live') {
      await this.liveStreamService.cleanupStream(streamId);
    } else {
      await this.vodService.cleanupStream(streamId);
    }

    this.streams.delete(streamId);
    this.logger.log(`Stream deleted: ${streamId}`);
  }

  /**
   * 获取流统计信息
   */
  async getStreamStats(streamId: string): Promise<StreamStats | null> {
    const stream = this.streams.get(streamId);
    if (!stream) {
      return null;
    }

    if (stream.type === 'live') {
      return await this.liveStreamService.getStreamStats(streamId);
    } else {
      return await this.vodService.getStreamStats(streamId);
    }
  }

  /**
   * 获取流播放URL
   */
  async getPlaybackUrls(streamId: string): Promise<{
    hls?: string;
    dash?: string;
    rtmp?: string;
    webrtc?: string;
  }> {
    const stream = this.streams.get(streamId);
    if (!stream) {
      throw new Error(`Stream not found: ${streamId}`);
    }

    const urls: any = {};

    for (const output of stream.outputs) {
      if (output.enabled) {
        urls[output.type] = output.url;
      }
    }

    return urls;
  }

  /**
   * 生成流访问令牌
   */
  async generateStreamToken(
    streamId: string,
    options: {
      expiresIn?: number;
      permissions?: string[];
      metadata?: Record<string, any>;
    } = {}
  ): Promise<string> {
    const stream = this.streams.get(streamId);
    if (!stream) {
      throw new Error(`Stream not found: ${streamId}`);
    }

    const token = this.generateToken();
    const expiresAt = new Date(Date.now() + (options.expiresIn || 3600) * 1000);

    // 存储令牌信息到Redis
    const tokenData = {
      streamId,
      permissions: options.permissions || ['view'],
      metadata: options.metadata,
      expiresAt,
    };

    // 这里应该存储到Redis
    // await this.redisService.setex(`stream_token:${token}`, options.expiresIn || 3600, JSON.stringify(tokenData));

    this.logger.debug(`Stream token generated for ${streamId}: ${token}`);
    return token;
  }

  /**
   * 验证流访问令牌
   */
  async validateStreamToken(token: string): Promise<{
    valid: boolean;
    streamId?: string;
    permissions?: string[];
    metadata?: Record<string, any>;
  }> {
    try {
      // 从Redis获取令牌信息
      // const tokenData = await this.redisService.get(`stream_token:${token}`);
      // if (!tokenData) {
      //   return { valid: false };
      // }

      // const data = JSON.parse(tokenData);
      // return {
      //   valid: true,
      //   streamId: data.streamId,
      //   permissions: data.permissions,
      //   metadata: data.metadata,
      // };

      // 临时实现
      return { valid: true };
    } catch (error) {
      this.logger.error('Failed to validate stream token:', error);
      return { valid: false };
    }
  }

  /**
   * 获取流媒体服务统计
   */
  async getServiceStats(): Promise<{
    totalStreams: number;
    activeStreams: number;
    liveStreams: number;
    vodStreams: number;
    totalViewers: number;
    totalBandwidth: number;
    streamsByStatus: Record<string, number>;
  }> {
    const streams = Array.from(this.streams.values());
    
    const stats = {
      totalStreams: streams.length,
      activeStreams: streams.filter(s => s.status === 'active').length,
      liveStreams: streams.filter(s => s.type === 'live').length,
      vodStreams: streams.filter(s => s.type === 'vod').length,
      totalViewers: 0,
      totalBandwidth: 0,
      streamsByStatus: {} as Record<string, number>,
    };

    // 统计状态分布
    for (const stream of streams) {
      stats.streamsByStatus[stream.status] = (stats.streamsByStatus[stream.status] || 0) + 1;
    }

    // 获取实时统计
    for (const stream of streams) {
      if (stream.status === 'active') {
        try {
          const streamStats = await this.getStreamStats(stream.id);
          if (streamStats) {
            stats.totalViewers += streamStats.viewers.current;
            stats.totalBandwidth += streamStats.bandwidth.total;
          }
        } catch (error) {
          this.logger.warn(`Failed to get stats for stream ${stream.id}:`, error);
        }
      }
    }

    return stats;
  }

  private async reconfigureStream(streamId: string): Promise<void> {
    // 重新配置运行中的流
    this.logger.debug(`Reconfiguring stream: ${streamId}`);
    
    // 这里可以实现热重载配置的逻辑
    // 例如更新转码参数、输出配置等
  }

  private generateStreamId(): string {
    return `stream_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateToken(): string {
    return `token_${Date.now()}_${Math.random().toString(36).substr(2, 16)}`;
  }
}
