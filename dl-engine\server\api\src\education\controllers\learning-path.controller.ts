import { Controller, Get, Post, Put, Delete, Body, Param, Query, UseGuards, Request } from '@nestjs/common'
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger'
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard'
import { RolesGuard } from '../../auth/guards/roles.guard'
import { Roles } from '../../auth/decorators/roles.decorator'
import { LearningPathService } from '../services/learning-path.service'
import { CreateLearningPathDto, UpdateLearningPathDto, LearningPathQueryDto } from '../dto/learning-path.dto'

@ApiTags('教育-学习路径')
@Controller('education/learning-paths')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class LearningPathController {
  constructor(private readonly learningPathService: LearningPathService) {}

  @Post()
  @Roles('teacher', 'admin')
  @ApiOperation({ summary: '创建学习路径' })
  @ApiResponse({ status: 201, description: '学习路径创建成功' })
  async create(@Body() createLearningPathDto: CreateLearningPathDto, @Request() req) {
    return this.learningPathService.create(createLearningPathDto, req.user.id)
  }

  @Get()
  @ApiOperation({ summary: '获取学习路径列表' })
  @ApiResponse({ status: 200, description: '学习路径列表获取成功' })
  async findAll(@Query() query: LearningPathQueryDto) {
    return this.learningPathService.findAll(query)
  }

  @Get('recommended')
  @Roles('student')
  @ApiOperation({ summary: '获取推荐学习路径' })
  @ApiResponse({ status: 200, description: '推荐学习路径获取成功' })
  async getRecommended(@Request() req) {
    return this.learningPathService.getRecommended(req.user.id)
  }

  @Get(':id')
  @ApiOperation({ summary: '获取学习路径详情' })
  @ApiResponse({ status: 200, description: '学习路径详情获取成功' })
  async findOne(@Param('id') id: string) {
    return this.learningPathService.findOne(id)
  }

  @Put(':id')
  @Roles('teacher', 'admin')
  @ApiOperation({ summary: '更新学习路径' })
  @ApiResponse({ status: 200, description: '学习路径更新成功' })
  async update(@Param('id') id: string, @Body() updateLearningPathDto: UpdateLearningPathDto, @Request() req) {
    return this.learningPathService.update(id, updateLearningPathDto, req.user.id)
  }

  @Delete(':id')
  @Roles('teacher', 'admin')
  @ApiOperation({ summary: '删除学习路径' })
  @ApiResponse({ status: 200, description: '学习路径删除成功' })
  async remove(@Param('id') id: string, @Request() req) {
    return this.learningPathService.remove(id, req.user.id)
  }

  @Post(':id/enroll')
  @Roles('student')
  @ApiOperation({ summary: '加入学习路径' })
  @ApiResponse({ status: 200, description: '学习路径加入成功' })
  async enroll(@Param('id') id: string, @Request() req) {
    return this.learningPathService.enroll(id, req.user.id)
  }

  @Get(':id/progress')
  @ApiOperation({ summary: '获取学习路径进度' })
  @ApiResponse({ status: 200, description: '学习进度获取成功' })
  async getProgress(@Param('id') id: string, @Request() req) {
    return this.learningPathService.getProgress(id, req.user.id)
  }

  @Post(':id/complete')
  @Roles('student')
  @ApiOperation({ summary: '完成学习路径' })
  @ApiResponse({ status: 200, description: '学习路径完成成功' })
  async complete(@Param('id') id: string, @Request() req) {
    return this.learningPathService.complete(id, req.user.id)
  }
}
