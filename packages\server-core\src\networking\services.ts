/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and 
provide for limited attribution for the Original Developer. In addition, 
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Infinite Reality Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Infinite Reality Engine team.

All portions of the code written by the Infinite Reality Engine team are Copyright © 2021-2025
Infinite Reality Engine. All Rights Reserved.
*/

import AllowedDomains from './allowed-domains/allowed-domains'
import InstanceAttendance from './instance-attendance/instance-attendance'
import InstanceAuthorizedUser from './instance-authorized-user/instance-authorized-user'
import InstanceProvision from './instance-provision/instance-provision'
import InstanceSignaling from './instance-signaling/instance-signaling'
import Instance from './instance/instance'
import InstanceServerLoad from './instanceserver-load/instanceserver-load.service'
import InstanceServerProvision from './instanceserver-provision/instanceserver-provision.service'

// DL-Engine 实例服务导入
import InstanceManager from '../../../dl-engine/server/instance/world/instance-manager'
import NetworkSync from '../../../dl-engine/server/instance/world/network-sync'
import PhysicsSync from '../../../dl-engine/server/instance/world/physics-sync'
import ScalingManager from '../../../dl-engine/server/instance/world/scaling-manager'

export default [
  AllowedDomains,
  Instance,
  InstanceServerLoad,
  InstanceServerProvision,
  InstanceProvision,
  InstanceAttendance,
  InstanceSignaling,
  InstanceAuthorizedUser,
  // DL-Engine 新增实例服务
  InstanceManager,
  NetworkSync,
  PhysicsSync,
  ScalingManager
]
