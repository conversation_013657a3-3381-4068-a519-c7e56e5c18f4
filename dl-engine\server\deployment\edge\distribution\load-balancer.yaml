# DL-Engine 智能负载均衡配置
# 支持地理位置感知和故障转移的负载均衡

apiVersion: v1
kind: ConfigMap
metadata:
  name: load-balancer-config
  namespace: dl-engine-edge
  labels:
    app.kubernetes.io/name: dl-engine
    app.kubernetes.io/component: load-balancer
data:
  # HAProxy配置
  haproxy.cfg: |
    global
        daemon
        log stdout local0 info
        chroot /var/lib/haproxy
        stats socket /run/haproxy/admin.sock mode 660 level admin
        stats timeout 30s
        user haproxy
        group haproxy
        
        # SSL配置
        ssl-default-bind-ciphers ECDHE+AESGCM:ECDHE+CHACHA20:RSA+AESGCM:RSA+AES:!aNULL:!MD5:!DSS
        ssl-default-bind-options ssl-min-ver TLSv1.2 no-tls-tickets
        
        # 性能调优
        tune.ssl.default-dh-param 2048
        tune.bufsize 32768
        tune.maxrewrite 1024
    
    defaults
        mode http
        log global
        option httplog
        option dontlognull
        option log-health-checks
        option forwardfor
        option http-server-close
        option redispatch
        retries 3
        timeout connect 5000
        timeout client 50000
        timeout server 50000
        timeout http-request 10s
        timeout http-keep-alive 2s
        timeout check 10s
        
        # 错误页面
        errorfile 400 /etc/haproxy/errors/400.http
        errorfile 403 /etc/haproxy/errors/403.http
        errorfile 408 /etc/haproxy/errors/408.http
        errorfile 500 /etc/haproxy/errors/500.http
        errorfile 502 /etc/haproxy/errors/502.http
        errorfile 503 /etc/haproxy/errors/503.http
        errorfile 504 /etc/haproxy/errors/504.http
    
    # 统计页面
    frontend stats
        bind *:8404
        stats enable
        stats uri /stats
        stats refresh 30s
        stats admin if TRUE
        stats auth admin:dl-engine-stats-password
    
    # 主前端
    frontend dl_engine_frontend
        bind *:80
        bind *:443 ssl crt /etc/ssl/certs/dl-engine.pem
        
        # 重定向HTTP到HTTPS
        redirect scheme https if !{ ssl_fc }
        
        # 健康检查
        monitor-uri /health
        
        # 地理位置检测
        acl is_asia hdr_sub(CF-IPCountry) CN JP KR SG TH VN MY ID PH TW HK
        acl is_europe hdr_sub(CF-IPCountry) GB DE FR IT ES NL BE CH AT SE NO DK FI
        acl is_america hdr_sub(CF-IPCountry) US CA MX BR AR CL PE CO VE
        
        # 路由规则
        use_backend asia_backend if is_asia
        use_backend europe_backend if is_europe
        use_backend america_backend if is_america
        default_backend global_backend
        
        # API路由
        acl is_api path_beg /api/
        use_backend api_backend if is_api
        
        # WebSocket路由
        acl is_websocket hdr(Upgrade) -i websocket
        use_backend websocket_backend if is_websocket
        
        # 静态资源路由
        acl is_static path_end .js .css .png .jpg .jpeg .gif .ico .svg .woff .woff2 .ttf .eot
        use_backend static_backend if is_static
        
        # 3D模型文件路由
        acl is_3d_model path_end .gltf .glb .fbx .obj .dae .3ds .blend
        use_backend model_backend if is_3d_model
    
    # 亚洲区域后端
    backend asia_backend
        balance roundrobin
        option httpchk GET /health
        http-check expect status 200
        
        server asia-edge-1 dl-engine-edge-asia-1.dl-engine-edge.svc.cluster.local:80 check inter 10s fall 3 rise 2
        server asia-edge-2 dl-engine-edge-asia-2.dl-engine-edge.svc.cluster.local:80 check inter 10s fall 3 rise 2
        server asia-edge-3 dl-engine-edge-asia-3.dl-engine-edge.svc.cluster.local:80 check inter 10s fall 3 rise 2 backup
    
    # 欧洲区域后端
    backend europe_backend
        balance roundrobin
        option httpchk GET /health
        http-check expect status 200
        
        server europe-edge-1 dl-engine-edge-europe-1.dl-engine-edge.svc.cluster.local:80 check inter 10s fall 3 rise 2
        server europe-edge-2 dl-engine-edge-europe-2.dl-engine-edge.svc.cluster.local:80 check inter 10s fall 3 rise 2
        server europe-edge-3 dl-engine-edge-europe-3.dl-engine-edge.svc.cluster.local:80 check inter 10s fall 3 rise 2 backup
    
    # 美洲区域后端
    backend america_backend
        balance roundrobin
        option httpchk GET /health
        http-check expect status 200
        
        server america-edge-1 dl-engine-edge-america-1.dl-engine-edge.svc.cluster.local:80 check inter 10s fall 3 rise 2
        server america-edge-2 dl-engine-edge-america-2.dl-engine-edge.svc.cluster.local:80 check inter 10s fall 3 rise 2
        server america-edge-3 dl-engine-edge-america-3.dl-engine-edge.svc.cluster.local:80 check inter 10s fall 3 rise 2 backup
    
    # 全球后端（备用）
    backend global_backend
        balance roundrobin
        option httpchk GET /health
        http-check expect status 200
        
        server global-edge-1 dl-engine-edge-service.dl-engine-edge.svc.cluster.local:80 check inter 10s fall 3 rise 2
        server global-edge-2 dl-engine-gateway.dl-engine.svc.cluster.local:3030 check inter 10s fall 3 rise 2 backup
    
    # API后端
    backend api_backend
        balance leastconn
        option httpchk GET /api/health
        http-check expect status 200
        
        server api-1 dl-engine-gateway.dl-engine.svc.cluster.local:3030 check inter 5s fall 2 rise 2
        server api-2 dl-engine-api.dl-engine.svc.cluster.local:3032 check inter 5s fall 2 rise 2
    
    # WebSocket后端
    backend websocket_backend
        balance source
        option httpchk GET /health
        http-check expect status 200
        
        server ws-1 dl-engine-instance.dl-engine.svc.cluster.local:8080 check inter 10s fall 3 rise 2
        server ws-2 dl-engine-instance.dl-engine.svc.cluster.local:8080 check inter 10s fall 3 rise 2
    
    # 静态资源后端
    backend static_backend
        balance roundrobin
        option httpchk GET /health
        http-check expect status 200
        
        # 启用缓存
        http-response set-header Cache-Control "public, max-age=3600"
        
        server static-1 dl-engine-edge-service.dl-engine-edge.svc.cluster.local:80 check inter 30s fall 3 rise 2
        server static-2 dl-engine-client.dl-engine.svc.cluster.local:3000 check inter 30s fall 3 rise 2 backup
    
    # 3D模型后端
    backend model_backend
        balance roundrobin
        option httpchk GET /health
        http-check expect status 200
        
        # 启用长期缓存
        http-response set-header Cache-Control "public, max-age=86400"
        
        server model-1 dl-engine-media.dl-engine.svc.cluster.local:3034 check inter 30s fall 3 rise 2
        server model-2 dl-engine-edge-service.dl-engine-edge.svc.cluster.local:80 check inter 30s fall 3 rise 2

  # 错误页面
  400.http: |
    HTTP/1.0 400 Bad request
    Cache-Control: no-cache
    Connection: close
    Content-Type: text/html
    
    <html><body><h1>400 Bad request</h1>
    Your browser sent an invalid request.
    </body></html>

  503.http: |
    HTTP/1.0 503 Service Unavailable
    Cache-Control: no-cache
    Connection: close
    Content-Type: text/html
    
    <html><body><h1>503 Service Unavailable</h1>
    No server is available to handle this request.
    </body></html>

---
# 负载均衡器部署
apiVersion: apps/v1
kind: Deployment
metadata:
  name: dl-engine-load-balancer
  namespace: dl-engine-edge
  labels:
    app.kubernetes.io/name: dl-engine
    app.kubernetes.io/component: load-balancer
    app.kubernetes.io/version: "1.0.0"
spec:
  replicas: 3
  selector:
    matchLabels:
      app.kubernetes.io/name: dl-engine
      app.kubernetes.io/component: load-balancer
  template:
    metadata:
      labels:
        app.kubernetes.io/name: dl-engine
        app.kubernetes.io/component: load-balancer
        app.kubernetes.io/version: "1.0.0"
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8404"
        prometheus.io/path: "/stats;csv"
    spec:
      containers:
        - name: haproxy
          image: haproxy:2.6-alpine
          ports:
            - containerPort: 80
              name: http
              protocol: TCP
            - containerPort: 443
              name: https
              protocol: TCP
            - containerPort: 8404
              name: stats
              protocol: TCP
          env:
            - name: HAPROXY_USER
              value: "haproxy"
            - name: HAPROXY_GROUP
              value: "haproxy"
          volumeMounts:
            - name: haproxy-config
              mountPath: /usr/local/etc/haproxy/haproxy.cfg
              subPath: haproxy.cfg
            - name: ssl-certs
              mountPath: /etc/ssl/certs
              readOnly: true
            - name: error-pages
              mountPath: /etc/haproxy/errors
          resources:
            limits:
              cpu: 2000m
              memory: 2Gi
            requests:
              cpu: 1000m
              memory: 1Gi
          livenessProbe:
            httpGet:
              path: /health
              port: 80
            initialDelaySeconds: 10
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3
          readinessProbe:
            httpGet:
              path: /health
              port: 80
            initialDelaySeconds: 5
            periodSeconds: 5
            timeoutSeconds: 3
            failureThreshold: 3
      volumes:
        - name: haproxy-config
          configMap:
            name: load-balancer-config
        - name: ssl-certs
          secret:
            secretName: dl-engine-tls
        - name: error-pages
          configMap:
            name: load-balancer-config
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 100
              podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: app.kubernetes.io/component
                      operator: In
                      values:
                        - load-balancer
                topologyKey: kubernetes.io/hostname

---
# 负载均衡器服务
apiVersion: v1
kind: Service
metadata:
  name: dl-engine-load-balancer
  namespace: dl-engine-edge
  labels:
    app.kubernetes.io/name: dl-engine
    app.kubernetes.io/component: load-balancer-service
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-type: "nlb"
    service.beta.kubernetes.io/aws-load-balancer-cross-zone-load-balancing-enabled: "true"
spec:
  type: LoadBalancer
  externalTrafficPolicy: Local
  ports:
    - port: 80
      targetPort: 80
      protocol: TCP
      name: http
    - port: 443
      targetPort: 443
      protocol: TCP
      name: https
    - port: 8404
      targetPort: 8404
      protocol: TCP
      name: stats
  selector:
    app.kubernetes.io/name: dl-engine
    app.kubernetes.io/component: load-balancer

---
# 网络策略
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: dl-engine-edge-network-policy
  namespace: dl-engine-edge
spec:
  podSelector:
    matchLabels:
      app.kubernetes.io/name: dl-engine
  policyTypes:
    - Ingress
    - Egress
  ingress:
    - from: []
      ports:
        - protocol: TCP
          port: 80
        - protocol: TCP
          port: 443
        - protocol: TCP
          port: 8404
  egress:
    - to:
        - namespaceSelector:
            matchLabels:
              name: dl-engine
      ports:
        - protocol: TCP
          port: 3030
        - protocol: TCP
          port: 3031
        - protocol: TCP
          port: 3032
        - protocol: TCP
          port: 8080
    - to: []
      ports:
        - protocol: TCP
          port: 53
        - protocol: UDP
          port: 53
