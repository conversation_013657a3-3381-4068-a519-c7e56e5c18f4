import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { MinioService } from '../minio/minio.service';
import { ImageProcessingService } from './image-processing.service';
import { VideoProcessingService } from './video-processing.service';
import { AudioProcessingService } from './audio-processing.service';
import { ModelProcessingService } from './model-processing.service';
import { MetadataService } from './metadata.service';

export interface MediaFile {
  id: string;
  originalName: string;
  mimeType: string;
  size: number;
  bucketName: string;
  objectName: string;
  url: string;
  metadata: MediaMetadata;
  processingStatus: 'pending' | 'processing' | 'completed' | 'failed';
  processingResults?: ProcessingResult[];
  createdAt: Date;
  updatedAt: Date;
}

export interface MediaMetadata {
  type: 'image' | 'video' | 'audio' | 'model' | 'document';
  format: string;
  duration?: number; // 音频/视频时长（秒）
  dimensions?: {
    width: number;
    height: number;
    depth?: number;
  };
  bitrate?: number;
  sampleRate?: number;
  channels?: number;
  colorSpace?: string;
  compression?: string;
  quality?: number;
  fileInfo?: {
    codec?: string;
    container?: string;
    profile?: string;
    level?: string;
  };
  technical?: Record<string, any>;
  custom?: Record<string, any>;
}

export interface ProcessingResult {
  id: string;
  type: 'thumbnail' | 'transcode' | 'compress' | 'convert' | 'analyze' | 'extract';
  status: 'pending' | 'processing' | 'completed' | 'failed';
  input: {
    bucketName: string;
    objectName: string;
  };
  output?: {
    bucketName: string;
    objectName: string;
    url: string;
  };
  parameters: Record<string, any>;
  metadata?: Record<string, any>;
  error?: string;
  startTime: Date;
  endTime?: Date;
  duration?: number;
}

export interface ProcessingOptions {
  priority?: number; // 1-10, 10为最高优先级
  timeout?: number; // 超时时间（毫秒）
  retryAttempts?: number;
  outputBucket?: string;
  outputPrefix?: string;
  preserveOriginal?: boolean;
  notifyOnComplete?: boolean;
  webhookUrl?: string;
  metadata?: Record<string, any>;
}

@Injectable()
export class MediaService {
  private readonly logger = new Logger(MediaService.name);
  private readonly mediaFiles = new Map<string, MediaFile>();

  constructor(
    private configService: ConfigService,
    private minioService: MinioService,
    private imageProcessingService: ImageProcessingService,
    private videoProcessingService: VideoProcessingService,
    private audioProcessingService: AudioProcessingService,
    private modelProcessingService: ModelProcessingService,
    private metadataService: MetadataService,
    @InjectQueue('image-processing') private imageQueue: Queue,
    @InjectQueue('video-processing') private videoQueue: Queue,
    @InjectQueue('audio-processing') private audioQueue: Queue,
    @InjectQueue('model-processing') private modelQueue: Queue,
  ) {}

  /**
   * 上传并处理媒体文件
   */
  async uploadAndProcessMedia(
    file: Buffer,
    originalName: string,
    mimeType: string,
    options: ProcessingOptions = {}
  ): Promise<string> {
    try {
      // 生成文件ID和存储路径
      const fileId = this.generateFileId();
      const bucketName = options.outputBucket || 'media';
      const objectName = options.outputPrefix 
        ? `${options.outputPrefix}/${fileId}/${originalName}`
        : `${fileId}/${originalName}`;

      // 上传原始文件
      await this.minioService.putObject(bucketName, objectName, file);
      const url = await this.minioService.getPresignedUrl('GET', bucketName, objectName);

      // 提取元数据
      const metadata = await this.metadataService.extractMetadata(file, mimeType);

      // 创建媒体文件记录
      const mediaFile: MediaFile = {
        id: fileId,
        originalName,
        mimeType,
        size: file.length,
        bucketName,
        objectName,
        url,
        metadata,
        processingStatus: 'pending',
        processingResults: [],
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      this.mediaFiles.set(fileId, mediaFile);

      // 根据文件类型启动处理任务
      await this.startProcessing(mediaFile, options);

      this.logger.log(`Media file uploaded and processing started: ${fileId}`);
      return fileId;
    } catch (error) {
      this.logger.error('Failed to upload and process media:', error);
      throw error;
    }
  }

  /**
   * 获取媒体文件信息
   */
  getMediaFile(fileId: string): MediaFile | null {
    return this.mediaFiles.get(fileId) || null;
  }

  /**
   * 获取媒体文件列表
   */
  getMediaFiles(filter?: {
    type?: string;
    status?: string;
    limit?: number;
    offset?: number;
  }): MediaFile[] {
    let files = Array.from(this.mediaFiles.values());

    if (filter?.type) {
      files = files.filter(file => file.metadata.type === filter.type);
    }

    if (filter?.status) {
      files = files.filter(file => file.processingStatus === filter.status);
    }

    // 排序
    files.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());

    // 分页
    if (filter?.offset) {
      files = files.slice(filter.offset);
    }
    if (filter?.limit) {
      files = files.slice(0, filter.limit);
    }

    return files;
  }

  /**
   * 删除媒体文件
   */
  async deleteMediaFile(fileId: string): Promise<void> {
    const mediaFile = this.mediaFiles.get(fileId);
    if (!mediaFile) {
      throw new Error(`Media file not found: ${fileId}`);
    }

    try {
      // 删除原始文件
      await this.minioService.removeObject(mediaFile.bucketName, mediaFile.objectName);

      // 删除处理结果文件
      for (const result of mediaFile.processingResults || []) {
        if (result.output) {
          try {
            await this.minioService.removeObject(result.output.bucketName, result.output.objectName);
          } catch (error) {
            this.logger.warn(`Failed to delete processing result: ${result.id}`, error);
          }
        }
      }

      // 删除记录
      this.mediaFiles.delete(fileId);

      this.logger.log(`Media file deleted: ${fileId}`);
    } catch (error) {
      this.logger.error(`Failed to delete media file: ${fileId}`, error);
      throw error;
    }
  }

  /**
   * 重新处理媒体文件
   */
  async reprocessMediaFile(fileId: string, options: ProcessingOptions = {}): Promise<void> {
    const mediaFile = this.mediaFiles.get(fileId);
    if (!mediaFile) {
      throw new Error(`Media file not found: ${fileId}`);
    }

    try {
      mediaFile.processingStatus = 'pending';
      mediaFile.processingResults = [];
      mediaFile.updatedAt = new Date();

      await this.startProcessing(mediaFile, options);

      this.logger.log(`Media file reprocessing started: ${fileId}`);
    } catch (error) {
      this.logger.error(`Failed to reprocess media file: ${fileId}`, error);
      throw error;
    }
  }

  /**
   * 批量处理媒体文件
   */
  async batchProcessMedia(
    files: Array<{
      buffer: Buffer;
      originalName: string;
      mimeType: string;
    }>,
    options: ProcessingOptions = {}
  ): Promise<string[]> {
    const fileIds: string[] = [];
    const errors: Error[] = [];

    for (const file of files) {
      try {
        const fileId = await this.uploadAndProcessMedia(
          file.buffer,
          file.originalName,
          file.mimeType,
          options
        );
        fileIds.push(fileId);
      } catch (error) {
        errors.push(error);
        this.logger.error(`Failed to process file ${file.originalName}:`, error);
      }
    }

    if (errors.length > 0) {
      this.logger.warn(`Batch processing completed with ${errors.length} errors out of ${files.length} files`);
    }

    return fileIds;
  }

  /**
   * 获取处理统计信息
   */
  getProcessingStats(): {
    totalFiles: number;
    filesByType: Record<string, number>;
    filesByStatus: Record<string, number>;
    totalSize: number;
    averageProcessingTime: number;
    queueStats: Record<string, any>;
  } {
    const files = Array.from(this.mediaFiles.values());
    
    const filesByType: Record<string, number> = {};
    const filesByStatus: Record<string, number> = {};
    let totalSize = 0;
    let totalProcessingTime = 0;
    let processedFiles = 0;

    for (const file of files) {
      // 按类型统计
      filesByType[file.metadata.type] = (filesByType[file.metadata.type] || 0) + 1;
      
      // 按状态统计
      filesByStatus[file.processingStatus] = (filesByStatus[file.processingStatus] || 0) + 1;
      
      // 大小统计
      totalSize += file.size;
      
      // 处理时间统计
      if (file.processingResults) {
        for (const result of file.processingResults) {
          if (result.duration) {
            totalProcessingTime += result.duration;
            processedFiles++;
          }
        }
      }
    }

    const averageProcessingTime = processedFiles > 0 ? totalProcessingTime / processedFiles : 0;

    return {
      totalFiles: files.length,
      filesByType,
      filesByStatus,
      totalSize,
      averageProcessingTime,
      queueStats: {}, // 这里可以添加队列统计信息
    };
  }

  /**
   * 搜索媒体文件
   */
  searchMediaFiles(query: {
    keyword?: string;
    type?: string;
    format?: string;
    sizeRange?: { min: number; max: number };
    dateRange?: { start: Date; end: Date };
    tags?: string[];
  }): MediaFile[] {
    let files = Array.from(this.mediaFiles.values());

    if (query.keyword) {
      const keyword = query.keyword.toLowerCase();
      files = files.filter(file => 
        file.originalName.toLowerCase().includes(keyword) ||
        (file.metadata.custom?.tags as string[])?.some(tag => 
          tag.toLowerCase().includes(keyword)
        )
      );
    }

    if (query.type) {
      files = files.filter(file => file.metadata.type === query.type);
    }

    if (query.format) {
      files = files.filter(file => file.metadata.format === query.format);
    }

    if (query.sizeRange) {
      files = files.filter(file => 
        file.size >= query.sizeRange.min && file.size <= query.sizeRange.max
      );
    }

    if (query.dateRange) {
      files = files.filter(file => 
        file.createdAt >= query.dateRange.start && file.createdAt <= query.dateRange.end
      );
    }

    if (query.tags && query.tags.length > 0) {
      files = files.filter(file => {
        const fileTags = file.metadata.custom?.tags as string[] || [];
        return query.tags.some(tag => fileTags.includes(tag));
      });
    }

    return files.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
  }

  private async startProcessing(mediaFile: MediaFile, options: ProcessingOptions): Promise<void> {
    mediaFile.processingStatus = 'processing';

    try {
      switch (mediaFile.metadata.type) {
        case 'image':
          await this.processImage(mediaFile, options);
          break;
        case 'video':
          await this.processVideo(mediaFile, options);
          break;
        case 'audio':
          await this.processAudio(mediaFile, options);
          break;
        case 'model':
          await this.processModel(mediaFile, options);
          break;
        default:
          this.logger.warn(`Unsupported media type: ${mediaFile.metadata.type}`);
      }

      mediaFile.processingStatus = 'completed';
    } catch (error) {
      mediaFile.processingStatus = 'failed';
      this.logger.error(`Media processing failed for ${mediaFile.id}:`, error);
    }

    mediaFile.updatedAt = new Date();
  }

  private async processImage(mediaFile: MediaFile, options: ProcessingOptions): Promise<void> {
    const job = await this.imageQueue.add('process-image', {
      fileId: mediaFile.id,
      bucketName: mediaFile.bucketName,
      objectName: mediaFile.objectName,
      options,
    }, {
      priority: options.priority || 5,
      attempts: options.retryAttempts || 3,
      timeout: options.timeout || 300000, // 5分钟
    });

    this.logger.debug(`Image processing job created: ${job.id} for file ${mediaFile.id}`);
  }

  private async processVideo(mediaFile: MediaFile, options: ProcessingOptions): Promise<void> {
    const job = await this.videoQueue.add('process-video', {
      fileId: mediaFile.id,
      bucketName: mediaFile.bucketName,
      objectName: mediaFile.objectName,
      options,
    }, {
      priority: options.priority || 5,
      attempts: options.retryAttempts || 3,
      timeout: options.timeout || 1800000, // 30分钟
    });

    this.logger.debug(`Video processing job created: ${job.id} for file ${mediaFile.id}`);
  }

  private async processAudio(mediaFile: MediaFile, options: ProcessingOptions): Promise<void> {
    const job = await this.audioQueue.add('process-audio', {
      fileId: mediaFile.id,
      bucketName: mediaFile.bucketName,
      objectName: mediaFile.objectName,
      options,
    }, {
      priority: options.priority || 5,
      attempts: options.retryAttempts || 3,
      timeout: options.timeout || 600000, // 10分钟
    });

    this.logger.debug(`Audio processing job created: ${job.id} for file ${mediaFile.id}`);
  }

  private async processModel(mediaFile: MediaFile, options: ProcessingOptions): Promise<void> {
    const job = await this.modelQueue.add('process-model', {
      fileId: mediaFile.id,
      bucketName: mediaFile.bucketName,
      objectName: mediaFile.objectName,
      options,
    }, {
      priority: options.priority || 5,
      attempts: options.retryAttempts || 3,
      timeout: options.timeout || 900000, // 15分钟
    });

    this.logger.debug(`Model processing job created: ${job.id} for file ${mediaFile.id}`);
  }

  private generateFileId(): string {
    return `media_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
