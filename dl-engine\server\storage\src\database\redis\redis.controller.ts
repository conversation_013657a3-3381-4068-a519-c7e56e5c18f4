import { Controller, Get, Post, Put, Delete, Body, Param, Query, Logger } from '@nestjs/common';
import { RedisService } from './redis.service';
import { CacheService } from './cache.service';
import { SessionService } from './session.service';
import { LockService } from './lock.service';
import { QueueService } from './queue.service';
import { PubSubService } from './pubsub.service';

@Controller('redis')
export class RedisController {
  private readonly logger = new Logger(RedisController.name);

  constructor(
    private redisService: RedisService,
    private cacheService: CacheService,
    private sessionService: SessionService,
    private lockService: LockService,
    private queueService: QueueService,
    private pubSubService: PubSubService,
  ) {}

  // Redis基础操作
  @Get('ping')
  async ping() {
    return { result: await this.redisService.ping() };
  }

  @Get('info/:section?')
  async getInfo(@Param('section') section?: string) {
    return { info: await this.redisService.info(section) };
  }

  @Post('keys')
  async setKey(@Body() { key, value, ttl }: { key: string; value: any; ttl?: number }) {
    await this.redisService.set(key, value, { ttl });
    return { success: true };
  }

  @Get('keys/:key')
  async getKey(@Param('key') key: string) {
    const value = await this.redisService.get(key);
    const ttl = await this.redisService.ttl(key);
    return { value, ttl };
  }

  @Delete('keys/:key')
  async deleteKey(@Param('key') key: string) {
    const deleted = await this.redisService.del(key);
    return { deleted };
  }

  @Get('keys')
  async getKeys(@Query('pattern') pattern: string = '*') {
    const keys = await this.redisService.keys(pattern);
    return { keys };
  }

  // 缓存操作
  @Get('cache/stats')
  async getCacheStats() {
    return await this.cacheService.getStats();
  }

  @Post('cache')
  async setCache(@Body() { key, value, ttl, tags }: { key: string; value: any; ttl?: number; tags?: string[] }) {
    await this.cacheService.set(key, value, { ttl, tags });
    return { success: true };
  }

  @Get('cache/:key')
  async getCache(@Param('key') key: string) {
    const value = await this.cacheService.get(key);
    const exists = await this.cacheService.exists(key);
    const ttl = await this.cacheService.ttl(key);
    return { value, exists, ttl };
  }

  @Delete('cache/:key')
  async deleteCache(@Param('key') key: string) {
    const deleted = await this.cacheService.del(key);
    return { deleted };
  }

  @Delete('cache/tag/:tag')
  async deleteCacheByTag(@Param('tag') tag: string) {
    const deleted = await this.cacheService.deleteByTag(tag);
    return { deleted };
  }

  @Delete('cache/pattern/:pattern')
  async deleteCacheByPattern(@Param('pattern') pattern: string) {
    const deleted = await this.cacheService.deleteByPattern(pattern);
    return { deleted };
  }

  @Delete('cache')
  async flushCache() {
    await this.cacheService.flush();
    return { success: true };
  }

  // 会话管理
  @Post('sessions')
  async createSession(@Body() { sessionId, sessionData, ttl }: { sessionId: string; sessionData: any; ttl?: number }) {
    await this.sessionService.createSession(sessionId, sessionData, { ttl });
    return { success: true };
  }

  @Get('sessions/:sessionId')
  async getSession(@Param('sessionId') sessionId: string) {
    const sessionData = await this.sessionService.getSession(sessionId);
    return { sessionData };
  }

  @Put('sessions/:sessionId')
  async updateSession(@Param('sessionId') sessionId: string, @Body() updates: any) {
    const updated = await this.sessionService.updateSession(sessionId, updates);
    return { updated };
  }

  @Delete('sessions/:sessionId')
  async deleteSession(@Param('sessionId') sessionId: string) {
    const deleted = await this.sessionService.deleteSession(sessionId);
    return { deleted };
  }

  @Get('sessions/user/:userId')
  async getUserSessions(@Param('userId') userId: string) {
    const sessions = await this.sessionService.getUserSessions(userId);
    return { sessions };
  }

  @Delete('sessions/user/:userId')
  async deleteUserSessions(@Param('userId') userId: string, @Query('exclude') excludeSessionId?: string) {
    const deleted = await this.sessionService.deleteUserSessions(userId, excludeSessionId);
    return { deleted };
  }

  @Post('sessions/:sessionId/validate')
  async validateSession(@Param('sessionId') sessionId: string) {
    return await this.sessionService.validateSession(sessionId);
  }

  @Get('sessions/stats/active')
  async getActiveSessionStats() {
    return await this.sessionService.getActiveSessionStats();
  }

  @Post('sessions/cleanup')
  async cleanupExpiredSessions() {
    const cleaned = await this.sessionService.cleanupExpiredSessions();
    return { cleaned };
  }

  // 分布式锁
  @Post('locks')
  async acquireLock(@Body() { key, ttl, retryCount }: { key: string; ttl?: number; retryCount?: number }) {
    return await this.lockService.acquireLock(key, { ttl, retryCount });
  }

  @Delete('locks/:key')
  async releaseLock(@Param('key') key: string, @Body() { identifier }: { identifier: string }) {
    const released = await this.lockService.releaseLock(key, identifier);
    return { released };
  }

  @Put('locks/:key/renew')
  async renewLock(@Param('key') key: string, @Body() { identifier, ttl }: { identifier: string; ttl?: number }) {
    const renewed = await this.lockService.renewLock(key, identifier, ttl);
    return { renewed };
  }

  @Get('locks/:key')
  async getLockInfo(@Param('key') key: string) {
    const lockInfo = await this.lockService.getLockInfo(key);
    return { lockInfo };
  }

  @Delete('locks/:key/force')
  async forceReleaseLock(@Param('key') key: string) {
    const released = await this.lockService.forceReleaseLock(key);
    return { released };
  }

  @Get('locks')
  async getAllLocks() {
    const locks = await this.lockService.getAllLocks();
    return { locks };
  }

  @Get('locks/stats')
  async getLockStats() {
    return await this.lockService.getLockStats();
  }

  @Post('locks/cleanup')
  async cleanupExpiredLocks() {
    const cleaned = await this.lockService.cleanupExpiredLocks();
    return { cleaned };
  }

  // 队列管理
  @Post('queues/:queueName/jobs')
  async addJob(@Param('queueName') queueName: string, @Body() { data, priority, delay, maxAttempts }: any) {
    const jobId = await this.queueService.addJob(queueName, data, { priority, delay, maxAttempts });
    return { jobId };
  }

  @Get('queues/:queueName/jobs/next')
  async getNextJob(@Param('queueName') queueName: string) {
    const job = await this.queueService.getNextJob(queueName);
    return { job };
  }

  @Post('queues/:queueName/jobs/:jobId/complete')
  async completeJob(@Param('queueName') queueName: string, @Param('jobId') jobId: string, @Body() { result }: any) {
    const completed = await this.queueService.completeJob(queueName, jobId, result);
    return { completed };
  }

  @Post('queues/:queueName/jobs/:jobId/fail')
  async failJob(@Param('queueName') queueName: string, @Param('jobId') jobId: string, @Body() { error }: { error: string }) {
    const failed = await this.queueService.failJob(queueName, jobId, error);
    return { failed };
  }

  @Get('queues/:queueName/jobs/:jobId')
  async getJob(@Param('jobId') jobId: string) {
    const job = await this.queueService.getJob(jobId);
    return { job };
  }

  @Delete('queues/:queueName/jobs/:jobId')
  async removeJob(@Param('queueName') queueName: string, @Param('jobId') jobId: string) {
    const removed = await this.queueService.removeJob(queueName, jobId);
    return { removed };
  }

  @Get('queues/:queueName/stats')
  async getQueueStats(@Param('queueName') queueName: string) {
    return await this.queueService.getQueueStats(queueName);
  }

  @Delete('queues/:queueName')
  async clearQueue(@Param('queueName') queueName: string, @Query('status') status?: string) {
    const deleted = await this.queueService.clearQueue(queueName, status as any);
    return { deleted };
  }

  @Post('queues/:queueName/retry')
  async retryFailedJobs(@Param('queueName') queueName: string, @Body() { jobIds }: { jobIds?: string[] }) {
    const retried = await this.queueService.retryFailedJobs(queueName, jobIds);
    return { retried };
  }

  // 发布订阅
  @Post('pubsub/publish')
  async publishMessage(@Body() { channel, data, metadata }: { channel: string; data: any; metadata?: any }) {
    const subscribers = await this.pubSubService.publish(channel, data, metadata);
    return { subscribers };
  }

  @Post('pubsub/broadcast')
  async broadcastMessage(@Body() { channels, data, metadata }: { channels: string[]; data: any; metadata?: any }) {
    const results = await this.pubSubService.broadcast(channels, data, metadata);
    return { results };
  }

  @Get('pubsub/channels')
  async getActiveChannels() {
    const channels = await this.pubSubService.getActiveChannels();
    return { channels };
  }

  @Get('pubsub/channels/:channel/subscribers')
  async getSubscriberCount(@Param('channel') channel: string) {
    const count = await this.pubSubService.getSubscriberCount(channel);
    return { count };
  }

  @Get('pubsub/stats')
  async getSubscriptionStats() {
    return await this.pubSubService.getSubscriptionStats();
  }

  // 批量操作
  @Post('batch/pipeline')
  async executePipeline(@Body() { commands }: { commands: Array<{ command: string; args: any[] }> }) {
    const results = await this.redisService.pipeline(commands);
    return { results };
  }

  @Post('batch/cache/mget')
  async batchGetCache(@Body() { keys }: { keys: string[] }) {
    const values = await this.cacheService.mget(keys);
    return { values };
  }

  @Post('batch/cache/mset')
  async batchSetCache(@Body() { data, ttl }: { data: Record<string, any>; ttl?: number }) {
    await this.cacheService.mset(data, { ttl });
    return { success: true };
  }
}
