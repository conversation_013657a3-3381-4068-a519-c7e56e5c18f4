import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource, QueryRunner } from 'typeorm';
import { UserEntity } from './entities/user.entity';
import { ProjectEntity } from './entities/project.entity';
import { SceneEntity } from './entities/scene.entity';
import { AssetEntity } from './entities/asset.entity';
import { CourseEntity } from './entities/course.entity';
import { AssignmentEntity } from './entities/assignment.entity';
import { AssessmentEntity } from './entities/assessment.entity';

@Injectable()
export class MySQLService {
  private readonly logger = new Logger(MySQLService.name);

  constructor(
    private dataSource: DataSource,
    @InjectRepository(UserEntity)
    private userRepository: Repository<UserEntity>,
    @InjectRepository(ProjectEntity)
    private projectRepository: Repository<ProjectEntity>,
    @InjectRepository(SceneEntity)
    private sceneRepository: Repository<SceneEntity>,
    @InjectRepository(AssetEntity)
    private assetRepository: Repository<AssetEntity>,
    @InjectRepository(CourseEntity)
    private courseRepository: Repository<CourseEntity>,
    @InjectRepository(AssignmentEntity)
    private assignmentRepository: Repository<AssignmentEntity>,
    @InjectRepository(AssessmentEntity)
    private assessmentRepository: Repository<AssessmentEntity>,
  ) {}

  // 用户管理
  async createUser(userData: Partial<UserEntity>): Promise<UserEntity> {
    const user = this.userRepository.create(userData);
    return await this.userRepository.save(user);
  }

  async findUserById(id: string): Promise<UserEntity | null> {
    return await this.userRepository.findOne({ where: { id } });
  }

  async findUserByPhone(phone: string): Promise<UserEntity | null> {
    return await this.userRepository.findOne({ where: { phone } });
  }

  async findUserByEmail(email: string): Promise<UserEntity | null> {
    return await this.userRepository.findOne({ where: { email } });
  }

  async updateUser(id: string, updateData: Partial<UserEntity>): Promise<UserEntity> {
    await this.userRepository.update(id, updateData);
    return await this.findUserById(id);
  }

  async updateUserLoginInfo(id: string, ip: string): Promise<void> {
    await this.userRepository.update(id, {
      lastLoginAt: new Date(),
      lastLoginIp: ip,
      loginCount: () => 'loginCount + 1',
    });
  }

  // 项目管理
  async createProject(projectData: Partial<ProjectEntity>): Promise<ProjectEntity> {
    const project = this.projectRepository.create(projectData);
    return await this.projectRepository.save(project);
  }

  async findProjectById(id: string): Promise<ProjectEntity | null> {
    return await this.projectRepository.findOne({
      where: { id },
      relations: ['owner', 'scenes', 'assets'],
    });
  }

  async findProjectsByOwner(ownerId: string): Promise<ProjectEntity[]> {
    return await this.projectRepository.find({
      where: { ownerId },
      relations: ['scenes', 'assets'],
      order: { updatedAt: 'DESC' },
    });
  }

  async updateProject(id: string, updateData: Partial<ProjectEntity>): Promise<ProjectEntity> {
    await this.projectRepository.update(id, updateData);
    return await this.findProjectById(id);
  }

  async updateProjectAccess(id: string): Promise<void> {
    await this.projectRepository.update(id, {
      lastAccessedAt: new Date(),
      viewCount: () => 'viewCount + 1',
    });
  }

  // 场景管理
  async createScene(sceneData: Partial<SceneEntity>): Promise<SceneEntity> {
    const scene = this.sceneRepository.create(sceneData);
    return await this.sceneRepository.save(scene);
  }

  async findSceneById(id: string): Promise<SceneEntity | null> {
    return await this.sceneRepository.findOne({
      where: { id },
      relations: ['project'],
    });
  }

  async findScenesByProject(projectId: string): Promise<SceneEntity[]> {
    return await this.sceneRepository.find({
      where: { projectId },
      order: { order: 'ASC' },
    });
  }

  async updateScene(id: string, updateData: Partial<SceneEntity>): Promise<SceneEntity> {
    await this.sceneRepository.update(id, updateData);
    return await this.findSceneById(id);
  }

  // 资产管理
  async createAsset(assetData: Partial<AssetEntity>): Promise<AssetEntity> {
    const asset = this.assetRepository.create(assetData);
    return await this.assetRepository.save(asset);
  }

  async findAssetById(id: string): Promise<AssetEntity | null> {
    return await this.assetRepository.findOne({
      where: { id },
      relations: ['project'],
    });
  }

  async findAssetsByProject(projectId: string): Promise<AssetEntity[]> {
    return await this.assetRepository.find({
      where: { projectId },
      order: { createdAt: 'DESC' },
    });
  }

  async updateAsset(id: string, updateData: Partial<AssetEntity>): Promise<AssetEntity> {
    await this.assetRepository.update(id, updateData);
    return await this.findAssetById(id);
  }

  async updateAssetAccess(id: string): Promise<void> {
    await this.assetRepository.update(id, {
      lastAccessedAt: new Date(),
      usageCount: () => 'usageCount + 1',
    });
  }

  // 课程管理
  async createCourse(courseData: Partial<CourseEntity>): Promise<CourseEntity> {
    const course = this.courseRepository.create(courseData);
    return await this.courseRepository.save(course);
  }

  async findCourseById(id: string): Promise<CourseEntity | null> {
    return await this.courseRepository.findOne({
      where: { id },
      relations: ['teacher', 'assignments', 'assessments'],
    });
  }

  async findCoursesByTeacher(teacherId: string): Promise<CourseEntity[]> {
    return await this.courseRepository.find({
      where: { teacherId },
      order: { createdAt: 'DESC' },
    });
  }

  async updateCourse(id: string, updateData: Partial<CourseEntity>): Promise<CourseEntity> {
    await this.courseRepository.update(id, updateData);
    return await this.findCourseById(id);
  }

  // 作业管理
  async createAssignment(assignmentData: Partial<AssignmentEntity>): Promise<AssignmentEntity> {
    const assignment = this.assignmentRepository.create(assignmentData);
    return await this.assignmentRepository.save(assignment);
  }

  async findAssignmentById(id: string): Promise<AssignmentEntity | null> {
    return await this.assignmentRepository.findOne({
      where: { id },
      relations: ['course', 'student'],
    });
  }

  async findAssignmentsByStudent(studentId: string): Promise<AssignmentEntity[]> {
    return await this.assignmentRepository.find({
      where: { studentId },
      relations: ['course'],
      order: { dueDate: 'ASC' },
    });
  }

  async updateAssignment(id: string, updateData: Partial<AssignmentEntity>): Promise<AssignmentEntity> {
    await this.assignmentRepository.update(id, updateData);
    return await this.findAssignmentById(id);
  }

  // 评估管理
  async createAssessment(assessmentData: Partial<AssessmentEntity>): Promise<AssessmentEntity> {
    const assessment = this.assessmentRepository.create(assessmentData);
    return await this.assessmentRepository.save(assessment);
  }

  async findAssessmentById(id: string): Promise<AssessmentEntity | null> {
    return await this.assessmentRepository.findOne({
      where: { id },
      relations: ['course', 'student'],
    });
  }

  async findAssessmentsByStudent(studentId: string): Promise<AssessmentEntity[]> {
    return await this.assessmentRepository.find({
      where: { studentId },
      relations: ['course'],
      order: { dueDate: 'ASC' },
    });
  }

  async updateAssessment(id: string, updateData: Partial<AssessmentEntity>): Promise<AssessmentEntity> {
    await this.assessmentRepository.update(id, updateData);
    return await this.findAssessmentById(id);
  }

  // 数据库健康检查
  async healthCheck(): Promise<{ status: string; details: any }> {
    try {
      const result = await this.dataSource.query('SELECT 1 as health');
      const stats = await this.getConnectionStats();
      
      return {
        status: 'healthy',
        details: {
          connection: 'active',
          stats,
          timestamp: new Date().toISOString(),
        },
      };
    } catch (error) {
      this.logger.error('MySQL health check failed:', error);
      return {
        status: 'unhealthy',
        details: {
          error: error.message,
          timestamp: new Date().toISOString(),
        },
      };
    }
  }

  private async getConnectionStats(): Promise<any> {
    try {
      const [connections] = await this.dataSource.query('SHOW STATUS LIKE "Threads_connected"');
      const [maxConnections] = await this.dataSource.query('SHOW VARIABLES LIKE "max_connections"');
      
      return {
        activeConnections: parseInt(connections.Value),
        maxConnections: parseInt(maxConnections.Value),
        poolSize: this.dataSource.options.extra?.connectionLimit || 'unknown',
      };
    } catch (error) {
      this.logger.warn('Failed to get connection stats:', error);
      return {};
    }
  }

  // 获取查询运行器用于事务
  async getQueryRunner(): Promise<QueryRunner> {
    return this.dataSource.createQueryRunner();
  }
}
