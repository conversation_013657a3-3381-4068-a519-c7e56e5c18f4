/**
 * DL-Engine 第三批次服务器端核心服务集成测试
 * 测试API网关、认证服务、核心API服务、实例服务和任务服务
 */

import { Test, TestingModule } from '@nestjs/testing'
import { INestApplication } from '@nestjs/common'
import { ConfigModule } from '@nestjs/config'
import { TypeOrmModule } from '@nestjs/typeorm'
import * as request from 'supertest'

// 核心服务
import { AuthService } from '../auth/src/phone-auth/phone-auth.service'
import { UserService } from '../api/src/users/services/user.service'
import { CourseService } from '../api/src/education/services/course.service'
import { ProjectService } from '../api/src/projects/services/project.service'
import { InstanceManagerService } from '../instance/world/instance-manager'
import { TaskSchedulerService } from '../task/scheduler/task-scheduler'

describe('第三批次：服务器端核心服务集成测试', () => {
  let app: INestApplication
  let authService: AuthService
  let userService: UserService
  let courseService: CourseService
  let projectService: ProjectService
  let instanceService: InstanceManagerService
  let taskService: TaskSchedulerService

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          isGlobal: true,
          envFilePath: ['.env.test']
        }),
        TypeOrmModule.forRoot({
          type: 'sqlite',
          database: ':memory:',
          entities: ['src/**/*.entity{.ts,.js}'],
          synchronize: true
        })
      ],
      providers: [
        AuthService,
        UserService,
        CourseService,
        ProjectService,
        InstanceManagerService,
        TaskSchedulerService
      ]
    }).compile()

    app = moduleFixture.createNestApplication()
    await app.init()

    authService = moduleFixture.get<AuthService>(AuthService)
    userService = moduleFixture.get<UserService>(UserService)
    courseService = moduleFixture.get<CourseService>(CourseService)
    projectService = moduleFixture.get<ProjectService>(ProjectService)
    instanceService = moduleFixture.get<InstanceManagerService>(InstanceManagerService)
    taskService = moduleFixture.get<TaskSchedulerService>(TaskSchedulerService)
  })

  afterAll(async () => {
    await app.close()
  })

  describe('1. API网关与认证服务测试', () => {
    describe('手机号认证系统', () => {
      it('应该能够发送验证码', async () => {
        const phoneNumber = '+8613800138000'
        
        const result = await authService.sendVerificationCode(phoneNumber)
        
        expect(result).toHaveProperty('success', true)
        expect(result).toHaveProperty('message')
        expect(result).toHaveProperty('expiresIn')
      })

      it('应该能够验证手机号码格式', async () => {
        const validPhones = ['+8613800138000', '+8615912345678', '+8618612345678']
        const invalidPhones = ['13800138000', '1380013800', '+86138001380001']

        for (const phone of validPhones) {
          expect(() => authService.validatePhoneNumber(phone)).not.toThrow()
        }

        for (const phone of invalidPhones) {
          expect(() => authService.validatePhoneNumber(phone)).toThrow()
        }
      })

      it('应该能够处理验证码登录', async () => {
        const phoneNumber = '+8613800138000'
        const verificationCode = '123456' // 测试验证码
        
        // 模拟验证码验证
        const result = await authService.verifyCodeAndLogin(phoneNumber, verificationCode)
        
        expect(result).toHaveProperty('accessToken')
        expect(result).toHaveProperty('refreshToken')
        expect(result).toHaveProperty('user')
        expect(result.user).toHaveProperty('phone', phoneNumber)
      })

      it('应该能够刷新JWT令牌', async () => {
        const refreshToken = 'valid-refresh-token'
        
        const result = await authService.refreshToken(refreshToken)
        
        expect(result).toHaveProperty('accessToken')
        expect(result).toHaveProperty('refreshToken')
      })
    })

    describe('权限管理', () => {
      it('应该能够验证用户权限', async () => {
        const userId = 'test-user-id'
        const resource = 'course'
        const action = 'create'
        
        const hasPermission = await authService.checkPermission(userId, resource, action)
        
        expect(typeof hasPermission).toBe('boolean')
      })

      it('应该能够分配角色', async () => {
        const userId = 'test-user-id'
        const role = 'teacher'
        
        const result = await authService.assignRole(userId, role)
        
        expect(result).toHaveProperty('success', true)
      })
    })
  })

  describe('2. 核心API服务测试', () => {
    describe('用户管理服务', () => {
      it('应该能够创建用户', async () => {
        const userData = {
          phone: '+8613800138001',
          countryCode: '+86',
          nickname: '测试用户',
          email: '<EMAIL>'
        }
        
        const user = await userService.create(userData)
        
        expect(user).toHaveProperty('id')
        expect(user).toHaveProperty('phone', userData.phone)
        expect(user).toHaveProperty('nickname', userData.nickname)
      })

      it('应该能够更新用户资料', async () => {
        const userId = 'test-user-id'
        const updateData = {
          nickname: '更新后的昵称',
          bio: '这是用户简介'
        }
        
        const updatedUser = await userService.update(userId, updateData)
        
        expect(updatedUser).toHaveProperty('nickname', updateData.nickname)
        expect(updatedUser).toHaveProperty('bio', updateData.bio)
      })

      it('应该能够获取用户列表', async () => {
        const query = { page: 1, limit: 10 }
        
        const result = await userService.findAll(query)
        
        expect(result).toHaveProperty('data')
        expect(result).toHaveProperty('total')
        expect(result).toHaveProperty('page')
        expect(result).toHaveProperty('limit')
        expect(Array.isArray(result.data)).toBe(true)
      })
    })

    describe('教育功能服务', () => {
      it('应该能够创建课程', async () => {
        const courseData = {
          title: '3D建模基础',
          description: '学习3D建模的基础知识',
          category: '3D设计',
          difficulty: 'beginner',
          estimatedHours: 20,
          instructorId: 'teacher-user-id'
        }
        
        const course = await courseService.create(courseData)
        
        expect(course).toHaveProperty('id')
        expect(course).toHaveProperty('title', courseData.title)
        expect(course).toHaveProperty('difficulty', courseData.difficulty)
      })

      it('应该能够发布课程', async () => {
        const courseId = 'test-course-id'
        const instructorId = 'teacher-user-id'
        
        const result = await courseService.publish(courseId, instructorId)
        
        expect(result).toHaveProperty('status', 'published')
      })

      it('应该能够学生注册课程', async () => {
        const courseId = 'test-course-id'
        const studentId = 'student-user-id'
        
        const enrollment = await courseService.enroll(courseId, studentId)
        
        expect(enrollment).toHaveProperty('courseId', courseId)
        expect(enrollment).toHaveProperty('studentId', studentId)
        expect(enrollment).toHaveProperty('status', 'active')
      })

      it('应该能够跟踪学习进度', async () => {
        const courseId = 'test-course-id'
        const studentId = 'student-user-id'
        
        const progress = await courseService.getProgress(courseId, studentId)
        
        expect(progress).toHaveProperty('courseId', courseId)
        expect(progress).toHaveProperty('studentId', studentId)
        expect(progress).toHaveProperty('completionPercentage')
        expect(typeof progress.completionPercentage).toBe('number')
      })
    })

    describe('项目管理服务', () => {
      it('应该能够创建项目', async () => {
        const projectData = {
          name: '我的3D场景',
          description: '一个测试3D场景项目',
          type: '3d_scene',
          ownerId: 'test-user-id'
        }
        
        const project = await projectService.create(projectData)
        
        expect(project).toHaveProperty('id')
        expect(project).toHaveProperty('name', projectData.name)
        expect(project).toHaveProperty('type', projectData.type)
      })

      it('应该能够分享项目', async () => {
        const projectId = 'test-project-id'
        const shareSettings = {
          visibility: 'public',
          allowComments: true,
          allowFork: true
        }
        
        const result = await projectService.share(projectId, shareSettings)
        
        expect(result).toHaveProperty('shareUrl')
        expect(result).toHaveProperty('visibility', shareSettings.visibility)
      })
    })
  })

  describe('3. 实例与任务服务测试', () => {
    describe('世界实例服务', () => {
      it('应该能够创建实例', async () => {
        const instanceConfig = {
          projectId: 'test-project-id',
          maxUsers: 10,
          settings: {
            physics: true,
            networking: true
          }
        }
        
        const instance = await instanceService.createInstance(instanceConfig)
        
        expect(instance).toHaveProperty('id')
        expect(instance).toHaveProperty('projectId', instanceConfig.projectId)
        expect(instance).toHaveProperty('status', 'running')
      })

      it('应该能够管理实例生命周期', async () => {
        const instanceId = 'test-instance-id'
        
        // 暂停实例
        await instanceService.pauseInstance(instanceId)
        let instance = await instanceService.getInstance(instanceId)
        expect(instance.status).toBe('paused')
        
        // 恢复实例
        await instanceService.resumeInstance(instanceId)
        instance = await instanceService.getInstance(instanceId)
        expect(instance.status).toBe('running')
        
        // 停止实例
        await instanceService.stopInstance(instanceId)
        instance = await instanceService.getInstance(instanceId)
        expect(instance.status).toBe('stopped')
      })

      it('应该能够处理用户连接', async () => {
        const instanceId = 'test-instance-id'
        const userId = 'test-user-id'
        
        // 用户加入
        const result = await instanceService.joinInstance(instanceId, userId)
        expect(result).toHaveProperty('success', true)
        
        // 获取实例用户列表
        const users = await instanceService.getInstanceUsers(instanceId)
        expect(users).toContain(userId)
        
        // 用户离开
        await instanceService.leaveInstance(instanceId, userId)
        const updatedUsers = await instanceService.getInstanceUsers(instanceId)
        expect(updatedUsers).not.toContain(userId)
      })
    })

    describe('任务调度服务', () => {
      it('应该能够创建任务', async () => {
        const taskData = {
          type: 'backup',
          payload: { projectId: 'test-project-id' },
          priority: 5,
          scheduledAt: new Date(Date.now() + 60000) // 1分钟后执行
        }
        
        const task = await taskService.createTask(taskData)
        
        expect(task).toHaveProperty('id')
        expect(task).toHaveProperty('type', taskData.type)
        expect(task).toHaveProperty('status', 'pending')
      })

      it('应该能够执行任务', async () => {
        const taskId = 'test-task-id'
        
        const result = await taskService.executeTask(taskId)
        
        expect(result).toHaveProperty('success', true)
        
        // 检查任务状态
        const task = await taskService.getTask(taskId)
        expect(['running', 'completed']).toContain(task.status)
      })

      it('应该能够获取任务统计', async () => {
        const stats = await taskService.getTaskStats()
        
        expect(stats).toHaveProperty('pending')
        expect(stats).toHaveProperty('running')
        expect(stats).toHaveProperty('completed')
        expect(stats).toHaveProperty('failed')
        expect(typeof stats.pending).toBe('number')
      })
    })
  })

  describe('4. 端到端集成测试', () => {
    it('应该能够完成完整的教育流程', async () => {
      // 1. 教师登录
      const teacherAuth = await authService.verifyCodeAndLogin('+8613800138001', '123456')
      expect(teacherAuth).toHaveProperty('accessToken')
      
      // 2. 创建课程
      const course = await courseService.create({
        title: '集成测试课程',
        description: '用于集成测试的课程',
        instructorId: teacherAuth.user.id
      })
      expect(course).toHaveProperty('id')
      
      // 3. 学生登录
      const studentAuth = await authService.verifyCodeAndLogin('+8613800138002', '123456')
      expect(studentAuth).toHaveProperty('accessToken')
      
      // 4. 学生注册课程
      const enrollment = await courseService.enroll(course.id, studentAuth.user.id)
      expect(enrollment).toHaveProperty('status', 'active')
      
      // 5. 创建项目实例
      const project = await projectService.create({
        name: '课程项目',
        type: '3d_scene',
        ownerId: studentAuth.user.id
      })
      
      const instance = await instanceService.createInstance({
        projectId: project.id,
        maxUsers: 5
      })
      expect(instance).toHaveProperty('status', 'running')
      
      // 6. 学生加入实例
      const joinResult = await instanceService.joinInstance(instance.id, studentAuth.user.id)
      expect(joinResult).toHaveProperty('success', true)
    })

    it('应该能够处理高并发场景', async () => {
      const concurrentUsers = 10
      const promises = []
      
      // 模拟多个用户同时登录
      for (let i = 0; i < concurrentUsers; i++) {
        const phone = `+861380013800${i}`
        promises.push(authService.verifyCodeAndLogin(phone, '123456'))
      }
      
      const results = await Promise.allSettled(promises)
      const successCount = results.filter(r => r.status === 'fulfilled').length
      
      expect(successCount).toBeGreaterThan(0)
      console.log(`并发登录成功率: ${successCount}/${concurrentUsers}`)
    })
  })
})

// 性能测试
describe('第三批次服务性能测试', () => {
  it('应该能够处理大量API请求', async () => {
    const startTime = Date.now()
    const requestCount = 100
    const promises = []
    
    for (let i = 0; i < requestCount; i++) {
      promises.push(
        request(app.getHttpServer())
          .get('/api/v1/health')
          .expect(200)
      )
    }
    
    await Promise.all(promises)
    const endTime = Date.now()
    const duration = endTime - startTime
    const rps = requestCount / (duration / 1000)
    
    console.log(`API性能测试: ${requestCount}个请求耗时${duration}ms, RPS: ${rps.toFixed(2)}`)
    expect(rps).toBeGreaterThan(50) // 期望RPS > 50
  })

  it('应该能够处理大量实例创建', async () => {
    const instanceCount = 20
    const promises = []
    
    for (let i = 0; i < instanceCount; i++) {
      promises.push(instanceService.createInstance({
        projectId: `test-project-${i}`,
        maxUsers: 5
      }))
    }
    
    const results = await Promise.allSettled(promises)
    const successCount = results.filter(r => r.status === 'fulfilled').length
    
    console.log(`实例创建成功率: ${successCount}/${instanceCount}`)
    expect(successCount).toBeGreaterThan(instanceCount * 0.8) // 期望80%以上成功率
  })
})
