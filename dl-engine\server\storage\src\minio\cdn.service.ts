import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { MinioService } from './minio.service';

export interface CDNConfig {
  provider: 'cloudflare' | 'aws' | 'custom';
  endpoint: string;
  apiKey?: string;
  secretKey?: string;
  zoneId?: string;
  distributionId?: string;
  customHeaders?: Record<string, string>;
}

export interface CacheRule {
  pattern: string; // 文件模式，如 "*.jpg", "*.mp4"
  ttl: number; // 缓存时间（秒）
  browserTtl?: number; // 浏览器缓存时间
  edgeTtl?: number; // 边缘缓存时间
  bypassCache?: boolean; // 是否绕过缓存
}

export interface CDNStats {
  totalRequests: number;
  cacheHitRate: number;
  bandwidth: number; // 字节
  topFiles: Array<{
    path: string;
    requests: number;
    bandwidth: number;
  }>;
  geographicDistribution: Record<string, number>;
}

@Injectable()
export class CDNService {
  private readonly logger = new Logger(CDNService.name);
  private readonly cdnConfig: CDNConfig;
  private readonly cacheRules: CacheRule[] = [];

  constructor(
    private configService: ConfigService,
    private minioService: MinioService,
  ) {
    this.cdnConfig = {
      provider: this.configService.get('CDN_PROVIDER', 'custom'),
      endpoint: this.configService.get('CDN_ENDPOINT', ''),
      apiKey: this.configService.get('CDN_API_KEY'),
      secretKey: this.configService.get('CDN_SECRET_KEY'),
      zoneId: this.configService.get('CDN_ZONE_ID'),
      distributionId: this.configService.get('CDN_DISTRIBUTION_ID'),
    };

    this.initializeDefaultCacheRules();
  }

  /**
   * 生成CDN URL
   */
  generateCDNUrl(bucketName: string, objectName: string, options?: {
    version?: string;
    transform?: {
      width?: number;
      height?: number;
      quality?: number;
      format?: string;
    };
    secure?: boolean;
  }): string {
    try {
      let baseUrl = this.cdnConfig.endpoint;
      
      if (!baseUrl) {
        // 如果没有配置CDN，返回Minio直接URL
        return this.generateMinioUrl(bucketName, objectName);
      }

      // 确保URL以/结尾
      if (!baseUrl.endsWith('/')) {
        baseUrl += '/';
      }

      let url = `${baseUrl}${bucketName}/${objectName}`;

      // 添加查询参数
      const params = new URLSearchParams();

      if (options?.version) {
        params.append('v', options.version);
      }

      if (options?.transform) {
        const { width, height, quality, format } = options.transform;
        
        if (width) params.append('w', width.toString());
        if (height) params.append('h', height.toString());
        if (quality) params.append('q', quality.toString());
        if (format) params.append('f', format);
      }

      if (params.toString()) {
        url += `?${params.toString()}`;
      }

      // 强制HTTPS
      if (options?.secure !== false && !url.startsWith('https://')) {
        url = url.replace(/^http:\/\//, 'https://');
      }

      return url;
    } catch (error) {
      this.logger.error(`Failed to generate CDN URL for ${bucketName}/${objectName}:`, error);
      return this.generateMinioUrl(bucketName, objectName);
    }
  }

  /**
   * 预热CDN缓存
   */
  async warmupCache(urls: string[]): Promise<{ success: string[]; failed: string[] }> {
    const success: string[] = [];
    const failed: string[] = [];

    try {
      switch (this.cdnConfig.provider) {
        case 'cloudflare':
          await this.warmupCloudflareCache(urls);
          success.push(...urls);
          break;
        
        case 'aws':
          await this.warmupAWSCache(urls);
          success.push(...urls);
          break;
        
        default:
          // 对于自定义CDN，通过HTTP请求预热
          for (const url of urls) {
            try {
              const response = await fetch(url, { method: 'HEAD' });
              if (response.ok) {
                success.push(url);
              } else {
                failed.push(url);
              }
            } catch (error) {
              failed.push(url);
            }
          }
      }

      this.logger.log(`Cache warmup completed: ${success.length} success, ${failed.length} failed`);
    } catch (error) {
      this.logger.error('Failed to warmup cache:', error);
      failed.push(...urls);
    }

    return { success, failed };
  }

  /**
   * 清除CDN缓存
   */
  async purgeCache(urls: string[]): Promise<{ success: string[]; failed: string[] }> {
    const success: string[] = [];
    const failed: string[] = [];

    try {
      switch (this.cdnConfig.provider) {
        case 'cloudflare':
          await this.purgeCloudflareCache(urls);
          success.push(...urls);
          break;
        
        case 'aws':
          await this.purgeAWSCache(urls);
          success.push(...urls);
          break;
        
        default:
          this.logger.warn('Cache purge not supported for custom CDN provider');
          failed.push(...urls);
      }

      this.logger.log(`Cache purge completed: ${success.length} success, ${failed.length} failed`);
    } catch (error) {
      this.logger.error('Failed to purge cache:', error);
      failed.push(...urls);
    }

    return { success, failed };
  }

  /**
   * 设置缓存规则
   */
  setCacheRule(rule: CacheRule): void {
    // 移除现有的相同模式规则
    const existingIndex = this.cacheRules.findIndex(r => r.pattern === rule.pattern);
    if (existingIndex >= 0) {
      this.cacheRules.splice(existingIndex, 1);
    }

    this.cacheRules.push(rule);
    this.logger.debug(`Cache rule set for pattern: ${rule.pattern}`);
  }

  /**
   * 获取文件的缓存规则
   */
  getCacheRule(fileName: string): CacheRule | null {
    for (const rule of this.cacheRules) {
      if (this.matchPattern(fileName, rule.pattern)) {
        return rule;
      }
    }
    return null;
  }

  /**
   * 生成带缓存控制的URL
   */
  generateCachedUrl(
    bucketName: string,
    objectName: string,
    options?: {
      maxAge?: number;
      immutable?: boolean;
      private?: boolean;
    }
  ): string {
    const url = this.generateCDNUrl(bucketName, objectName);
    
    if (!options) {
      return url;
    }

    const params = new URLSearchParams();
    
    if (options.maxAge) {
      params.append('cache', options.maxAge.toString());
    }
    
    if (options.immutable) {
      params.append('immutable', '1');
    }
    
    if (options.private) {
      params.append('private', '1');
    }

    if (params.toString()) {
      const separator = url.includes('?') ? '&' : '?';
      return `${url}${separator}${params.toString()}`;
    }

    return url;
  }

  /**
   * 获取CDN统计信息
   */
  async getCDNStats(timeRange?: { start: Date; end: Date }): Promise<CDNStats> {
    try {
      switch (this.cdnConfig.provider) {
        case 'cloudflare':
          return await this.getCloudflareStats(timeRange);
        
        case 'aws':
          return await this.getAWSStats(timeRange);
        
        default:
          return this.getDefaultStats();
      }
    } catch (error) {
      this.logger.error('Failed to get CDN stats:', error);
      return this.getDefaultStats();
    }
  }

  /**
   * 批量生成CDN URL
   */
  generateBatchCDNUrls(
    objects: Array<{ bucketName: string; objectName: string }>,
    options?: {
      baseOptions?: any;
      transforms?: Record<string, any>;
    }
  ): Array<{ bucketName: string; objectName: string; url: string }> {
    return objects.map(({ bucketName, objectName }) => {
      const transform = options?.transforms?.[objectName];
      const url = this.generateCDNUrl(bucketName, objectName, {
        ...options?.baseOptions,
        transform,
      });

      return { bucketName, objectName, url };
    });
  }

  /**
   * 检查CDN健康状态
   */
  async healthCheck(): Promise<{ status: string; details: any }> {
    try {
      if (!this.cdnConfig.endpoint) {
        return {
          status: 'disabled',
          details: { message: 'CDN not configured' },
        };
      }

      // 测试CDN连接
      const testUrl = `${this.cdnConfig.endpoint}/health`;
      const response = await fetch(testUrl, { 
        method: 'HEAD',
        timeout: 5000,
      });

      return {
        status: response.ok ? 'healthy' : 'unhealthy',
        details: {
          endpoint: this.cdnConfig.endpoint,
          provider: this.cdnConfig.provider,
          responseStatus: response.status,
          timestamp: new Date().toISOString(),
        },
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        details: {
          error: error.message,
          timestamp: new Date().toISOString(),
        },
      };
    }
  }

  private generateMinioUrl(bucketName: string, objectName: string): string {
    const minioEndpoint = this.configService.get('MINIO_ENDPOINT', 'localhost:9000');
    const useSSL = this.configService.get('MINIO_USE_SSL', false);
    const protocol = useSSL ? 'https' : 'http';
    
    return `${protocol}://${minioEndpoint}/${bucketName}/${objectName}`;
  }

  private async warmupCloudflareCache(urls: string[]): Promise<void> {
    if (!this.cdnConfig.apiKey || !this.cdnConfig.zoneId) {
      throw new Error('Cloudflare API key and zone ID required');
    }

    const response = await fetch(`https://api.cloudflare.com/client/v4/zones/${this.cdnConfig.zoneId}/purge_cache`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.cdnConfig.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        prefixes: urls,
      }),
    });

    if (!response.ok) {
      throw new Error(`Cloudflare API error: ${response.statusText}`);
    }
  }

  private async purgeCloudflareCache(urls: string[]): Promise<void> {
    if (!this.cdnConfig.apiKey || !this.cdnConfig.zoneId) {
      throw new Error('Cloudflare API key and zone ID required');
    }

    const response = await fetch(`https://api.cloudflare.com/client/v4/zones/${this.cdnConfig.zoneId}/purge_cache`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.cdnConfig.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        files: urls,
      }),
    });

    if (!response.ok) {
      throw new Error(`Cloudflare API error: ${response.statusText}`);
    }
  }

  private async warmupAWSCache(urls: string[]): Promise<void> {
    // AWS CloudFront 预热实现
    this.logger.warn('AWS CloudFront warmup not implemented');
  }

  private async purgeAWSCache(urls: string[]): Promise<void> {
    // AWS CloudFront 清除实现
    this.logger.warn('AWS CloudFront purge not implemented');
  }

  private async getCloudflareStats(timeRange?: { start: Date; end: Date }): Promise<CDNStats> {
    // Cloudflare 统计实现
    return this.getDefaultStats();
  }

  private async getAWSStats(timeRange?: { start: Date; end: Date }): Promise<CDNStats> {
    // AWS CloudFront 统计实现
    return this.getDefaultStats();
  }

  private getDefaultStats(): CDNStats {
    return {
      totalRequests: 0,
      cacheHitRate: 0,
      bandwidth: 0,
      topFiles: [],
      geographicDistribution: {},
    };
  }

  private matchPattern(fileName: string, pattern: string): boolean {
    // 简单的通配符匹配
    const regex = new RegExp(pattern.replace(/\*/g, '.*').replace(/\?/g, '.'));
    return regex.test(fileName);
  }

  private initializeDefaultCacheRules(): void {
    // 图片文件 - 长期缓存
    this.setCacheRule({
      pattern: '*.{jpg,jpeg,png,gif,webp,svg}',
      ttl: 86400 * 30, // 30天
      browserTtl: 86400 * 7, // 7天
    });

    // 视频文件 - 长期缓存
    this.setCacheRule({
      pattern: '*.{mp4,webm,avi,mov}',
      ttl: 86400 * 30, // 30天
      browserTtl: 86400 * 7, // 7天
    });

    // 音频文件 - 长期缓存
    this.setCacheRule({
      pattern: '*.{mp3,wav,ogg,aac}',
      ttl: 86400 * 30, // 30天
      browserTtl: 86400 * 7, // 7天
    });

    // 文档文件 - 中期缓存
    this.setCacheRule({
      pattern: '*.{pdf,doc,docx,ppt,pptx,xls,xlsx}',
      ttl: 86400 * 7, // 7天
      browserTtl: 86400, // 1天
    });

    // 3D模型文件 - 长期缓存
    this.setCacheRule({
      pattern: '*.{gltf,glb,fbx,obj}',
      ttl: 86400 * 30, // 30天
      browserTtl: 86400 * 7, // 7天
    });

    // 临时文件 - 短期缓存
    this.setCacheRule({
      pattern: '*/temp/*',
      ttl: 3600, // 1小时
      browserTtl: 300, // 5分钟
    });
  }
}
