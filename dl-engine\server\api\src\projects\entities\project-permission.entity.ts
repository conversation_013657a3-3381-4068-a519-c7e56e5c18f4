import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
  Unique
} from 'typeorm'
import { User } from '../../users/entities/user.entity'
import { Project } from './project.entity'

/**
 * 权限类型枚举
 */
export enum PermissionType {
  OWNER = 'owner',           // 所有者
  ADMIN = 'admin',           // 管理员
  EDITOR = 'editor',         // 编辑者
  VIEWER = 'viewer',         // 查看者
  COMMENTER = 'commenter'    // 评论者
}

/**
 * 权限来源枚举
 */
export enum PermissionSource {
  DIRECT = 'direct',         // 直接授权
  INHERITED = 'inherited',   // 继承权限
  ORGANIZATION = 'organization', // 组织权限
  PUBLIC = 'public'          // 公开权限
}

/**
 * 项目权限实体
 * 
 * 管理用户对项目的访问权限
 */
@Entity('project_permissions')
@Unique(['projectId', 'userId'])
@Index(['projectId', 'type'])
@Index(['userId', 'type'])
export class ProjectPermission {
  @PrimaryGeneratedColumn('uuid')
  id: string

  /**
   * 项目ID
   */
  @Column({ type: 'uuid' })
  projectId: string

  /**
   * 用户ID
   */
  @Column({ type: 'uuid' })
  userId: string

  /**
   * 权限类型
   */
  @Column({
    type: 'enum',
    enum: PermissionType
  })
  type: PermissionType

  /**
   * 权限来源
   */
  @Column({
    type: 'enum',
    enum: PermissionSource,
    default: PermissionSource.DIRECT
  })
  source: PermissionSource

  /**
   * 授权者ID
   */
  @Column({ type: 'uuid', nullable: true })
  grantedById?: string

  /**
   * 权限详细设置
   */
  @Column({ type: 'json', nullable: true })
  permissions?: {
    canRead: boolean
    canWrite: boolean
    canDelete: boolean
    canShare: boolean
    canManagePermissions: boolean
    canManageVersions: boolean
    canManageCollaborators: boolean
    canPublish: boolean
    canArchive: boolean
    canExport: boolean
    canComment: boolean
    canViewAnalytics: boolean
    [key: string]: boolean
  }

  /**
   * 权限限制
   */
  @Column({ type: 'json', nullable: true })
  restrictions?: {
    ipWhitelist?: string[]
    timeRestriction?: {
      startTime: string // HH:mm
      endTime: string   // HH:mm
      timezone: string
      days: number[]    // 0-6, 0为周日
    }
    expiresAt?: Date
    maxSessions?: number
    allowedActions?: string[]
    deniedActions?: string[]
  }

  /**
   * 是否激活
   */
  @Column({ type: 'boolean', default: true })
  isActive: boolean

  /**
   * 权限过期时间
   */
  @Column({ type: 'timestamp', nullable: true })
  expiresAt?: Date

  /**
   * 最后使用时间
   */
  @Column({ type: 'timestamp', nullable: true })
  lastUsedAt?: Date

  /**
   * 创建时间
   */
  @CreateDateColumn()
  createdAt: Date

  /**
   * 更新时间
   */
  @UpdateDateColumn()
  updatedAt: Date

  // 关联关系
  @ManyToOne(() => Project, project => project.permissions, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'projectId' })
  project: Project

  @ManyToOne(() => User)
  @JoinColumn({ name: 'userId' })
  user: User

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'grantedById' })
  grantedBy?: User

  /**
   * 检查是否有指定权限
   */
  hasPermission(permission: string): boolean {
    if (!this.isActive) return false
    if (this.isExpired()) return false

    // 所有者拥有所有权限
    if (this.type === PermissionType.OWNER) return true

    // 检查具体权限设置
    if (this.permissions && typeof this.permissions[permission] === 'boolean') {
      return this.permissions[permission]
    }

    // 根据权限类型返回默认权限
    return this.getDefaultPermissions()[permission] || false
  }

  /**
   * 检查权限是否过期
   */
  isExpired(): boolean {
    if (!this.expiresAt) return false
    return new Date() > this.expiresAt
  }

  /**
   * 检查是否可以读取
   */
  canRead(): boolean {
    return this.hasPermission('canRead')
  }

  /**
   * 检查是否可以写入
   */
  canWrite(): boolean {
    return this.hasPermission('canWrite')
  }

  /**
   * 检查是否可以删除
   */
  canDelete(): boolean {
    return this.hasPermission('canDelete')
  }

  /**
   * 检查是否可以分享
   */
  canShare(): boolean {
    return this.hasPermission('canShare')
  }

  /**
   * 检查是否可以管理权限
   */
  canManagePermissions(): boolean {
    return this.hasPermission('canManagePermissions')
  }

  /**
   * 检查是否可以管理版本
   */
  canManageVersions(): boolean {
    return this.hasPermission('canManageVersions')
  }

  /**
   * 检查是否可以管理协作者
   */
  canManageCollaborators(): boolean {
    return this.hasPermission('canManageCollaborators')
  }

  /**
   * 检查是否可以发布
   */
  canPublish(): boolean {
    return this.hasPermission('canPublish')
  }

  /**
   * 检查是否可以归档
   */
  canArchive(): boolean {
    return this.hasPermission('canArchive')
  }

  /**
   * 检查是否可以导出
   */
  canExport(): boolean {
    return this.hasPermission('canExport')
  }

  /**
   * 检查是否可以评论
   */
  canComment(): boolean {
    return this.hasPermission('canComment')
  }

  /**
   * 检查是否可以查看分析数据
   */
  canViewAnalytics(): boolean {
    return this.hasPermission('canViewAnalytics')
  }

  /**
   * 获取默认权限设置
   */
  private getDefaultPermissions(): Record<string, boolean> {
    const defaultPermissions = {
      canRead: false,
      canWrite: false,
      canDelete: false,
      canShare: false,
      canManagePermissions: false,
      canManageVersions: false,
      canManageCollaborators: false,
      canPublish: false,
      canArchive: false,
      canExport: false,
      canComment: false,
      canViewAnalytics: false
    }

    switch (this.type) {
      case PermissionType.OWNER:
        return Object.keys(defaultPermissions).reduce((acc, key) => {
          acc[key] = true
          return acc
        }, {} as Record<string, boolean>)

      case PermissionType.ADMIN:
        return {
          ...defaultPermissions,
          canRead: true,
          canWrite: true,
          canDelete: true,
          canShare: true,
          canManagePermissions: true,
          canManageVersions: true,
          canManageCollaborators: true,
          canPublish: true,
          canArchive: true,
          canExport: true,
          canComment: true,
          canViewAnalytics: true
        }

      case PermissionType.EDITOR:
        return {
          ...defaultPermissions,
          canRead: true,
          canWrite: true,
          canShare: true,
          canManageVersions: true,
          canExport: true,
          canComment: true
        }

      case PermissionType.VIEWER:
        return {
          ...defaultPermissions,
          canRead: true,
          canExport: true,
          canComment: true
        }

      case PermissionType.COMMENTER:
        return {
          ...defaultPermissions,
          canRead: true,
          canComment: true
        }

      default:
        return defaultPermissions
    }
  }

  /**
   * 更新最后使用时间
   */
  updateLastUsed(): void {
    this.lastUsedAt = new Date()
  }

  /**
   * 检查IP是否在白名单中
   */
  isIpAllowed(ip: string): boolean {
    if (!this.restrictions?.ipWhitelist) return true
    return this.restrictions.ipWhitelist.includes(ip)
  }

  /**
   * 检查当前时间是否在允许的时间范围内
   */
  isTimeAllowed(): boolean {
    if (!this.restrictions?.timeRestriction) return true

    const now = new Date()
    const restriction = this.restrictions.timeRestriction
    
    // 检查星期几
    if (!restriction.days.includes(now.getDay())) return false

    // 检查时间范围
    const currentTime = now.toTimeString().slice(0, 5) // HH:mm
    return currentTime >= restriction.startTime && currentTime <= restriction.endTime
  }

  /**
   * 检查是否允许执行指定操作
   */
  isActionAllowed(action: string): boolean {
    if (!this.restrictions) return true

    // 检查拒绝的操作
    if (this.restrictions.deniedActions?.includes(action)) return false

    // 检查允许的操作
    if (this.restrictions.allowedActions) {
      return this.restrictions.allowedActions.includes(action)
    }

    return true
  }
}
