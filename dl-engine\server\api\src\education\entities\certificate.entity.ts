import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, Index } from 'typeorm'

@Entity('certificates')
@Index(['studentId'])
@Index(['type'])
@Index(['status'])
export class Certificate {
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Column({ length: 200 })
  title: string

  @Column({ type: 'text', nullable: true })
  description: string

  @Column({ 
    type: 'enum',
    enum: ['course_completion', 'skill_certification', 'program_graduation', 'achievement_badge'],
    default: 'course_completion'
  })
  type: string

  @Column({ 
    type: 'enum',
    enum: ['draft', 'issued', 'revoked', 'expired'],
    default: 'draft'
  })
  status: string

  @Column({ length: 100, unique: true })
  certificateNumber: string

  @Column({ type: 'text', nullable: true })
  templateUrl: string

  @Column({ type: 'text', nullable: true })
  pdfUrl: string

  @Column({ type: 'text', nullable: true })
  verificationUrl: string

  @Column({ type: 'json', nullable: true })
  criteria: any // 获得条件

  @Column({ type: 'json', nullable: true })
  skills: any // 认证技能

  @Column({ type: 'datetime' })
  issuedAt: Date

  @Column({ type: 'datetime', nullable: true })
  expiresAt: Date

  @Column({ type: 'json', nullable: true })
  metadata: any

  // 关联关系
  @Column({ name: 'student_id' })
  studentId: string

  @Column({ name: 'course_id', nullable: true })
  courseId: string

  @Column({ name: 'learning_path_id', nullable: true })
  learningPathId: string

  @Column({ name: 'issued_by' })
  issuedBy: string // 颁发机构/教师ID

  @CreateDateColumn()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date
}
