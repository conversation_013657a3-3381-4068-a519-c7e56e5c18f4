/*
DL-Engine Instance Services
数字化学习引擎 - 实例服务集成

整合实例管理、网络同步、物理同步和扩缩容管理服务
*/

import { Application } from '@feathersjs/feathers'
import logger from '@ir-engine/server-core/src/ServerLogger'

// 导入服务
import InstanceManagerService from './world/instance-manager'
import NetworkSyncService from './world/network-sync'
import PhysicsSyncService from './world/physics-sync'
import ScalingManagerService from './world/scaling-manager'

/**
 * 实例服务配置接口
 */
export interface InstanceServicesConfig {
  instanceManager: {
    enabled: boolean
    defaultResourceConfig: {
      cpu: number
      memory: number
      maxUsers: number
      priority: number
    }
  }
  networkSync: {
    enabled: boolean
    maxClientsPerInstance: number
    heartbeatInterval: number
    stateUpdateInterval: number
    compressionEnabled: boolean
  }
  physicsSync: {
    enabled: boolean
    syncRate: number
    interpolationEnabled: boolean
    predictionEnabled: boolean
    compressionLevel: number
  }
  scalingManager: {
    enabled: boolean
    defaultStrategy: string
    minInstances: number
    maxInstances: number
    evaluationPeriod: number
  }
}

/**
 * 实例服务管理器类
 */
export class InstanceServices {
  private app: Application
  private config: InstanceServicesConfig
  private services: Map<string, any> = new Map()
  private isInitialized: boolean = false

  constructor(app: Application, config?: Partial<InstanceServicesConfig>) {
    this.app = app
    this.config = this.mergeWithDefaultConfig(config)
  }

  /**
   * 初始化所有实例服务
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      logger.warn('实例服务已经初始化')
      return
    }

    try {
      logger.info('开始初始化实例服务')

      // 初始化实例管理服务
      if (this.config.instanceManager.enabled) {
        await this.initializeInstanceManager()
      }

      // 初始化网络同步服务
      if (this.config.networkSync.enabled) {
        await this.initializeNetworkSync()
      }

      // 初始化物理同步服务
      if (this.config.physicsSync.enabled) {
        await this.initializePhysicsSync()
      }

      // 初始化扩缩容管理服务
      if (this.config.scalingManager.enabled) {
        await this.initializeScalingManager()
      }

      // 设置服务间的依赖关系
      this.setupServiceDependencies()

      this.isInitialized = true
      logger.info('实例服务初始化完成')

    } catch (error) {
      logger.error('实例服务初始化失败', { error })
      throw error
    }
  }

  /**
   * 启动所有服务
   */
  async start(): Promise<void> {
    if (!this.isInitialized) {
      await this.initialize()
    }

    try {
      logger.info('启动实例服务')

      // 启动实例管理服务
      const instanceManager = this.services.get('instanceManager')
      if (instanceManager && instanceManager.start) {
        await instanceManager.start()
      }

      // 启动网络同步服务
      const networkSync = this.services.get('networkSync')
      if (networkSync && networkSync.start) {
        await networkSync.start()
      }

      // 启动物理同步服务
      const physicsSync = this.services.get('physicsSync')
      if (physicsSync && physicsSync.start) {
        await physicsSync.start()
      }

      // 启动扩缩容管理服务
      const scalingManager = this.services.get('scalingManager')
      if (scalingManager && scalingManager.start) {
        await scalingManager.start()
      }

      logger.info('实例服务启动完成')

    } catch (error) {
      logger.error('实例服务启动失败', { error })
      throw error
    }
  }

  /**
   * 停止所有服务
   */
  async stop(): Promise<void> {
    try {
      logger.info('停止实例服务')

      // 按相反顺序停止服务
      const serviceNames = ['scalingManager', 'physicsSync', 'networkSync', 'instanceManager']
      
      for (const serviceName of serviceNames) {
        const service = this.services.get(serviceName)
        if (service && service.stop) {
          await service.stop()
        }
      }

      logger.info('实例服务停止完成')

    } catch (error) {
      logger.error('实例服务停止失败', { error })
      throw error
    }
  }

  /**
   * 获取服务实例
   */
  getService<T>(serviceName: string): T | null {
    return this.services.get(serviceName) || null
  }

  /**
   * 获取所有服务状态
   */
  getServicesStatus(): Record<string, any> {
    const status: Record<string, any> = {}

    for (const [serviceName, service] of this.services) {
      status[serviceName] = {
        enabled: true,
        running: service.isRunning ? service.isRunning() : true,
        stats: service.getStats ? service.getStats() : null
      }
    }

    return status
  }

  /**
   * 重新加载配置
   */
  async reloadConfig(newConfig: Partial<InstanceServicesConfig>): Promise<void> {
    try {
      logger.info('重新加载实例服务配置')

      // 合并新配置
      this.config = this.mergeWithDefaultConfig(newConfig)

      // 重新配置各个服务
      await this.reconfigureServices()

      logger.info('实例服务配置重新加载完成')

    } catch (error) {
      logger.error('重新加载配置失败', { error })
      throw error
    }
  }

  /**
   * 初始化实例管理服务
   */
  private async initializeInstanceManager(): Promise<void> {
    logger.debug('初始化实例管理服务')
    
    // 注册服务到应用
    InstanceManagerService(this.app)
    
    const instanceManager = this.app.get('instanceManager')
    this.services.set('instanceManager', instanceManager)
    
    logger.debug('实例管理服务初始化完成')
  }

  /**
   * 初始化网络同步服务
   */
  private async initializeNetworkSync(): Promise<void> {
    logger.debug('初始化网络同步服务')
    
    // 注册服务到应用
    NetworkSyncService(this.app)
    
    const networkSync = this.app.get('networkSync')
    this.services.set('networkSync', networkSync)
    
    logger.debug('网络同步服务初始化完成')
  }

  /**
   * 初始化物理同步服务
   */
  private async initializePhysicsSync(): Promise<void> {
    logger.debug('初始化物理同步服务')
    
    // 注册服务到应用
    PhysicsSyncService(this.app)
    
    const physicsSync = this.app.get('physicsSync')
    this.services.set('physicsSync', physicsSync)
    
    logger.debug('物理同步服务初始化完成')
  }

  /**
   * 初始化扩缩容管理服务
   */
  private async initializeScalingManager(): Promise<void> {
    logger.debug('初始化扩缩容管理服务')
    
    // 注册服务到应用
    ScalingManagerService(this.app)
    
    const scalingManager = this.app.get('scalingManager')
    this.services.set('scalingManager', scalingManager)
    
    logger.debug('扩缩容管理服务初始化完成')
  }

  /**
   * 设置服务间的依赖关系
   */
  private setupServiceDependencies(): void {
    logger.debug('设置服务间依赖关系')

    const instanceManager = this.services.get('instanceManager')
    const networkSync = this.services.get('networkSync')
    const physicsSync = this.services.get('physicsSync')
    const scalingManager = this.services.get('scalingManager')

    // 实例管理器监听网络同步事件
    if (instanceManager && networkSync) {
      networkSync.on('client-connected', (clientId: string, userId: string, instanceId: string) => {
        // 更新实例用户数
        instanceManager.updateInstanceUserCount(instanceId, 1)
      })

      networkSync.on('client-disconnected', (clientId: string, userId: string, instanceId: string) => {
        // 更新实例用户数
        instanceManager.updateInstanceUserCount(instanceId, -1)
      })
    }

    // 扩缩容管理器监听实例事件
    if (scalingManager && instanceManager) {
      instanceManager.on('instance-created', (instance: any) => {
        scalingManager.onInstanceCreated(instance)
      })

      instanceManager.on('instance-destroyed', (instanceId: string) => {
        scalingManager.onInstanceDestroyed(instanceId)
      })
    }

    // 物理同步监听网络同步事件
    if (physicsSync && networkSync) {
      networkSync.on('client-state-update', (clientId: string, stateData: any) => {
        physicsSync.handleClientPhysicsUpdate(clientId, stateData)
      })
    }

    logger.debug('服务间依赖关系设置完成')
  }

  /**
   * 重新配置服务
   */
  private async reconfigureServices(): Promise<void> {
    // 重新配置各个服务
    for (const [serviceName, service] of this.services) {
      if (service.updateConfig) {
        const serviceConfig = (this.config as any)[serviceName]
        if (serviceConfig) {
          await service.updateConfig(serviceConfig)
        }
      }
    }
  }

  /**
   * 合并默认配置
   */
  private mergeWithDefaultConfig(config?: Partial<InstanceServicesConfig>): InstanceServicesConfig {
    const defaultConfig: InstanceServicesConfig = {
      instanceManager: {
        enabled: true,
        defaultResourceConfig: {
          cpu: 1,
          memory: 1024,
          maxUsers: 50,
          priority: 5
        }
      },
      networkSync: {
        enabled: true,
        maxClientsPerInstance: 100,
        heartbeatInterval: 30000,
        stateUpdateInterval: 50,
        compressionEnabled: true
      },
      physicsSync: {
        enabled: true,
        syncRate: 60,
        interpolationEnabled: true,
        predictionEnabled: true,
        compressionLevel: 3
      },
      scalingManager: {
        enabled: true,
        defaultStrategy: 'auto_hybrid',
        minInstances: 1,
        maxInstances: 10,
        evaluationPeriod: 30
      }
    }

    return {
      instanceManager: { ...defaultConfig.instanceManager, ...config?.instanceManager },
      networkSync: { ...defaultConfig.networkSync, ...config?.networkSync },
      physicsSync: { ...defaultConfig.physicsSync, ...config?.physicsSync },
      scalingManager: { ...defaultConfig.scalingManager, ...config?.scalingManager }
    }
  }
}

/**
 * 创建并配置实例服务
 */
export function createInstanceServices(app: Application, config?: Partial<InstanceServicesConfig>): InstanceServices {
  return new InstanceServices(app, config)
}

/**
 * 默认导出 - 用于 FeathersJS 服务注册
 */
export default (app: Application): void => {
  // 创建实例服务管理器
  const instanceServices = createInstanceServices(app)
  
  // 注册到应用
  app.set('instanceServices', instanceServices)
  
  // 自动初始化和启动
  instanceServices.initialize().then(() => {
    return instanceServices.start()
  }).catch(error => {
    logger.error('实例服务自动启动失败', { error })
  })

  logger.info('DL-Engine 实例服务已注册')
}
