apiVersion: v1
kind: Secret
metadata:
  name: {{ include "dl-engine.fullname" . }}-secret
  labels:
    {{- include "dl-engine.labels" . | nindent 4 }}
type: Opaque
data:
  # 数据库密码
  MYSQL_PASSWORD: {{ .Values.secrets.app.MYSQL_PASSWORD | b64enc | quote }}
  REDIS_PASSWORD: {{ .Values.secrets.app.REDIS_PASSWORD | b64enc | quote }}
  POSTGRESQL_PASSWORD: {{ .Values.secrets.app.POSTGRESQL_PASSWORD | b64enc | quote }}
  
  # 对象存储密钥
  MINIO_ACCESS_KEY: {{ .Values.secrets.app.MINIO_ACCESS_KEY | b64enc | quote }}
  MINIO_SECRET_KEY: {{ .Values.secrets.app.MINIO_SECRET_KEY | b64enc | quote }}
  
  # JWT密钥
  JWT_SECRET: {{ .Values.secrets.app.JWT_SECRET | b64enc | quote }}
  
  # 短信API密钥
  SMS_API_KEY: {{ .Values.secrets.app.SMS_API_KEY | b64enc | quote }}
  
  # AI服务密钥
  OLLAMA_API_KEY: {{ .Values.secrets.app.OLLAMA_API_KEY | b64enc | quote }}
  
  # 加密密钥
  ENCRYPTION_KEY: {{ randAlphaNum 32 | b64enc | quote }}
  
  # 会话密钥
  SESSION_SECRET: {{ randAlphaNum 64 | b64enc | quote }}
  
  # 数据库连接字符串
  DATABASE_URL: {{ printf "mysql://%s:%s@%s:%s/%s" "dl_engine" .Values.secrets.app.MYSQL_PASSWORD .Values.configMaps.app.MYSQL_HOST .Values.configMaps.app.MYSQL_PORT .Values.configMaps.app.MYSQL_DATABASE | b64enc | quote }}
  
  # Redis连接字符串
  REDIS_URL: {{ printf "redis://:%s@%s:%s" .Values.secrets.app.REDIS_PASSWORD .Values.configMaps.app.REDIS_HOST .Values.configMaps.app.REDIS_PORT | b64enc | quote }}
  
  # PostgreSQL连接字符串
  POSTGRESQL_URL: {{ printf "postgresql://%s:%s@%s:%s/dl_engine_vector" "dl_engine" .Values.secrets.app.POSTGRESQL_PASSWORD .Values.configMaps.app.POSTGRESQL_HOST .Values.configMaps.app.POSTGRESQL_PORT | b64enc | quote }}
  
  # Minio连接配置
  MINIO_ENDPOINT: {{ printf "%s:%s" .Values.configMaps.app.MINIO_HOST .Values.configMaps.app.MINIO_PORT | b64enc | quote }}
  
  # 第三方服务密钥
  GOOGLE_CLIENT_ID: {{ "your-google-client-id" | b64enc | quote }}
  GOOGLE_CLIENT_SECRET: {{ "your-google-client-secret" | b64enc | quote }}
  WECHAT_APP_ID: {{ "your-wechat-app-id" | b64enc | quote }}
  WECHAT_APP_SECRET: {{ "your-wechat-app-secret" | b64enc | quote }}
  
  # 监控密钥
  PROMETHEUS_PASSWORD: {{ "dl-engine-prometheus-password" | b64enc | quote }}
  GRAFANA_PASSWORD: {{ "dl-engine-grafana-password" | b64enc | quote }}
  
  # SSL证书密钥（如果需要）
  TLS_CERT: {{ "" | b64enc | quote }}
  TLS_KEY: {{ "" | b64enc | quote }}
