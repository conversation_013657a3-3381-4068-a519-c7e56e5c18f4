import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';

export enum DocumentType {
  TEXT = 'text',
  PDF = 'pdf',
  WORD = 'word',
  MARKDOWN = 'markdown',
  HTML = 'html',
  CODE = 'code',
  PRESENTATION = 'presentation',
  SPREADSHEET = 'spreadsheet',
}

export enum DocumentStatus {
  UPLOADED = 'uploaded',
  PROCESSING = 'processing',
  INDEXED = 'indexed',
  FAILED = 'failed',
}

@Entity('documents')
@Index(['type'])
@Index(['status'])
@Index(['ownerId'])
@Index(['namespace'])
@Index(['createdAt'])
export class DocumentEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 200 })
  title: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'enum', enum: DocumentType })
  type: DocumentType;

  @Column({ type: 'enum', enum: DocumentStatus, default: DocumentStatus.UPLOADED })
  status: DocumentStatus;

  @Column({ type: 'uuid' })
  ownerId: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  namespace: string;

  @Column({ type: 'varchar', length: 500 })
  filePath: string;

  @Column({ type: 'varchar', length: 200 })
  fileName: string;

  @Column({ type: 'varchar', length: 100 })
  mimeType: string;

  @Column({ type: 'bigint' })
  fileSize: number;

  @Column({ type: 'varchar', length: 32 })
  fileHash: string;

  @Column({ type: 'text', nullable: true })
  extractedText: string;

  @Column({ type: 'json', nullable: true })
  chunks: Array<{
    id: string;
    text: string;
    startIndex: number;
    endIndex: number;
    pageNumber?: number;
    sectionTitle?: string;
    embedding?: number[];
    metadata?: Record<string, any>;
  }>;

  @Column({ type: 'json', nullable: true })
  metadata: {
    author?: string;
    createdDate?: Date;
    modifiedDate?: Date;
    language?: string;
    pageCount?: number;
    wordCount?: number;
    characterCount?: number;
    keywords?: string[];
    categories?: string[];
    tags?: string[];
    summary?: string;
    tableOfContents?: Array<{
      title: string;
      level: number;
      pageNumber?: number;
      startIndex?: number;
    }>;
    images?: Array<{
      id: string;
      caption?: string;
      altText?: string;
      pageNumber?: number;
      position?: {
        x: number;
        y: number;
        width: number;
        height: number;
      };
    }>;
    tables?: Array<{
      id: string;
      caption?: string;
      pageNumber?: number;
      rowCount: number;
      columnCount: number;
      data?: any[][];
    }>;
    links?: Array<{
      text: string;
      url: string;
      type: 'internal' | 'external';
    }>;
  };

  @Column({ type: 'json', nullable: true })
  processingConfig: {
    chunkSize?: number;
    chunkOverlap?: number;
    extractImages?: boolean;
    extractTables?: boolean;
    extractLinks?: boolean;
    ocrEnabled?: boolean;
    languageDetection?: boolean;
    summarization?: boolean;
    keywordExtraction?: boolean;
    entityExtraction?: boolean;
  };

  @Column({ type: 'json', nullable: true })
  processingResult: {
    processingTime?: number;
    chunkCount?: number;
    extractedImageCount?: number;
    extractedTableCount?: number;
    extractedLinkCount?: number;
    detectedLanguage?: string;
    confidence?: number;
    errors?: string[];
    warnings?: string[];
  };

  @Column({ type: 'json', nullable: true })
  searchIndex: {
    terms: Record<string, number>; // 词频统计
    phrases: Record<string, number>; // 短语频率
    entities: Array<{
      text: string;
      type: string;
      frequency: number;
      positions: number[];
    }>;
    topics: Array<{
      name: string;
      confidence: number;
      keywords: string[];
    }>;
  };

  @Column({ type: 'int', default: 0 })
  accessCount: number;

  @Column({ type: 'datetime', nullable: true })
  lastAccessedAt: Date;

  @Column({ type: 'datetime', nullable: true })
  indexedAt: Date;

  @Column({ type: 'boolean', default: true })
  isActive: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // 虚拟字段
  get isIndexed(): boolean {
    return this.status === DocumentStatus.INDEXED;
  }

  get isProcessing(): boolean {
    return this.status === DocumentStatus.PROCESSING;
  }

  get hasFailed(): boolean {
    return this.status === DocumentStatus.FAILED;
  }

  get chunkCount(): number {
    return this.chunks?.length || 0;
  }

  get wordCount(): number {
    return this.metadata?.wordCount || 0;
  }

  get pageCount(): number {
    return this.metadata?.pageCount || 0;
  }

  get fileSizeFormatted(): string {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = this.fileSize;
    let unitIndex = 0;
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }
    
    return `${size.toFixed(2)} ${units[unitIndex]}`;
  }

  get extractedTextPreview(): string {
    if (!this.extractedText) return '';
    return this.extractedText.length > 500 
      ? this.extractedText.substring(0, 500) + '...'
      : this.extractedText;
  }

  get hasImages(): boolean {
    return (this.metadata?.images?.length || 0) > 0;
  }

  get hasTables(): boolean {
    return (this.metadata?.tables?.length || 0) > 0;
  }

  get hasLinks(): boolean {
    return (this.metadata?.links?.length || 0) > 0;
  }
}
