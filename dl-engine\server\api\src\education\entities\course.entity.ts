import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  ManyToMany,
  JoinTable,
  JoinColumn,
  Index
} from 'typeorm'
import { User } from '../../users/entities/user.entity'
import { Lesson } from './lesson.entity'
import { Assignment } from './assignment.entity'
import { Enrollment } from './enrollment.entity'
import { LearningProgress } from './learning-progress.entity'
import { Achievement } from './achievement.entity'
import { Classroom } from './classroom.entity'

/**
 * 课程状态枚举
 */
export enum CourseStatus {
  DRAFT = 'draft',             // 草稿
  PUBLISHED = 'published',     // 已发布
  ARCHIVED = 'archived',       // 已归档
  DELETED = 'deleted'          // 已删除
}

/**
 * 课程难度枚举
 */
export enum CourseDifficulty {
  BEGINNER = 'beginner',       // 初级
  INTERMEDIATE = 'intermediate', // 中级
  ADVANCED = 'advanced',       // 高级
  EXPERT = 'expert'            // 专家级
}

/**
 * 课程类型枚举
 */
export enum CourseType {
  SELF_PACED = 'self_paced',   // 自主学习
  INSTRUCTOR_LED = 'instructor_led', // 教师指导
  BLENDED = 'blended',         // 混合式
  WORKSHOP = 'workshop',       // 工作坊
  SEMINAR = 'seminar'          // 研讨会
}

/**
 * 课程实体
 * 
 * 存储课程的基本信息和配置
 */
@Entity('courses')
@Index(['instructorId', 'status'])
@Index(['status', 'visibility'])
@Index(['subject', 'gradeLevel'])
@Index(['createdAt'])
export class Course {
  @PrimaryGeneratedColumn('uuid')
  id: string

  /**
   * 课程标题
   */
  @Column({ type: 'varchar', length: 200 })
  title: string

  /**
   * 课程描述
   */
  @Column({ type: 'text', nullable: true })
  description?: string

  /**
   * 课程简介
   */
  @Column({ type: 'text', nullable: true })
  summary?: string

  /**
   * 课程状态
   */
  @Column({
    type: 'enum',
    enum: CourseStatus,
    default: CourseStatus.DRAFT
  })
  status: CourseStatus

  /**
   * 课程类型
   */
  @Column({
    type: 'enum',
    enum: CourseType,
    default: CourseType.SELF_PACED
  })
  type: CourseType

  /**
   * 课程难度
   */
  @Column({
    type: 'enum',
    enum: CourseDifficulty,
    default: CourseDifficulty.BEGINNER
  })
  difficulty: CourseDifficulty

  /**
   * 授课教师ID
   */
  @Column({ type: 'uuid' })
  instructorId: string

  /**
   * 课程封面图URL
   */
  @Column({ type: 'text', nullable: true })
  coverImageUrl?: string

  /**
   * 课程预告视频URL
   */
  @Column({ type: 'text', nullable: true })
  trailerVideoUrl?: string

  /**
   * 学科领域
   */
  @Column({ type: 'varchar', length: 100 })
  subject: string

  /**
   * 适用年级
   */
  @Column({ type: 'json' })
  gradeLevel: string[]

  /**
   * 课程标签
   */
  @Column({ type: 'json', nullable: true })
  tags?: string[]

  /**
   * 学习目标
   */
  @Column({ type: 'json' })
  learningObjectives: string[]

  /**
   * 先修要求
   */
  @Column({ type: 'json', nullable: true })
  prerequisites?: string[]

  /**
   * 课程大纲
   */
  @Column({ type: 'json', nullable: true })
  syllabus?: Array<{
    week: number
    title: string
    description: string
    topics: string[]
    assignments?: string[]
    readings?: string[]
  }>

  /**
   * 课程设置
   */
  @Column({ type: 'json', nullable: true })
  settings?: {
    // 可见性设置
    visibility: 'public' | 'private' | 'unlisted'
    // 注册设置
    enrollment: {
      open: boolean
      requireApproval: boolean
      maxStudents?: number
      startDate?: Date
      endDate?: Date
      selfEnrollment: boolean
    }
    // 评分设置
    grading: {
      passingGrade: number
      gradingScale: 'percentage' | 'points' | 'letter'
      showGradesToStudents: boolean
      allowLateSubmissions: boolean
      latePenalty?: number
    }
    // 讨论设置
    discussions: {
      enabled: boolean
      moderationRequired: boolean
      allowAnonymous: boolean
    }
    // 证书设置
    certificates: {
      enabled: boolean
      template?: string
      requirements: {
        completionRate: number
        minimumGrade: number
        requiredAssignments: string[]
      }
    }
  }

  /**
   * 课程统计
   */
  @Column({ type: 'json', nullable: true })
  statistics?: {
    totalStudents: number
    activeStudents: number
    completedStudents: number
    averageProgress: number
    averageGrade: number
    totalLessons: number
    totalAssignments: number
    totalDuration: number // 分钟
    lastActivity: Date
  }

  /**
   * 课程元数据
   */
  @Column({ type: 'json', nullable: true })
  metadata?: {
    // 教育标准对齐
    standards?: Array<{
      framework: string // Common Core, NGSS, etc.
      code: string
      description: string
    }>
    // 语言和本地化
    language: string
    locale: string
    // 技术要求
    technicalRequirements?: {
      devices: string[]
      browsers: string[]
      plugins: string[]
      bandwidth: string
    }
    // 无障碍支持
    accessibility?: {
      screenReaderSupport: boolean
      keyboardNavigation: boolean
      closedCaptions: boolean
      audioDescriptions: boolean
    }
  }

  /**
   * 预计学习时间（小时）
   */
  @Column({ type: 'int', nullable: true })
  estimatedHours?: number

  /**
   * 课程价格（分）
   */
  @Column({ type: 'int', default: 0 })
  price: number

  /**
   * 货币代码
   */
  @Column({ type: 'varchar', length: 3, default: 'CNY' })
  currency: string

  /**
   * 是否免费
   */
  @Column({ type: 'boolean', default: true })
  isFree: boolean

  /**
   * 开始日期
   */
  @Column({ type: 'timestamp', nullable: true })
  startDate?: Date

  /**
   * 结束日期
   */
  @Column({ type: 'timestamp', nullable: true })
  endDate?: Date

  /**
   * 注册截止日期
   */
  @Column({ type: 'timestamp', nullable: true })
  enrollmentDeadline?: Date

  /**
   * 发布时间
   */
  @Column({ type: 'timestamp', nullable: true })
  publishedAt?: Date

  /**
   * 归档时间
   */
  @Column({ type: 'timestamp', nullable: true })
  archivedAt?: Date

  /**
   * 创建时间
   */
  @CreateDateColumn()
  createdAt: Date

  /**
   * 更新时间
   */
  @UpdateDateColumn()
  updatedAt: Date

  // 关联关系
  @ManyToOne(() => User)
  @JoinColumn({ name: 'instructorId' })
  instructor: User

  @OneToMany(() => Lesson, lesson => lesson.course, { cascade: true })
  lessons: Lesson[]

  @OneToMany(() => Assignment, assignment => assignment.course, { cascade: true })
  assignments: Assignment[]

  @OneToMany(() => Enrollment, enrollment => enrollment.course, { cascade: true })
  enrollments: Enrollment[]

  @OneToMany(() => LearningProgress, progress => progress.course, { cascade: true })
  learningProgress: LearningProgress[]

  @ManyToMany(() => Achievement)
  @JoinTable({
    name: 'course_achievements',
    joinColumn: { name: 'courseId', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'achievementId', referencedColumnName: 'id' }
  })
  achievements: Achievement[]

  @ManyToMany(() => Classroom, classroom => classroom.courses)
  classrooms: Classroom[]

  /**
   * 检查课程是否已发布
   */
  isPublished(): boolean {
    return this.status === CourseStatus.PUBLISHED
  }

  /**
   * 检查课程是否可注册
   */
  canEnroll(): boolean {
    if (!this.isPublished()) return false
    if (!this.settings?.enrollment?.open) return false
    
    const now = new Date()
    if (this.enrollmentDeadline && now > this.enrollmentDeadline) return false
    if (this.endDate && now > this.endDate) return false
    
    return true
  }

  /**
   * 检查是否已开始
   */
  hasStarted(): boolean {
    if (!this.startDate) return true
    return new Date() >= this.startDate
  }

  /**
   * 检查是否已结束
   */
  hasEnded(): boolean {
    if (!this.endDate) return false
    return new Date() > this.endDate
  }

  /**
   * 检查是否正在进行中
   */
  isActive(): boolean {
    return this.hasStarted() && !this.hasEnded()
  }

  /**
   * 获取课程进度百分比
   */
  getProgressPercentage(): number {
    if (!this.startDate || !this.endDate) return 0
    
    const now = new Date()
    const total = this.endDate.getTime() - this.startDate.getTime()
    const elapsed = now.getTime() - this.startDate.getTime()
    
    return Math.min(100, Math.max(0, (elapsed / total) * 100))
  }

  /**
   * 获取剩余天数
   */
  getDaysRemaining(): number | null {
    if (!this.endDate) return null
    
    const now = new Date()
    const diff = this.endDate.getTime() - now.getTime()
    return Math.max(0, Math.ceil(diff / (1000 * 60 * 60 * 24)))
  }

  /**
   * 更新统计信息
   */
  updateStatistics(field: keyof Course['statistics'], value: number): void {
    if (!this.statistics) {
      this.statistics = {
        totalStudents: 0,
        activeStudents: 0,
        completedStudents: 0,
        averageProgress: 0,
        averageGrade: 0,
        totalLessons: 0,
        totalAssignments: 0,
        totalDuration: 0,
        lastActivity: new Date()
      }
    }

    this.statistics[field] = value
    this.statistics.lastActivity = new Date()
  }

  /**
   * 检查是否达到最大学生数
   */
  isAtCapacity(): boolean {
    const maxStudents = this.settings?.enrollment?.maxStudents
    if (!maxStudents) return false
    
    return (this.statistics?.totalStudents || 0) >= maxStudents
  }

  /**
   * 获取课程URL友好的slug
   */
  getSlug(): string {
    return this.title
      .toLowerCase()
      .replace(/[^a-z0-9\u4e00-\u9fa5]/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '')
  }

  /**
   * 获取格式化的价格
   */
  getFormattedPrice(): string {
    if (this.isFree) return '免费'
    
    const price = this.price / 100 // 转换为元
    return `¥${price.toFixed(2)}`
  }
}
