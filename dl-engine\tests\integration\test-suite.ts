/**
 * DL-Engine 全系统集成测试套件
 * 验证所有模块的协同工作和端到端功能
 */

import { describe, test, expect, beforeAll, afterAll, beforeEach, afterEach } from 'vitest'
import axios from 'axios'
import { io, Socket } from 'socket.io-client'
import { WebDriver, Builder, By, until } from 'selenium-webdriver'
import { Options as ChromeOptions } from 'selenium-webdriver/chrome'

// 测试配置
const TEST_CONFIG = {
  // 服务端点
  API_BASE_URL: process.env.TEST_API_URL || 'http://localhost:3030',
  AUTH_BASE_URL: process.env.TEST_AUTH_URL || 'http://localhost:3031',
  CLIENT_BASE_URL: process.env.TEST_CLIENT_URL || 'http://localhost:3000',
  WEBSOCKET_URL: process.env.TEST_WS_URL || 'ws://localhost:8080',
  
  // 测试用户
  TEST_USERS: {
    TEACHER: {
      username: 'test_teacher',
      password: 'test_password_123',
      email: '<EMAIL>',
      phone: '+8613800000001'
    },
    STUDENT: {
      username: 'test_student',
      password: 'test_password_123',
      email: '<EMAIL>',
      phone: '+8613800000002'
    },
    ADMIN: {
      username: 'test_admin',
      password: 'admin_password_123',
      email: '<EMAIL>',
      phone: '+8613800000000'
    }
  },
  
  // 测试超时
  TIMEOUT: {
    API: 10000,
    WEBSOCKET: 5000,
    PAGE_LOAD: 30000,
    ELEMENT_WAIT: 10000
  }
}

// 测试工具类
class TestUtils {
  static async waitForService(url: string, timeout = 30000): Promise<boolean> {
    const startTime = Date.now()
    while (Date.now() - startTime < timeout) {
      try {
        await axios.get(`${url}/health`, { timeout: 5000 })
        return true
      } catch (error) {
        await new Promise(resolve => setTimeout(resolve, 1000))
      }
    }
    return false
  }

  static async createTestUser(userData: any): Promise<string> {
    const response = await axios.post(`${TEST_CONFIG.AUTH_BASE_URL}/register`, userData)
    expect(response.status).toBe(201)
    return response.data.data.token
  }

  static async loginUser(credentials: any): Promise<string> {
    const response = await axios.post(`${TEST_CONFIG.AUTH_BASE_URL}/login`, credentials)
    expect(response.status).toBe(200)
    return response.data.data.token
  }

  static async createTestCourse(token: string, courseData: any): Promise<string> {
    const response = await axios.post(
      `${TEST_CONFIG.API_BASE_URL}/api/courses`,
      courseData,
      { headers: { Authorization: `Bearer ${token}` } }
    )
    expect(response.status).toBe(201)
    return response.data.data.id
  }

  static async createTestScene(token: string, sceneData: any): Promise<string> {
    const response = await axios.post(
      `${TEST_CONFIG.API_BASE_URL}/api/scenes`,
      sceneData,
      { headers: { Authorization: `Bearer ${token}` } }
    )
    expect(response.status).toBe(201)
    return response.data.data.id
  }
}

// 全局测试设置
describe('DL-Engine 全系统集成测试', () => {
  let teacherToken: string
  let studentToken: string
  let adminToken: string
  let driver: WebDriver
  let socket: Socket

  beforeAll(async () => {
    console.log('🚀 开始全系统集成测试...')
    
    // 等待所有服务启动
    console.log('⏳ 等待服务启动...')
    const services = [
      TEST_CONFIG.API_BASE_URL,
      TEST_CONFIG.AUTH_BASE_URL,
      TEST_CONFIG.CLIENT_BASE_URL
    ]
    
    for (const service of services) {
      const isReady = await TestUtils.waitForService(service)
      if (!isReady) {
        throw new Error(`服务未启动: ${service}`)
      }
    }
    console.log('✅ 所有服务已启动')

    // 创建测试用户
    console.log('👥 创建测试用户...')
    try {
      teacherToken = await TestUtils.createTestUser({
        ...TEST_CONFIG.TEST_USERS.TEACHER,
        role: 'teacher'
      })
      studentToken = await TestUtils.createTestUser({
        ...TEST_CONFIG.TEST_USERS.STUDENT,
        role: 'student'
      })
      adminToken = await TestUtils.createTestUser({
        ...TEST_CONFIG.TEST_USERS.ADMIN,
        role: 'admin'
      })
      console.log('✅ 测试用户创建完成')
    } catch (error) {
      // 如果用户已存在，尝试登录
      console.log('🔄 用户已存在，尝试登录...')
      teacherToken = await TestUtils.loginUser(TEST_CONFIG.TEST_USERS.TEACHER)
      studentToken = await TestUtils.loginUser(TEST_CONFIG.TEST_USERS.STUDENT)
      adminToken = await TestUtils.loginUser(TEST_CONFIG.TEST_USERS.ADMIN)
      console.log('✅ 用户登录完成')
    }

    // 初始化WebDriver
    console.log('🌐 初始化浏览器...')
    const chromeOptions = new ChromeOptions()
    chromeOptions.addArguments('--headless')
    chromeOptions.addArguments('--no-sandbox')
    chromeOptions.addArguments('--disable-dev-shm-usage')
    chromeOptions.addArguments('--disable-gpu')
    chromeOptions.addArguments('--window-size=1920,1080')
    
    driver = await new Builder()
      .forBrowser('chrome')
      .setChromeOptions(chromeOptions)
      .build()
    console.log('✅ 浏览器初始化完成')
  }, 60000)

  afterAll(async () => {
    console.log('🧹 清理测试环境...')
    
    if (socket) {
      socket.disconnect()
    }
    
    if (driver) {
      await driver.quit()
    }
    
    console.log('✅ 测试环境清理完成')
  })

  describe('🔐 认证系统测试', () => {
    test('用户注册和登录流程', async () => {
      // 测试注册
      const newUser = {
        username: `test_user_${Date.now()}`,
        password: 'test_password_123',
        email: `test_${Date.now()}@example.com`,
        phone: `+861380000${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}`
      }

      const registerResponse = await axios.post(
        `${TEST_CONFIG.AUTH_BASE_URL}/register`,
        newUser
      )
      expect(registerResponse.status).toBe(201)
      expect(registerResponse.data.success).toBe(true)
      expect(registerResponse.data.data.token).toBeDefined()

      // 测试登录
      const loginResponse = await axios.post(
        `${TEST_CONFIG.AUTH_BASE_URL}/login`,
        {
          username: newUser.username,
          password: newUser.password
        }
      )
      expect(loginResponse.status).toBe(200)
      expect(loginResponse.data.success).toBe(true)
      expect(loginResponse.data.data.token).toBeDefined()
    })

    test('手机号验证码登录', async () => {
      // 发送验证码
      const sendCodeResponse = await axios.post(
        `${TEST_CONFIG.AUTH_BASE_URL}/send-code`,
        { phone: TEST_CONFIG.TEST_USERS.TEACHER.phone }
      )
      expect(sendCodeResponse.status).toBe(200)

      // 注意：在实际测试中，这里需要获取真实的验证码
      // 这里使用测试环境的固定验证码
      const loginResponse = await axios.post(
        `${TEST_CONFIG.AUTH_BASE_URL}/login-phone`,
        {
          phone: TEST_CONFIG.TEST_USERS.TEACHER.phone,
          code: '123456' // 测试环境固定验证码
        }
      )
      expect(loginResponse.status).toBe(200)
      expect(loginResponse.data.success).toBe(true)
    })

    test('Token刷新机制', async () => {
      const refreshResponse = await axios.post(
        `${TEST_CONFIG.AUTH_BASE_URL}/refresh`,
        {},
        { headers: { Authorization: `Bearer ${teacherToken}` } }
      )
      expect(refreshResponse.status).toBe(200)
      expect(refreshResponse.data.data.token).toBeDefined()
    })
  })

  describe('📚 课程管理测试', () => {
    let courseId: string

    test('创建课程', async () => {
      const courseData = {
        title: '测试课程',
        description: '这是一个测试课程',
        category: 'technology',
        level: 'beginner',
        duration: 60,
        tags: ['测试', '编程']
      }

      courseId = await TestUtils.createTestCourse(teacherToken, courseData)
      expect(courseId).toBeDefined()
    })

    test('获取课程列表', async () => {
      const response = await axios.get(
        `${TEST_CONFIG.API_BASE_URL}/api/courses`,
        { headers: { Authorization: `Bearer ${teacherToken}` } }
      )
      expect(response.status).toBe(200)
      expect(Array.isArray(response.data.data)).toBe(true)
    })

    test('学生报名课程', async () => {
      const enrollResponse = await axios.post(
        `${TEST_CONFIG.API_BASE_URL}/api/courses/${courseId}/enroll`,
        {},
        { headers: { Authorization: `Bearer ${studentToken}` } }
      )
      expect(enrollResponse.status).toBe(200)
    })

    test('获取学习进度', async () => {
      const progressResponse = await axios.get(
        `${TEST_CONFIG.API_BASE_URL}/api/courses/${courseId}/progress`,
        { headers: { Authorization: `Bearer ${studentToken}` } }
      )
      expect(progressResponse.status).toBe(200)
      expect(progressResponse.data.data.progress).toBeDefined()
    })
  })

  describe('🎮 3D场景测试', () => {
    let sceneId: string

    test('创建3D场景', async () => {
      const sceneData = {
        name: '测试场景',
        description: '这是一个测试3D场景',
        template: 'basic_classroom',
        settings: {
          maxParticipants: 20,
          enablePhysics: true,
          enableVoiceChat: true
        }
      }

      sceneId = await TestUtils.createTestScene(teacherToken, sceneData)
      expect(sceneId).toBeDefined()
    })

    test('加载场景资源', async () => {
      const assetsResponse = await axios.get(
        `${TEST_CONFIG.API_BASE_URL}/api/scenes/${sceneId}/assets`,
        { headers: { Authorization: `Bearer ${teacherToken}` } }
      )
      expect(assetsResponse.status).toBe(200)
      expect(Array.isArray(assetsResponse.data.data)).toBe(true)
    })

    test('场景对象操作', async () => {
      // 添加对象
      const addObjectResponse = await axios.post(
        `${TEST_CONFIG.API_BASE_URL}/api/scenes/${sceneId}/objects`,
        {
          type: 'box',
          position: { x: 0, y: 1, z: 0 },
          rotation: { x: 0, y: 0, z: 0, w: 1 },
          scale: { x: 1, y: 1, z: 1 },
          material: { color: '#ff0000' }
        },
        { headers: { Authorization: `Bearer ${teacherToken}` } }
      )
      expect(addObjectResponse.status).toBe(201)

      const objectId = addObjectResponse.data.data.id

      // 更新对象
      const updateObjectResponse = await axios.put(
        `${TEST_CONFIG.API_BASE_URL}/api/scenes/${sceneId}/objects/${objectId}`,
        {
          position: { x: 1, y: 1, z: 1 }
        },
        { headers: { Authorization: `Bearer ${teacherToken}` } }
      )
      expect(updateObjectResponse.status).toBe(200)

      // 删除对象
      const deleteObjectResponse = await axios.delete(
        `${TEST_CONFIG.API_BASE_URL}/api/scenes/${sceneId}/objects/${objectId}`,
        { headers: { Authorization: `Bearer ${teacherToken}` } }
      )
      expect(deleteObjectResponse.status).toBe(200)
    })
  })

  describe('🤝 实时协作测试', () => {
    test('WebSocket连接和消息传递', async () => {
      return new Promise<void>((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('WebSocket测试超时'))
        }, TEST_CONFIG.TIMEOUT.WEBSOCKET)

        socket = io(TEST_CONFIG.WEBSOCKET_URL, {
          auth: { token: teacherToken },
          transports: ['websocket']
        })

        socket.on('connect', () => {
          console.log('✅ WebSocket连接成功')
          
          // 测试消息发送
          socket.emit('test:message', { content: 'Hello, World!' })
        })

        socket.on('test:message', (data) => {
          expect(data.content).toBe('Hello, World!')
          clearTimeout(timeout)
          resolve()
        })

        socket.on('connect_error', (error) => {
          clearTimeout(timeout)
          reject(error)
        })
      })
    })

    test('多用户协作场景', async () => {
      // 这里需要创建多个WebSocket连接来模拟多用户协作
      const teacherSocket = io(TEST_CONFIG.WEBSOCKET_URL, {
        auth: { token: teacherToken }
      })
      
      const studentSocket = io(TEST_CONFIG.WEBSOCKET_URL, {
        auth: { token: studentToken }
      })

      return new Promise<void>((resolve, reject) => {
        let connectCount = 0
        const timeout = setTimeout(() => {
          reject(new Error('多用户协作测试超时'))
        }, TEST_CONFIG.TIMEOUT.WEBSOCKET)

        const checkConnections = () => {
          connectCount++
          if (connectCount === 2) {
            // 测试协作消息
            teacherSocket.emit('collaboration:update', {
              type: 'object:move',
              objectId: 'test-object',
              position: { x: 1, y: 0, z: 0 }
            })
          }
        }

        teacherSocket.on('connect', checkConnections)
        studentSocket.on('connect', checkConnections)

        studentSocket.on('collaboration:update', (data) => {
          expect(data.type).toBe('object:move')
          expect(data.objectId).toBe('test-object')
          
          teacherSocket.disconnect()
          studentSocket.disconnect()
          clearTimeout(timeout)
          resolve()
        })
      })
    })
  })

  describe('🌐 前端界面测试', () => {
    test('页面加载和导航', async () => {
      await driver.get(TEST_CONFIG.CLIENT_BASE_URL)
      
      // 等待页面加载
      await driver.wait(
        until.titleContains('DL-Engine'),
        TEST_CONFIG.TIMEOUT.PAGE_LOAD
      )

      // 检查页面标题
      const title = await driver.getTitle()
      expect(title).toContain('DL-Engine')

      // 检查主要元素
      const loginButton = await driver.wait(
        until.elementLocated(By.css('[data-testid="login-button"]')),
        TEST_CONFIG.TIMEOUT.ELEMENT_WAIT
      )
      expect(loginButton).toBeDefined()
    })

    test('用户登录流程', async () => {
      await driver.get(`${TEST_CONFIG.CLIENT_BASE_URL}/login`)

      // 填写登录表单
      const usernameInput = await driver.findElement(By.css('[data-testid="username-input"]'))
      const passwordInput = await driver.findElement(By.css('[data-testid="password-input"]'))
      const loginButton = await driver.findElement(By.css('[data-testid="login-submit"]'))

      await usernameInput.sendKeys(TEST_CONFIG.TEST_USERS.TEACHER.username)
      await passwordInput.sendKeys(TEST_CONFIG.TEST_USERS.TEACHER.password)
      await loginButton.click()

      // 等待登录成功跳转
      await driver.wait(
        until.urlContains('/app'),
        TEST_CONFIG.TIMEOUT.PAGE_LOAD
      )

      const currentUrl = await driver.getCurrentUrl()
      expect(currentUrl).toContain('/app')
    })

    test('响应式设计测试', async () => {
      // 测试桌面端
      await driver.manage().window().setRect({ width: 1920, height: 1080 })
      await driver.get(`${TEST_CONFIG.CLIENT_BASE_URL}/app`)
      
      const sidebar = await driver.findElement(By.css('[data-testid="sidebar"]'))
      const isDisplayed = await sidebar.isDisplayed()
      expect(isDisplayed).toBe(true)

      // 测试移动端
      await driver.manage().window().setRect({ width: 375, height: 667 })
      await driver.sleep(1000) // 等待响应式布局调整

      const mobileMenu = await driver.findElement(By.css('[data-testid="mobile-menu"]'))
      const isMobileMenuDisplayed = await mobileMenu.isDisplayed()
      expect(isMobileMenuDisplayed).toBe(true)
    })
  })

  describe('📊 性能测试', () => {
    test('API响应时间测试', async () => {
      const startTime = Date.now()
      
      const response = await axios.get(
        `${TEST_CONFIG.API_BASE_URL}/api/courses`,
        { headers: { Authorization: `Bearer ${teacherToken}` } }
      )
      
      const responseTime = Date.now() - startTime
      
      expect(response.status).toBe(200)
      expect(responseTime).toBeLessThan(2000) // 响应时间应小于2秒
    })

    test('并发用户测试', async () => {
      const concurrentRequests = 10
      const requests = []

      for (let i = 0; i < concurrentRequests; i++) {
        requests.push(
          axios.get(
            `${TEST_CONFIG.API_BASE_URL}/api/courses`,
            { headers: { Authorization: `Bearer ${teacherToken}` } }
          )
        )
      }

      const startTime = Date.now()
      const responses = await Promise.all(requests)
      const totalTime = Date.now() - startTime

      responses.forEach(response => {
        expect(response.status).toBe(200)
      })

      expect(totalTime).toBeLessThan(5000) // 10个并发请求应在5秒内完成
    })

    test('内存使用测试', async () => {
      // 获取系统性能指标
      const metricsResponse = await axios.get(
        `${TEST_CONFIG.API_BASE_URL}/metrics`,
        { headers: { Authorization: `Bearer ${adminToken}` } }
      )

      expect(metricsResponse.status).toBe(200)
      
      const metrics = metricsResponse.data
      expect(metrics.memory).toBeDefined()
      expect(metrics.cpu).toBeDefined()
      
      // 内存使用率应小于80%
      expect(metrics.memory.usage_percent).toBeLessThan(80)
    })
  })
})

// 运行测试的辅助脚本
export const runIntegrationTests = async () => {
  console.log('🧪 开始运行DL-Engine集成测试套件...')
  
  try {
    // 这里可以添加测试前的准备工作
    console.log('✅ 集成测试完成')
  } catch (error) {
    console.error('❌ 集成测试失败:', error)
    process.exit(1)
  }
}
