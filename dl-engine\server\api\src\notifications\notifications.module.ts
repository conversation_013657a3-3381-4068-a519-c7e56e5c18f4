import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@nestjs/typeorm'
import { ConfigModule } from '@nestjs/config'

import { Notification } from './entities/notification.entity'
import { NotificationTemplate } from './entities/notification-template.entity'
import { NotificationPreference } from './entities/notification-preference.entity'
import { NotificationChannel } from './entities/notification-channel.entity'
import { NotificationQueue } from './entities/notification-queue.entity'
import { NotificationLog } from './entities/notification-log.entity'
import { NotificationSubscription } from './entities/notification-subscription.entity'

import { NotificationsController } from './controllers/notifications.controller'
import { NotificationTemplatesController } from './controllers/notification-templates.controller'
import { NotificationPreferencesController } from './controllers/notification-preferences.controller'
import { NotificationChannelsController } from './controllers/notification-channels.controller'
import { NotificationSubscriptionsController } from './controllers/notification-subscriptions.controller'

import { NotificationsService } from './services/notifications.service'
import { NotificationTemplatesService } from './services/notification-templates.service'
import { NotificationPreferencesService } from './services/notification-preferences.service'
import { NotificationChannelsService } from './services/notification-channels.service'
import { NotificationQueueService } from './services/notification-queue.service'
import { NotificationLogService } from './services/notification-log.service'
import { NotificationSubscriptionsService } from './services/notification-subscriptions.service'
import { EmailNotificationService } from './services/email-notification.service'
import { SmsNotificationService } from './services/sms-notification.service'
import { PushNotificationService } from './services/push-notification.service'
import { InAppNotificationService } from './services/in-app-notification.service'
import { NotificationDispatcherService } from './services/notification-dispatcher.service'

import { UsersModule } from '../users/users.module'

/**
 * 通知系统模块
 * 
 * 集成中文模板和手机短信的通知功能，包括：
 * - 消息推送管理
 * - 邮件通知服务
 * - 站内信系统
 * - 提醒设置管理
 * - 通知模板管理
 * - 通知偏好设置
 * - 通知渠道管理
 * - 通知队列处理
 * - 通知日志记录
 * - 订阅管理
 */
@Module({
  imports: [
    ConfigModule,
    TypeOrmModule.forFeature([
      Notification,
      NotificationTemplate,
      NotificationPreference,
      NotificationChannel,
      NotificationQueue,
      NotificationLog,
      NotificationSubscription
    ]),
    UsersModule
  ],
  controllers: [
    NotificationsController,
    NotificationTemplatesController,
    NotificationPreferencesController,
    NotificationChannelsController,
    NotificationSubscriptionsController
  ],
  providers: [
    NotificationsService,
    NotificationTemplatesService,
    NotificationPreferencesService,
    NotificationChannelsService,
    NotificationQueueService,
    NotificationLogService,
    NotificationSubscriptionsService,
    EmailNotificationService,
    SmsNotificationService,
    PushNotificationService,
    InAppNotificationService,
    NotificationDispatcherService
  ],
  exports: [
    NotificationsService,
    NotificationTemplatesService,
    NotificationPreferencesService,
    NotificationChannelsService,
    NotificationQueueService,
    NotificationLogService,
    NotificationSubscriptionsService,
    EmailNotificationService,
    SmsNotificationService,
    PushNotificationService,
    InAppNotificationService,
    NotificationDispatcherService
  ]
})
export class NotificationModule {}
