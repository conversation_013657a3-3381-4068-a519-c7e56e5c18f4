import { Injectable, Inject, Logger, OnModuleDestroy } from '@nestjs/common';

export interface PubSubMessage<T = any> {
  channel: string;
  data: T;
  timestamp: Date;
  messageId: string;
  metadata?: Record<string, any>;
}

export interface SubscriptionOptions {
  pattern?: boolean; // 是否为模式订阅
  autoReconnect?: boolean; // 是否自动重连
  maxRetries?: number; // 最大重试次数
}

export type MessageHandler<T = any> = (message: PubSubMessage<T>) => Promise<void> | void;

@Injectable()
export class PubSubService implements OnModuleDestroy {
  private readonly logger = new Logger(PubSubService.name);
  private readonly subscriptions = new Map<string, Set<MessageHandler>>();
  private readonly patternSubscriptions = new Map<string, Set<MessageHandler>>();
  private isConnected = false;

  constructor(
    @Inject('REDIS_SUBSCRIBER') private readonly subscriber: any,
    @Inject('REDIS_PUBLISHER') private readonly publisher: any,
  ) {
    this.initializeSubscriber();
  }

  async onModuleDestroy() {
    await this.disconnect();
  }

  /**
   * 发布消息
   */
  async publish<T>(channel: string, data: T, metadata?: Record<string, any>): Promise<number> {
    try {
      const message: PubSubMessage<T> = {
        channel,
        data,
        timestamp: new Date(),
        messageId: this.generateMessageId(),
        metadata,
      };

      const serializedMessage = JSON.stringify(message);
      const subscriberCount = await this.publisher.publish(channel, serializedMessage);
      
      this.logger.debug(`Message published to channel ${channel}: ${message.messageId} (${subscriberCount} subscribers)`);
      return subscriberCount;
    } catch (error) {
      this.logger.error(`Failed to publish message to channel ${channel}:`, error);
      throw error;
    }
  }

  /**
   * 订阅频道
   */
  async subscribe<T>(
    channel: string,
    handler: MessageHandler<T>,
    options: SubscriptionOptions = {}
  ): Promise<void> {
    try {
      const { pattern = false } = options;

      if (pattern) {
        // 模式订阅
        if (!this.patternSubscriptions.has(channel)) {
          this.patternSubscriptions.set(channel, new Set());
          await this.subscriber.pSubscribe(channel);
        }
        this.patternSubscriptions.get(channel)!.add(handler);
      } else {
        // 普通订阅
        if (!this.subscriptions.has(channel)) {
          this.subscriptions.set(channel, new Set());
          await this.subscriber.subscribe(channel);
        }
        this.subscriptions.get(channel)!.add(handler);
      }

      this.logger.debug(`Subscribed to channel ${channel} (pattern: ${pattern})`);
    } catch (error) {
      this.logger.error(`Failed to subscribe to channel ${channel}:`, error);
      throw error;
    }
  }

  /**
   * 取消订阅
   */
  async unsubscribe<T>(
    channel: string,
    handler?: MessageHandler<T>,
    options: SubscriptionOptions = {}
  ): Promise<void> {
    try {
      const { pattern = false } = options;

      if (pattern) {
        const handlers = this.patternSubscriptions.get(channel);
        if (handlers) {
          if (handler) {
            handlers.delete(handler);
            if (handlers.size === 0) {
              this.patternSubscriptions.delete(channel);
              await this.subscriber.pUnsubscribe(channel);
            }
          } else {
            this.patternSubscriptions.delete(channel);
            await this.subscriber.pUnsubscribe(channel);
          }
        }
      } else {
        const handlers = this.subscriptions.get(channel);
        if (handlers) {
          if (handler) {
            handlers.delete(handler);
            if (handlers.size === 0) {
              this.subscriptions.delete(channel);
              await this.subscriber.unsubscribe(channel);
            }
          } else {
            this.subscriptions.delete(channel);
            await this.subscriber.unsubscribe(channel);
          }
        }
      }

      this.logger.debug(`Unsubscribed from channel ${channel} (pattern: ${pattern})`);
    } catch (error) {
      this.logger.error(`Failed to unsubscribe from channel ${channel}:`, error);
      throw error;
    }
  }

  /**
   * 批量发布消息
   */
  async publishBatch<T>(messages: Array<{ channel: string; data: T; metadata?: Record<string, any> }>): Promise<number[]> {
    try {
      const results = await Promise.all(
        messages.map(({ channel, data, metadata }) => 
          this.publish(channel, data, metadata)
        )
      );

      this.logger.debug(`Batch published ${messages.length} messages`);
      return results;
    } catch (error) {
      this.logger.error('Failed to publish batch messages:', error);
      throw error;
    }
  }

  /**
   * 广播消息到多个频道
   */
  async broadcast<T>(channels: string[], data: T, metadata?: Record<string, any>): Promise<number[]> {
    try {
      const results = await Promise.all(
        channels.map(channel => this.publish(channel, data, metadata))
      );

      const totalSubscribers = results.reduce((sum, count) => sum + count, 0);
      this.logger.debug(`Broadcasted message to ${channels.length} channels (${totalSubscribers} total subscribers)`);
      
      return results;
    } catch (error) {
      this.logger.error('Failed to broadcast message:', error);
      throw error;
    }
  }

  /**
   * 获取频道订阅者数量
   */
  async getSubscriberCount(channel: string): Promise<number> {
    try {
      const result = await this.publisher.pubSubNumSub(channel);
      return result[channel] || 0;
    } catch (error) {
      this.logger.error(`Failed to get subscriber count for channel ${channel}:`, error);
      return 0;
    }
  }

  /**
   * 获取所有活跃频道
   */
  async getActiveChannels(): Promise<string[]> {
    try {
      return await this.publisher.pubSubChannels();
    } catch (error) {
      this.logger.error('Failed to get active channels:', error);
      return [];
    }
  }

  /**
   * 获取订阅统计信息
   */
  async getSubscriptionStats(): Promise<{
    totalChannels: number;
    totalPatterns: number;
    channelSubscriptions: Record<string, number>;
    patternSubscriptions: Record<string, number>;
  }> {
    const channelSubscriptions: Record<string, number> = {};
    const patternSubscriptions: Record<string, number> = {};

    for (const [channel, handlers] of this.subscriptions.entries()) {
      channelSubscriptions[channel] = handlers.size;
    }

    for (const [pattern, handlers] of this.patternSubscriptions.entries()) {
      patternSubscriptions[pattern] = handlers.size;
    }

    return {
      totalChannels: this.subscriptions.size,
      totalPatterns: this.patternSubscriptions.size,
      channelSubscriptions,
      patternSubscriptions,
    };
  }

  /**
   * 创建频道命名空间
   */
  createNamespace(namespace: string) {
    return {
      publish: <T>(channel: string, data: T, metadata?: Record<string, any>) =>
        this.publish(`${namespace}:${channel}`, data, metadata),
      
      subscribe: <T>(channel: string, handler: MessageHandler<T>, options?: SubscriptionOptions) =>
        this.subscribe(`${namespace}:${channel}`, handler, options),
      
      unsubscribe: <T>(channel: string, handler?: MessageHandler<T>, options?: SubscriptionOptions) =>
        this.unsubscribe(`${namespace}:${channel}`, handler, options),
      
      broadcast: <T>(channels: string[], data: T, metadata?: Record<string, any>) =>
        this.broadcast(channels.map(ch => `${namespace}:${ch}`), data, metadata),
    };
  }

  /**
   * 创建请求-响应模式
   */
  async request<TRequest, TResponse>(
    channel: string,
    data: TRequest,
    timeout: number = 5000
  ): Promise<TResponse> {
    return new Promise((resolve, reject) => {
      const responseChannel = `${channel}:response:${this.generateMessageId()}`;
      const timeoutId = setTimeout(() => {
        this.unsubscribe(responseChannel);
        reject(new Error('Request timeout'));
      }, timeout);

      // 订阅响应频道
      this.subscribe<TResponse>(responseChannel, (message) => {
        clearTimeout(timeoutId);
        this.unsubscribe(responseChannel);
        resolve(message.data);
      });

      // 发送请求
      this.publish(channel, data, { responseChannel });
    });
  }

  /**
   * 处理请求-响应模式的响应
   */
  async respond<TRequest, TResponse>(
    channel: string,
    handler: (data: TRequest) => Promise<TResponse> | TResponse
  ): Promise<void> {
    await this.subscribe<TRequest>(channel, async (message) => {
      try {
        const responseChannel = message.metadata?.responseChannel;
        if (responseChannel) {
          const response = await handler(message.data);
          await this.publish(responseChannel, response);
        }
      } catch (error) {
        this.logger.error(`Error handling request on channel ${channel}:`, error);
      }
    });
  }

  /**
   * 断开连接
   */
  async disconnect(): Promise<void> {
    try {
      if (this.isConnected) {
        await this.subscriber.quit();
        await this.publisher.quit();
        this.isConnected = false;
        this.logger.log('PubSub disconnected');
      }
    } catch (error) {
      this.logger.error('Failed to disconnect PubSub:', error);
    }
  }

  /**
   * 重新连接
   */
  async reconnect(): Promise<void> {
    try {
      await this.disconnect();
      await this.initializeSubscriber();
      
      // 重新订阅所有频道
      for (const channel of this.subscriptions.keys()) {
        await this.subscriber.subscribe(channel);
      }
      
      for (const pattern of this.patternSubscriptions.keys()) {
        await this.subscriber.pSubscribe(pattern);
      }
      
      this.logger.log('PubSub reconnected');
    } catch (error) {
      this.logger.error('Failed to reconnect PubSub:', error);
      throw error;
    }
  }

  private async initializeSubscriber(): Promise<void> {
    try {
      // 设置消息处理器
      this.subscriber.on('message', (channel: string, message: string) => {
        this.handleMessage(channel, message, false);
      });

      this.subscriber.on('pmessage', (pattern: string, channel: string, message: string) => {
        this.handleMessage(channel, message, true, pattern);
      });

      this.subscriber.on('error', (error: Error) => {
        this.logger.error('Redis subscriber error:', error);
      });

      this.subscriber.on('connect', () => {
        this.isConnected = true;
        this.logger.log('Redis subscriber connected');
      });

      this.subscriber.on('disconnect', () => {
        this.isConnected = false;
        this.logger.warn('Redis subscriber disconnected');
      });

    } catch (error) {
      this.logger.error('Failed to initialize subscriber:', error);
      throw error;
    }
  }

  private handleMessage(channel: string, message: string, isPattern: boolean, pattern?: string): void {
    try {
      const parsedMessage: PubSubMessage = JSON.parse(message);
      
      if (isPattern && pattern) {
        const handlers = this.patternSubscriptions.get(pattern);
        if (handlers) {
          handlers.forEach(handler => {
            try {
              handler(parsedMessage);
            } catch (error) {
              this.logger.error(`Error in pattern message handler for ${pattern}:`, error);
            }
          });
        }
      } else {
        const handlers = this.subscriptions.get(channel);
        if (handlers) {
          handlers.forEach(handler => {
            try {
              handler(parsedMessage);
            } catch (error) {
              this.logger.error(`Error in message handler for ${channel}:`, error);
            }
          });
        }
      }
    } catch (error) {
      this.logger.error(`Failed to parse message from channel ${channel}:`, error);
    }
  }

  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
