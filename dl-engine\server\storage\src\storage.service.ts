import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { MySQLService } from './database/mysql/mysql.service';
import { RedisService } from './database/redis/redis.service';
import { PostgreSQLService } from './database/postgresql/postgresql.service';
import { MinioService } from './minio/minio.service';
import { BackupService } from './backup/backup.service';
import { MigrationService } from './backup/migration.service';
import { MediaService } from './media/media.service';

export interface StorageHealthStatus {
  status: 'healthy' | 'degraded' | 'unhealthy';
  services: {
    mysql: { status: string; details?: any };
    redis: { status: string; details?: any };
    postgresql: { status: string; details?: any };
    minio: { status: string; details?: any };
    media: { status: string; details?: any };
  };
  timestamp: string;
  uptime: number;
  version: string;
}

export interface StorageMetrics {
  databases: {
    mysql: {
      connections: number;
      queries: number;
      slowQueries: number;
      size: number;
    };
    redis: {
      memory: number;
      keys: number;
      hits: number;
      misses: number;
    };
    postgresql: {
      connections: number;
      vectors: number;
      size: number;
    };
  };
  storage: {
    minio: {
      buckets: number;
      objects: number;
      size: number;
      bandwidth: number;
    };
  };
  media: {
    totalFiles: number;
    processing: number;
    completed: number;
    failed: number;
  };
  backup: {
    lastBackup: Date | null;
    backupSize: number;
    backupCount: number;
  };
}

@Injectable()
export class StorageService implements OnModuleInit {
  private readonly logger = new Logger(StorageService.name);
  private readonly startTime = Date.now();

  constructor(
    private configService: ConfigService,
    private mysqlService: MySQLService,
    private redisService: RedisService,
    private postgresqlService: PostgreSQLService,
    private minioService: MinioService,
    private backupService: BackupService,
    private migrationService: MigrationService,
    private mediaService: MediaService,
  ) {}

  async onModuleInit() {
    this.logger.log('Storage service initializing...');
    
    try {
      // 初始化各个服务
      await this.initializeServices();
      
      // 执行健康检查
      const health = await this.getHealthStatus();
      
      if (health.status === 'healthy') {
        this.logger.log('Storage service initialized successfully');
      } else {
        this.logger.warn('Storage service initialized with issues', health);
      }
    } catch (error) {
      this.logger.error('Failed to initialize storage service:', error);
      throw error;
    }
  }

  /**
   * 获取存储服务健康状态
   */
  async getHealthStatus(): Promise<StorageHealthStatus> {
    const services = {
      mysql: { status: 'unknown' },
      redis: { status: 'unknown' },
      postgresql: { status: 'unknown' },
      minio: { status: 'unknown' },
      media: { status: 'unknown' },
    };

    try {
      // 检查MySQL
      const mysqlHealth = await this.mysqlService.healthCheck();
      services.mysql = mysqlHealth;
    } catch (error) {
      services.mysql = { status: 'unhealthy', details: { error: error.message } };
    }

    try {
      // 检查Redis
      const redisHealth = await this.redisService.ping();
      services.redis = { status: redisHealth === 'PONG' ? 'healthy' : 'unhealthy' };
    } catch (error) {
      services.redis = { status: 'unhealthy', details: { error: error.message } };
    }

    try {
      // 检查PostgreSQL
      const postgresqlHealth = await this.postgresqlService.healthCheck();
      services.postgresql = postgresqlHealth;
    } catch (error) {
      services.postgresql = { status: 'unhealthy', details: { error: error.message } };
    }

    try {
      // 检查Minio
      const minioHealth = await this.minioService.healthCheck();
      services.minio = minioHealth;
    } catch (error) {
      services.minio = { status: 'unhealthy', details: { error: error.message } };
    }

    try {
      // 检查媒体处理服务
      const mediaStats = this.mediaService.getProcessingStats();
      services.media = { status: 'healthy', details: mediaStats };
    } catch (error) {
      services.media = { status: 'unhealthy', details: { error: error.message } };
    }

    // 计算整体状态
    const healthyCount = Object.values(services).filter(s => s.status === 'healthy').length;
    const totalCount = Object.keys(services).length;
    
    let overallStatus: 'healthy' | 'degraded' | 'unhealthy';
    if (healthyCount === totalCount) {
      overallStatus = 'healthy';
    } else if (healthyCount >= totalCount / 2) {
      overallStatus = 'degraded';
    } else {
      overallStatus = 'unhealthy';
    }

    return {
      status: overallStatus,
      services,
      timestamp: new Date().toISOString(),
      uptime: Date.now() - this.startTime,
      version: this.configService.get('APP_VERSION', '1.0.0'),
    };
  }

  /**
   * 获取存储指标
   */
  async getMetrics(): Promise<StorageMetrics> {
    try {
      const [
        mysqlStats,
        redisInfo,
        postgresqlStats,
        minioStats,
        mediaStats,
        backupStats,
      ] = await Promise.all([
        this.getMySQLMetrics(),
        this.getRedisMetrics(),
        this.getPostgreSQLMetrics(),
        this.getMinioMetrics(),
        this.getMediaMetrics(),
        this.getBackupMetrics(),
      ]);

      return {
        databases: {
          mysql: mysqlStats,
          redis: redisInfo,
          postgresql: postgresqlStats,
        },
        storage: {
          minio: minioStats,
        },
        media: mediaStats,
        backup: backupStats,
      };
    } catch (error) {
      this.logger.error('Failed to get storage metrics:', error);
      throw error;
    }
  }

  /**
   * 执行存储优化
   */
  async optimizeStorage(): Promise<{
    mysql: any;
    redis: any;
    postgresql: any;
    minio: any;
  }> {
    this.logger.log('Starting storage optimization...');

    const results = {
      mysql: null,
      redis: null,
      postgresql: null,
      minio: null,
    };

    try {
      // MySQL优化
      results.mysql = await this.optimizeMySQL();
      
      // Redis优化
      results.redis = await this.optimizeRedis();
      
      // PostgreSQL优化
      results.postgresql = await this.optimizePostgreSQL();
      
      // Minio优化
      results.minio = await this.optimizeMinio();

      this.logger.log('Storage optimization completed');
      return results;
    } catch (error) {
      this.logger.error('Storage optimization failed:', error);
      throw error;
    }
  }

  /**
   * 清理存储空间
   */
  async cleanupStorage(options: {
    cleanTempFiles?: boolean;
    cleanOldBackups?: boolean;
    cleanFailedJobs?: boolean;
    cleanExpiredSessions?: boolean;
    cleanUnusedMedia?: boolean;
  } = {}): Promise<{
    tempFiles: number;
    oldBackups: number;
    failedJobs: number;
    expiredSessions: number;
    unusedMedia: number;
    totalFreed: number;
  }> {
    this.logger.log('Starting storage cleanup...');

    const results = {
      tempFiles: 0,
      oldBackups: 0,
      failedJobs: 0,
      expiredSessions: 0,
      unusedMedia: 0,
      totalFreed: 0,
    };

    try {
      if (options.cleanTempFiles) {
        results.tempFiles = await this.cleanTempFiles();
      }

      if (options.cleanOldBackups) {
        results.oldBackups = await this.cleanOldBackups();
      }

      if (options.cleanFailedJobs) {
        results.failedJobs = await this.cleanFailedJobs();
      }

      if (options.cleanExpiredSessions) {
        results.expiredSessions = await this.cleanExpiredSessions();
      }

      if (options.cleanUnusedMedia) {
        results.unusedMedia = await this.cleanUnusedMedia();
      }

      results.totalFreed = Object.values(results).reduce((sum, value) => sum + value, 0);

      this.logger.log(`Storage cleanup completed: ${results.totalFreed} items cleaned`);
      return results;
    } catch (error) {
      this.logger.error('Storage cleanup failed:', error);
      throw error;
    }
  }

  /**
   * 数据同步
   */
  async syncData(config: {
    source: string;
    target: string;
    tables?: string[];
    buckets?: string[];
    options?: any;
  }): Promise<any> {
    this.logger.log(`Starting data sync: ${config.source} -> ${config.target}`);

    try {
      const result = await this.migrationService.syncData({
        source: {
          type: config.source as any,
          connection: {},
          tables: config.tables,
          buckets: config.buckets,
        },
        target: {
          type: config.target as any,
          connection: {},
          tables: config.tables,
          buckets: config.buckets,
        },
        options: {
          batchSize: 1000,
          parallel: true,
          skipExisting: false,
          transformData: false,
          validateData: true,
          ...config.options,
        },
      });

      this.logger.log(`Data sync completed: ${result.syncedRecords}/${result.totalRecords} records`);
      return result;
    } catch (error) {
      this.logger.error('Data sync failed:', error);
      throw error;
    }
  }

  private async initializeServices(): Promise<void> {
    // 确保必要的存储桶存在
    const requiredBuckets = ['media', 'backups', 'temp', 'thumbnails', 'transcoded'];
    
    for (const bucket of requiredBuckets) {
      try {
        await this.minioService.createBucket(bucket);
      } catch (error) {
        this.logger.warn(`Failed to create bucket ${bucket}:`, error);
      }
    }

    // 初始化Redis键空间
    // 这里可以添加Redis初始化逻辑

    // 初始化PostgreSQL扩展
    // 这里可以添加PostgreSQL扩展初始化逻辑
  }

  private async getMySQLMetrics(): Promise<any> {
    try {
      // 获取MySQL指标
      return {
        connections: 0,
        queries: 0,
        slowQueries: 0,
        size: 0,
      };
    } catch (error) {
      this.logger.error('Failed to get MySQL metrics:', error);
      return { connections: 0, queries: 0, slowQueries: 0, size: 0 };
    }
  }

  private async getRedisMetrics(): Promise<any> {
    try {
      const info = await this.redisService.info();
      return {
        memory: 0,
        keys: 0,
        hits: 0,
        misses: 0,
      };
    } catch (error) {
      this.logger.error('Failed to get Redis metrics:', error);
      return { memory: 0, keys: 0, hits: 0, misses: 0 };
    }
  }

  private async getPostgreSQLMetrics(): Promise<any> {
    try {
      const stats = await this.postgresqlService.getVectorStats();
      return {
        connections: 0,
        vectors: stats.totalVectors,
        size: 0,
      };
    } catch (error) {
      this.logger.error('Failed to get PostgreSQL metrics:', error);
      return { connections: 0, vectors: 0, size: 0 };
    }
  }

  private async getMinioMetrics(): Promise<any> {
    try {
      // 获取Minio指标
      const buckets = await this.minioService.listBuckets();
      return {
        buckets: buckets.length,
        objects: 0,
        size: 0,
        bandwidth: 0,
      };
    } catch (error) {
      this.logger.error('Failed to get Minio metrics:', error);
      return { buckets: 0, objects: 0, size: 0, bandwidth: 0 };
    }
  }

  private async getMediaMetrics(): Promise<any> {
    try {
      return this.mediaService.getProcessingStats();
    } catch (error) {
      this.logger.error('Failed to get media metrics:', error);
      return { totalFiles: 0, processing: 0, completed: 0, failed: 0 };
    }
  }

  private async getBackupMetrics(): Promise<any> {
    try {
      const stats = await this.backupService.getBackupStats();
      return {
        lastBackup: stats.lastBackup,
        backupSize: stats.totalSize,
        backupCount: stats.totalBackups,
      };
    } catch (error) {
      this.logger.error('Failed to get backup metrics:', error);
      return { lastBackup: null, backupSize: 0, backupCount: 0 };
    }
  }

  private async optimizeMySQL(): Promise<any> {
    // 实现MySQL优化逻辑
    return { optimized: true };
  }

  private async optimizeRedis(): Promise<any> {
    // 实现Redis优化逻辑
    return { optimized: true };
  }

  private async optimizePostgreSQL(): Promise<any> {
    // 实现PostgreSQL优化逻辑
    return { optimized: true };
  }

  private async optimizeMinio(): Promise<any> {
    // 实现Minio优化逻辑
    return { optimized: true };
  }

  private async cleanTempFiles(): Promise<number> {
    // 清理临时文件
    return 0;
  }

  private async cleanOldBackups(): Promise<number> {
    // 清理旧备份
    return 0;
  }

  private async cleanFailedJobs(): Promise<number> {
    // 清理失败的任务
    return 0;
  }

  private async cleanExpiredSessions(): Promise<number> {
    // 清理过期会话
    return 0;
  }

  private async cleanUnusedMedia(): Promise<number> {
    // 清理未使用的媒体文件
    return 0;
  }
}
