import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  ManyToMany,
  JoinTable,
  JoinColumn,
  Index
} from 'typeorm'
import { User } from '../../users/entities/user.entity'
import { ProjectPermission } from './project-permission.entity'
import { ProjectVersion } from './project-version.entity'
import { ProjectCollaborator } from './project-collaborator.entity'
import { ProjectCategory } from './project-category.entity'
import { ProjectTag } from './project-tag.entity'
import { ProjectActivity } from './project-activity.entity'

/**
 * 项目状态枚举
 */
export enum ProjectStatus {
  DRAFT = 'draft',           // 草稿
  ACTIVE = 'active',         // 活跃
  ARCHIVED = 'archived',     // 已归档
  DELETED = 'deleted',       // 已删除
  TEMPLATE = 'template'      // 模板
}

/**
 * 项目可见性枚举
 */
export enum ProjectVisibility {
  PUBLIC = 'public',         // 公开
  PRIVATE = 'private',       // 私有
  UNLISTED = 'unlisted',     // 不公开列出
  ORGANIZATION = 'organization' // 组织内可见
}

/**
 * 项目类型枚举
 */
export enum ProjectType {
  SCENE = 'scene',           // 3D场景
  COURSE = 'course',         // 课程
  ASSIGNMENT = 'assignment', // 作业
  EXPERIMENT = 'experiment', // 实验
  GAME = 'game',            // 游戏
  PRESENTATION = 'presentation', // 演示
  OTHER = 'other'           // 其他
}

/**
 * 项目实体
 * 
 * 存储项目的基本信息和元数据
 */
@Entity('projects')
@Index(['ownerId', 'status'])
@Index(['visibility', 'status'])
@Index(['type', 'status'])
@Index(['createdAt'])
export class Project {
  @PrimaryGeneratedColumn('uuid')
  id: string

  /**
   * 项目名称
   */
  @Column({ type: 'varchar', length: 200 })
  name: string

  /**
   * 项目描述
   */
  @Column({ type: 'text', nullable: true })
  description?: string

  /**
   * 项目类型
   */
  @Column({
    type: 'enum',
    enum: ProjectType,
    default: ProjectType.SCENE
  })
  type: ProjectType

  /**
   * 项目状态
   */
  @Column({
    type: 'enum',
    enum: ProjectStatus,
    default: ProjectStatus.DRAFT
  })
  status: ProjectStatus

  /**
   * 可见性设置
   */
  @Column({
    type: 'enum',
    enum: ProjectVisibility,
    default: ProjectVisibility.PRIVATE
  })
  visibility: ProjectVisibility

  /**
   * 项目所有者ID
   */
  @Column({ type: 'uuid' })
  ownerId: string

  /**
   * 项目缩略图URL
   */
  @Column({ type: 'text', nullable: true })
  thumbnailUrl?: string

  /**
   * 项目封面图URL
   */
  @Column({ type: 'text', nullable: true })
  coverImageUrl?: string

  /**
   * 项目文件存储路径
   */
  @Column({ type: 'varchar', length: 500, nullable: true })
  storagePath?: string

  /**
   * 项目大小（字节）
   */
  @Column({ type: 'bigint', default: 0 })
  size: number

  /**
   * 项目版本号
   */
  @Column({ type: 'varchar', length: 50, default: '1.0.0' })
  version: string

  /**
   * 是否为模板项目
   */
  @Column({ type: 'boolean', default: false })
  isTemplate: boolean

  /**
   * 模板项目ID（如果基于模板创建）
   */
  @Column({ type: 'uuid', nullable: true })
  templateId?: string

  /**
   * 项目设置（JSON格式）
   */
  @Column({ type: 'json', nullable: true })
  settings?: {
    allowComments: boolean
    allowFork: boolean
    allowDownload: boolean
    enableVersionControl: boolean
    autoSave: boolean
    autoSaveInterval: number
    maxFileSize: number
    allowedFileTypes: string[]
    [key: string]: any
  }

  /**
   * 项目元数据
   */
  @Column({ type: 'json', nullable: true })
  metadata?: {
    engine: string
    engineVersion: string
    platform: string[]
    requirements: Record<string, string>
    features: string[]
    difficulty: 'beginner' | 'intermediate' | 'advanced'
    estimatedTime: number // 预计完成时间（分钟）
    language: string
    [key: string]: any
  }

  /**
   * 统计信息
   */
  @Column({ type: 'json', nullable: true })
  statistics?: {
    views: number
    downloads: number
    forks: number
    likes: number
    comments: number
    collaborators: number
    versions: number
    lastActivity: Date
  }

  /**
   * 教育相关信息
   */
  @Column({ type: 'json', nullable: true })
  educationInfo?: {
    subject: string
    gradeLevel: string[]
    learningObjectives: string[]
    prerequisites: string[]
    assessmentCriteria: string[]
    instructorNotes: string
    studentInstructions: string
    duration: number // 课程时长（分钟）
    maxStudents: number
  }

  /**
   * 最后访问时间
   */
  @Column({ type: 'timestamp', nullable: true })
  lastAccessedAt?: Date

  /**
   * 最后修改时间
   */
  @Column({ type: 'timestamp', nullable: true })
  lastModifiedAt?: Date

  /**
   * 发布时间
   */
  @Column({ type: 'timestamp', nullable: true })
  publishedAt?: Date

  /**
   * 归档时间
   */
  @Column({ type: 'timestamp', nullable: true })
  archivedAt?: Date

  /**
   * 创建时间
   */
  @CreateDateColumn()
  createdAt: Date

  /**
   * 更新时间
   */
  @UpdateDateColumn()
  updatedAt: Date

  // 关联关系
  @ManyToOne(() => User)
  @JoinColumn({ name: 'ownerId' })
  owner: User

  @OneToMany(() => ProjectPermission, permission => permission.project, { cascade: true })
  permissions: ProjectPermission[]

  @OneToMany(() => ProjectVersion, version => version.project, { cascade: true })
  versions: ProjectVersion[]

  @OneToMany(() => ProjectCollaborator, collaborator => collaborator.project, { cascade: true })
  collaborators: ProjectCollaborator[]

  @OneToMany(() => ProjectActivity, activity => activity.project, { cascade: true })
  activities: ProjectActivity[]

  @ManyToOne(() => ProjectCategory, { nullable: true })
  @JoinColumn({ name: 'categoryId' })
  category?: ProjectCategory

  @Column({ type: 'uuid', nullable: true })
  categoryId?: string

  @ManyToMany(() => ProjectTag)
  @JoinTable({
    name: 'project_tag_relations',
    joinColumn: { name: 'projectId', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'tagId', referencedColumnName: 'id' }
  })
  tags: ProjectTag[]

  /**
   * 检查用户是否为项目所有者
   */
  isOwner(userId: string): boolean {
    return this.ownerId === userId
  }

  /**
   * 检查项目是否公开
   */
  isPublic(): boolean {
    return this.visibility === ProjectVisibility.PUBLIC
  }

  /**
   * 检查项目是否活跃
   */
  isActive(): boolean {
    return this.status === ProjectStatus.ACTIVE
  }

  /**
   * 检查项目是否已发布
   */
  isPublished(): boolean {
    return this.publishedAt !== null && this.publishedAt !== undefined
  }

  /**
   * 获取项目URL友好的slug
   */
  getSlug(): string {
    return this.name
      .toLowerCase()
      .replace(/[^a-z0-9\u4e00-\u9fa5]/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '')
  }

  /**
   * 更新统计信息
   */
  updateStatistics(field: keyof Project['statistics'], increment = 1): void {
    if (!this.statistics) {
      this.statistics = {
        views: 0,
        downloads: 0,
        forks: 0,
        likes: 0,
        comments: 0,
        collaborators: 0,
        versions: 0,
        lastActivity: new Date()
      }
    }

    if (typeof this.statistics[field] === 'number') {
      this.statistics[field] += increment
    }
    this.statistics.lastActivity = new Date()
  }

  /**
   * 检查是否可以编辑
   */
  canEdit(): boolean {
    return this.status !== ProjectStatus.ARCHIVED && this.status !== ProjectStatus.DELETED
  }

  /**
   * 检查是否可以删除
   */
  canDelete(): boolean {
    return this.status !== ProjectStatus.DELETED
  }
}
