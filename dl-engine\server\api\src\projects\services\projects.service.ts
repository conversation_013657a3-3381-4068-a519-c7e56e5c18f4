import { Injectable, NotFoundException, ForbiddenException, ConflictException } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { Repository, FindOptionsWhere, Like, In } from 'typeorm'
import { ConfigService } from '@nestjs/config'

import { Project, ProjectStatus, ProjectVisibility, ProjectType } from '../entities/project.entity'
import { ProjectPermission, PermissionType } from '../entities/project-permission.entity'
import { ProjectVersion } from '../entities/project-version.entity'
import { ProjectActivity } from '../entities/project-activity.entity'

import { CreateProjectDto } from '../dto/create-project.dto'
import { UpdateProjectDto } from '../dto/update-project.dto'
import { ProjectQueryDto } from '../dto/project-query.dto'
import { ProjectResponseDto } from '../dto/project-response.dto'

import { ProjectValidationService } from './project-validation.service'
import { ProjectNotificationService } from './project-notification.service'
import { ProjectPermissionsService } from './project-permissions.service'
import { ProjectActivitiesService } from './project-activities.service'

/**
 * 项目管理服务
 * 
 * 提供项目的CRUD操作和相关业务逻辑
 */
@Injectable()
export class ProjectsService {
  constructor(
    @InjectRepository(Project)
    private readonly projectRepository: Repository<Project>,
    @InjectRepository(ProjectPermission)
    private readonly permissionRepository: Repository<ProjectPermission>,
    @InjectRepository(ProjectVersion)
    private readonly versionRepository: Repository<ProjectVersion>,
    private readonly configService: ConfigService,
    private readonly validationService: ProjectValidationService,
    private readonly notificationService: ProjectNotificationService,
    private readonly permissionsService: ProjectPermissionsService,
    private readonly activitiesService: ProjectActivitiesService
  ) {}

  /**
   * 创建新项目
   */
  async create(createProjectDto: CreateProjectDto, userId: string): Promise<ProjectResponseDto> {
    // 验证输入数据
    await this.validationService.validateCreateProject(createProjectDto, userId)

    // 检查项目名称是否重复（同一用户下）
    await this.checkProjectNameExists(createProjectDto.name, userId)

    // 创建项目实体
    const project = this.projectRepository.create({
      ...createProjectDto,
      ownerId: userId,
      status: ProjectStatus.DRAFT,
      version: '1.0.0',
      statistics: {
        views: 0,
        downloads: 0,
        forks: 0,
        likes: 0,
        comments: 0,
        collaborators: 1,
        versions: 1,
        lastActivity: new Date()
      }
    })

    // 保存项目
    const savedProject = await this.projectRepository.save(project)

    // 创建所有者权限
    await this.permissionsService.create({
      projectId: savedProject.id,
      userId,
      type: PermissionType.OWNER
    })

    // 创建初始版本
    await this.createInitialVersion(savedProject.id, userId)

    // 记录活动
    await this.activitiesService.recordActivity(
      savedProject.id,
      userId,
      'project_created',
      { projectName: savedProject.name }
    )

    // 发送通知
    await this.notificationService.sendProjectCreatedNotification(savedProject)

    return this.mapToResponseDto(savedProject)
  }

  /**
   * 根据ID查找项目
   */
  async findById(id: string, userId?: string, includeRelations = false): Promise<ProjectResponseDto> {
    const relations = includeRelations 
      ? ['owner', 'permissions', 'versions', 'collaborators', 'category', 'tags', 'activities']
      : ['owner']
    
    const project = await this.projectRepository.findOne({
      where: { id },
      relations
    })

    if (!project) {
      throw new NotFoundException(`项目不存在: ${id}`)
    }

    // 检查访问权限
    if (userId) {
      await this.checkProjectAccess(project, userId, 'read')
      
      // 更新访问统计和最后访问时间
      project.updateStatistics('views')
      project.lastAccessedAt = new Date()
      await this.projectRepository.save(project)

      // 记录访问活动
      await this.activitiesService.recordActivity(
        project.id,
        userId,
        'project_viewed',
        { projectName: project.name }
      )
    }

    return this.mapToResponseDto(project)
  }

  /**
   * 分页查询项目列表
   */
  async findMany(query: ProjectQueryDto, userId?: string): Promise<{
    projects: ProjectResponseDto[]
    total: number
    page: number
    limit: number
  }> {
    const {
      page = 1,
      limit = 20,
      search,
      type,
      status,
      visibility,
      ownerId,
      categoryId,
      tags,
      sortBy = 'updatedAt',
      sortOrder = 'DESC'
    } = query

    const queryBuilder = this.projectRepository.createQueryBuilder('project')
      .leftJoinAndSelect('project.owner', 'owner')
      .leftJoinAndSelect('project.category', 'category')
      .leftJoinAndSelect('project.tags', 'tags')

    // 权限过滤
    if (userId) {
      queryBuilder.leftJoin('project.permissions', 'permission', 'permission.userId = :userId', { userId })
      queryBuilder.andWhere(
        '(project.visibility = :publicVisibility OR project.ownerId = :userId OR permission.userId IS NOT NULL)',
        { publicVisibility: ProjectVisibility.PUBLIC, userId }
      )
    } else {
      // 未登录用户只能看到公开项目
      queryBuilder.andWhere('project.visibility = :publicVisibility', { 
        publicVisibility: ProjectVisibility.PUBLIC 
      })
    }

    // 状态过滤（排除已删除的项目）
    queryBuilder.andWhere('project.status != :deletedStatus', { 
      deletedStatus: ProjectStatus.DELETED 
    })

    // 搜索条件
    if (search) {
      queryBuilder.andWhere(
        '(project.name LIKE :search OR project.description LIKE :search)',
        { search: `%${search}%` }
      )
    }

    // 项目类型过滤
    if (type) {
      queryBuilder.andWhere('project.type = :type', { type })
    }

    // 状态过滤
    if (status) {
      queryBuilder.andWhere('project.status = :status', { status })
    }

    // 可见性过滤
    if (visibility) {
      queryBuilder.andWhere('project.visibility = :visibility', { visibility })
    }

    // 所有者过滤
    if (ownerId) {
      queryBuilder.andWhere('project.ownerId = :ownerId', { ownerId })
    }

    // 分类过滤
    if (categoryId) {
      queryBuilder.andWhere('project.categoryId = :categoryId', { categoryId })
    }

    // 标签过滤
    if (tags && tags.length > 0) {
      queryBuilder.andWhere('tags.id IN (:...tags)', { tags })
    }

    // 排序
    queryBuilder.orderBy(`project.${sortBy}`, sortOrder)

    // 分页
    const offset = (page - 1) * limit
    queryBuilder.skip(offset).take(limit)

    const [projects, total] = await queryBuilder.getManyAndCount()

    return {
      projects: projects.map(project => this.mapToResponseDto(project)),
      total,
      page,
      limit
    }
  }

  /**
   * 获取用户的项目列表
   */
  async findUserProjects(userId: string, query: ProjectQueryDto): Promise<{
    projects: ProjectResponseDto[]
    total: number
    page: number
    limit: number
  }> {
    const {
      page = 1,
      limit = 20,
      search,
      type,
      status,
      sortBy = 'updatedAt',
      sortOrder = 'DESC'
    } = query

    const queryBuilder = this.projectRepository.createQueryBuilder('project')
      .leftJoinAndSelect('project.owner', 'owner')
      .leftJoinAndSelect('project.category', 'category')
      .leftJoinAndSelect('project.tags', 'tags')
      .leftJoin('project.permissions', 'permission')
      .where('(project.ownerId = :userId OR permission.userId = :userId)', { userId })
      .andWhere('project.status != :deletedStatus', { deletedStatus: ProjectStatus.DELETED })

    // 搜索条件
    if (search) {
      queryBuilder.andWhere(
        '(project.name LIKE :search OR project.description LIKE :search)',
        { search: `%${search}%` }
      )
    }

    // 项目类型过滤
    if (type) {
      queryBuilder.andWhere('project.type = :type', { type })
    }

    // 状态过滤
    if (status) {
      queryBuilder.andWhere('project.status = :status', { status })
    }

    // 排序
    queryBuilder.orderBy(`project.${sortBy}`, sortOrder)

    // 分页
    const offset = (page - 1) * limit
    queryBuilder.skip(offset).take(limit)

    const [projects, total] = await queryBuilder.getManyAndCount()

    return {
      projects: projects.map(project => this.mapToResponseDto(project)),
      total,
      page,
      limit
    }
  }

  /**
   * 更新项目信息
   */
  async update(id: string, updateProjectDto: UpdateProjectDto, userId: string): Promise<ProjectResponseDto> {
    const project = await this.projectRepository.findOne({ 
      where: { id },
      relations: ['owner']
    })
    
    if (!project) {
      throw new NotFoundException(`项目不存在: ${id}`)
    }

    // 检查编辑权限
    await this.checkProjectAccess(project, userId, 'write')

    // 验证更新数据
    await this.validationService.validateUpdateProject(updateProjectDto, project)

    // 检查项目名称冲突
    if (updateProjectDto.name && updateProjectDto.name !== project.name) {
      await this.checkProjectNameExists(updateProjectDto.name, project.ownerId, id)
    }

    // 记录变更前的状态
    const oldStatus = project.status
    const oldVisibility = project.visibility

    // 更新项目信息
    Object.assign(project, updateProjectDto)
    project.lastModifiedAt = new Date()

    const updatedProject = await this.projectRepository.save(project)

    // 记录活动
    const changes: string[] = []
    if (updateProjectDto.name) changes.push('名称')
    if (updateProjectDto.description) changes.push('描述')
    if (updateProjectDto.status && updateProjectDto.status !== oldStatus) changes.push('状态')
    if (updateProjectDto.visibility && updateProjectDto.visibility !== oldVisibility) changes.push('可见性')

    if (changes.length > 0) {
      await this.activitiesService.recordActivity(
        project.id,
        userId,
        'project_updated',
        { 
          projectName: project.name,
          changes: changes.join(', ')
        }
      )
    }

    // 发送通知
    if (updateProjectDto.status && updateProjectDto.status !== oldStatus) {
      await this.notificationService.sendProjectStatusChangedNotification(updatedProject, oldStatus)
    }

    return this.mapToResponseDto(updatedProject)
  }

  /**
   * 删除项目（软删除）
   */
  async remove(id: string, userId: string): Promise<void> {
    const project = await this.projectRepository.findOne({ 
      where: { id },
      relations: ['owner']
    })
    
    if (!project) {
      throw new NotFoundException(`项目不存在: ${id}`)
    }

    // 检查删除权限
    await this.checkProjectAccess(project, userId, 'delete')

    // 软删除项目
    project.status = ProjectStatus.DELETED
    project.lastModifiedAt = new Date()
    await this.projectRepository.save(project)

    // 记录活动
    await this.activitiesService.recordActivity(
      project.id,
      userId,
      'project_deleted',
      { projectName: project.name }
    )

    // 发送通知
    await this.notificationService.sendProjectDeletedNotification(project)
  }

  /**
   * 发布项目
   */
  async publish(id: string, userId: string): Promise<ProjectResponseDto> {
    const project = await this.projectRepository.findOne({ 
      where: { id },
      relations: ['owner']
    })
    
    if (!project) {
      throw new NotFoundException(`项目不存在: ${id}`)
    }

    // 检查发布权限
    await this.checkProjectAccess(project, userId, 'publish')

    // 检查项目是否可以发布
    if (project.status === ProjectStatus.ACTIVE) {
      throw new ConflictException('项目已经是发布状态')
    }

    if (project.status === ProjectStatus.DELETED) {
      throw new ConflictException('已删除的项目无法发布')
    }

    // 更新项目状态
    project.status = ProjectStatus.ACTIVE
    project.publishedAt = new Date()
    project.lastModifiedAt = new Date()

    const updatedProject = await this.projectRepository.save(project)

    // 记录活动
    await this.activitiesService.recordActivity(
      project.id,
      userId,
      'project_published',
      { projectName: project.name }
    )

    // 发送通知
    await this.notificationService.sendProjectPublishedNotification(updatedProject)

    return this.mapToResponseDto(updatedProject)
  }

  /**
   * 归档项目
   */
  async archive(id: string, userId: string): Promise<ProjectResponseDto> {
    const project = await this.projectRepository.findOne({ 
      where: { id },
      relations: ['owner']
    })
    
    if (!project) {
      throw new NotFoundException(`项目不存在: ${id}`)
    }

    // 检查归档权限
    await this.checkProjectAccess(project, userId, 'archive')

    // 更新项目状态
    project.status = ProjectStatus.ARCHIVED
    project.archivedAt = new Date()
    project.lastModifiedAt = new Date()

    const updatedProject = await this.projectRepository.save(project)

    // 记录活动
    await this.activitiesService.recordActivity(
      project.id,
      userId,
      'project_archived',
      { projectName: project.name }
    )

    return this.mapToResponseDto(updatedProject)
  }

  /**
   * 复制项目
   */
  async fork(id: string, userId: string, newName?: string): Promise<ProjectResponseDto> {
    const originalProject = await this.projectRepository.findOne({ 
      where: { id },
      relations: ['owner', 'tags']
    })
    
    if (!originalProject) {
      throw new NotFoundException(`项目不存在: ${id}`)
    }

    // 检查访问权限
    await this.checkProjectAccess(originalProject, userId, 'read')

    // 检查是否允许复制
    if (!originalProject.settings?.allowFork) {
      throw new ForbiddenException('该项目不允许复制')
    }

    // 生成新项目名称
    const forkName = newName || `${originalProject.name} (副本)`
    await this.checkProjectNameExists(forkName, userId)

    // 创建新项目
    const forkedProject = this.projectRepository.create({
      ...originalProject,
      id: undefined,
      name: forkName,
      ownerId: userId,
      status: ProjectStatus.DRAFT,
      visibility: ProjectVisibility.PRIVATE,
      templateId: originalProject.id,
      publishedAt: null,
      archivedAt: null,
      statistics: {
        views: 0,
        downloads: 0,
        forks: 0,
        likes: 0,
        comments: 0,
        collaborators: 1,
        versions: 1,
        lastActivity: new Date()
      }
    })

    const savedProject = await this.projectRepository.save(forkedProject)

    // 创建所有者权限
    await this.permissionsService.create({
      projectId: savedProject.id,
      userId,
      type: PermissionType.OWNER
    })

    // 更新原项目的复制统计
    originalProject.updateStatistics('forks')
    await this.projectRepository.save(originalProject)

    // 记录活动
    await this.activitiesService.recordActivity(
      savedProject.id,
      userId,
      'project_forked',
      { 
        projectName: savedProject.name,
        originalProject: originalProject.name
      }
    )

    await this.activitiesService.recordActivity(
      originalProject.id,
      userId,
      'project_was_forked',
      { 
        projectName: originalProject.name,
        forkedProject: savedProject.name
      }
    )

    return this.mapToResponseDto(savedProject)
  }

  /**
   * 检查项目名称是否存在
   */
  private async checkProjectNameExists(name: string, ownerId: string, excludeId?: string): Promise<void> {
    const where: FindOptionsWhere<Project> = { 
      name, 
      ownerId,
      status: In([ProjectStatus.DRAFT, ProjectStatus.ACTIVE, ProjectStatus.ARCHIVED])
    }
    
    if (excludeId) {
      where.id = { $ne: excludeId } as any
    }

    const existingProject = await this.projectRepository.findOne({ where })
    if (existingProject) {
      throw new ConflictException('项目名称已存在')
    }
  }

  /**
   * 检查项目访问权限
   */
  private async checkProjectAccess(project: Project, userId: string, action: string): Promise<void> {
    // 项目所有者拥有所有权限
    if (project.ownerId === userId) return

    // 公开项目的读取权限
    if (action === 'read' && project.isPublic()) return

    // 检查用户权限
    const permission = await this.permissionRepository.findOne({
      where: { projectId: project.id, userId }
    })

    if (!permission || !permission.hasPermission(`can${action.charAt(0).toUpperCase() + action.slice(1)}`)) {
      throw new ForbiddenException(`无权限执行此操作: ${action}`)
    }
  }

  /**
   * 创建初始版本
   */
  private async createInitialVersion(projectId: string, userId: string): Promise<void> {
    const version = this.versionRepository.create({
      projectId,
      version: '1.0.0',
      name: '初始版本',
      description: '项目的初始版本',
      createdById: userId,
      isCurrent: true,
      status: 'draft' as any,
      type: 'minor' as any
    })

    await this.versionRepository.save(version)
  }

  /**
   * 将项目实体映射为响应DTO
   */
  private mapToResponseDto(project: Project): ProjectResponseDto {
    return {
      id: project.id,
      name: project.name,
      description: project.description,
      type: project.type,
      status: project.status,
      visibility: project.visibility,
      ownerId: project.ownerId,
      thumbnailUrl: project.thumbnailUrl,
      coverImageUrl: project.coverImageUrl,
      size: project.size,
      version: project.version,
      isTemplate: project.isTemplate,
      templateId: project.templateId,
      settings: project.settings,
      metadata: project.metadata,
      statistics: project.statistics,
      educationInfo: project.educationInfo,
      lastAccessedAt: project.lastAccessedAt,
      lastModifiedAt: project.lastModifiedAt,
      publishedAt: project.publishedAt,
      archivedAt: project.archivedAt,
      createdAt: project.createdAt,
      updatedAt: project.updatedAt,
      owner: project.owner,
      category: project.category,
      tags: project.tags,
      permissions: project.permissions,
      versions: project.versions,
      collaborators: project.collaborators,
      activities: project.activities
    }
  }
}
