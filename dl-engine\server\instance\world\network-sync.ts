/*
DL-Engine Network Sync Service
数字化学习引擎 - 网络同步服务

实现状态同步、事件广播和客户端管理功能
*/

import { Application } from '@feathersjs/feathers'
import { Spark } from 'primus'
import { InstanceID } from '@ir-engine/common/src/schemas/networking/instance.schema'
import { UserID } from '@ir-engine/common/src/schemas/user/user.schema'
import logger from '@ir-engine/server-core/src/ServerLogger'
import { Action, NetworkActions } from '@ir-engine/hyperflux'

/**
 * 网络同步事件类型
 */
export enum NetworkSyncEventType {
  USER_JOINED = 'user_joined',
  USER_LEFT = 'user_left',
  STATE_UPDATE = 'state_update',
  ENTITY_CREATE = 'entity_create',
  ENTITY_UPDATE = 'entity_update',
  ENTITY_DESTROY = 'entity_destroy',
  CHAT_MESSAGE = 'chat_message',
  VOICE_DATA = 'voice_data',
  CUSTOM_EVENT = 'custom_event'
}

/**
 * 客户端连接信息
 */
interface ClientConnection {
  userId: UserID
  spark: Spark
  instanceId: InstanceID
  joinedAt: Date
  lastPing: Date
  isActive: boolean
  permissions: string[]
  metadata: Record<string, any>
}

/**
 * 网络同步配置
 */
interface NetworkSyncConfig {
  maxClientsPerInstance: number
  heartbeatInterval: number
  stateUpdateInterval: number
  compressionEnabled: boolean
  bandwidthLimit: number
}

/**
 * 状态同步数据
 */
interface StateSyncData {
  timestamp: number
  instanceId: InstanceID
  entities: EntityStateData[]
  worldState: Record<string, any>
  checksum: string
}

/**
 * 实体状态数据
 */
interface EntityStateData {
  entityId: string
  components: Record<string, any>
  transform: {
    position: [number, number, number]
    rotation: [number, number, number, number]
    scale: [number, number, number]
  }
  version: number
}

/**
 * 网络同步服务类
 */
export class NetworkSyncService {
  private app: Application
  private clients: Map<string, ClientConnection> = new Map()
  private instanceClients: Map<InstanceID, Set<string>> = new Map()
  private config: NetworkSyncConfig
  private syncIntervals: Map<InstanceID, NodeJS.Timeout> = new Map()

  constructor(app: Application) {
    this.app = app
    this.config = this.getDefaultConfig()
    this.initializeNetworkSync()
  }

  /**
   * 客户端连接处理
   */
  async handleClientConnection(spark: Spark, userId: UserID, instanceId: InstanceID): Promise<void> {
    try {
      const clientId = spark.id
      
      // 检查实例是否存在
      const instance = await this.app.service('instance').get(instanceId)
      if (!instance) {
        throw new Error('实例不存在')
      }

      // 检查客户端数量限制
      const instanceClientCount = this.instanceClients.get(instanceId)?.size || 0
      if (instanceClientCount >= this.config.maxClientsPerInstance) {
        throw new Error('实例客户端数量已达上限')
      }

      // 创建客户端连接记录
      const connection: ClientConnection = {
        userId,
        spark,
        instanceId,
        joinedAt: new Date(),
        lastPing: new Date(),
        isActive: true,
        permissions: await this.getUserPermissions(userId, instanceId),
        metadata: {}
      }

      // 注册客户端
      this.clients.set(clientId, connection)
      
      // 添加到实例客户端列表
      if (!this.instanceClients.has(instanceId)) {
        this.instanceClients.set(instanceId, new Set())
      }
      this.instanceClients.get(instanceId)!.add(clientId)

      // 设置事件监听器
      this.setupClientEventListeners(spark, connection)

      // 发送初始状态
      await this.sendInitialState(connection)

      // 广播用户加入事件
      await this.broadcastToInstance(instanceId, {
        type: NetworkSyncEventType.USER_JOINED,
        userId,
        timestamp: Date.now()
      }, clientId)

      // 启动实例同步（如果还没有启动）
      this.startInstanceSync(instanceId)

      logger.info('客户端连接成功', { clientId, userId, instanceId })

    } catch (error) {
      logger.error('客户端连接失败', { error, userId, instanceId })
      spark.end('连接失败: ' + error.message)
    }
  }

  /**
   * 客户端断开连接处理
   */
  async handleClientDisconnection(clientId: string): Promise<void> {
    try {
      const connection = this.clients.get(clientId)
      if (!connection) return

      const { userId, instanceId } = connection

      // 从客户端列表中移除
      this.clients.delete(clientId)
      
      // 从实例客户端列表中移除
      const instanceClients = this.instanceClients.get(instanceId)
      if (instanceClients) {
        instanceClients.delete(clientId)
        
        // 如果实例没有客户端了，停止同步
        if (instanceClients.size === 0) {
          this.stopInstanceSync(instanceId)
          this.instanceClients.delete(instanceId)
        }
      }

      // 广播用户离开事件
      await this.broadcastToInstance(instanceId, {
        type: NetworkSyncEventType.USER_LEFT,
        userId,
        timestamp: Date.now()
      })

      logger.info('客户端断开连接', { clientId, userId, instanceId })

    } catch (error) {
      logger.error('客户端断开连接处理失败', { error, clientId })
    }
  }

  /**
   * 广播消息到实例中的所有客户端
   */
  async broadcastToInstance(instanceId: InstanceID, data: any, excludeClientId?: string): Promise<void> {
    const instanceClients = this.instanceClients.get(instanceId)
    if (!instanceClients) return

    const message = JSON.stringify(data)
    
    for (const clientId of instanceClients) {
      if (clientId === excludeClientId) continue
      
      const connection = this.clients.get(clientId)
      if (connection && connection.isActive) {
        try {
          connection.spark.write(message)
        } catch (error) {
          logger.error('广播消息失败', { error, clientId, instanceId })
          // 标记连接为非活跃
          connection.isActive = false
        }
      }
    }
  }

  /**
   * 发送消息给特定客户端
   */
  async sendToClient(clientId: string, data: any): Promise<void> {
    const connection = this.clients.get(clientId)
    if (!connection || !connection.isActive) return

    try {
      const message = JSON.stringify(data)
      connection.spark.write(message)
    } catch (error) {
      logger.error('发送消息给客户端失败', { error, clientId })
      connection.isActive = false
    }
  }

  /**
   * 处理状态更新
   */
  async handleStateUpdate(instanceId: InstanceID, stateData: StateSyncData): Promise<void> {
    try {
      // 验证状态数据
      if (!this.validateStateData(stateData)) {
        logger.warn('状态数据验证失败', { instanceId })
        return
      }

      // 压缩数据（如果启用）
      const data = this.config.compressionEnabled 
        ? this.compressData(stateData)
        : stateData

      // 广播状态更新
      await this.broadcastToInstance(instanceId, {
        type: NetworkSyncEventType.STATE_UPDATE,
        data,
        timestamp: Date.now()
      })

    } catch (error) {
      logger.error('状态更新处理失败', { error, instanceId })
    }
  }

  /**
   * 处理实体创建
   */
  async handleEntityCreate(instanceId: InstanceID, entityData: EntityStateData): Promise<void> {
    await this.broadcastToInstance(instanceId, {
      type: NetworkSyncEventType.ENTITY_CREATE,
      entity: entityData,
      timestamp: Date.now()
    })
  }

  /**
   * 处理实体更新
   */
  async handleEntityUpdate(instanceId: InstanceID, entityData: EntityStateData): Promise<void> {
    await this.broadcastToInstance(instanceId, {
      type: NetworkSyncEventType.ENTITY_UPDATE,
      entity: entityData,
      timestamp: Date.now()
    })
  }

  /**
   * 处理实体销毁
   */
  async handleEntityDestroy(instanceId: InstanceID, entityId: string): Promise<void> {
    await this.broadcastToInstance(instanceId, {
      type: NetworkSyncEventType.ENTITY_DESTROY,
      entityId,
      timestamp: Date.now()
    })
  }

  /**
   * 获取实例客户端数量
   */
  getInstanceClientCount(instanceId: InstanceID): number {
    return this.instanceClients.get(instanceId)?.size || 0
  }

  /**
   * 获取实例客户端列表
   */
  getInstanceClients(instanceId: InstanceID): ClientConnection[] {
    const clientIds = this.instanceClients.get(instanceId)
    if (!clientIds) return []

    return Array.from(clientIds)
      .map(id => this.clients.get(id))
      .filter(Boolean) as ClientConnection[]
  }

  /**
   * 设置客户端事件监听器
   */
  private setupClientEventListeners(spark: Spark, connection: ClientConnection): void {
    const clientId = spark.id

    // 心跳检测
    spark.on('heartbeat', () => {
      connection.lastPing = new Date()
    })

    // 状态更新
    spark.on('state-update', (data) => {
      this.handleClientStateUpdate(connection, data)
    })

    // 聊天消息
    spark.on('chat-message', (data) => {
      this.handleChatMessage(connection, data)
    })

    // 语音数据
    spark.on('voice-data', (data) => {
      this.handleVoiceData(connection, data)
    })

    // 自定义事件
    spark.on('custom-event', (data) => {
      this.handleCustomEvent(connection, data)
    })

    // 断开连接
    spark.on('end', () => {
      this.handleClientDisconnection(clientId)
    })
  }

  /**
   * 发送初始状态给客户端
   */
  private async sendInitialState(connection: ClientConnection): Promise<void> {
    try {
      // 获取实例当前状态
      const instanceState = await this.getInstanceState(connection.instanceId)
      
      // 发送初始状态
      await this.sendToClient(connection.spark.id, {
        type: 'initial-state',
        state: instanceState,
        timestamp: Date.now()
      })

    } catch (error) {
      logger.error('发送初始状态失败', { error, clientId: connection.spark.id })
    }
  }

  /**
   * 启动实例同步
   */
  private startInstanceSync(instanceId: InstanceID): void {
    if (this.syncIntervals.has(instanceId)) return

    const interval = setInterval(async () => {
      try {
        const stateData = await this.collectInstanceState(instanceId)
        await this.handleStateUpdate(instanceId, stateData)
      } catch (error) {
        logger.error('实例同步失败', { error, instanceId })
      }
    }, this.config.stateUpdateInterval)

    this.syncIntervals.set(instanceId, interval)
    logger.info('启动实例同步', { instanceId })
  }

  /**
   * 停止实例同步
   */
  private stopInstanceSync(instanceId: InstanceID): void {
    const interval = this.syncIntervals.get(instanceId)
    if (interval) {
      clearInterval(interval)
      this.syncIntervals.delete(instanceId)
      logger.info('停止实例同步', { instanceId })
    }
  }

  /**
   * 获取默认配置
   */
  private getDefaultConfig(): NetworkSyncConfig {
    return {
      maxClientsPerInstance: 100,
      heartbeatInterval: 30000,
      stateUpdateInterval: 50, // 20 FPS
      compressionEnabled: true,
      bandwidthLimit: 1024 * 1024 // 1MB/s
    }
  }

  /**
   * 获取用户权限
   */
  private async getUserPermissions(userId: UserID, instanceId: InstanceID): Promise<string[]> {
    // 实现权限获取逻辑
    return ['read', 'write']
  }

  /**
   * 验证状态数据
   */
  private validateStateData(data: StateSyncData): boolean {
    return data && data.instanceId && data.entities && Array.isArray(data.entities)
  }

  /**
   * 压缩数据
   */
  private compressData(data: any): any {
    // 实现数据压缩逻辑
    return data
  }

  /**
   * 获取实例状态
   */
  private async getInstanceState(instanceId: InstanceID): Promise<any> {
    // 实现实例状态获取逻辑
    return {}
  }

  /**
   * 收集实例状态
   */
  private async collectInstanceState(instanceId: InstanceID): Promise<StateSyncData> {
    // 实现状态收集逻辑
    return {
      timestamp: Date.now(),
      instanceId,
      entities: [],
      worldState: {},
      checksum: ''
    }
  }

  /**
   * 处理客户端状态更新
   */
  private async handleClientStateUpdate(connection: ClientConnection, data: any): Promise<void> {
    // 实现客户端状态更新处理
  }

  /**
   * 处理聊天消息
   */
  private async handleChatMessage(connection: ClientConnection, data: any): Promise<void> {
    await this.broadcastToInstance(connection.instanceId, {
      type: NetworkSyncEventType.CHAT_MESSAGE,
      userId: connection.userId,
      message: data.message,
      timestamp: Date.now()
    }, connection.spark.id)
  }

  /**
   * 处理语音数据
   */
  private async handleVoiceData(connection: ClientConnection, data: any): Promise<void> {
    // 实现语音数据处理
  }

  /**
   * 处理自定义事件
   */
  private async handleCustomEvent(connection: ClientConnection, data: any): Promise<void> {
    await this.broadcastToInstance(connection.instanceId, {
      type: NetworkSyncEventType.CUSTOM_EVENT,
      userId: connection.userId,
      eventData: data,
      timestamp: Date.now()
    }, connection.spark.id)
  }
}

// 导出服务
export default (app: Application): void => {
  const networkSync = new NetworkSyncService(app)
  app.set('networkSync', networkSync)
}
