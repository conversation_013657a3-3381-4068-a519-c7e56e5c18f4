import { Injectable, Logger } from '@nestjs/common';
import { MinioService } from './minio.service';
import * as crypto from 'crypto';

export interface AccessPermission {
  userId: string;
  bucketName: string;
  objectName?: string; // 如果为空，则是桶级权限
  permissions: ('read' | 'write' | 'delete' | 'admin')[];
  expiresAt?: Date;
  conditions?: {
    ipWhitelist?: string[];
    timeRange?: {
      start: string; // HH:mm
      end: string; // HH:mm
    };
    maxDownloads?: number;
    refererWhitelist?: string[];
  };
  createdAt: Date;
  createdBy: string;
}

export interface TemporaryAccess {
  token: string;
  userId: string;
  bucketName: string;
  objectName?: string;
  permissions: string[];
  expiresAt: Date;
  usageCount: number;
  maxUsage: number;
  metadata?: Record<string, any>;
}

export interface AccessLog {
  id: string;
  userId: string;
  action: string;
  bucketName: string;
  objectName?: string;
  ip: string;
  userAgent: string;
  success: boolean;
  error?: string;
  timestamp: Date;
  responseTime: number;
}

@Injectable()
export class AccessControlService {
  private readonly logger = new Logger(AccessControlService.name);
  private readonly permissions = new Map<string, AccessPermission>();
  private readonly temporaryAccess = new Map<string, TemporaryAccess>();
  private readonly accessLogs: AccessLog[] = [];
  private readonly maxLogEntries = 10000;

  constructor(private minioService: MinioService) {
    // 定期清理过期的权限和临时访问
    setInterval(() => this.cleanupExpiredAccess(), 60000); // 每分钟清理一次
  }

  /**
   * 设置用户权限
   */
  setUserPermission(permission: Omit<AccessPermission, 'createdAt'>): void {
    const key = this.generatePermissionKey(permission.userId, permission.bucketName, permission.objectName);
    
    this.permissions.set(key, {
      ...permission,
      createdAt: new Date(),
    });

    this.logger.debug(`Permission set for user ${permission.userId} on ${permission.bucketName}${permission.objectName ? `/${permission.objectName}` : ''}`);
  }

  /**
   * 检查用户权限
   */
  async checkPermission(
    userId: string,
    bucketName: string,
    action: 'read' | 'write' | 'delete' | 'admin',
    objectName?: string,
    context?: {
      ip?: string;
      userAgent?: string;
      referer?: string;
    }
  ): Promise<boolean> {
    try {
      // 检查对象级权限
      if (objectName) {
        const objectKey = this.generatePermissionKey(userId, bucketName, objectName);
        const objectPermission = this.permissions.get(objectKey);
        
        if (objectPermission && this.validatePermission(objectPermission, action, context)) {
          return true;
        }
      }

      // 检查桶级权限
      const bucketKey = this.generatePermissionKey(userId, bucketName);
      const bucketPermission = this.permissions.get(bucketKey);
      
      if (bucketPermission && this.validatePermission(bucketPermission, action, context)) {
        return true;
      }

      // 检查全局权限（所有桶）
      const globalKey = this.generatePermissionKey(userId, '*');
      const globalPermission = this.permissions.get(globalKey);
      
      if (globalPermission && this.validatePermission(globalPermission, action, context)) {
        return true;
      }

      return false;
    } catch (error) {
      this.logger.error(`Failed to check permission for user ${userId}:`, error);
      return false;
    }
  }

  /**
   * 检查上传权限
   */
  async checkUploadPermission(
    userId: string,
    bucketName: string,
    action: 'write' | 'admin'
  ): Promise<boolean> {
    return await this.checkPermission(userId, bucketName, action);
  }

  /**
   * 创建临时访问令牌
   */
  createTemporaryAccess(
    userId: string,
    bucketName: string,
    permissions: string[],
    expiresIn: number = 3600, // 默认1小时
    maxUsage: number = 1,
    objectName?: string,
    metadata?: Record<string, any>
  ): string {
    const token = this.generateAccessToken();
    const expiresAt = new Date(Date.now() + expiresIn * 1000);

    const temporaryAccess: TemporaryAccess = {
      token,
      userId,
      bucketName,
      objectName,
      permissions,
      expiresAt,
      usageCount: 0,
      maxUsage,
      metadata,
    };

    this.temporaryAccess.set(token, temporaryAccess);

    this.logger.debug(`Temporary access created for user ${userId}: ${token}`);
    return token;
  }

  /**
   * 验证临时访问令牌
   */
  validateTemporaryAccess(
    token: string,
    action: string,
    bucketName: string,
    objectName?: string
  ): boolean {
    const access = this.temporaryAccess.get(token);
    
    if (!access) {
      return false;
    }

    // 检查是否过期
    if (new Date() > access.expiresAt) {
      this.temporaryAccess.delete(token);
      return false;
    }

    // 检查使用次数
    if (access.usageCount >= access.maxUsage) {
      this.temporaryAccess.delete(token);
      return false;
    }

    // 检查桶名
    if (access.bucketName !== bucketName) {
      return false;
    }

    // 检查对象名（如果指定）
    if (access.objectName && access.objectName !== objectName) {
      return false;
    }

    // 检查权限
    if (!access.permissions.includes(action)) {
      return false;
    }

    // 增加使用次数
    access.usageCount++;

    return true;
  }

  /**
   * 撤销临时访问令牌
   */
  revokeTemporaryAccess(token: string): boolean {
    const deleted = this.temporaryAccess.delete(token);
    if (deleted) {
      this.logger.debug(`Temporary access revoked: ${token}`);
    }
    return deleted;
  }

  /**
   * 生成预签名URL（带权限检查）
   */
  async generateSecurePresignedUrl(
    userId: string,
    method: 'GET' | 'PUT' | 'DELETE',
    bucketName: string,
    objectName: string,
    expiresIn: number = 3600,
    context?: {
      ip?: string;
      userAgent?: string;
      referer?: string;
    }
  ): Promise<string | null> {
    // 映射HTTP方法到权限
    const actionMap = {
      'GET': 'read' as const,
      'PUT': 'write' as const,
      'DELETE': 'delete' as const,
    };

    const action = actionMap[method];
    const hasPermission = await this.checkPermission(userId, bucketName, action, objectName, context);

    if (!hasPermission) {
      this.logger.warn(`Permission denied for user ${userId} to ${method} ${bucketName}/${objectName}`);
      return null;
    }

    try {
      const url = await this.minioService.getPresignedUrl(method, bucketName, objectName, { expires: expiresIn });
      
      this.logAccess({
        userId,
        action: `presigned_${method.toLowerCase()}`,
        bucketName,
        objectName,
        ip: context?.ip || 'unknown',
        userAgent: context?.userAgent || 'unknown',
        success: true,
        timestamp: new Date(),
        responseTime: 0,
      });

      return url;
    } catch (error) {
      this.logger.error(`Failed to generate presigned URL for ${bucketName}/${objectName}:`, error);
      
      this.logAccess({
        userId,
        action: `presigned_${method.toLowerCase()}`,
        bucketName,
        objectName,
        ip: context?.ip || 'unknown',
        userAgent: context?.userAgent || 'unknown',
        success: false,
        error: error.message,
        timestamp: new Date(),
        responseTime: 0,
      });

      return null;
    }
  }

  /**
   * 记录访问日志
   */
  logAccess(log: Omit<AccessLog, 'id'>): void {
    const accessLog: AccessLog = {
      id: crypto.randomUUID(),
      ...log,
    };

    this.accessLogs.push(accessLog);

    // 限制日志数量
    if (this.accessLogs.length > this.maxLogEntries) {
      this.accessLogs.splice(0, this.accessLogs.length - this.maxLogEntries);
    }

    if (!log.success) {
      this.logger.warn(`Access denied: ${log.userId} ${log.action} ${log.bucketName}/${log.objectName || ''} - ${log.error}`);
    }
  }

  /**
   * 获取访问日志
   */
  getAccessLogs(
    userId?: string,
    bucketName?: string,
    limit: number = 100
  ): AccessLog[] {
    let logs = this.accessLogs;

    if (userId) {
      logs = logs.filter(log => log.userId === userId);
    }

    if (bucketName) {
      logs = logs.filter(log => log.bucketName === bucketName);
    }

    return logs
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(0, limit);
  }

  /**
   * 获取用户权限列表
   */
  getUserPermissions(userId: string): AccessPermission[] {
    const userPermissions: AccessPermission[] = [];

    for (const permission of this.permissions.values()) {
      if (permission.userId === userId) {
        userPermissions.push(permission);
      }
    }

    return userPermissions.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
  }

  /**
   * 删除用户权限
   */
  removeUserPermission(userId: string, bucketName: string, objectName?: string): boolean {
    const key = this.generatePermissionKey(userId, bucketName, objectName);
    const deleted = this.permissions.delete(key);
    
    if (deleted) {
      this.logger.debug(`Permission removed for user ${userId} on ${bucketName}${objectName ? `/${objectName}` : ''}`);
    }
    
    return deleted;
  }

  /**
   * 获取访问统计
   */
  getAccessStats(timeRange?: { start: Date; end: Date }): {
    totalRequests: number;
    successfulRequests: number;
    failedRequests: number;
    topUsers: Array<{ userId: string; count: number }>;
    topBuckets: Array<{ bucketName: string; count: number }>;
    actionStats: Record<string, number>;
  } {
    let logs = this.accessLogs;

    if (timeRange) {
      logs = logs.filter(log => 
        log.timestamp >= timeRange.start && log.timestamp <= timeRange.end
      );
    }

    const totalRequests = logs.length;
    const successfulRequests = logs.filter(log => log.success).length;
    const failedRequests = totalRequests - successfulRequests;

    // 统计用户访问次数
    const userCounts = new Map<string, number>();
    const bucketCounts = new Map<string, number>();
    const actionCounts = new Map<string, number>();

    for (const log of logs) {
      userCounts.set(log.userId, (userCounts.get(log.userId) || 0) + 1);
      bucketCounts.set(log.bucketName, (bucketCounts.get(log.bucketName) || 0) + 1);
      actionCounts.set(log.action, (actionCounts.get(log.action) || 0) + 1);
    }

    const topUsers = Array.from(userCounts.entries())
      .map(([userId, count]) => ({ userId, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    const topBuckets = Array.from(bucketCounts.entries())
      .map(([bucketName, count]) => ({ bucketName, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    const actionStats = Object.fromEntries(actionCounts);

    return {
      totalRequests,
      successfulRequests,
      failedRequests,
      topUsers,
      topBuckets,
      actionStats,
    };
  }

  private validatePermission(
    permission: AccessPermission,
    action: string,
    context?: {
      ip?: string;
      userAgent?: string;
      referer?: string;
    }
  ): boolean {
    // 检查权限是否包含所需操作
    if (!permission.permissions.includes(action as any)) {
      return false;
    }

    // 检查是否过期
    if (permission.expiresAt && new Date() > permission.expiresAt) {
      return false;
    }

    // 检查条件
    if (permission.conditions && context) {
      // IP白名单检查
      if (permission.conditions.ipWhitelist && context.ip) {
        if (!permission.conditions.ipWhitelist.includes(context.ip)) {
          return false;
        }
      }

      // 时间范围检查
      if (permission.conditions.timeRange) {
        const now = new Date();
        const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
        
        if (currentTime < permission.conditions.timeRange.start || currentTime > permission.conditions.timeRange.end) {
          return false;
        }
      }

      // Referer白名单检查
      if (permission.conditions.refererWhitelist && context.referer) {
        if (!permission.conditions.refererWhitelist.some(allowed => context.referer!.includes(allowed))) {
          return false;
        }
      }
    }

    return true;
  }

  private generatePermissionKey(userId: string, bucketName: string, objectName?: string): string {
    return `${userId}:${bucketName}${objectName ? `:${objectName}` : ''}`;
  }

  private generateAccessToken(): string {
    return crypto.randomBytes(32).toString('hex');
  }

  private cleanupExpiredAccess(): void {
    const now = new Date();
    let cleanedPermissions = 0;
    let cleanedTokens = 0;

    // 清理过期权限
    for (const [key, permission] of this.permissions.entries()) {
      if (permission.expiresAt && now > permission.expiresAt) {
        this.permissions.delete(key);
        cleanedPermissions++;
      }
    }

    // 清理过期临时访问令牌
    for (const [token, access] of this.temporaryAccess.entries()) {
      if (now > access.expiresAt) {
        this.temporaryAccess.delete(token);
        cleanedTokens++;
      }
    }

    if (cleanedPermissions > 0 || cleanedTokens > 0) {
      this.logger.debug(`Cleaned up ${cleanedPermissions} expired permissions and ${cleanedTokens} expired tokens`);
    }
  }
}
