/*
DL-Engine Task Services
数字化学习引擎 - 任务服务集成

整合任务调度器、任务队列和工作进程服务
*/

import { Application } from '@feathersjs/feathers'
import logger from '@ir-engine/server-core/src/ServerLogger'

// 导入服务
import TaskSchedulerService from './scheduler/task-scheduler'
import TaskQueueService from './queue/task-queue'
import TaskWorkerService from './workers/task-worker'

/**
 * 任务服务配置接口
 */
export interface TaskServicesConfig {
  scheduler: {
    enabled: boolean
    maxConcurrentTasks: number
    processingInterval: number
    defaultRetryConfig: {
      maxAttempts: number
      backoffStrategy: string
      initialDelay: number
      maxDelay: number
    }
  }
  queue: {
    enabled: boolean
    defaultQueueConfig: {
      maxSize: number
      maxRetries: number
      retryDelay: number
      visibility: number
      persistence: boolean
    }
    redis: {
      host: string
      port: number
      password?: string
      db: number
    }
  }
  worker: {
    enabled: boolean
    defaultWorkerCount: number
    workerConfig: {
      maxConcurrency: number
      timeout: number
      retryAttempts: number
      healthCheckInterval: number
    }
  }
}

/**
 * 任务服务管理器类
 */
export class TaskServices {
  private app: Application
  private config: TaskServicesConfig
  private services: Map<string, any> = new Map()
  private isInitialized: boolean = false

  constructor(app: Application, config?: Partial<TaskServicesConfig>) {
    this.app = app
    this.config = this.mergeWithDefaultConfig(config)
  }

  /**
   * 初始化所有任务服务
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      logger.warn('任务服务已经初始化')
      return
    }

    try {
      logger.info('开始初始化任务服务')

      // 初始化任务队列服务
      if (this.config.queue.enabled) {
        await this.initializeTaskQueue()
      }

      // 初始化工作进程服务
      if (this.config.worker.enabled) {
        await this.initializeTaskWorker()
      }

      // 初始化任务调度器服务
      if (this.config.scheduler.enabled) {
        await this.initializeTaskScheduler()
      }

      // 设置服务间的依赖关系
      this.setupServiceDependencies()

      // 注册默认任务处理器
      this.registerDefaultHandlers()

      this.isInitialized = true
      logger.info('任务服务初始化完成')

    } catch (error) {
      logger.error('任务服务初始化失败', { error })
      throw error
    }
  }

  /**
   * 启动所有服务
   */
  async start(): Promise<void> {
    if (!this.isInitialized) {
      await this.initialize()
    }

    try {
      logger.info('启动任务服务')

      // 启动任务队列服务
      const taskQueue = this.services.get('taskQueue')
      if (taskQueue && taskQueue.start) {
        await taskQueue.start()
      }

      // 启动工作进程服务
      const taskWorker = this.services.get('taskWorker')
      if (taskWorker && taskWorker.start) {
        await taskWorker.start()
      }

      // 启动任务调度器服务
      const taskScheduler = this.services.get('taskScheduler')
      if (taskScheduler && taskScheduler.start) {
        await taskScheduler.start()
      }

      logger.info('任务服务启动完成')

    } catch (error) {
      logger.error('任务服务启动失败', { error })
      throw error
    }
  }

  /**
   * 停止所有服务
   */
  async stop(): Promise<void> {
    try {
      logger.info('停止任务服务')

      // 按相反顺序停止服务
      const serviceNames = ['taskScheduler', 'taskWorker', 'taskQueue']
      
      for (const serviceName of serviceNames) {
        const service = this.services.get(serviceName)
        if (service && service.stop) {
          await service.stop()
        }
      }

      logger.info('任务服务停止完成')

    } catch (error) {
      logger.error('任务服务停止失败', { error })
      throw error
    }
  }

  /**
   * 获取服务实例
   */
  getService<T>(serviceName: string): T | null {
    return this.services.get(serviceName) || null
  }

  /**
   * 获取所有服务状态
   */
  getServicesStatus(): Record<string, any> {
    const status: Record<string, any> = {}

    for (const [serviceName, service] of this.services) {
      status[serviceName] = {
        enabled: true,
        running: service.isRunning ? service.isRunning() : true,
        stats: service.getStats ? service.getStats() : null
      }
    }

    return status
  }

  /**
   * 创建任务
   */
  async createTask(taskDefinition: any): Promise<string> {
    const taskScheduler = this.services.get('taskScheduler')
    if (!taskScheduler) {
      throw new Error('任务调度器服务未启用')
    }

    return await taskScheduler.createTask(taskDefinition)
  }

  /**
   * 取消任务
   */
  async cancelTask(taskId: string): Promise<void> {
    const taskScheduler = this.services.get('taskScheduler')
    if (!taskScheduler) {
      throw new Error('任务调度器服务未启用')
    }

    return await taskScheduler.cancelTask(taskId)
  }

  /**
   * 获取任务状态
   */
  getTaskStatus(taskId: string): any {
    const taskScheduler = this.services.get('taskScheduler')
    if (!taskScheduler) {
      throw new Error('任务调度器服务未启用')
    }

    return taskScheduler.getTaskStatus(taskId)
  }

  /**
   * 注册任务处理器
   */
  registerTaskHandler(name: string, handler: any): void {
    const taskScheduler = this.services.get('taskScheduler')
    const taskWorker = this.services.get('taskWorker')

    if (taskScheduler) {
      taskScheduler.registerHandler(name, handler)
    }

    if (taskWorker) {
      taskWorker.registerTaskHandler(name, handler)
    }

    logger.info('任务处理器已注册', { name })
  }

  /**
   * 创建队列
   */
  async createQueue(queueConfig: any): Promise<void> {
    const taskQueue = this.services.get('taskQueue')
    if (!taskQueue) {
      throw new Error('任务队列服务未启用')
    }

    return await taskQueue.createQueue(queueConfig)
  }

  /**
   * 发送消息到队列
   */
  async enqueueMessage(queueName: string, payload: any, options?: any): Promise<string> {
    const taskQueue = this.services.get('taskQueue')
    if (!taskQueue) {
      throw new Error('任务队列服务未启用')
    }

    return await taskQueue.enqueue(queueName, payload, options)
  }

  /**
   * 初始化任务调度器服务
   */
  private async initializeTaskScheduler(): Promise<void> {
    logger.debug('初始化任务调度器服务')
    
    // 注册服务到应用
    TaskSchedulerService(this.app)
    
    const taskScheduler = this.app.get('taskScheduler')
    this.services.set('taskScheduler', taskScheduler)
    
    logger.debug('任务调度器服务初始化完成')
  }

  /**
   * 初始化任务队列服务
   */
  private async initializeTaskQueue(): Promise<void> {
    logger.debug('初始化任务队列服务')
    
    // 注册服务到应用
    TaskQueueService(this.app)
    
    const taskQueue = this.app.get('taskQueue')
    this.services.set('taskQueue', taskQueue)
    
    // 创建默认队列
    await this.createDefaultQueues(taskQueue)
    
    logger.debug('任务队列服务初始化完成')
  }

  /**
   * 初始化工作进程服务
   */
  private async initializeTaskWorker(): Promise<void> {
    logger.debug('初始化工作进程服务')
    
    // 注册服务到应用
    TaskWorkerService(this.app)
    
    const taskWorker = this.app.get('taskWorker')
    this.services.set('taskWorker', taskWorker)
    
    logger.debug('工作进程服务初始化完成')
  }

  /**
   * 设置服务间的依赖关系
   */
  private setupServiceDependencies(): void {
    logger.debug('设置服务间依赖关系')

    const taskScheduler = this.services.get('taskScheduler')
    const taskQueue = this.services.get('taskQueue')
    const taskWorker = this.services.get('taskWorker')

    // 任务调度器使用队列和工作进程
    if (taskScheduler && taskQueue) {
      // 任务调度器监听队列事件
      taskQueue.on('message-enqueued', (message: any) => {
        logger.debug('消息已入队', { messageId: message.id, queueName: message.queueName })
      })

      taskQueue.on('message-failed', (message: any, error: Error) => {
        logger.warn('消息处理失败', { messageId: message.id, error: error.message })
      })
    }

    if (taskScheduler && taskWorker) {
      // 任务调度器监听工作进程事件
      taskWorker.on('task-completed', (task: any, result: any) => {
        logger.debug('任务执行完成', { taskId: task.id, workerId: result.workerId })
      })

      taskWorker.on('task-failed', (task: any, error: Error) => {
        logger.warn('任务执行失败', { taskId: task.id, error: error.message })
      })
    }

    logger.debug('服务间依赖关系设置完成')
  }

  /**
   * 注册默认任务处理器
   */
  private registerDefaultHandlers(): void {
    logger.debug('注册默认任务处理器')

    // 实例管理任务处理器
    this.registerTaskHandler('instance-create', {
      async execute(payload: any, context: any) {
        const instanceManager = context.app.get('instanceManager')
        if (instanceManager) {
          return await instanceManager.createInstance(payload)
        }
        throw new Error('实例管理器服务不可用')
      }
    })

    this.registerTaskHandler('instance-destroy', {
      async execute(payload: any, context: any) {
        const instanceManager = context.app.get('instanceManager')
        if (instanceManager) {
          return await instanceManager.destroyInstance(payload.instanceId, payload.force)
        }
        throw new Error('实例管理器服务不可用')
      }
    })

    // 扩缩容任务处理器
    this.registerTaskHandler('scaling-evaluate', {
      async execute(payload: any, context: any) {
        const scalingManager = context.app.get('scalingManager')
        if (scalingManager) {
          return await scalingManager.evaluateScaling(payload.locationId)
        }
        throw new Error('扩缩容管理器服务不可用')
      }
    })

    // 数据清理任务处理器
    this.registerTaskHandler('cleanup-expired-data', {
      async execute(payload: any, context: any) {
        // 实现数据清理逻辑
        logger.info('执行数据清理任务', { payload })
        return { cleaned: true, timestamp: new Date() }
      }
    })

    // 健康检查任务处理器
    this.registerTaskHandler('health-check', {
      async execute(payload: any, context: any) {
        // 实现健康检查逻辑
        const services = ['instanceManager', 'networkSync', 'physicsSync', 'scalingManager']
        const results: Record<string, boolean> = {}

        for (const serviceName of services) {
          const service = context.app.get(serviceName)
          results[serviceName] = service ? true : false
        }

        return { healthy: Object.values(results).every(Boolean), services: results }
      }
    })

    logger.debug('默认任务处理器注册完成')
  }

  /**
   * 创建默认队列
   */
  private async createDefaultQueues(taskQueue: any): Promise<void> {
    const defaultQueues = [
      {
        name: 'instance-tasks',
        type: 'priority',
        maxSize: 1000,
        maxRetries: 3,
        retryDelay: 5000,
        visibility: 30000,
        persistence: true
      },
      {
        name: 'scaling-tasks',
        type: 'fifo',
        maxSize: 500,
        maxRetries: 2,
        retryDelay: 10000,
        visibility: 60000,
        persistence: true
      },
      {
        name: 'cleanup-tasks',
        type: 'delay',
        maxSize: 100,
        maxRetries: 1,
        retryDelay: 30000,
        visibility: 120000,
        persistence: false
      }
    ]

    for (const queueConfig of defaultQueues) {
      try {
        await taskQueue.createQueue(queueConfig)
        logger.debug('默认队列创建成功', { queueName: queueConfig.name })
      } catch (error) {
        logger.warn('默认队列创建失败', { error, queueName: queueConfig.name })
      }
    }
  }

  /**
   * 合并默认配置
   */
  private mergeWithDefaultConfig(config?: Partial<TaskServicesConfig>): TaskServicesConfig {
    const defaultConfig: TaskServicesConfig = {
      scheduler: {
        enabled: true,
        maxConcurrentTasks: 10,
        processingInterval: 1000,
        defaultRetryConfig: {
          maxAttempts: 3,
          backoffStrategy: 'exponential',
          initialDelay: 1000,
          maxDelay: 30000
        }
      },
      queue: {
        enabled: true,
        defaultQueueConfig: {
          maxSize: 1000,
          maxRetries: 3,
          retryDelay: 5000,
          visibility: 30000,
          persistence: true
        },
        redis: {
          host: 'localhost',
          port: 6379,
          db: 0
        }
      },
      worker: {
        enabled: true,
        defaultWorkerCount: 4,
        workerConfig: {
          maxConcurrency: 1,
          timeout: 30000,
          retryAttempts: 3,
          healthCheckInterval: 30000
        }
      }
    }

    return {
      scheduler: { ...defaultConfig.scheduler, ...config?.scheduler },
      queue: { ...defaultConfig.queue, ...config?.queue },
      worker: { ...defaultConfig.worker, ...config?.worker }
    }
  }
}

/**
 * 创建并配置任务服务
 */
export function createTaskServices(app: Application, config?: Partial<TaskServicesConfig>): TaskServices {
  return new TaskServices(app, config)
}

/**
 * 默认导出 - 用于 FeathersJS 服务注册
 */
export default (app: Application): void => {
  // 创建任务服务管理器
  const taskServices = createTaskServices(app)
  
  // 注册到应用
  app.set('taskServices', taskServices)
  
  // 自动初始化和启动
  taskServices.initialize().then(() => {
    return taskServices.start()
  }).catch(error => {
    logger.error('任务服务自动启动失败', { error })
  })

  logger.info('DL-Engine 任务服务已注册')
}
