import { Injectable, Inject, Logger, OnModuleDestroy } from '@nestjs/common';

export interface RedisKeyOptions {
  ttl?: number; // 过期时间（秒）
  nx?: boolean; // 仅当键不存在时设置
  xx?: boolean; // 仅当键存在时设置
}

@Injectable()
export class RedisService implements OnModuleDestroy {
  private readonly logger = new Logger(RedisService.name);

  constructor(
    @Inject('REDIS_CLIENT') private readonly client: any,
  ) {}

  async onModuleDestroy() {
    await this.client.quit();
  }

  // 基础操作
  async set(key: string, value: any, options?: RedisKeyOptions): Promise<void> {
    try {
      const serializedValue = typeof value === 'string' ? value : JSON.stringify(value);
      
      const args = [key, serializedValue];
      
      if (options?.ttl) {
        args.push('EX', options.ttl.toString());
      }
      if (options?.nx) {
        args.push('NX');
      }
      if (options?.xx) {
        args.push('XX');
      }
      
      await this.client.set(...args);
    } catch (error) {
      this.logger.error(`Failed to set key ${key}:`, error);
      throw error;
    }
  }

  async get<T = any>(key: string): Promise<T | null> {
    try {
      const value = await this.client.get(key);
      if (value === null) return null;
      
      try {
        return JSON.parse(value);
      } catch {
        return value as T;
      }
    } catch (error) {
      this.logger.error(`Failed to get key ${key}:`, error);
      throw error;
    }
  }

  async del(key: string | string[]): Promise<number> {
    try {
      const keys = Array.isArray(key) ? key : [key];
      return await this.client.del(...keys);
    } catch (error) {
      this.logger.error(`Failed to delete keys:`, error);
      throw error;
    }
  }

  async exists(key: string): Promise<boolean> {
    try {
      const result = await this.client.exists(key);
      return result === 1;
    } catch (error) {
      this.logger.error(`Failed to check existence of key ${key}:`, error);
      throw error;
    }
  }

  async expire(key: string, seconds: number): Promise<boolean> {
    try {
      const result = await this.client.expire(key, seconds);
      return result === 1;
    } catch (error) {
      this.logger.error(`Failed to set expiration for key ${key}:`, error);
      throw error;
    }
  }

  async ttl(key: string): Promise<number> {
    try {
      return await this.client.ttl(key);
    } catch (error) {
      this.logger.error(`Failed to get TTL for key ${key}:`, error);
      throw error;
    }
  }

  // 哈希操作
  async hset(key: string, field: string, value: any): Promise<void> {
    try {
      const serializedValue = typeof value === 'string' ? value : JSON.stringify(value);
      await this.client.hSet(key, field, serializedValue);
    } catch (error) {
      this.logger.error(`Failed to hset ${key}.${field}:`, error);
      throw error;
    }
  }

  async hget<T = any>(key: string, field: string): Promise<T | null> {
    try {
      const value = await this.client.hGet(key, field);
      if (value === null) return null;
      
      try {
        return JSON.parse(value);
      } catch {
        return value as T;
      }
    } catch (error) {
      this.logger.error(`Failed to hget ${key}.${field}:`, error);
      throw error;
    }
  }

  async hmset(key: string, data: Record<string, any>): Promise<void> {
    try {
      const serializedData = {};
      for (const [field, value] of Object.entries(data)) {
        serializedData[field] = typeof value === 'string' ? value : JSON.stringify(value);
      }
      await this.client.hMSet(key, serializedData);
    } catch (error) {
      this.logger.error(`Failed to hmset ${key}:`, error);
      throw error;
    }
  }

  async hmget<T = any>(key: string, fields: string[]): Promise<(T | null)[]> {
    try {
      const values = await this.client.hmGet(key, fields);
      return values.map(value => {
        if (value === null) return null;
        try {
          return JSON.parse(value);
        } catch {
          return value as T;
        }
      });
    } catch (error) {
      this.logger.error(`Failed to hmget ${key}:`, error);
      throw error;
    }
  }

  async hgetall<T = any>(key: string): Promise<Record<string, T>> {
    try {
      const data = await this.client.hGetAll(key);
      const result = {};
      
      for (const [field, value] of Object.entries(data)) {
        try {
          result[field] = JSON.parse(value as string);
        } catch {
          result[field] = value;
        }
      }
      
      return result;
    } catch (error) {
      this.logger.error(`Failed to hgetall ${key}:`, error);
      throw error;
    }
  }

  async hdel(key: string, field: string | string[]): Promise<number> {
    try {
      const fields = Array.isArray(field) ? field : [field];
      return await this.client.hDel(key, ...fields);
    } catch (error) {
      this.logger.error(`Failed to hdel ${key}:`, error);
      throw error;
    }
  }

  // 列表操作
  async lpush(key: string, value: any): Promise<number> {
    try {
      const serializedValue = typeof value === 'string' ? value : JSON.stringify(value);
      return await this.client.lPush(key, serializedValue);
    } catch (error) {
      this.logger.error(`Failed to lpush ${key}:`, error);
      throw error;
    }
  }

  async rpush(key: string, value: any): Promise<number> {
    try {
      const serializedValue = typeof value === 'string' ? value : JSON.stringify(value);
      return await this.client.rPush(key, serializedValue);
    } catch (error) {
      this.logger.error(`Failed to rpush ${key}:`, error);
      throw error;
    }
  }

  async lpop<T = any>(key: string): Promise<T | null> {
    try {
      const value = await this.client.lPop(key);
      if (value === null) return null;
      
      try {
        return JSON.parse(value);
      } catch {
        return value as T;
      }
    } catch (error) {
      this.logger.error(`Failed to lpop ${key}:`, error);
      throw error;
    }
  }

  async rpop<T = any>(key: string): Promise<T | null> {
    try {
      const value = await this.client.rPop(key);
      if (value === null) return null;
      
      try {
        return JSON.parse(value);
      } catch {
        return value as T;
      }
    } catch (error) {
      this.logger.error(`Failed to rpop ${key}:`, error);
      throw error;
    }
  }

  async llen(key: string): Promise<number> {
    try {
      return await this.client.lLen(key);
    } catch (error) {
      this.logger.error(`Failed to llen ${key}:`, error);
      throw error;
    }
  }

  // 集合操作
  async sadd(key: string, member: any): Promise<number> {
    try {
      const serializedMember = typeof member === 'string' ? member : JSON.stringify(member);
      return await this.client.sAdd(key, serializedMember);
    } catch (error) {
      this.logger.error(`Failed to sadd ${key}:`, error);
      throw error;
    }
  }

  async srem(key: string, member: any): Promise<number> {
    try {
      const serializedMember = typeof member === 'string' ? member : JSON.stringify(member);
      return await this.client.sRem(key, serializedMember);
    } catch (error) {
      this.logger.error(`Failed to srem ${key}:`, error);
      throw error;
    }
  }

  async sismember(key: string, member: any): Promise<boolean> {
    try {
      const serializedMember = typeof member === 'string' ? member : JSON.stringify(member);
      const result = await this.client.sIsMember(key, serializedMember);
      return result === 1;
    } catch (error) {
      this.logger.error(`Failed to sismember ${key}:`, error);
      throw error;
    }
  }

  async smembers<T = any>(key: string): Promise<T[]> {
    try {
      const members = await this.client.sMembers(key);
      return members.map(member => {
        try {
          return JSON.parse(member);
        } catch {
          return member as T;
        }
      });
    } catch (error) {
      this.logger.error(`Failed to smembers ${key}:`, error);
      throw error;
    }
  }

  // 有序集合操作
  async zadd(key: string, score: number, member: any): Promise<number> {
    try {
      const serializedMember = typeof member === 'string' ? member : JSON.stringify(member);
      return await this.client.zAdd(key, { score, value: serializedMember });
    } catch (error) {
      this.logger.error(`Failed to zadd ${key}:`, error);
      throw error;
    }
  }

  async zrange<T = any>(key: string, start: number, stop: number, withScores = false): Promise<T[]> {
    try {
      const options = withScores ? { WITHSCORES: true } : {};
      const result = await this.client.zRange(key, start, stop, options);
      
      if (withScores) {
        const pairs = [];
        for (let i = 0; i < result.length; i += 2) {
          try {
            pairs.push({
              value: JSON.parse(result[i]),
              score: parseFloat(result[i + 1]),
            });
          } catch {
            pairs.push({
              value: result[i] as T,
              score: parseFloat(result[i + 1]),
            });
          }
        }
        return pairs as T[];
      } else {
        return result.map(member => {
          try {
            return JSON.parse(member);
          } catch {
            return member as T;
          }
        });
      }
    } catch (error) {
      this.logger.error(`Failed to zrange ${key}:`, error);
      throw error;
    }
  }

  // 模式匹配
  async keys(pattern: string): Promise<string[]> {
    try {
      return await this.client.keys(pattern);
    } catch (error) {
      this.logger.error(`Failed to get keys with pattern ${pattern}:`, error);
      throw error;
    }
  }

  // 管道操作
  async pipeline(commands: Array<{ command: string; args: any[] }>): Promise<any[]> {
    try {
      const pipeline = this.client.multi();
      
      for (const { command, args } of commands) {
        pipeline[command](...args);
      }
      
      return await pipeline.exec();
    } catch (error) {
      this.logger.error('Failed to execute pipeline:', error);
      throw error;
    }
  }

  // 健康检查
  async ping(): Promise<string> {
    try {
      return await this.client.ping();
    } catch (error) {
      this.logger.error('Redis ping failed:', error);
      throw error;
    }
  }

  // 获取信息
  async info(section?: string): Promise<string> {
    try {
      return await this.client.info(section);
    } catch (error) {
      this.logger.error('Failed to get Redis info:', error);
      throw error;
    }
  }

  // 获取客户端
  getClient() {
    return this.client;
  }
}
