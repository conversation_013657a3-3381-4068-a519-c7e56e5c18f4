/**
 * DL-Engine 项目版本控制服务
 * 
 * 核心功能：
 * - 版本创建和管理
 * - 版本比较和合并
 * - 分支管理
 * - 版本回滚
 * - 变更历史追踪
 */

import { Injectable, NotFoundException, ConflictException, BadRequestException } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { Repository } from 'typeorm'
import { ConfigService } from '@nestjs/config'

import { Project } from '../entities/project.entity'
import { ProjectVersion, VersionStatus, VersionType } from '../entities/project-version.entity'
import { ProjectActivity } from '../entities/project-activity.entity'

@Injectable()
export class ProjectVersionService {
  constructor(
    @InjectRepository(Project)
    private readonly projectRepository: Repository<Project>,
    @InjectRepository(ProjectVersion)
    private readonly versionRepository: Repository<ProjectVersion>,
    @InjectRepository(ProjectActivity)
    private readonly activityRepository: Repository<ProjectActivity>,
    private readonly configService: ConfigService
  ) {}

  /**
   * 创建新版本
   */
  async createVersion(projectId: string, versionData: any, userId: string): Promise<ProjectVersion> {
    const project = await this.projectRepository.findOne({ where: { id: projectId } })
    
    if (!project) {
      throw new NotFoundException('项目不存在')
    }

    // 获取当前版本
    const currentVersion = await this.getCurrentVersion(projectId)
    
    // 生成新版本号
    const newVersionNumber = this.generateVersionNumber(currentVersion?.version || '0.0.0', versionData.type)

    // 检查版本号是否已存在
    const existingVersion = await this.versionRepository.findOne({
      where: { projectId, version: newVersionNumber }
    })

    if (existingVersion) {
      throw new ConflictException(`版本 ${newVersionNumber} 已存在`)
    }

    // 创建新版本
    const version = this.versionRepository.create({
      projectId,
      version: newVersionNumber,
      name: versionData.name || `版本 ${newVersionNumber}`,
      description: versionData.description,
      type: versionData.type || VersionType.MINOR,
      status: VersionStatus.DRAFT,
      createdById: userId,
      isCurrent: false,
      changes: versionData.changes || [],
      metadata: {
        ...versionData.metadata,
        createdAt: new Date(),
        createdBy: userId
      }
    })

    const savedVersion = await this.versionRepository.save(version)

    // 记录活动
    await this.recordVersionActivity(projectId, userId, 'version_created', {
      version: newVersionNumber,
      type: versionData.type
    })

    return savedVersion
  }

  /**
   * 发布版本
   */
  async publishVersion(projectId: string, versionId: string, userId: string): Promise<ProjectVersion> {
    const version = await this.versionRepository.findOne({
      where: { id: versionId, projectId }
    })

    if (!version) {
      throw new NotFoundException('版本不存在')
    }

    if (version.status === VersionStatus.PUBLISHED) {
      throw new ConflictException('版本已经发布')
    }

    // 将当前版本设为非当前版本
    await this.versionRepository.update(
      { projectId, isCurrent: true },
      { isCurrent: false }
    )

    // 发布新版本
    version.status = VersionStatus.PUBLISHED
    version.isCurrent = true
    version.publishedAt = new Date()
    version.publishedById = userId

    const publishedVersion = await this.versionRepository.save(version)

    // 更新项目版本号
    await this.projectRepository.update(projectId, {
      version: version.version,
      lastModifiedAt: new Date()
    })

    // 记录活动
    await this.recordVersionActivity(projectId, userId, 'version_published', {
      version: version.version
    })

    return publishedVersion
  }

  /**
   * 获取版本列表
   */
  async getVersions(projectId: string, options: any = {}): Promise<{
    versions: ProjectVersion[]
    total: number
    current: ProjectVersion | null
  }> {
    const { page = 1, limit = 20, status, type } = options

    const queryBuilder = this.versionRepository.createQueryBuilder('version')
      .where('version.projectId = :projectId', { projectId })
      .leftJoinAndSelect('version.createdBy', 'createdBy')
      .leftJoinAndSelect('version.publishedBy', 'publishedBy')

    if (status) {
      queryBuilder.andWhere('version.status = :status', { status })
    }

    if (type) {
      queryBuilder.andWhere('version.type = :type', { type })
    }

    queryBuilder.orderBy('version.createdAt', 'DESC')

    const offset = (page - 1) * limit
    queryBuilder.skip(offset).take(limit)

    const [versions, total] = await queryBuilder.getManyAndCount()

    // 获取当前版本
    const current = await this.getCurrentVersion(projectId)

    return { versions, total, current }
  }

  /**
   * 获取当前版本
   */
  async getCurrentVersion(projectId: string): Promise<ProjectVersion | null> {
    return await this.versionRepository.findOne({
      where: { projectId, isCurrent: true },
      relations: ['createdBy', 'publishedBy']
    })
  }

  /**
   * 版本回滚
   */
  async rollbackToVersion(projectId: string, versionId: string, userId: string): Promise<ProjectVersion> {
    const targetVersion = await this.versionRepository.findOne({
      where: { id: versionId, projectId }
    })

    if (!targetVersion) {
      throw new NotFoundException('目标版本不存在')
    }

    if (targetVersion.status !== VersionStatus.PUBLISHED) {
      throw new BadRequestException('只能回滚到已发布的版本')
    }

    // 创建回滚版本
    const currentVersion = await this.getCurrentVersion(projectId)
    const rollbackVersionNumber = this.generateVersionNumber(
      currentVersion?.version || '1.0.0', 
      VersionType.PATCH
    )

    const rollbackVersion = this.versionRepository.create({
      projectId,
      version: rollbackVersionNumber,
      name: `回滚到 ${targetVersion.version}`,
      description: `回滚到版本 ${targetVersion.version}`,
      type: VersionType.PATCH,
      status: VersionStatus.PUBLISHED,
      createdById: userId,
      publishedById: userId,
      publishedAt: new Date(),
      isCurrent: true,
      isRollback: true,
      rollbackFromId: targetVersion.id,
      changes: [`回滚到版本 ${targetVersion.version}`],
      metadata: {
        rollbackTarget: targetVersion.version,
        rollbackReason: '手动回滚',
        createdAt: new Date(),
        createdBy: userId
      }
    })

    // 将当前版本设为非当前版本
    await this.versionRepository.update(
      { projectId, isCurrent: true },
      { isCurrent: false }
    )

    const savedRollbackVersion = await this.versionRepository.save(rollbackVersion)

    // 更新项目版本号
    await this.projectRepository.update(projectId, {
      version: rollbackVersionNumber,
      lastModifiedAt: new Date()
    })

    // 记录活动
    await this.recordVersionActivity(projectId, userId, 'version_rollback', {
      fromVersion: currentVersion?.version,
      toVersion: targetVersion.version,
      newVersion: rollbackVersionNumber
    })

    return savedRollbackVersion
  }

  /**
   * 比较版本
   */
  async compareVersions(projectId: string, fromVersionId: string, toVersionId: string): Promise<any> {
    const fromVersion = await this.versionRepository.findOne({
      where: { id: fromVersionId, projectId }
    })

    const toVersion = await this.versionRepository.findOne({
      where: { id: toVersionId, projectId }
    })

    if (!fromVersion || !toVersion) {
      throw new NotFoundException('版本不存在')
    }

    return {
      from: {
        id: fromVersion.id,
        version: fromVersion.version,
        name: fromVersion.name,
        createdAt: fromVersion.createdAt
      },
      to: {
        id: toVersion.id,
        version: toVersion.version,
        name: toVersion.name,
        createdAt: toVersion.createdAt
      },
      changes: this.calculateVersionDiff(fromVersion, toVersion),
      summary: {
        addedFeatures: toVersion.changes?.filter(c => c.startsWith('新增')) || [],
        modifiedFeatures: toVersion.changes?.filter(c => c.startsWith('修改')) || [],
        removedFeatures: toVersion.changes?.filter(c => c.startsWith('删除')) || [],
        bugFixes: toVersion.changes?.filter(c => c.startsWith('修复')) || []
      }
    }
  }

  /**
   * 生成版本号
   */
  private generateVersionNumber(currentVersion: string, type: VersionType): string {
    const [major, minor, patch] = currentVersion.split('.').map(Number)

    switch (type) {
      case VersionType.MAJOR:
        return `${major + 1}.0.0`
      case VersionType.MINOR:
        return `${major}.${minor + 1}.0`
      case VersionType.PATCH:
        return `${major}.${minor}.${patch + 1}`
      default:
        return `${major}.${minor}.${patch + 1}`
    }
  }

  /**
   * 计算版本差异
   */
  private calculateVersionDiff(fromVersion: ProjectVersion, toVersion: ProjectVersion): any {
    return {
      metadata: {
        sizeDiff: (toVersion.metadata?.size || 0) - (fromVersion.metadata?.size || 0),
        timeDiff: toVersion.createdAt.getTime() - fromVersion.createdAt.getTime()
      },
      changes: toVersion.changes || [],
      type: toVersion.type
    }
  }

  /**
   * 记录版本活动
   */
  private async recordVersionActivity(
    projectId: string, 
    userId: string, 
    action: string, 
    data: any
  ): Promise<void> {
    const activity = this.activityRepository.create({
      projectId,
      userId,
      action,
      data,
      timestamp: new Date()
    })

    await this.activityRepository.save(activity)
  }
}
