{{- if .Values.services.gateway.enabled }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "dl-engine.fullname" . }}-gateway
  labels:
    {{- include "dl-engine.labels" . | nindent 4 }}
    app.kubernetes.io/component: gateway
spec:
  {{- if not .Values.services.gateway.autoscaling.enabled }}
  replicas: {{ .Values.services.gateway.replicaCount }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "dl-engine.selectorLabels" . | nindent 6 }}
      app.kubernetes.io/component: gateway
  template:
    metadata:
      annotations:
        checksum/config: {{ include (print $.Template.BasePath "/configmap.yaml") . | sha256sum }}
        checksum/secret: {{ include (print $.Template.BasePath "/secret.yaml") . | sha256sum }}
      labels:
        {{- include "dl-engine.selectorLabels" . | nindent 8 }}
        app.kubernetes.io/component: gateway
    spec:
      {{- with .Values.global.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "dl-engine.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: gateway
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.registry }}/{{ .Values.services.gateway.image.repository }}:{{ .Values.services.gateway.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - name: http
              containerPort: {{ .Values.services.gateway.service.targetPort }}
              protocol: TCP
          env:
            - name: NODE_ENV
              valueFrom:
                configMapKeyRef:
                  name: {{ include "dl-engine.fullname" . }}-config
                  key: NODE_ENV
            - name: LOG_LEVEL
              valueFrom:
                configMapKeyRef:
                  name: {{ include "dl-engine.fullname" . }}-config
                  key: LOG_LEVEL
            - name: PORT
              value: "{{ .Values.services.gateway.service.targetPort }}"
            - name: MYSQL_HOST
              valueFrom:
                configMapKeyRef:
                  name: {{ include "dl-engine.fullname" . }}-config
                  key: MYSQL_HOST
            - name: MYSQL_PORT
              valueFrom:
                configMapKeyRef:
                  name: {{ include "dl-engine.fullname" . }}-config
                  key: MYSQL_PORT
            - name: MYSQL_DATABASE
              valueFrom:
                configMapKeyRef:
                  name: {{ include "dl-engine.fullname" . }}-config
                  key: MYSQL_DATABASE
            - name: MYSQL_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "dl-engine.fullname" . }}-secret
                  key: MYSQL_PASSWORD
            - name: REDIS_HOST
              valueFrom:
                configMapKeyRef:
                  name: {{ include "dl-engine.fullname" . }}-config
                  key: REDIS_HOST
            - name: REDIS_PORT
              valueFrom:
                configMapKeyRef:
                  name: {{ include "dl-engine.fullname" . }}-config
                  key: REDIS_PORT
            - name: REDIS_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "dl-engine.fullname" . }}-secret
                  key: REDIS_PASSWORD
            - name: JWT_SECRET
              valueFrom:
                secretKeyRef:
                  name: {{ include "dl-engine.fullname" . }}-secret
                  key: JWT_SECRET
          livenessProbe:
            httpGet:
              path: /health
              port: http
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3
          readinessProbe:
            httpGet:
              path: /ready
              port: http
            initialDelaySeconds: 5
            periodSeconds: 5
            timeoutSeconds: 3
            failureThreshold: 3
          resources:
            {{- toYaml .Values.services.gateway.resources | nindent 12 }}
          volumeMounts:
            - name: tmp
              mountPath: /tmp
            - name: var-cache
              mountPath: /var/cache
      volumes:
        - name: tmp
          emptyDir: {}
        - name: var-cache
          emptyDir: {}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
---
apiVersion: v1
kind: Service
metadata:
  name: {{ include "dl-engine.fullname" . }}-gateway
  labels:
    {{- include "dl-engine.labels" . | nindent 4 }}
    app.kubernetes.io/component: gateway
spec:
  type: {{ .Values.services.gateway.service.type }}
  ports:
    - port: {{ .Values.services.gateway.service.port }}
      targetPort: http
      protocol: TCP
      name: http
  selector:
    {{- include "dl-engine.selectorLabels" . | nindent 4 }}
    app.kubernetes.io/component: gateway
{{- if .Values.services.gateway.autoscaling.enabled }}
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: {{ include "dl-engine.fullname" . }}-gateway
  labels:
    {{- include "dl-engine.labels" . | nindent 4 }}
    app.kubernetes.io/component: gateway
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {{ include "dl-engine.fullname" . }}-gateway
  minReplicas: {{ .Values.services.gateway.autoscaling.minReplicas }}
  maxReplicas: {{ .Values.services.gateway.autoscaling.maxReplicas }}
  metrics:
    {{- if .Values.services.gateway.autoscaling.targetCPUUtilizationPercentage }}
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: {{ .Values.services.gateway.autoscaling.targetCPUUtilizationPercentage }}
    {{- end }}
{{- end }}
{{- end }}
