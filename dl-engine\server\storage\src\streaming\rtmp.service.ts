import { Injectable, Logger, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as NodeMediaServer from 'node-media-server';

export interface RTMPEndpoint {
  streamId: string;
  streamKey: string;
  url: string;
  status: 'inactive' | 'waiting' | 'active';
  createdAt: Date;
  lastActivity?: Date;
  clientInfo?: {
    ip: string;
    userAgent: string;
    app: string;
    flashVer: string;
  };
}

export interface RTMPSession {
  id: string;
  streamId: string;
  ip: string;
  app: string;
  stream: string;
  startTime: Date;
  bytes: {
    read: number;
    write: number;
  };
  bitrate: {
    video: number;
    audio: number;
  };
  isPublishing: boolean;
  isPlaying: boolean;
}

@Injectable()
export class RTMPService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(RTMPService.name);
  private mediaServer: any;
  private readonly endpoints = new Map<string, RTMPEndpoint>();
  private readonly sessions = new Map<string, RTMPSession>();
  private readonly config: any;

  constructor(private configService: ConfigService) {
    this.config = {
      rtmp: {
        port: this.configService.get('RTMP_PORT', 1935),
        chunk_size: 60000,
        gop_cache: true,
        ping: 30,
        ping_timeout: 60,
      },
      http: {
        port: this.configService.get('RTMP_HTTP_PORT', 8000),
        mediaroot: './media',
        allow_origin: '*',
      },
      auth: {
        play: true,
        publish: true,
        secret: this.configService.get('RTMP_SECRET', 'nodemedia2017privatekey'),
      },
      relay: {
        ffmpeg: this.configService.get('FFMPEG_PATH', '/usr/local/bin/ffmpeg'),
        tasks: [],
      },
    };
  }

  async onModuleInit() {
    await this.startServer();
  }

  async onModuleDestroy() {
    await this.stopServer();
  }

  /**
   * 启动RTMP服务器
   */
  private async startServer(): Promise<void> {
    try {
      this.mediaServer = new NodeMediaServer(this.config);

      // 注册事件监听器
      this.registerEventHandlers();

      this.mediaServer.run();
      this.logger.log(`RTMP server started on port ${this.config.rtmp.port}`);
    } catch (error) {
      this.logger.error('Failed to start RTMP server:', error);
      throw error;
    }
  }

  /**
   * 停止RTMP服务器
   */
  private async stopServer(): Promise<void> {
    if (this.mediaServer) {
      try {
        this.mediaServer.stop();
        this.logger.log('RTMP server stopped');
      } catch (error) {
        this.logger.error('Failed to stop RTMP server:', error);
      }
    }
  }

  /**
   * 创建RTMP端点
   */
  async createEndpoint(streamId: string, url: string): Promise<string> {
    const streamKey = this.generateStreamKey();
    const endpoint: RTMPEndpoint = {
      streamId,
      streamKey,
      url: `rtmp://localhost:${this.config.rtmp.port}/live/${streamKey}`,
      status: 'inactive',
      createdAt: new Date(),
    };

    this.endpoints.set(streamId, endpoint);
    this.logger.log(`RTMP endpoint created: ${streamId} -> ${streamKey}`);
    
    return streamKey;
  }

  /**
   * 移除RTMP端点
   */
  async removeEndpoint(streamId: string): Promise<void> {
    const endpoint = this.endpoints.get(streamId);
    if (endpoint) {
      // 断开相关会话
      const sessions = Array.from(this.sessions.values()).filter(s => s.streamId === streamId);
      for (const session of sessions) {
        await this.disconnectSession(session.id);
      }

      this.endpoints.delete(streamId);
      this.logger.log(`RTMP endpoint removed: ${streamId}`);
    }
  }

  /**
   * 开始监听流
   */
  async startListening(streamId: string): Promise<void> {
    const endpoint = this.endpoints.get(streamId);
    if (!endpoint) {
      throw new Error(`RTMP endpoint not found: ${streamId}`);
    }

    endpoint.status = 'waiting';
    this.logger.log(`RTMP listening started: ${streamId}`);
  }

  /**
   * 停止监听流
   */
  async stopListening(streamId: string): Promise<void> {
    const endpoint = this.endpoints.get(streamId);
    if (endpoint) {
      endpoint.status = 'inactive';
      
      // 断开相关会话
      const sessions = Array.from(this.sessions.values()).filter(s => s.streamId === streamId);
      for (const session of sessions) {
        await this.disconnectSession(session.id);
      }

      this.logger.log(`RTMP listening stopped: ${streamId}`);
    }
  }

  /**
   * 获取流URL
   */
  async getStreamUrl(streamId: string, sessionId: string): Promise<string> {
    const endpoint = this.endpoints.get(streamId);
    if (!endpoint) {
      throw new Error(`RTMP endpoint not found: ${streamId}`);
    }

    // 返回内部流URL供FFmpeg使用
    return `rtmp://localhost:${this.config.rtmp.port}/live/${endpoint.streamKey}`;
  }

  /**
   * 获取端点信息
   */
  getEndpoint(streamId: string): RTMPEndpoint | null {
    return this.endpoints.get(streamId) || null;
  }

  /**
   * 获取所有端点
   */
  getEndpoints(): RTMPEndpoint[] {
    return Array.from(this.endpoints.values());
  }

  /**
   * 获取活跃会话
   */
  getActiveSessions(streamId?: string): RTMPSession[] {
    let sessions = Array.from(this.sessions.values());
    
    if (streamId) {
      sessions = sessions.filter(s => s.streamId === streamId);
    }
    
    return sessions.filter(s => s.isPublishing || s.isPlaying);
  }

  /**
   * 断开会话
   */
  async disconnectSession(sessionId: string): Promise<void> {
    const session = this.sessions.get(sessionId);
    if (session) {
      try {
        // 通过媒体服务器断开会话
        if (this.mediaServer && this.mediaServer.getSession) {
          const nmsSession = this.mediaServer.getSession(sessionId);
          if (nmsSession) {
            nmsSession.reject();
          }
        }

        this.sessions.delete(sessionId);
        this.logger.log(`RTMP session disconnected: ${sessionId}`);
      } catch (error) {
        this.logger.error(`Failed to disconnect RTMP session ${sessionId}:`, error);
      }
    }
  }

  /**
   * 验证推流权限
   */
  private async validatePublishAuth(streamKey: string, clientInfo: any): Promise<boolean> {
    // 查找对应的端点
    const endpoint = Array.from(this.endpoints.values()).find(e => e.streamKey === streamKey);
    if (!endpoint) {
      this.logger.warn(`Invalid stream key: ${streamKey}`);
      return false;
    }

    // 检查端点状态
    if (endpoint.status !== 'waiting') {
      this.logger.warn(`Endpoint not ready for publishing: ${endpoint.streamId}`);
      return false;
    }

    // 这里可以添加更多的权限验证逻辑
    // 例如检查IP白名单、令牌验证等

    return true;
  }

  /**
   * 验证播放权限
   */
  private async validatePlayAuth(streamKey: string, clientInfo: any): Promise<boolean> {
    // 查找对应的端点
    const endpoint = Array.from(this.endpoints.values()).find(e => e.streamKey === streamKey);
    if (!endpoint) {
      this.logger.warn(`Invalid stream key for play: ${streamKey}`);
      return false;
    }

    // 检查是否有活跃的推流
    if (endpoint.status !== 'active') {
      this.logger.warn(`No active stream for playback: ${endpoint.streamId}`);
      return false;
    }

    return true;
  }

  /**
   * 注册事件处理器
   */
  private registerEventHandlers(): void {
    // 预发布事件
    this.mediaServer.on('prePublish', async (id: string, StreamPath: string, args: any) => {
      this.logger.debug(`Pre-publish: ${id} ${StreamPath}`);
      
      const streamKey = StreamPath.split('/').pop();
      const clientInfo = {
        ip: args.ip || 'unknown',
        userAgent: args.userAgent || 'unknown',
        app: args.app || 'live',
        flashVer: args.flashVer || 'unknown',
      };

      const isAuthorized = await this.validatePublishAuth(streamKey, clientInfo);
      if (!isAuthorized) {
        this.mediaServer.getSession(id).reject();
        return;
      }
    });

    // 发布开始事件
    this.mediaServer.on('postPublish', (id: string, StreamPath: string, args: any) => {
      this.logger.log(`Publish started: ${id} ${StreamPath}`);
      
      const streamKey = StreamPath.split('/').pop();
      const endpoint = Array.from(this.endpoints.values()).find(e => e.streamKey === streamKey);
      
      if (endpoint) {
        endpoint.status = 'active';
        endpoint.lastActivity = new Date();
        endpoint.clientInfo = {
          ip: args.ip || 'unknown',
          userAgent: args.userAgent || 'unknown',
          app: args.app || 'live',
          flashVer: args.flashVer || 'unknown',
        };

        // 创建会话记录
        const session: RTMPSession = {
          id,
          streamId: endpoint.streamId,
          ip: args.ip || 'unknown',
          app: args.app || 'live',
          stream: streamKey,
          startTime: new Date(),
          bytes: { read: 0, write: 0 },
          bitrate: { video: 0, audio: 0 },
          isPublishing: true,
          isPlaying: false,
        };

        this.sessions.set(id, session);
      }
    });

    // 发布结束事件
    this.mediaServer.on('donePublish', (id: string, StreamPath: string, args: any) => {
      this.logger.log(`Publish ended: ${id} ${StreamPath}`);
      
      const streamKey = StreamPath.split('/').pop();
      const endpoint = Array.from(this.endpoints.values()).find(e => e.streamKey === streamKey);
      
      if (endpoint) {
        endpoint.status = 'waiting';
        endpoint.lastActivity = new Date();
      }

      // 移除会话记录
      this.sessions.delete(id);
    });

    // 预播放事件
    this.mediaServer.on('prePlay', async (id: string, StreamPath: string, args: any) => {
      this.logger.debug(`Pre-play: ${id} ${StreamPath}`);
      
      const streamKey = StreamPath.split('/').pop();
      const clientInfo = {
        ip: args.ip || 'unknown',
        userAgent: args.userAgent || 'unknown',
        app: args.app || 'live',
        flashVer: args.flashVer || 'unknown',
      };

      const isAuthorized = await this.validatePlayAuth(streamKey, clientInfo);
      if (!isAuthorized) {
        this.mediaServer.getSession(id).reject();
        return;
      }
    });

    // 播放开始事件
    this.mediaServer.on('postPlay', (id: string, StreamPath: string, args: any) => {
      this.logger.log(`Play started: ${id} ${StreamPath}`);
      
      const streamKey = StreamPath.split('/').pop();
      const endpoint = Array.from(this.endpoints.values()).find(e => e.streamKey === streamKey);
      
      if (endpoint) {
        // 创建播放会话记录
        const session: RTMPSession = {
          id,
          streamId: endpoint.streamId,
          ip: args.ip || 'unknown',
          app: args.app || 'live',
          stream: streamKey,
          startTime: new Date(),
          bytes: { read: 0, write: 0 },
          bitrate: { video: 0, audio: 0 },
          isPublishing: false,
          isPlaying: true,
        };

        this.sessions.set(id, session);
      }
    });

    // 播放结束事件
    this.mediaServer.on('donePlay', (id: string, StreamPath: string, args: any) => {
      this.logger.log(`Play ended: ${id} ${StreamPath}`);
      
      // 移除播放会话记录
      this.sessions.delete(id);
    });
  }

  private generateStreamKey(): string {
    return `${Date.now()}_${Math.random().toString(36).substr(2, 16)}`;
  }
}
