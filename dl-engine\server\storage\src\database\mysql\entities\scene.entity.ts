import {
  <PERSON><PERSON><PERSON>,
  PrimaryGenerated<PERSON><PERSON>umn,
  <PERSON><PERSON><PERSON>,
  CreateDate<PERSON><PERSON>umn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { ProjectEntity } from './project.entity';

export enum SceneType {
  MAIN = 'main',
  LEVEL = 'level',
  MENU = 'menu',
  LOADING = 'loading',
  CUTSCENE = 'cutscene',
}

@Entity('scenes')
@Index(['projectId'])
@Index(['type'])
export class SceneEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 200 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'enum', enum: SceneType, default: SceneType.MAIN })
  type: SceneType;

  @Column({ type: 'varchar', length: 500, nullable: true })
  thumbnail: string;

  @Column({ type: 'json' })
  sceneData: {
    entities: {
      id: string;
      name: string;
      components: Record<string, any>;
      children: string[];
      parent?: string;
    }[];
    environment: {
      skybox?: string;
      lighting: {
        ambient: number[];
        directional: {
          color: number[];
          intensity: number;
          direction: number[];
          shadows: boolean;
        };
        fog?: {
          enabled: boolean;
          color: number[];
          near: number;
          far: number;
        };
      };
      physics: {
        gravity: number[];
        enabled: boolean;
      };
    };
    camera: {
      position: number[];
      rotation: number[];
      fov: number;
      near: number;
      far: number;
    };
    audio: {
      backgroundMusic?: string;
      ambientSounds: {
        id: string;
        url: string;
        volume: number;
        loop: boolean;
        spatial: boolean;
        position?: number[];
      }[];
    };
  };

  @Column({ type: 'json', nullable: true })
  metadata: {
    version: string;
    lastEditedBy: string;
    editHistory: {
      userId: string;
      action: string;
      timestamp: Date;
      changes: Record<string, any>;
    }[];
    performance: {
      entityCount: number;
      triangleCount: number;
      textureMemory: number;
      estimatedFPS: number;
    };
    validation: {
      errors: string[];
      warnings: string[];
      lastChecked: Date;
    };
  };

  @Column({ type: 'int', default: 0 })
  order: number;

  @Column({ type: 'boolean', default: true })
  isActive: boolean;

  @Column({ type: 'bigint', default: 0 })
  fileSize: number;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // 关联关系
  @Column({ type: 'uuid' })
  projectId: string;

  @ManyToOne(() => ProjectEntity, (project) => project.scenes)
  @JoinColumn({ name: 'projectId' })
  project: ProjectEntity;

  // 虚拟字段
  get entityCount(): number {
    return this.sceneData?.entities?.length || 0;
  }

  get hasErrors(): boolean {
    return (this.metadata?.validation?.errors?.length || 0) > 0;
  }

  get hasWarnings(): boolean {
    return (this.metadata?.validation?.warnings?.length || 0) > 0;
  }
}
