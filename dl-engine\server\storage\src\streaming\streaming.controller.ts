import { 
  Controller, 
  Get, 
  Post, 
  Put, 
  Delete, 
  Body, 
  Param, 
  Query, 
  Headers,
  Logger,
  Req,
} from '@nestjs/common';
import { StreamingService } from './streaming.service';
import { LiveStreamService } from './live-stream.service';
import { VodService } from './vod.service';
import { RTMPService } from './rtmp.service';

@Controller('streaming')
export class StreamingController {
  private readonly logger = new Logger(StreamingController.name);

  constructor(
    private streamingService: StreamingService,
    private liveStreamService: LiveStreamService,
    private vodService: VodService,
    private rtmpService: RTMPService,
  ) {}

  // 流管理
  @Post('streams')
  async createStream(@Body() config: any) {
    const streamId = await this.streamingService.createStream(config);
    return { streamId };
  }

  @Get('streams')
  async getStreams(
    @Query('type') type?: 'live' | 'vod',
    @Query('status') status?: string,
    @Query('limit') limit?: string,
    @Query('offset') offset?: string
  ) {
    const filter = {
      type,
      status,
      limit: limit ? parseInt(limit) : undefined,
      offset: offset ? parseInt(offset) : undefined,
    };

    const streams = this.streamingService.getStreams(filter);
    return { streams };
  }

  @Get('streams/:streamId')
  async getStream(@Param('streamId') streamId: string) {
    const stream = this.streamingService.getStream(streamId);
    if (!stream) {
      return { error: 'Stream not found' };
    }
    return { stream };
  }

  @Put('streams/:streamId')
  async updateStream(@Param('streamId') streamId: string, @Body() updates: any) {
    await this.streamingService.updateStream(streamId, updates);
    return { success: true };
  }

  @Delete('streams/:streamId')
  async deleteStream(@Param('streamId') streamId: string) {
    await this.streamingService.deleteStream(streamId);
    return { success: true };
  }

  @Post('streams/:streamId/start')
  async startStream(@Param('streamId') streamId: string) {
    await this.streamingService.startStream(streamId);
    return { success: true };
  }

  @Post('streams/:streamId/stop')
  async stopStream(@Param('streamId') streamId: string) {
    await this.streamingService.stopStream(streamId);
    return { success: true };
  }

  @Get('streams/:streamId/stats')
  async getStreamStats(@Param('streamId') streamId: string) {
    const stats = await this.streamingService.getStreamStats(streamId);
    if (!stats) {
      return { error: 'Stream not found or no stats available' };
    }
    return { stats };
  }

  @Get('streams/:streamId/playback-urls')
  async getPlaybackUrls(@Param('streamId') streamId: string) {
    const urls = await this.streamingService.getPlaybackUrls(streamId);
    return { urls };
  }

  // 访问令牌管理
  @Post('streams/:streamId/tokens')
  async generateStreamToken(
    @Param('streamId') streamId: string,
    @Body() options: {
      expiresIn?: number;
      permissions?: string[];
      metadata?: Record<string, any>;
    }
  ) {
    const token = await this.streamingService.generateStreamToken(streamId, options);
    return { token };
  }

  @Post('tokens/validate')
  async validateStreamToken(@Body() { token }: { token: string }) {
    const result = await this.streamingService.validateStreamToken(token);
    return result;
  }

  // 直播流管理
  @Get('live/sessions')
  async getLiveSessions(@Query('streamId') streamId?: string) {
    const sessions = this.liveStreamService.getActiveSessions(streamId);
    return { sessions };
  }

  @Post('live/sessions/:sessionId/disconnect')
  async disconnectLiveSession(@Param('sessionId') sessionId: string) {
    await this.liveStreamService.disconnectSession(sessionId);
    return { success: true };
  }

  @Post('live/streams/:streamId/connection')
  async handleLiveConnection(
    @Param('streamId') streamId: string,
    @Body() { sessionId, clientInfo }: any
  ) {
    await this.liveStreamService.handleStreamConnection(streamId, sessionId, clientInfo);
    return { success: true };
  }

  @Post('live/streams/:streamId/disconnection')
  async handleLiveDisconnection(@Body() { sessionId }: { sessionId: string }) {
    await this.liveStreamService.handleStreamDisconnection(sessionId);
    return { success: true };
  }

  // RTMP管理
  @Get('rtmp/endpoints')
  async getRTMPEndpoints() {
    const endpoints = this.rtmpService.getEndpoints();
    return { endpoints };
  }

  @Get('rtmp/endpoints/:streamId')
  async getRTMPEndpoint(@Param('streamId') streamId: string) {
    const endpoint = this.rtmpService.getEndpoint(streamId);
    if (!endpoint) {
      return { error: 'RTMP endpoint not found' };
    }
    return { endpoint };
  }

  @Get('rtmp/sessions')
  async getRTMPSessions(@Query('streamId') streamId?: string) {
    const sessions = this.rtmpService.getActiveSessions(streamId);
    return { sessions };
  }

  @Post('rtmp/sessions/:sessionId/disconnect')
  async disconnectRTMPSession(@Param('sessionId') sessionId: string) {
    await this.rtmpService.disconnectSession(sessionId);
    return { success: true };
  }

  // VOD管理
  @Post('vod/assets')
  async createVodAsset(@Body() config: any) {
    const assetId = await this.vodService.createVodAsset(config);
    return { assetId };
  }

  @Get('vod/assets/:assetId')
  async getVodAsset(@Param('assetId') assetId: string) {
    const asset = this.vodService.getVodAsset(assetId);
    if (!asset) {
      return { error: 'VOD asset not found' };
    }
    return { asset };
  }

  @Post('vod/assets/:assetId/playback')
  async startVodPlayback(
    @Param('assetId') assetId: string,
    @Body() clientInfo: any,
    @Req() req: any
  ) {
    const sessionId = await this.vodService.startPlaybackSession(assetId, {
      ip: req.ip,
      userAgent: req.headers['user-agent'],
      ...clientInfo,
    });
    return { sessionId };
  }

  @Put('vod/playback/:sessionId/progress')
  async updateVodProgress(
    @Param('sessionId') sessionId: string,
    @Body() { position, quality }: { position: number; quality?: string }
  ) {
    await this.vodService.updatePlaybackProgress(sessionId, position, quality);
    return { success: true };
  }

  @Post('vod/playback/:sessionId/buffering')
  async recordVodBuffering(
    @Param('sessionId') sessionId: string,
    @Body() { duration, position }: { duration: number; position: number }
  ) {
    await this.vodService.recordBufferingEvent(sessionId, duration, position);
    return { success: true };
  }

  @Post('vod/playback/:sessionId/end')
  async endVodPlayback(@Param('sessionId') sessionId: string) {
    await this.vodService.endPlaybackSession(sessionId);
    return { success: true };
  }

  @Get('vod/playback/:sessionId')
  async getVodPlaybackSession(@Param('sessionId') sessionId: string) {
    const session = this.vodService.getPlaybackSession(sessionId);
    if (!session) {
      return { error: 'Playback session not found' };
    }
    return { session };
  }

  // 统计信息
  @Get('stats')
  async getServiceStats() {
    return await this.streamingService.getServiceStats();
  }

  @Get('stats/live')
  async getLiveStats() {
    const sessions = this.liveStreamService.getActiveSessions();
    return {
      activeSessions: sessions.length,
      sessions: sessions.map(s => ({
        streamId: s.streamId,
        sessionId: s.sessionId,
        status: s.status,
        startTime: s.startTime,
        viewers: s.outputStats.viewers,
        bandwidth: s.outputStats.bandwidth,
      })),
    };
  }

  @Get('stats/rtmp')
  async getRTMPStats() {
    const sessions = this.rtmpService.getActiveSessions();
    const endpoints = this.rtmpService.getEndpoints();
    
    return {
      activeEndpoints: endpoints.filter(e => e.status === 'active').length,
      totalEndpoints: endpoints.length,
      activeSessions: sessions.length,
      publishingSessions: sessions.filter(s => s.isPublishing).length,
      playingSessions: sessions.filter(s => s.isPlaying).length,
    };
  }

  // 健康检查
  @Get('health')
  async healthCheck() {
    return {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      services: {
        streaming: 'healthy',
        rtmp: 'healthy',
        live: 'healthy',
        vod: 'healthy',
      },
    };
  }

  // 配置管理
  @Get('config')
  async getConfig() {
    return {
      rtmp: {
        port: 1935,
        httpPort: 8000,
      },
      hls: {
        segmentDuration: 6,
        playlistSize: 10,
      },
      transcoding: {
        profiles: [
          { name: '720p', resolution: '1280x720', bitrate: '2500k' },
          { name: '480p', resolution: '854x480', bitrate: '1000k' },
          { name: '360p', resolution: '640x360', bitrate: '500k' },
        ],
      },
    };
  }

  @Put('config')
  async updateConfig(@Body() config: any) {
    // 更新配置逻辑
    return { success: true, message: 'Configuration updated' };
  }
}
