import { Injectable, Logger } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { ConfigService } from '@nestjs/config';
import { PostgreSQLService } from '../database/postgresql/postgresql.service';
import { RedisService } from '../database/redis/redis.service';
import { OllamaService } from './ollama.service';

export interface EmbeddingJob {
  id: string;
  content: string;
  contentType: 'text' | 'image' | 'audio' | 'video';
  model: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  embedding?: number[];
  metadata?: Record<string, any>;
  createdAt: Date;
  completedAt?: Date;
  error?: string;
}

export interface EmbeddingBatch {
  id: string;
  jobs: string[];
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress: number;
  totalJobs: number;
  completedJobs: number;
  failedJobs: number;
  createdAt: Date;
  completedAt?: Date;
}

export interface EmbeddingStats {
  totalEmbeddings: number;
  embeddingsByModel: Record<string, number>;
  embeddingsByType: Record<string, number>;
  averageProcessingTime: number;
  successRate: number;
  queueStats: {
    waiting: number;
    active: number;
    completed: number;
    failed: number;
  };
}

@Injectable()
export class EmbeddingService {
  private readonly logger = new Logger(EmbeddingService.name);
  private readonly jobs = new Map<string, EmbeddingJob>();
  private readonly batches = new Map<string, EmbeddingBatch>();

  constructor(
    private configService: ConfigService,
    private postgresqlService: PostgreSQLService,
    private redisService: RedisService,
    private ollamaService: OllamaService,
    @InjectQueue('embedding-generation') private embeddingQueue: Queue,
  ) {}

  /**
   * 生成单个嵌入向量
   */
  async generateEmbedding(
    content: string,
    options: {
      model?: string;
      contentType?: 'text' | 'image' | 'audio' | 'video';
      normalize?: boolean;
      cache?: boolean;
      metadata?: Record<string, any>;
    } = {}
  ): Promise<number[]> {
    try {
      const model = options.model || this.configService.get('AI_EMBEDDING_MODEL', 'nomic-embed-text');
      const contentType = options.contentType || 'text';
      
      // 检查缓存
      if (options.cache !== false) {
        const cached = await this.getCachedEmbedding(content, model);
        if (cached) {
          this.logger.debug(`Using cached embedding for content: ${content.substring(0, 50)}...`);
          return cached;
        }
      }

      // 生成嵌入向量
      let embedding: number[];
      
      switch (contentType) {
        case 'text':
          embedding = await this.generateTextEmbedding(content, model);
          break;
        case 'image':
          embedding = await this.generateImageEmbedding(content, model);
          break;
        case 'audio':
          embedding = await this.generateAudioEmbedding(content, model);
          break;
        case 'video':
          embedding = await this.generateVideoEmbedding(content, model);
          break;
        default:
          throw new Error(`Unsupported content type: ${contentType}`);
      }

      // 标准化向量
      if (options.normalize !== false) {
        embedding = this.normalizeVector(embedding);
      }

      // 缓存结果
      if (options.cache !== false) {
        await this.cacheEmbedding(content, model, embedding);
      }

      // 存储到向量数据库
      await this.storeEmbedding(content, embedding, {
        model,
        contentType,
        metadata: options.metadata,
      });

      this.logger.debug(`Generated embedding for content: ${content.substring(0, 50)}...`);
      return embedding;
    } catch (error) {
      this.logger.error('Failed to generate embedding:', error);
      throw error;
    }
  }

  /**
   * 批量生成嵌入向量
   */
  async generateBatchEmbeddings(
    contents: Array<{
      content: string;
      contentType?: 'text' | 'image' | 'audio' | 'video';
      metadata?: Record<string, any>;
    }>,
    options: {
      model?: string;
      batchSize?: number;
      parallel?: boolean;
      onProgress?: (completed: number, total: number) => void;
    } = {}
  ): Promise<string> {
    try {
      const batchId = this.generateBatchId();
      const model = options.model || this.configService.get('AI_EMBEDDING_MODEL', 'nomic-embed-text');
      const batchSize = options.batchSize || 10;

      const batch: EmbeddingBatch = {
        id: batchId,
        jobs: [],
        status: 'pending',
        progress: 0,
        totalJobs: contents.length,
        completedJobs: 0,
        failedJobs: 0,
        createdAt: new Date(),
      };

      this.batches.set(batchId, batch);

      // 创建任务
      for (const item of contents) {
        const jobId = await this.createEmbeddingJob(item.content, {
          model,
          contentType: item.contentType || 'text',
          metadata: item.metadata,
        });
        batch.jobs.push(jobId);
      }

      // 提交到队列
      batch.status = 'processing';
      
      if (options.parallel) {
        // 并行处理
        const chunks = this.chunkArray(batch.jobs, batchSize);
        
        for (const chunk of chunks) {
          await Promise.all(
            chunk.map(jobId => this.embeddingQueue.add('process-embedding', { jobId }, {
              priority: 5,
              attempts: 3,
            }))
          );
        }
      } else {
        // 串行处理
        for (const jobId of batch.jobs) {
          await this.embeddingQueue.add('process-embedding', { jobId }, {
            priority: 5,
            attempts: 3,
          });
        }
      }

      this.logger.log(`Batch embedding job created: ${batchId} (${contents.length} items)`);
      return batchId;
    } catch (error) {
      this.logger.error('Failed to create batch embedding job:', error);
      throw error;
    }
  }

  /**
   * 处理嵌入任务
   */
  async processEmbeddingJob(jobId: string): Promise<void> {
    const job = this.jobs.get(jobId);
    if (!job) {
      throw new Error(`Embedding job not found: ${jobId}`);
    }

    try {
      job.status = 'processing';
      
      const embedding = await this.generateEmbedding(job.content, {
        model: job.model,
        contentType: job.contentType,
        metadata: job.metadata,
      });

      job.embedding = embedding;
      job.status = 'completed';
      job.completedAt = new Date();

      // 更新批次进度
      await this.updateBatchProgress(jobId);

      this.logger.debug(`Embedding job completed: ${jobId}`);
    } catch (error) {
      job.status = 'failed';
      job.error = error.message;
      job.completedAt = new Date();

      // 更新批次进度
      await this.updateBatchProgress(jobId);

      this.logger.error(`Embedding job failed: ${jobId}`, error);
      throw error;
    }
  }

  /**
   * 获取嵌入任务
   */
  getEmbeddingJob(jobId: string): EmbeddingJob | null {
    return this.jobs.get(jobId) || null;
  }

  /**
   * 获取批次信息
   */
  getBatch(batchId: string): EmbeddingBatch | null {
    return this.batches.get(batchId) || null;
  }

  /**
   * 搜索相似嵌入
   */
  async searchSimilarEmbeddings(
    queryEmbedding: number[],
    options: {
      collection?: string;
      limit?: number;
      threshold?: number;
      filters?: Record<string, any>;
    } = {}
  ): Promise<Array<{
    id: string;
    content: string;
    similarity: number;
    metadata?: Record<string, any>;
  }>> {
    try {
      return await this.postgresqlService.searchSimilarVectors(queryEmbedding, {
        collection: options.collection || 'embeddings',
        limit: options.limit || 10,
        threshold: options.threshold || 0.7,
        filters: options.filters,
      });
    } catch (error) {
      this.logger.error('Failed to search similar embeddings:', error);
      throw error;
    }
  }

  /**
   * 获取统计信息
   */
  async getStats(): Promise<EmbeddingStats> {
    try {
      const jobs = Array.from(this.jobs.values());
      const completedJobs = jobs.filter(j => j.status === 'completed');
      const failedJobs = jobs.filter(j => j.status === 'failed');

      const embeddingsByModel: Record<string, number> = {};
      const embeddingsByType: Record<string, number> = {};

      for (const job of completedJobs) {
        embeddingsByModel[job.model] = (embeddingsByModel[job.model] || 0) + 1;
        embeddingsByType[job.contentType] = (embeddingsByType[job.contentType] || 0) + 1;
      }

      const averageProcessingTime = completedJobs.length > 0
        ? completedJobs.reduce((sum, job) => {
            return sum + (job.completedAt!.getTime() - job.createdAt.getTime());
          }, 0) / completedJobs.length
        : 0;

      const successRate = jobs.length > 0
        ? (completedJobs.length / jobs.length) * 100
        : 0;

      const queueStats = await this.getQueueStats();

      return {
        totalEmbeddings: completedJobs.length,
        embeddingsByModel,
        embeddingsByType,
        averageProcessingTime,
        successRate,
        queueStats,
      };
    } catch (error) {
      this.logger.error('Failed to get embedding stats:', error);
      throw error;
    }
  }

  private async generateTextEmbedding(text: string, model: string): Promise<number[]> {
    return await this.ollamaService.generateEmbedding(text, { model });
  }

  private async generateImageEmbedding(imagePath: string, model: string): Promise<number[]> {
    // 实现图像嵌入生成
    // 这里可以使用CLIP或其他多模态模型
    throw new Error('Image embedding not implemented yet');
  }

  private async generateAudioEmbedding(audioPath: string, model: string): Promise<number[]> {
    // 实现音频嵌入生成
    // 这里可以使用Wav2Vec或其他音频模型
    throw new Error('Audio embedding not implemented yet');
  }

  private async generateVideoEmbedding(videoPath: string, model: string): Promise<number[]> {
    // 实现视频嵌入生成
    // 这里可以结合图像和音频嵌入
    throw new Error('Video embedding not implemented yet');
  }

  private normalizeVector(vector: number[]): number[] {
    const magnitude = Math.sqrt(vector.reduce((sum, val) => sum + val * val, 0));
    return magnitude > 0 ? vector.map(val => val / magnitude) : vector;
  }

  private async getCachedEmbedding(content: string, model: string): Promise<number[] | null> {
    try {
      const key = `embedding:${model}:${this.hashContent(content)}`;
      const cached = await this.redisService.get(key);
      return cached ? JSON.parse(cached) : null;
    } catch (error) {
      this.logger.warn('Failed to get cached embedding:', error);
      return null;
    }
  }

  private async cacheEmbedding(content: string, model: string, embedding: number[]): Promise<void> {
    try {
      const key = `embedding:${model}:${this.hashContent(content)}`;
      const ttl = this.configService.get('EMBEDDING_CACHE_TTL', 86400); // 24小时
      await this.redisService.setex(key, ttl, JSON.stringify(embedding));
    } catch (error) {
      this.logger.warn('Failed to cache embedding:', error);
    }
  }

  private async storeEmbedding(
    content: string,
    embedding: number[],
    metadata: {
      model: string;
      contentType: string;
      metadata?: Record<string, any>;
    }
  ): Promise<void> {
    try {
      await this.postgresqlService.storeVector({
        content,
        vector: embedding,
        collection: 'embeddings',
        metadata: {
          model: metadata.model,
          contentType: metadata.contentType,
          ...metadata.metadata,
        },
      });
    } catch (error) {
      this.logger.warn('Failed to store embedding:', error);
    }
  }

  private async createEmbeddingJob(
    content: string,
    options: {
      model: string;
      contentType: 'text' | 'image' | 'audio' | 'video';
      metadata?: Record<string, any>;
    }
  ): Promise<string> {
    const jobId = this.generateJobId();
    const job: EmbeddingJob = {
      id: jobId,
      content,
      contentType: options.contentType,
      model: options.model,
      status: 'pending',
      metadata: options.metadata,
      createdAt: new Date(),
    };

    this.jobs.set(jobId, job);
    return jobId;
  }

  private async updateBatchProgress(jobId: string): Promise<void> {
    // 查找包含此任务的批次
    for (const batch of this.batches.values()) {
      if (batch.jobs.includes(jobId)) {
        const job = this.jobs.get(jobId);
        if (job) {
          if (job.status === 'completed') {
            batch.completedJobs++;
          } else if (job.status === 'failed') {
            batch.failedJobs++;
          }

          batch.progress = ((batch.completedJobs + batch.failedJobs) / batch.totalJobs) * 100;

          if (batch.progress >= 100) {
            batch.status = batch.failedJobs === 0 ? 'completed' : 'failed';
            batch.completedAt = new Date();
          }
        }
        break;
      }
    }
  }

  private async getQueueStats(): Promise<any> {
    try {
      const waiting = await this.embeddingQueue.waiting();
      const active = await this.embeddingQueue.active();
      const completed = await this.embeddingQueue.completed();
      const failed = await this.embeddingQueue.failed();

      return {
        waiting: waiting.length,
        active: active.length,
        completed: completed.length,
        failed: failed.length,
      };
    } catch (error) {
      return { waiting: 0, active: 0, completed: 0, failed: 0 };
    }
  }

  private chunkArray<T>(array: T[], chunkSize: number): T[][] {
    const chunks = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize));
    }
    return chunks;
  }

  private hashContent(content: string): string {
    // 简单的哈希函数
    let hash = 0;
    for (let i = 0; i < content.length; i++) {
      const char = content.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash).toString(36);
  }

  private generateJobId(): string {
    return `job_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateBatchId(): string {
    return `batch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
