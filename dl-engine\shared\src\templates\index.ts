/**
 * DL-Engine 项目模板系统
 * 提供预定义的教育场景和项目模板
 */

import { Course, Lesson, Transform, Material, Geometry } from '../utils'

// 模板类型枚举
export enum TemplateType {
  // 基础模板
  EMPTY_SCENE = 'empty_scene',
  BASIC_CLASSROOM = 'basic_classroom',
  
  // 学科模板
  PHYSICS_LAB = 'physics_lab',
  CHEMISTRY_LAB = 'chemistry_lab',
  BIOLOGY_LAB = 'biology_lab',
  MATH_CLASSROOM = 'math_classroom',
  HISTORY_MUSEUM = 'history_museum',
  GEOGRAPHY_WORLD = 'geography_world',
  
  // 技能培训模板
  CODING_BOOTCAMP = 'coding_bootcamp',
  DESIGN_STUDIO = 'design_studio',
  LANGUAGE_CAFE = 'language_cafe',
  
  // 特殊场景模板
  VIRTUAL_CONFERENCE = 'virtual_conference',
  EXHIBITION_HALL = 'exhibition_hall',
  TRAINING_GROUND = 'training_ground'
}

// 模板难度级别
export enum TemplateLevel {
  BEGINNER = 'beginner',
  INTERMEDIATE = 'intermediate',
  ADVANCED = 'advanced',
  EXPERT = 'expert'
}

// 模板分类
export enum TemplateCategory {
  EDUCATION = 'education',
  TRAINING = 'training',
  PRESENTATION = 'presentation',
  COLLABORATION = 'collaboration',
  ENTERTAINMENT = 'entertainment'
}

// 模板接口
export interface Template {
  id: string
  name: string
  description: string
  type: TemplateType
  category: TemplateCategory
  level: TemplateLevel
  tags: string[]
  thumbnail: string
  previewImages: string[]
  author: string
  version: string
  createdAt: Date
  updatedAt: Date
  
  // 模板配置
  config: TemplateConfig
  
  // 场景数据
  scene: SceneTemplate
  
  // 课程数据（如果是教育模板）
  course?: CourseTemplate
  
  // 资源列表
  assets: AssetTemplate[]
  
  // 使用统计
  stats: TemplateStats
}

// 模板配置
export interface TemplateConfig {
  // 基础设置
  maxParticipants: number
  defaultDuration: number // 分钟
  supportedDevices: ('desktop' | 'mobile' | 'vr' | 'ar')[]
  
  // 功能开关
  features: {
    voiceChat: boolean
    videoChat: boolean
    screenShare: boolean
    whiteboard: boolean
    fileShare: boolean
    recording: boolean
    analytics: boolean
  }
  
  // 权限设置
  permissions: {
    allowGuestAccess: boolean
    requireApproval: boolean
    moderatorRequired: boolean
    allowUserGeneration: boolean
  }
  
  // 自定义设置
  customization: {
    allowLayoutChange: boolean
    allowAssetUpload: boolean
    allowScriptExecution: boolean
    allowPhysicsSimulation: boolean
  }
}

// 场景模板
export interface SceneTemplate {
  name: string
  description: string
  environment: EnvironmentTemplate
  lighting: LightingTemplate
  objects: ObjectTemplate[]
  spawnPoints: SpawnPointTemplate[]
  interactionZones: InteractionZoneTemplate[]
  physics: PhysicsTemplate
}

// 环境模板
export interface EnvironmentTemplate {
  skybox?: string
  backgroundColor?: string
  fog?: {
    enabled: boolean
    color: string
    near: number
    far: number
  }
  ground?: {
    type: 'plane' | 'terrain' | 'custom'
    material: string
    size?: { width: number; height: number }
    heightMap?: string
  }
}

// 光照模板
export interface LightingTemplate {
  ambient: {
    color: string
    intensity: number
  }
  directional: Array<{
    color: string
    intensity: number
    position: Transform['position']
    target: Transform['position']
    castShadow: boolean
  }>
  point: Array<{
    color: string
    intensity: number
    position: Transform['position']
    distance: number
    decay: number
  }>
  spot: Array<{
    color: string
    intensity: number
    position: Transform['position']
    target: Transform['position']
    angle: number
    penumbra: number
    distance: number
  }>
}

// 对象模板
export interface ObjectTemplate {
  id: string
  name: string
  type: 'mesh' | 'group' | 'sprite' | 'particle' | 'audio' | 'video'
  transform: Transform
  geometry?: Geometry
  material?: Material
  modelUrl?: string
  animations?: Array<{
    name: string
    duration: number
    loop: boolean
    autoPlay: boolean
  }>
  physics?: {
    enabled: boolean
    type: 'static' | 'dynamic' | 'kinematic'
    mass: number
    friction: number
    restitution: number
    shape: 'box' | 'sphere' | 'capsule' | 'mesh'
  }
  interactions?: Array<{
    type: 'click' | 'hover' | 'proximity' | 'collision'
    action: string
    parameters: Record<string, any>
  }>
  metadata?: Record<string, any>
}

// 生成点模板
export interface SpawnPointTemplate {
  id: string
  name: string
  position: Transform['position']
  rotation: Transform['rotation']
  role?: string // 指定角色才能使用此生成点
  maxUsers?: number
}

// 交互区域模板
export interface InteractionZoneTemplate {
  id: string
  name: string
  type: 'sphere' | 'box' | 'cylinder' | 'custom'
  position: Transform['position']
  size: Transform['scale']
  triggers: Array<{
    event: 'enter' | 'exit' | 'stay'
    action: string
    parameters: Record<string, any>
  }>
}

// 物理模板
export interface PhysicsTemplate {
  enabled: boolean
  gravity: Transform['position']
  timeStep: number
  maxSubSteps: number
  collisionDetection: 'discrete' | 'continuous'
  worldScale: number
}

// 课程模板
export interface CourseTemplate {
  title: string
  description: string
  objectives: string[]
  prerequisites: string[]
  estimatedDuration: number // 分钟
  difficulty: TemplateLevel
  lessons: LessonTemplate[]
  assessments: AssessmentTemplate[]
}

// 课程模板
export interface LessonTemplate {
  title: string
  description: string
  type: 'introduction' | 'content' | 'activity' | 'assessment' | 'summary'
  duration: number // 分钟
  content: {
    text?: string
    media?: string[]
    interactions?: Array<{
      type: string
      config: Record<string, any>
    }>
  }
  objectives: string[]
  activities: ActivityTemplate[]
}

// 活动模板
export interface ActivityTemplate {
  id: string
  name: string
  type: 'quiz' | 'simulation' | 'collaboration' | 'exploration' | 'creation'
  description: string
  instructions: string[]
  duration: number // 分钟
  config: Record<string, any>
  scoring?: {
    maxPoints: number
    passingScore: number
    criteria: Array<{
      name: string
      weight: number
      description: string
    }>
  }
}

// 评估模板
export interface AssessmentTemplate {
  id: string
  name: string
  type: 'quiz' | 'project' | 'presentation' | 'peer_review'
  description: string
  questions: QuestionTemplate[]
  timeLimit?: number // 分钟
  attempts: number
  passingScore: number
}

// 问题模板
export interface QuestionTemplate {
  id: string
  type: 'multiple_choice' | 'true_false' | 'short_answer' | 'essay' | 'matching' | 'ordering'
  question: string
  options?: string[]
  correctAnswer: any
  explanation?: string
  points: number
  difficulty: 'easy' | 'medium' | 'hard'
  tags: string[]
}

// 资源模板
export interface AssetTemplate {
  id: string
  name: string
  type: 'model' | 'texture' | 'audio' | 'video' | 'image' | 'script' | 'material'
  url: string
  size: number
  format: string
  metadata: Record<string, any>
  dependencies?: string[]
  license?: {
    type: string
    attribution?: string
    url?: string
  }
}

// 模板统计
export interface TemplateStats {
  downloads: number
  uses: number
  rating: number
  reviews: number
  lastUsed: Date
}

// 模板管理器
export class TemplateManager {
  private templates: Map<string, Template> = new Map()

  // 注册模板
  registerTemplate(template: Template): void {
    this.templates.set(template.id, template)
  }

  // 获取模板
  getTemplate(id: string): Template | undefined {
    return this.templates.get(id)
  }

  // 获取所有模板
  getAllTemplates(): Template[] {
    return Array.from(this.templates.values())
  }

  // 按类型筛选模板
  getTemplatesByType(type: TemplateType): Template[] {
    return this.getAllTemplates().filter(template => template.type === type)
  }

  // 按分类筛选模板
  getTemplatesByCategory(category: TemplateCategory): Template[] {
    return this.getAllTemplates().filter(template => template.category === category)
  }

  // 按难度筛选模板
  getTemplatesByLevel(level: TemplateLevel): Template[] {
    return this.getAllTemplates().filter(template => template.level === level)
  }

  // 搜索模板
  searchTemplates(query: string): Template[] {
    const lowerQuery = query.toLowerCase()
    return this.getAllTemplates().filter(template => 
      template.name.toLowerCase().includes(lowerQuery) ||
      template.description.toLowerCase().includes(lowerQuery) ||
      template.tags.some(tag => tag.toLowerCase().includes(lowerQuery))
    )
  }

  // 创建场景实例
  createSceneFromTemplate(templateId: string, customizations?: Partial<SceneTemplate>): SceneTemplate | null {
    const template = this.getTemplate(templateId)
    if (!template) {
      return null
    }

    // 深拷贝场景模板
    const scene = JSON.parse(JSON.stringify(template.scene))
    
    // 应用自定义设置
    if (customizations) {
      Object.assign(scene, customizations)
    }

    return scene
  }

  // 创建课程实例
  createCourseFromTemplate(templateId: string, customizations?: Partial<CourseTemplate>): CourseTemplate | null {
    const template = this.getTemplate(templateId)
    if (!template || !template.course) {
      return null
    }

    // 深拷贝课程模板
    const course = JSON.parse(JSON.stringify(template.course))
    
    // 应用自定义设置
    if (customizations) {
      Object.assign(course, customizations)
    }

    return course
  }

  // 验证模板
  validateTemplate(template: Template): { valid: boolean; errors: string[] } {
    const errors: string[] = []

    // 基础验证
    if (!template.id) errors.push('模板ID不能为空')
    if (!template.name) errors.push('模板名称不能为空')
    if (!template.scene) errors.push('场景数据不能为空')

    // 场景验证
    if (template.scene) {
      if (!template.scene.spawnPoints || template.scene.spawnPoints.length === 0) {
        errors.push('至少需要一个生成点')
      }
    }

    // 配置验证
    if (template.config) {
      if (template.config.maxParticipants <= 0) {
        errors.push('最大参与者数量必须大于0')
      }
    }

    return {
      valid: errors.length === 0,
      errors
    }
  }
}

// 预定义模板
export const PREDEFINED_TEMPLATES = {
  EMPTY_SCENE: {
    id: 'empty_scene',
    name: '空白场景',
    description: '一个空白的3D场景，适合从零开始创建内容',
    type: TemplateType.EMPTY_SCENE,
    category: TemplateCategory.EDUCATION,
    level: TemplateLevel.BEGINNER
  },
  
  BASIC_CLASSROOM: {
    id: 'basic_classroom',
    name: '基础教室',
    description: '传统教室布局，包含讲台、桌椅和黑板',
    type: TemplateType.BASIC_CLASSROOM,
    category: TemplateCategory.EDUCATION,
    level: TemplateLevel.BEGINNER
  },
  
  PHYSICS_LAB: {
    id: 'physics_lab',
    name: '物理实验室',
    description: '配备各种物理实验器材的虚拟实验室',
    type: TemplateType.PHYSICS_LAB,
    category: TemplateCategory.EDUCATION,
    level: TemplateLevel.INTERMEDIATE
  }
} as const

// 导出默认模板管理器实例
export const templateManager = new TemplateManager()
