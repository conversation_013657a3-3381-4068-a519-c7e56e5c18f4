import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { UserEntity } from './user.entity';
import { CourseEntity } from './course.entity';

export enum AssessmentType {
  QUIZ = 'quiz',
  EXAM = 'exam',
  SURVEY = 'survey',
  SELF_ASSESSMENT = 'self_assessment',
  PEER_ASSESSMENT = 'peer_assessment',
}

export enum QuestionType {
  MULTIPLE_CHOICE = 'multiple_choice',
  TRUE_FALSE = 'true_false',
  SHORT_ANSWER = 'short_answer',
  ESSAY = 'essay',
  FILL_BLANK = 'fill_blank',
  MATCHING = 'matching',
  ORDERING = 'ordering',
  INTERACTIVE_3D = 'interactive_3d',
}

export enum AssessmentStatus {
  NOT_STARTED = 'not_started',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  GRADED = 'graded',
  EXPIRED = 'expired',
}

@Entity('assessments')
@Index(['courseId'])
@Index(['studentId'])
@Index(['type'])
@Index(['status'])
export class AssessmentEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 200 })
  title: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'enum', enum: AssessmentType })
  type: AssessmentType;

  @Column({ type: 'enum', enum: AssessmentStatus, default: AssessmentStatus.NOT_STARTED })
  status: AssessmentStatus;

  @Column({ type: 'json' })
  questions: {
    id: string;
    type: QuestionType;
    question: string;
    options?: string[];
    correctAnswer?: string | string[];
    points: number;
    explanation?: string;
    media?: {
      type: 'image' | 'video' | 'audio' | '3d_scene';
      url: string;
      metadata?: Record<string, any>;
    };
    interactiveElements?: {
      sceneId?: string;
      expectedActions?: string[];
      validationRules?: Record<string, any>;
    };
  }[];

  @Column({ type: 'json', nullable: true })
  responses: {
    questionId: string;
    answer: string | string[];
    isCorrect?: boolean;
    points?: number;
    timeSpent?: number;
    attempts?: number;
    metadata?: Record<string, any>;
  }[];

  @Column({ type: 'json', nullable: true })
  grading: {
    totalScore: number;
    maxScore: number;
    percentage: number;
    grade: string;
    feedback: string;
    questionFeedback: {
      questionId: string;
      score: number;
      maxScore: number;
      feedback: string;
      isCorrect: boolean;
    }[];
    gradedAt: Date;
    autoGraded: boolean;
  };

  @Column({ type: 'int', default: 60 })
  timeLimit: number; // 分钟

  @Column({ type: 'datetime', nullable: true })
  startedAt: Date;

  @Column({ type: 'datetime', nullable: true })
  completedAt: Date;

  @Column({ type: 'datetime', nullable: true })
  dueDate: Date;

  @Column({ type: 'int', default: 1 })
  maxAttempts: number;

  @Column({ type: 'int', default: 0 })
  attemptCount: number;

  @Column({ type: 'json', nullable: true })
  attempts: {
    attemptNumber: number;
    startedAt: Date;
    completedAt?: Date;
    score?: number;
    responses: any[];
    timeSpent: number;
  }[];

  @Column({ type: 'json', nullable: true })
  settings: {
    shuffleQuestions: boolean;
    shuffleOptions: boolean;
    showCorrectAnswers: boolean;
    showScoreImmediately: boolean;
    allowReview: boolean;
    allowBacktrack: boolean;
    requireProctoring: boolean;
    lockdownBrowser: boolean;
    randomizeFromPool: boolean;
    questionPoolSize?: number;
  };

  @Column({ type: 'json', nullable: true })
  analytics: {
    timeSpent: number;
    questionAnalytics: {
      questionId: string;
      timeSpent: number;
      attempts: number;
      difficulty: number;
      discrimination: number;
    }[];
    behaviorMetrics: {
      tabSwitches: number;
      windowBlurs: number;
      copyPasteAttempts: number;
      suspiciousActivity: string[];
    };
  };

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // 关联关系
  @Column({ type: 'uuid' })
  courseId: string;

  @Column({ type: 'uuid' })
  studentId: string;

  @ManyToOne(() => CourseEntity, (course) => course.assessments)
  @JoinColumn({ name: 'courseId' })
  course: CourseEntity;

  @ManyToOne(() => UserEntity)
  @JoinColumn({ name: 'studentId' })
  student: UserEntity;

  // 虚拟字段
  get isExpired(): boolean {
    if (!this.startedAt || !this.timeLimit) return false;
    const expiryTime = new Date(this.startedAt.getTime() + this.timeLimit * 60 * 1000);
    return new Date() > expiryTime;
  }

  get isOverdue(): boolean {
    return this.dueDate ? new Date() > this.dueDate : false;
  }

  get canStart(): boolean {
    return this.status === AssessmentStatus.NOT_STARTED && 
           this.attemptCount < this.maxAttempts && 
           !this.isOverdue;
  }

  get canContinue(): boolean {
    return this.status === AssessmentStatus.IN_PROGRESS && 
           !this.isExpired && 
           !this.isOverdue;
  }

  get totalQuestions(): number {
    return this.questions?.length || 0;
  }

  get maxScore(): number {
    return this.questions?.reduce((sum, q) => sum + q.points, 0) || 0;
  }

  get remainingTime(): number {
    if (!this.startedAt || !this.timeLimit) return this.timeLimit * 60;
    const elapsed = (new Date().getTime() - this.startedAt.getTime()) / 1000;
    const remaining = (this.timeLimit * 60) - elapsed;
    return Math.max(0, remaining);
  }
}
