/*
DL-Engine Task Queue Service
数字化学习引擎 - 任务队列服务

实现消息队列、任务持久化和失败重试功能
*/

import { Application } from '@feathersjs/feathers'
import { EventEmitter } from 'events'
import Redis from 'ioredis'
import logger from '@ir-engine/server-core/src/ServerLogger'
import { TaskDefinition, TaskStatus, TaskResult } from '../scheduler/task-scheduler'

/**
 * 队列类型枚举
 */
export enum QueueType {
  FIFO = 'fifo',           // 先进先出
  LIFO = 'lifo',           // 后进先出
  PRIORITY = 'priority',    // 优先级队列
  DELAY = 'delay'          // 延迟队列
}

/**
 * 队列配置接口
 */
export interface QueueConfig {
  name: string
  type: QueueType
  maxSize: number          // 最大队列长度
  maxRetries: number       // 最大重试次数
  retryDelay: number       // 重试延迟 (ms)
  visibility: number       // 消息可见性超时 (ms)
  persistence: boolean     // 是否持久化
  deadLetterQueue?: string // 死信队列名称
}

/**
 * 队列消息接口
 */
export interface QueueMessage {
  id: string
  queueName: string
  payload: any
  priority: number
  attempts: number
  maxAttempts: number
  createdAt: Date
  scheduledAt?: Date
  processedAt?: Date
  completedAt?: Date
  failedAt?: Date
  error?: string
  metadata: Record<string, any>
}

/**
 * 消息处理器接口
 */
export interface MessageProcessor {
  process(message: QueueMessage): Promise<any>
}

/**
 * 队列统计信息接口
 */
export interface QueueStats {
  queueName: string
  totalMessages: number
  pendingMessages: number
  processingMessages: number
  completedMessages: number
  failedMessages: number
  deadLetterMessages: number
  avgProcessingTime: number
  throughput: number
}

/**
 * 任务队列服务类
 */
export class TaskQueueService extends EventEmitter {
  private app: Application
  private redis: Redis
  private queues: Map<string, QueueConfig> = new Map()
  private processors: Map<string, MessageProcessor> = new Map()
  private processingMessages: Map<string, QueueMessage> = new Map()
  private isRunning: boolean = false
  private processingIntervals: Map<string, NodeJS.Timeout> = new Map()

  constructor(app: Application) {
    super()
    this.app = app
    this.initializeRedis()
    this.initializeTaskQueue()
  }

  /**
   * 创建队列
   */
  async createQueue(config: QueueConfig): Promise<void> {
    try {
      // 验证配置
      this.validateQueueConfig(config)

      // 保存队列配置
      this.queues.set(config.name, config)

      // 在 Redis 中初始化队列
      await this.initializeQueueInRedis(config)

      // 如果需要死信队列，创建死信队列
      if (config.deadLetterQueue) {
        const dlqConfig: QueueConfig = {
          ...config,
          name: config.deadLetterQueue,
          type: QueueType.FIFO,
          maxRetries: 0,
          deadLetterQueue: undefined
        }
        await this.createQueue(dlqConfig)
      }

      logger.info('队列创建成功', { queueName: config.name, config })

    } catch (error) {
      logger.error('队列创建失败', { error, config })
      throw error
    }
  }

  /**
   * 删除队列
   */
  async deleteQueue(queueName: string, force: boolean = false): Promise<void> {
    try {
      const config = this.queues.get(queueName)
      if (!config) {
        throw new Error('队列不存在')
      }

      // 检查队列是否为空
      if (!force) {
        const stats = await this.getQueueStats(queueName)
        if (stats.totalMessages > 0) {
          throw new Error('队列不为空，无法删除')
        }
      }

      // 停止处理
      this.stopProcessing(queueName)

      // 从 Redis 中删除队列数据
      await this.deleteQueueFromRedis(queueName)

      // 移除配置
      this.queues.delete(queueName)

      logger.info('队列删除成功', { queueName })

    } catch (error) {
      logger.error('队列删除失败', { error, queueName })
      throw error
    }
  }

  /**
   * 注册消息处理器
   */
  registerProcessor(queueName: string, processor: MessageProcessor): void {
    this.processors.set(queueName, processor)
    logger.info('消息处理器已注册', { queueName })
  }

  /**
   * 发送消息到队列
   */
  async enqueue(queueName: string, payload: any, options?: {
    priority?: number
    delay?: number
    maxAttempts?: number
    metadata?: Record<string, any>
  }): Promise<string> {
    try {
      const config = this.queues.get(queueName)
      if (!config) {
        throw new Error('队列不存在')
      }

      // 检查队列大小限制
      const queueSize = await this.getQueueSize(queueName)
      if (queueSize >= config.maxSize) {
        throw new Error('队列已满')
      }

      // 创建消息
      const message: QueueMessage = {
        id: this.generateMessageId(),
        queueName,
        payload,
        priority: options?.priority || 0,
        attempts: 0,
        maxAttempts: options?.maxAttempts || config.maxRetries,
        createdAt: new Date(),
        scheduledAt: options?.delay ? new Date(Date.now() + options.delay) : undefined,
        metadata: options?.metadata || {}
      }

      // 根据队列类型添加消息
      await this.addMessageToQueue(config, message)

      logger.debug('消息已入队', { queueName, messageId: message.id })
      
      this.emit('message-enqueued', message)
      return message.id

    } catch (error) {
      logger.error('消息入队失败', { error, queueName })
      throw error
    }
  }

  /**
   * 从队列获取消息
   */
  async dequeue(queueName: string, timeout: number = 0): Promise<QueueMessage | null> {
    try {
      const config = this.queues.get(queueName)
      if (!config) {
        throw new Error('队列不存在')
      }

      // 从队列获取消息
      const message = await this.getMessageFromQueue(config, timeout)
      
      if (message) {
        // 标记为处理中
        message.processedAt = new Date()
        message.attempts++
        this.processingMessages.set(message.id, message)

        logger.debug('消息已出队', { queueName, messageId: message.id })
        this.emit('message-dequeued', message)
      }

      return message

    } catch (error) {
      logger.error('消息出队失败', { error, queueName })
      throw error
    }
  }

  /**
   * 确认消息处理完成
   */
  async ackMessage(messageId: string, result?: any): Promise<void> {
    try {
      const message = this.processingMessages.get(messageId)
      if (!message) {
        throw new Error('消息不存在或未在处理中')
      }

      // 更新消息状态
      message.completedAt = new Date()
      
      // 从处理中列表移除
      this.processingMessages.delete(messageId)

      // 从 Redis 中删除消息
      await this.deleteMessageFromRedis(message)

      // 记录处理结果
      if (result !== undefined) {
        await this.recordMessageResult(message, result)
      }

      logger.debug('消息处理完成', { messageId, queueName: message.queueName })
      
      this.emit('message-acked', message, result)

    } catch (error) {
      logger.error('消息确认失败', { error, messageId })
      throw error
    }
  }

  /**
   * 拒绝消息（重新入队或进入死信队列）
   */
  async nackMessage(messageId: string, error: Error, requeue: boolean = true): Promise<void> {
    try {
      const message = this.processingMessages.get(messageId)
      if (!message) {
        throw new Error('消息不存在或未在处理中')
      }

      const config = this.queues.get(message.queueName)
      if (!config) {
        throw new Error('队列配置不存在')
      }

      // 更新消息错误信息
      message.error = error.message
      message.failedAt = new Date()

      // 从处理中列表移除
      this.processingMessages.delete(messageId)

      // 检查是否需要重试
      if (requeue && message.attempts < message.maxAttempts) {
        // 计算重试延迟
        const retryDelay = this.calculateRetryDelay(message.attempts, config.retryDelay)
        message.scheduledAt = new Date(Date.now() + retryDelay)
        
        // 重新入队
        await this.addMessageToQueue(config, message)
        
        logger.debug('消息重新入队', { 
          messageId, 
          queueName: message.queueName, 
          attempt: message.attempts,
          retryDelay 
        })
        
        this.emit('message-requeued', message)
      } else {
        // 进入死信队列
        await this.moveToDeadLetterQueue(message, config)
        
        logger.warn('消息进入死信队列', { 
          messageId, 
          queueName: message.queueName,
          attempts: message.attempts,
          error: error.message
        })
        
        this.emit('message-dead-lettered', message, error)
      }

    } catch (error) {
      logger.error('消息拒绝失败', { error, messageId })
      throw error
    }
  }

  /**
   * 获取队列统计信息
   */
  async getQueueStats(queueName: string): Promise<QueueStats> {
    try {
      const config = this.queues.get(queueName)
      if (!config) {
        throw new Error('队列不存在')
      }

      // 从 Redis 获取统计信息
      const stats = await this.getQueueStatsFromRedis(queueName)
      
      return {
        queueName,
        totalMessages: stats.total || 0,
        pendingMessages: stats.pending || 0,
        processingMessages: stats.processing || 0,
        completedMessages: stats.completed || 0,
        failedMessages: stats.failed || 0,
        deadLetterMessages: stats.deadLetter || 0,
        avgProcessingTime: stats.avgProcessingTime || 0,
        throughput: stats.throughput || 0
      }

    } catch (error) {
      logger.error('获取队列统计失败', { error, queueName })
      throw error
    }
  }

  /**
   * 清空队列
   */
  async purgeQueue(queueName: string): Promise<number> {
    try {
      const config = this.queues.get(queueName)
      if (!config) {
        throw new Error('队列不存在')
      }

      // 停止处理
      this.stopProcessing(queueName)

      // 清空 Redis 中的队列数据
      const deletedCount = await this.purgeQueueInRedis(queueName)

      // 清空处理中的消息
      for (const [messageId, message] of this.processingMessages) {
        if (message.queueName === queueName) {
          this.processingMessages.delete(messageId)
        }
      }

      logger.info('队列已清空', { queueName, deletedCount })
      
      this.emit('queue-purged', queueName, deletedCount)
      return deletedCount

    } catch (error) {
      logger.error('清空队列失败', { error, queueName })
      throw error
    }
  }

  /**
   * 启动队列处理
   */
  startProcessing(queueName: string): void {
    if (this.processingIntervals.has(queueName)) {
      return // 已经在处理中
    }

    const processor = this.processors.get(queueName)
    if (!processor) {
      throw new Error('未注册消息处理器')
    }

    const interval = setInterval(async () => {
      try {
        await this.processMessages(queueName, processor)
      } catch (error) {
        logger.error('消息处理失败', { error, queueName })
      }
    }, 1000) // 每秒处理一次

    this.processingIntervals.set(queueName, interval)
    logger.info('队列处理已启动', { queueName })
  }

  /**
   * 停止队列处理
   */
  stopProcessing(queueName: string): void {
    const interval = this.processingIntervals.get(queueName)
    if (interval) {
      clearInterval(interval)
      this.processingIntervals.delete(queueName)
      logger.info('队列处理已停止', { queueName })
    }
  }

  /**
   * 启动所有队列处理
   */
  start(): void {
    if (this.isRunning) return

    this.isRunning = true

    // 启动所有已注册处理器的队列
    for (const queueName of this.processors.keys()) {
      this.startProcessing(queueName)
    }

    // 启动可见性超时检查
    this.startVisibilityTimeoutCheck()

    logger.info('任务队列服务已启动')
    this.emit('service-started')
  }

  /**
   * 停止所有队列处理
   */
  stop(): void {
    if (!this.isRunning) return

    this.isRunning = false

    // 停止所有队列处理
    for (const queueName of this.processingIntervals.keys()) {
      this.stopProcessing(queueName)
    }

    // 停止可见性超时检查
    this.stopVisibilityTimeoutCheck()

    logger.info('任务队列服务已停止')
    this.emit('service-stopped')
  }

  /**
   * 处理消息
   */
  private async processMessages(queueName: string, processor: MessageProcessor): Promise<void> {
    const message = await this.dequeue(queueName, 1000) // 1秒超时
    if (!message) return

    try {
      // 处理消息
      const result = await processor.process(message)
      
      // 确认处理完成
      await this.ackMessage(message.id, result)

    } catch (error) {
      // 处理失败，拒绝消息
      await this.nackMessage(message.id, error as Error)
    }
  }

  /**
   * 验证队列配置
   */
  private validateQueueConfig(config: QueueConfig): void {
    if (!config.name) {
      throw new Error('队列名称不能为空')
    }
    if (config.maxSize <= 0) {
      throw new Error('队列最大大小必须大于0')
    }
    if (config.maxRetries < 0) {
      throw new Error('最大重试次数不能小于0')
    }
  }

  /**
   * 初始化 Redis 连接
   */
  private initializeRedis(): void {
    // 这里应该从配置中获取 Redis 连接信息
    this.redis = new Redis({
      host: 'localhost',
      port: 6379,
      retryDelayOnFailover: 100,
      maxRetriesPerRequest: 3
    })

    this.redis.on('error', (error) => {
      logger.error('Redis 连接错误', { error })
    })

    this.redis.on('connect', () => {
      logger.info('Redis 连接成功')
    })
  }

  /**
   * 在 Redis 中初始化队列
   */
  private async initializeQueueInRedis(config: QueueConfig): Promise<void> {
    const queueKey = `queue:${config.name}`
    const configKey = `queue:${config.name}:config`
    
    // 保存队列配置
    await this.redis.hset(configKey, {
      name: config.name,
      type: config.type,
      maxSize: config.maxSize,
      maxRetries: config.maxRetries,
      retryDelay: config.retryDelay,
      visibility: config.visibility,
      persistence: config.persistence ? '1' : '0',
      deadLetterQueue: config.deadLetterQueue || ''
    })

    // 初始化队列数据结构
    switch (config.type) {
      case QueueType.FIFO:
      case QueueType.LIFO:
        await this.redis.del(`${queueKey}:list`)
        break
      case QueueType.PRIORITY:
        await this.redis.del(`${queueKey}:zset`)
        break
      case QueueType.DELAY:
        await this.redis.del(`${queueKey}:delay`)
        break
    }
  }

  /**
   * 从 Redis 中删除队列
   */
  private async deleteQueueFromRedis(queueName: string): Promise<void> {
    const pattern = `queue:${queueName}*`
    const keys = await this.redis.keys(pattern)
    
    if (keys.length > 0) {
      await this.redis.del(...keys)
    }
  }

  /**
   * 获取队列大小
   */
  private async getQueueSize(queueName: string): Promise<number> {
    const config = this.queues.get(queueName)
    if (!config) return 0

    const queueKey = `queue:${queueName}`

    switch (config.type) {
      case QueueType.FIFO:
      case QueueType.LIFO:
        return await this.redis.llen(`${queueKey}:list`)
      case QueueType.PRIORITY:
        return await this.redis.zcard(`${queueKey}:zset`)
      case QueueType.DELAY:
        return await this.redis.zcard(`${queueKey}:delay`)
      default:
        return 0
    }
  }

  /**
   * 添加消息到队列
   */
  private async addMessageToQueue(config: QueueConfig, message: QueueMessage): Promise<void> {
    const queueKey = `queue:${config.name}`
    const messageData = JSON.stringify(message)

    switch (config.type) {
      case QueueType.FIFO:
        await this.redis.lpush(`${queueKey}:list`, messageData)
        break
      case QueueType.LIFO:
        await this.redis.rpush(`${queueKey}:list`, messageData)
        break
      case QueueType.PRIORITY:
        await this.redis.zadd(`${queueKey}:zset`, message.priority, messageData)
        break
      case QueueType.DELAY:
        const score = message.scheduledAt ? message.scheduledAt.getTime() : Date.now()
        await this.redis.zadd(`${queueKey}:delay`, score, messageData)
        break
    }

    // 如果启用持久化，保存到数据库
    if (config.persistence) {
      await this.persistMessage(message)
    }
  }

  /**
   * 从队列获取消息
   */
  private async getMessageFromQueue(config: QueueConfig, timeout: number): Promise<QueueMessage | null> {
    const queueKey = `queue:${config.name}`

    let messageData: string | null = null

    switch (config.type) {
      case QueueType.FIFO:
        const fifoResult = timeout > 0 
          ? await this.redis.brpop(`${queueKey}:list`, timeout / 1000)
          : await this.redis.rpop(`${queueKey}:list`)
        messageData = Array.isArray(fifoResult) ? fifoResult[1] : fifoResult
        break

      case QueueType.LIFO:
        const lifoResult = timeout > 0
          ? await this.redis.blpop(`${queueKey}:list`, timeout / 1000)
          : await this.redis.lpop(`${queueKey}:list`)
        messageData = Array.isArray(lifoResult) ? lifoResult[1] : lifoResult
        break

      case QueueType.PRIORITY:
        const priorityResult = await this.redis.zpopmax(`${queueKey}:zset`)
        messageData = priorityResult.length > 0 ? priorityResult[0] : null
        break

      case QueueType.DELAY:
        const now = Date.now()
        const delayResult = await this.redis.zrangebyscore(`${queueKey}:delay`, 0, now, 'LIMIT', 0, 1)
        if (delayResult.length > 0) {
          messageData = delayResult[0]
          await this.redis.zrem(`${queueKey}:delay`, messageData)
        }
        break
    }

    return messageData ? JSON.parse(messageData) : null
  }

  /**
   * 从 Redis 中删除消息
   */
  private async deleteMessageFromRedis(message: QueueMessage): Promise<void> {
    // 如果启用持久化，从数据库删除
    const config = this.queues.get(message.queueName)
    if (config?.persistence) {
      await this.deletePersistedMessage(message.id)
    }
  }

  /**
   * 移动到死信队列
   */
  private async moveToDeadLetterQueue(message: QueueMessage, config: QueueConfig): Promise<void> {
    if (!config.deadLetterQueue) return

    const dlqConfig = this.queues.get(config.deadLetterQueue)
    if (dlqConfig) {
      message.queueName = config.deadLetterQueue
      await this.addMessageToQueue(dlqConfig, message)
    }
  }

  /**
   * 计算重试延迟
   */
  private calculateRetryDelay(attempt: number, baseDelay: number): number {
    // 指数退避策略
    return baseDelay * Math.pow(2, attempt - 1)
  }

  /**
   * 生成消息ID
   */
  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 持久化消息
   */
  private async persistMessage(message: QueueMessage): Promise<void> {
    // 实现消息持久化到数据库
  }

  /**
   * 删除持久化消息
   */
  private async deletePersistedMessage(messageId: string): Promise<void> {
    // 实现从数据库删除消息
  }

  /**
   * 记录消息处理结果
   */
  private async recordMessageResult(message: QueueMessage, result: any): Promise<void> {
    // 实现消息处理结果记录
  }

  /**
   * 从 Redis 获取队列统计信息
   */
  private async getQueueStatsFromRedis(queueName: string): Promise<any> {
    // 实现从 Redis 获取统计信息
    return {}
  }

  /**
   * 清空 Redis 中的队列
   */
  private async purgeQueueInRedis(queueName: string): Promise<number> {
    // 实现清空队列
    return 0
  }

  /**
   * 启动可见性超时检查
   */
  private startVisibilityTimeoutCheck(): void {
    // 实现可见性超时检查
  }

  /**
   * 停止可见性超时检查
   */
  private stopVisibilityTimeoutCheck(): void {
    // 实现停止可见性超时检查
  }

  /**
   * 初始化任务队列
   */
  private initializeTaskQueue(): void {
    logger.info('任务队列服务初始化完成')
  }
}

// 导出服务
export default (app: Application): void => {
  const taskQueue = new TaskQueueService(app)
  app.set('taskQueue', taskQueue)
}
