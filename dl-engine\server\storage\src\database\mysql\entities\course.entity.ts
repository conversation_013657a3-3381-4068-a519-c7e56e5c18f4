import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
  Index,
} from 'typeorm';
import { UserEntity } from './user.entity';
import { AssignmentEntity } from './assignment.entity';
import { AssessmentEntity } from './assessment.entity';

export enum CourseStatus {
  DRAFT = 'draft',
  PUBLISHED = 'published',
  ARCHIVED = 'archived',
  SUSPENDED = 'suspended',
}

export enum CourseDifficulty {
  BEGINNER = 'beginner',
  INTERMEDIATE = 'intermediate',
  ADVANCED = 'advanced',
  EXPERT = 'expert',
}

@Entity('courses')
@Index(['teacherId'])
@Index(['status'])
@Index(['difficulty'])
@Index(['category'])
export class CourseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 200 })
  title: string;

  @Column({ type: 'text' })
  description: string;

  @Column({ type: 'varchar', length: 100 })
  category: string;

  @Column({ type: 'json' })
  tags: string[];

  @Column({ type: 'enum', enum: CourseDifficulty })
  difficulty: CourseDifficulty;

  @Column({ type: 'enum', enum: CourseStatus, default: CourseStatus.DRAFT })
  status: CourseStatus;

  @Column({ type: 'varchar', length: 500, nullable: true })
  coverImage: string;

  @Column({ type: 'varchar', length: 1000, nullable: true })
  introVideo: string;

  @Column({ type: 'int', default: 0 })
  estimatedHours: number;

  @Column({ type: 'json' })
  learningObjectives: string[];

  @Column({ type: 'json' })
  prerequisites: string[];

  @Column({ type: 'json' })
  syllabus: {
    id: string;
    title: string;
    description: string;
    order: number;
    estimatedMinutes: number;
    type: 'lesson' | 'assignment' | 'assessment' | 'project';
    content: {
      sceneId?: string;
      projectId?: string;
      videoUrl?: string;
      documentUrl?: string;
      interactiveElements?: any[];
    };
    isCompleted?: boolean;
  }[];

  @Column({ type: 'json', nullable: true })
  settings: {
    enrollment: {
      isOpen: boolean;
      maxStudents: number;
      requireApproval: boolean;
      startDate?: Date;
      endDate?: Date;
    };
    grading: {
      passingScore: number;
      gradingScale: {
        A: number;
        B: number;
        C: number;
        D: number;
        F: number;
      };
      allowRetakes: boolean;
      maxAttempts: number;
    };
    features: {
      discussions: boolean;
      collaboration: boolean;
      vrMode: boolean;
      arMode: boolean;
      offlineAccess: boolean;
    };
  };

  @Column({ type: 'int', default: 0 })
  enrolledCount: number;

  @Column({ type: 'int', default: 0 })
  completedCount: number;

  @Column({ type: 'decimal', precision: 3, scale: 2, default: 0 })
  averageRating: number;

  @Column({ type: 'int', default: 0 })
  ratingCount: number;

  @Column({ type: 'datetime', nullable: true })
  publishedAt: Date;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // 关联关系
  @Column({ type: 'uuid' })
  teacherId: string;

  @ManyToOne(() => UserEntity)
  @JoinColumn({ name: 'teacherId' })
  teacher: UserEntity;

  @OneToMany(() => AssignmentEntity, (assignment) => assignment.course)
  assignments: AssignmentEntity[];

  @OneToMany(() => AssessmentEntity, (assessment) => assessment.course)
  assessments: AssessmentEntity[];

  // 虚拟字段
  get isPublished(): boolean {
    return this.status === CourseStatus.PUBLISHED;
  }

  get canEnroll(): boolean {
    if (!this.isPublished) return false;
    if (!this.settings?.enrollment?.isOpen) return false;
    
    const now = new Date();
    const startDate = this.settings.enrollment.startDate;
    const endDate = this.settings.enrollment.endDate;
    
    if (startDate && now < new Date(startDate)) return false;
    if (endDate && now > new Date(endDate)) return false;
    if (this.settings.enrollment.maxStudents > 0 && 
        this.enrolledCount >= this.settings.enrollment.maxStudents) return false;
    
    return true;
  }

  get completionRate(): number {
    return this.enrolledCount > 0 ? (this.completedCount / this.enrolledCount) * 100 : 0;
  }

  get totalLessons(): number {
    return this.syllabus?.length || 0;
  }
}
