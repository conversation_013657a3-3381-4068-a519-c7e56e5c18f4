/*
DL-Engine Task Worker Service
数字化学习引擎 - 工作进程服务

实现工作节点、任务执行和结果回调功能
*/

import { Application } from '@feathersjs/feathers'
import { EventEmitter } from 'events'
import { Worker, isMainThread, parentPort, workerData } from 'worker_threads'
import { cpus } from 'os'
import logger from '@ir-engine/server-core/src/ServerLogger'
import { TaskDefinition, TaskStatus, TaskResult, TaskHandler, TaskContext } from '../scheduler/task-scheduler'
import { QueueMessage, MessageProcessor } from '../queue/task-queue'

/**
 * 工作节点状态枚举
 */
export enum WorkerStatus {
  IDLE = 'idle',           // 空闲
  BUSY = 'busy',           // 忙碌
  STOPPING = 'stopping',   // 停止中
  STOPPED = 'stopped',     // 已停止
  ERROR = 'error'          // 错误
}

/**
 * 工作节点类型枚举
 */
export enum WorkerType {
  THREAD = 'thread',       // 线程工作节点
  PROCESS = 'process',     // 进程工作节点
  CLUSTER = 'cluster'      // 集群工作节点
}

/**
 * 工作节点配置接口
 */
export interface WorkerConfig {
  id: string
  type: WorkerType
  maxConcurrency: number   // 最大并发任务数
  timeout: number          // 任务超时时间 (ms)
  retryAttempts: number    // 重试次数
  healthCheckInterval: number // 健康检查间隔 (ms)
  resourceLimits?: {
    maxOldGenerationSizeMb?: number
    maxYoungGenerationSizeMb?: number
    codeRangeSizeMb?: number
  }
}

/**
 * 工作节点信息接口
 */
export interface WorkerInfo {
  id: string
  type: WorkerType
  status: WorkerStatus
  currentTasks: number
  maxConcurrency: number
  totalProcessed: number
  totalErrors: number
  avgProcessingTime: number
  memoryUsage: NodeJS.MemoryUsage
  cpuUsage: number
  startTime: Date
  lastActivity: Date
}

/**
 * 任务执行上下文接口
 */
export interface WorkerTaskContext extends TaskContext {
  workerId: string
  workerType: WorkerType
  startTime: Date
  timeout: number
}

/**
 * 工作节点管理器类
 */
export class TaskWorkerService extends EventEmitter {
  private app: Application
  private workers: Map<string, WorkerInstance> = new Map()
  private workerPool: WorkerPool
  private taskHandlers: Map<string, TaskHandler> = new Map()
  private isRunning: boolean = false
  private healthCheckInterval: NodeJS.Timeout | null = null

  constructor(app: Application) {
    super()
    this.app = app
    this.workerPool = new WorkerPool()
    this.initializeWorkerService()
  }

  /**
   * 创建工作节点
   */
  async createWorker(config: WorkerConfig): Promise<string> {
    try {
      // 验证配置
      this.validateWorkerConfig(config)

      // 创建工作节点实例
      const worker = await this.createWorkerInstance(config)

      // 注册工作节点
      this.workers.set(config.id, worker)

      // 添加到工作池
      this.workerPool.addWorker(worker)

      logger.info('工作节点创建成功', { workerId: config.id, type: config.type })
      
      this.emit('worker-created', worker.getInfo())
      return config.id

    } catch (error) {
      logger.error('工作节点创建失败', { error, config })
      throw error
    }
  }

  /**
   * 销毁工作节点
   */
  async destroyWorker(workerId: string, force: boolean = false): Promise<void> {
    try {
      const worker = this.workers.get(workerId)
      if (!worker) {
        throw new Error('工作节点不存在')
      }

      // 从工作池移除
      this.workerPool.removeWorker(workerId)

      // 停止工作节点
      await worker.stop(force)

      // 从映射中移除
      this.workers.delete(workerId)

      logger.info('工作节点销毁成功', { workerId })
      
      this.emit('worker-destroyed', workerId)

    } catch (error) {
      logger.error('工作节点销毁失败', { error, workerId })
      throw error
    }
  }

  /**
   * 注册任务处理器
   */
  registerTaskHandler(name: string, handler: TaskHandler): void {
    this.taskHandlers.set(name, handler)
    
    // 将处理器分发到所有工作节点
    for (const worker of this.workers.values()) {
      worker.registerHandler(name, handler)
    }

    logger.info('任务处理器已注册', { name })
  }

  /**
   * 执行任务
   */
  async executeTask(task: TaskDefinition): Promise<TaskResult> {
    try {
      // 获取可用的工作节点
      const worker = await this.workerPool.getAvailableWorker(task)
      if (!worker) {
        throw new Error('没有可用的工作节点')
      }

      // 执行任务
      const result = await worker.executeTask(task)

      logger.info('任务执行完成', { 
        taskId: task.id, 
        workerId: worker.getId(),
        duration: result.duration 
      })

      this.emit('task-completed', task, result)
      return result

    } catch (error) {
      logger.error('任务执行失败', { error, taskId: task.id })
      
      const result: TaskResult = {
        taskId: task.id,
        status: TaskStatus.FAILED,
        error: error as Error,
        startTime: new Date(),
        endTime: new Date(),
        duration: 0,
        attempts: 1
      }

      this.emit('task-failed', task, result)
      return result
    }
  }

  /**
   * 获取工作节点信息
   */
  getWorkerInfo(workerId: string): WorkerInfo | null {
    const worker = this.workers.get(workerId)
    return worker ? worker.getInfo() : null
  }

  /**
   * 列出所有工作节点
   */
  listWorkers(): WorkerInfo[] {
    return Array.from(this.workers.values()).map(worker => worker.getInfo())
  }

  /**
   * 获取工作池统计信息
   */
  getPoolStats(): any {
    return this.workerPool.getStats()
  }

  /**
   * 启动工作节点服务
   */
  start(): void {
    if (this.isRunning) return

    this.isRunning = true

    // 启动所有工作节点
    for (const worker of this.workers.values()) {
      worker.start()
    }

    // 启动健康检查
    this.startHealthCheck()

    logger.info('工作节点服务已启动')
    this.emit('service-started')
  }

  /**
   * 停止工作节点服务
   */
  async stop(force: boolean = false): Promise<void> {
    if (!this.isRunning) return

    this.isRunning = false

    // 停止健康检查
    this.stopHealthCheck()

    // 停止所有工作节点
    const stopPromises = Array.from(this.workers.values()).map(worker => 
      worker.stop(force)
    )

    await Promise.all(stopPromises)

    logger.info('工作节点服务已停止')
    this.emit('service-stopped')
  }

  /**
   * 创建工作节点实例
   */
  private async createWorkerInstance(config: WorkerConfig): Promise<WorkerInstance> {
    switch (config.type) {
      case WorkerType.THREAD:
        return new ThreadWorker(config, this.app)
      case WorkerType.PROCESS:
        return new ProcessWorker(config, this.app)
      case WorkerType.CLUSTER:
        return new ClusterWorker(config, this.app)
      default:
        throw new Error(`不支持的工作节点类型: ${config.type}`)
    }
  }

  /**
   * 验证工作节点配置
   */
  private validateWorkerConfig(config: WorkerConfig): void {
    if (!config.id) {
      throw new Error('工作节点ID不能为空')
    }
    if (config.maxConcurrency <= 0) {
      throw new Error('最大并发数必须大于0')
    }
    if (config.timeout <= 0) {
      throw new Error('超时时间必须大于0')
    }
  }

  /**
   * 启动健康检查
   */
  private startHealthCheck(): void {
    this.healthCheckInterval = setInterval(() => {
      this.performHealthCheck()
    }, 30000) // 每30秒检查一次
  }

  /**
   * 停止健康检查
   */
  private stopHealthCheck(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval)
      this.healthCheckInterval = null
    }
  }

  /**
   * 执行健康检查
   */
  private async performHealthCheck(): Promise<void> {
    for (const [workerId, worker] of this.workers) {
      try {
        const isHealthy = await worker.healthCheck()
        if (!isHealthy) {
          logger.warn('工作节点健康检查失败', { workerId })
          this.emit('worker-unhealthy', workerId)
          
          // 尝试重启工作节点
          await this.restartWorker(workerId)
        }
      } catch (error) {
        logger.error('健康检查异常', { error, workerId })
      }
    }
  }

  /**
   * 重启工作节点
   */
  private async restartWorker(workerId: string): Promise<void> {
    try {
      const worker = this.workers.get(workerId)
      if (!worker) return

      const config = worker.getConfig()
      
      // 停止旧工作节点
      await worker.stop(true)
      
      // 创建新工作节点
      const newWorker = await this.createWorkerInstance(config)
      
      // 替换工作节点
      this.workers.set(workerId, newWorker)
      this.workerPool.replaceWorker(workerId, newWorker)
      
      // 重新注册处理器
      for (const [name, handler] of this.taskHandlers) {
        newWorker.registerHandler(name, handler)
      }
      
      // 启动新工作节点
      newWorker.start()

      logger.info('工作节点重启成功', { workerId })
      this.emit('worker-restarted', workerId)

    } catch (error) {
      logger.error('工作节点重启失败', { error, workerId })
    }
  }

  /**
   * 初始化工作节点服务
   */
  private initializeWorkerService(): void {
    // 创建默认工作节点池
    const cpuCount = cpus().length
    const defaultWorkerCount = Math.max(2, Math.floor(cpuCount / 2))

    for (let i = 0; i < defaultWorkerCount; i++) {
      const config: WorkerConfig = {
        id: `default-worker-${i}`,
        type: WorkerType.THREAD,
        maxConcurrency: 1,
        timeout: 30000,
        retryAttempts: 3,
        healthCheckInterval: 30000
      }

      this.createWorker(config).catch(error => {
        logger.error('创建默认工作节点失败', { error, workerId: config.id })
      })
    }

    logger.info('工作节点服务初始化完成', { defaultWorkerCount })
  }
}

/**
 * 工作节点实例抽象类
 */
abstract class WorkerInstance extends EventEmitter {
  protected config: WorkerConfig
  protected app: Application
  protected status: WorkerStatus = WorkerStatus.IDLE
  protected currentTasks: Map<string, TaskDefinition> = new Map()
  protected handlers: Map<string, TaskHandler> = new Map()
  protected stats: {
    totalProcessed: number
    totalErrors: number
    totalProcessingTime: number
    startTime: Date
    lastActivity: Date
  }

  constructor(config: WorkerConfig, app: Application) {
    super()
    this.config = config
    this.app = app
    this.stats = {
      totalProcessed: 0,
      totalErrors: 0,
      totalProcessingTime: 0,
      startTime: new Date(),
      lastActivity: new Date()
    }
  }

  abstract start(): void
  abstract stop(force?: boolean): Promise<void>
  abstract executeTask(task: TaskDefinition): Promise<TaskResult>
  abstract healthCheck(): Promise<boolean>

  getId(): string {
    return this.config.id
  }

  getConfig(): WorkerConfig {
    return { ...this.config }
  }

  getStatus(): WorkerStatus {
    return this.status
  }

  registerHandler(name: string, handler: TaskHandler): void {
    this.handlers.set(name, handler)
  }

  getInfo(): WorkerInfo {
    const memoryUsage = process.memoryUsage()
    
    return {
      id: this.config.id,
      type: this.config.type,
      status: this.status,
      currentTasks: this.currentTasks.size,
      maxConcurrency: this.config.maxConcurrency,
      totalProcessed: this.stats.totalProcessed,
      totalErrors: this.stats.totalErrors,
      avgProcessingTime: this.stats.totalProcessed > 0 
        ? this.stats.totalProcessingTime / this.stats.totalProcessed 
        : 0,
      memoryUsage,
      cpuUsage: 0, // 需要实现 CPU 使用率计算
      startTime: this.stats.startTime,
      lastActivity: this.stats.lastActivity
    }
  }

  canAcceptTask(): boolean {
    return this.status === WorkerStatus.IDLE && 
           this.currentTasks.size < this.config.maxConcurrency
  }

  protected updateStats(duration: number, success: boolean): void {
    this.stats.totalProcessed++
    this.stats.totalProcessingTime += duration
    this.stats.lastActivity = new Date()
    
    if (!success) {
      this.stats.totalErrors++
    }
  }
}

/**
 * 线程工作节点类
 */
class ThreadWorker extends WorkerInstance {
  private worker: Worker | null = null

  start(): void {
    if (this.status !== WorkerStatus.IDLE && this.status !== WorkerStatus.STOPPED) {
      return
    }

    try {
      // 创建工作线程
      this.worker = new Worker(__filename, {
        workerData: {
          config: this.config,
          handlers: Array.from(this.handlers.keys())
        },
        resourceLimits: this.config.resourceLimits
      })

      // 设置事件监听器
      this.setupWorkerEventListeners()

      this.status = WorkerStatus.IDLE
      logger.debug('线程工作节点启动', { workerId: this.config.id })

    } catch (error) {
      this.status = WorkerStatus.ERROR
      logger.error('线程工作节点启动失败', { error, workerId: this.config.id })
      throw error
    }
  }

  async stop(force: boolean = false): Promise<void> {
    if (this.status === WorkerStatus.STOPPED) return

    this.status = WorkerStatus.STOPPING

    if (this.worker) {
      if (force) {
        await this.worker.terminate()
      } else {
        // 等待当前任务完成
        this.worker.postMessage({ type: 'shutdown' })
        await new Promise(resolve => {
          this.worker!.once('exit', resolve)
        })
      }
      this.worker = null
    }

    this.status = WorkerStatus.STOPPED
    logger.debug('线程工作节点停止', { workerId: this.config.id })
  }

  async executeTask(task: TaskDefinition): Promise<TaskResult> {
    if (!this.worker || !this.canAcceptTask()) {
      throw new Error('工作节点不可用')
    }

    return new Promise((resolve, reject) => {
      const startTime = new Date()
      this.currentTasks.set(task.id, task)
      this.status = WorkerStatus.BUSY

      // 设置超时
      const timeout = setTimeout(() => {
        this.currentTasks.delete(task.id)
        this.status = this.currentTasks.size > 0 ? WorkerStatus.BUSY : WorkerStatus.IDLE
        reject(new Error('任务执行超时'))
      }, this.config.timeout)

      // 监听任务完成
      const onMessage = (message: any) => {
        if (message.taskId === task.id) {
          clearTimeout(timeout)
          this.worker!.off('message', onMessage)
          this.currentTasks.delete(task.id)
          this.status = this.currentTasks.size > 0 ? WorkerStatus.BUSY : WorkerStatus.IDLE

          const endTime = new Date()
          const duration = endTime.getTime() - startTime.getTime()
          
          if (message.error) {
            this.updateStats(duration, false)
            reject(new Error(message.error))
          } else {
            this.updateStats(duration, true)
            resolve({
              taskId: task.id,
              status: TaskStatus.COMPLETED,
              result: message.result,
              startTime,
              endTime,
              duration,
              attempts: 1
            })
          }
        }
      }

      this.worker.on('message', onMessage)
      this.worker.postMessage({ type: 'execute', task })
    })
  }

  async healthCheck(): Promise<boolean> {
    if (!this.worker || this.status === WorkerStatus.ERROR) {
      return false
    }

    return new Promise(resolve => {
      const timeout = setTimeout(() => resolve(false), 5000)
      
      const onMessage = (message: any) => {
        if (message.type === 'health-check-response') {
          clearTimeout(timeout)
          this.worker!.off('message', onMessage)
          resolve(message.healthy)
        }
      }

      this.worker.on('message', onMessage)
      this.worker.postMessage({ type: 'health-check' })
    })
  }

  private setupWorkerEventListeners(): void {
    if (!this.worker) return

    this.worker.on('error', (error) => {
      this.status = WorkerStatus.ERROR
      logger.error('工作线程错误', { error, workerId: this.config.id })
      this.emit('error', error)
    })

    this.worker.on('exit', (code) => {
      this.status = WorkerStatus.STOPPED
      logger.debug('工作线程退出', { code, workerId: this.config.id })
      this.emit('exit', code)
    })
  }
}

/**
 * 进程工作节点类
 */
class ProcessWorker extends WorkerInstance {
  start(): void {
    // 实现进程工作节点
    this.status = WorkerStatus.IDLE
  }

  async stop(force?: boolean): Promise<void> {
    this.status = WorkerStatus.STOPPED
  }

  async executeTask(task: TaskDefinition): Promise<TaskResult> {
    // 实现任务执行
    throw new Error('进程工作节点未实现')
  }

  async healthCheck(): Promise<boolean> {
    return true
  }
}

/**
 * 集群工作节点类
 */
class ClusterWorker extends WorkerInstance {
  start(): void {
    // 实现集群工作节点
    this.status = WorkerStatus.IDLE
  }

  async stop(force?: boolean): Promise<void> {
    this.status = WorkerStatus.STOPPED
  }

  async executeTask(task: TaskDefinition): Promise<TaskResult> {
    // 实现任务执行
    throw new Error('集群工作节点未实现')
  }

  async healthCheck(): Promise<boolean> {
    return true
  }
}

/**
 * 工作池类
 */
class WorkerPool {
  private workers: Map<string, WorkerInstance> = new Map()
  private roundRobinIndex: number = 0

  addWorker(worker: WorkerInstance): void {
    this.workers.set(worker.getId(), worker)
  }

  removeWorker(workerId: string): void {
    this.workers.delete(workerId)
  }

  replaceWorker(workerId: string, newWorker: WorkerInstance): void {
    this.workers.set(workerId, newWorker)
  }

  async getAvailableWorker(task: TaskDefinition): Promise<WorkerInstance | null> {
    const availableWorkers = Array.from(this.workers.values())
      .filter(worker => worker.canAcceptTask())

    if (availableWorkers.length === 0) {
      return null
    }

    // 简单的轮询调度
    const worker = availableWorkers[this.roundRobinIndex % availableWorkers.length]
    this.roundRobinIndex++
    
    return worker
  }

  getStats(): any {
    const workers = Array.from(this.workers.values())
    
    return {
      totalWorkers: workers.length,
      idleWorkers: workers.filter(w => w.getStatus() === WorkerStatus.IDLE).length,
      busyWorkers: workers.filter(w => w.getStatus() === WorkerStatus.BUSY).length,
      errorWorkers: workers.filter(w => w.getStatus() === WorkerStatus.ERROR).length,
      totalCapacity: workers.reduce((sum, w) => sum + w.getConfig().maxConcurrency, 0),
      currentLoad: workers.reduce((sum, w) => sum + w.getInfo().currentTasks, 0)
    }
  }
}

// 工作线程代码
if (!isMainThread && parentPort) {
  const { config, handlers } = workerData
  const taskHandlers = new Map<string, TaskHandler>()

  // 加载处理器
  for (const handlerName of handlers) {
    // 这里应该动态加载处理器
    // taskHandlers.set(handlerName, require(`./handlers/${handlerName}`))
  }

  parentPort.on('message', async (message) => {
    switch (message.type) {
      case 'execute':
        try {
          const task = message.task
          const handler = taskHandlers.get(task.handler)
          
          if (!handler) {
            throw new Error(`处理器不存在: ${task.handler}`)
          }

          const context: WorkerTaskContext = {
            taskId: task.id,
            app: null as any, // 在工作线程中不可用
            workerId: config.id,
            workerType: config.type,
            startTime: new Date(),
            timeout: config.timeout
          }

          const result = await handler.execute(task.payload, context)
          
          parentPort!.postMessage({
            taskId: task.id,
            result
          })
        } catch (error) {
          parentPort!.postMessage({
            taskId: message.task.id,
            error: error.message
          })
        }
        break

      case 'health-check':
        parentPort!.postMessage({
          type: 'health-check-response',
          healthy: true
        })
        break

      case 'shutdown':
        process.exit(0)
        break
    }
  })
}

// 导出服务
export default (app: Application): void => {
  const taskWorker = new TaskWorkerService(app)
  app.set('taskWorker', taskWorker)
}
