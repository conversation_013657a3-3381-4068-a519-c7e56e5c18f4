import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, OneToMany, JoinColumn, Index } from 'typeorm'
import { Course } from './course.entity'
import { Assignment } from './assignment.entity'

@Entity('lessons')
@Index(['courseId'])
@Index(['status'])
@Index(['order'])
export class Lesson {
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Column({ length: 200 })
  title: string

  @Column({ type: 'text', nullable: true })
  description: string

  @Column({ type: 'text', nullable: true })
  content: string

  @Column({ type: 'int', default: 0 })
  order: number

  @Column({ type: 'int', default: 0 })
  duration: number // 预计学习时长（分钟）

  @Column({ 
    type: 'enum',
    enum: ['draft', 'published', 'archived'],
    default: 'draft'
  })
  status: string

  @Column({ type: 'json', nullable: true })
  resources: any // 学习资源（视频、文档、3D模型等）

  @Column({ type: 'json', nullable: true })
  objectives: any // 学习目标

  @Column({ type: 'json', nullable: true })
  metadata: any

  // 关联关系
  @Column({ name: 'course_id' })
  courseId: string

  @ManyToOne(() => Course, course => course.lessons)
  @JoinColumn({ name: 'course_id' })
  course: Course

  @OneToMany(() => Assignment, assignment => assignment.lesson)
  assignments: Assignment[]

  @CreateDateColumn()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date
}
