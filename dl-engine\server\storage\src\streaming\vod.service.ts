import { Injectable, Logger } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { ConfigService } from '@nestjs/config';
import { MinioService } from '../minio/minio.service';
import { StreamConfig, StreamStats } from './streaming.service';
import * as ffmpeg from 'fluent-ffmpeg';
import * as fs from 'fs/promises';
import * as path from 'path';

export interface VodAsset {
  id: string;
  streamId: string;
  title: string;
  description?: string;
  duration: number;
  fileSize: number;
  format: string;
  resolution: string;
  bitrate: number;
  framerate: number;
  audioCodec: string;
  videoCodec: string;
  thumbnails: string[];
  subtitles: Array<{
    language: string;
    url: string;
    format: 'srt' | 'vtt' | 'ass';
  }>;
  chapters: Array<{
    title: string;
    startTime: number;
    endTime: number;
    thumbnail?: string;
  }>;
  status: 'uploading' | 'processing' | 'ready' | 'error';
  source: {
    bucketName: string;
    objectName: string;
    url: string;
  };
  outputs: Array<{
    quality: string;
    format: 'hls' | 'dash' | 'mp4';
    url: string;
    bitrate: number;
    resolution: string;
  }>;
  analytics: {
    views: number;
    watchTime: number;
    completionRate: number;
    averageViewDuration: number;
  };
  createdAt: Date;
  updatedAt: Date;
  metadata?: Record<string, any>;
}

export interface VodPlaybackSession {
  sessionId: string;
  assetId: string;
  userId?: string;
  clientIp: string;
  userAgent: string;
  quality: string;
  startTime: Date;
  lastActivity: Date;
  currentPosition: number;
  watchedDuration: number;
  bufferingEvents: Array<{
    timestamp: Date;
    duration: number;
    position: number;
  }>;
  qualityChanges: Array<{
    timestamp: Date;
    fromQuality: string;
    toQuality: string;
    reason: string;
  }>;
  completed: boolean;
  metadata?: Record<string, any>;
}

@Injectable()
export class VodService {
  private readonly logger = new Logger(VodService.name);
  private readonly assets = new Map<string, VodAsset>();
  private readonly playbackSessions = new Map<string, VodPlaybackSession>();
  private readonly vodDir: string;

  constructor(
    private configService: ConfigService,
    private minioService: MinioService,
    @InjectQueue('vod-processing') private vodQueue: Queue,
  ) {
    this.vodDir = this.configService.get('VOD_OUTPUT_DIR', './vod');
    this.ensureVodDirectory();
  }

  /**
   * 初始化点播流
   */
  async initializeStream(config: StreamConfig): Promise<void> {
    try {
      // 创建VOD输出目录
      const streamDir = path.join(this.vodDir, config.id);
      await fs.mkdir(streamDir, { recursive: true });

      // 如果是文件输入，创建VOD资产
      if (config.input.type === 'file') {
        await this.createVodAsset(config);
      }

      this.logger.log(`VOD stream initialized: ${config.id}`);
    } catch (error) {
      this.logger.error(`Failed to initialize VOD stream ${config.id}:`, error);
      throw error;
    }
  }

  /**
   * 启动点播流
   */
  async startStream(streamId: string): Promise<void> {
    try {
      const asset = Array.from(this.assets.values()).find(a => a.streamId === streamId);
      if (!asset) {
        throw new Error(`VOD asset not found for stream: ${streamId}`);
      }

      // 如果资产还在处理中，等待处理完成
      if (asset.status === 'processing') {
        this.logger.log(`VOD asset is processing, waiting for completion: ${streamId}`);
        return;
      }

      if (asset.status !== 'ready') {
        throw new Error(`VOD asset not ready: ${asset.status}`);
      }

      this.logger.log(`VOD stream started: ${streamId}`);
    } catch (error) {
      this.logger.error(`Failed to start VOD stream ${streamId}:`, error);
      throw error;
    }
  }

  /**
   * 停止点播流
   */
  async stopStream(streamId: string): Promise<void> {
    try {
      // 停止所有播放会话
      const sessions = Array.from(this.playbackSessions.values()).filter(s => {
        const asset = this.assets.get(s.assetId);
        return asset && asset.streamId === streamId;
      });

      for (const session of sessions) {
        await this.endPlaybackSession(session.sessionId);
      }

      this.logger.log(`VOD stream stopped: ${streamId}`);
    } catch (error) {
      this.logger.error(`Failed to stop VOD stream ${streamId}:`, error);
      throw error;
    }
  }

  /**
   * 创建VOD资产
   */
  async createVodAsset(config: StreamConfig): Promise<string> {
    try {
      const assetId = this.generateAssetId();
      
      // 下载源文件
      const sourceUrl = config.input.url;
      const sourceBuffer = await this.downloadSourceFile(sourceUrl);
      
      // 上传到Minio
      const bucketName = 'vod-sources';
      const objectName = `${assetId}/source.mp4`;
      await this.minioService.putObject(bucketName, objectName, sourceBuffer);
      
      const asset: VodAsset = {
        id: assetId,
        streamId: config.id,
        title: config.name,
        description: config.metadata?.description,
        duration: 0,
        fileSize: sourceBuffer.length,
        format: 'mp4',
        resolution: '',
        bitrate: 0,
        framerate: 0,
        audioCodec: '',
        videoCodec: '',
        thumbnails: [],
        subtitles: [],
        chapters: [],
        status: 'processing',
        source: {
          bucketName,
          objectName,
          url: await this.minioService.getPresignedUrl('GET', bucketName, objectName),
        },
        outputs: [],
        analytics: {
          views: 0,
          watchTime: 0,
          completionRate: 0,
          averageViewDuration: 0,
        },
        createdAt: new Date(),
        updatedAt: new Date(),
        metadata: config.metadata,
      };

      this.assets.set(assetId, asset);

      // 启动处理任务
      await this.vodQueue.add('process-vod-asset', {
        assetId,
        config,
      }, {
        priority: 5,
        attempts: 3,
        timeout: 3600000, // 1小时
      });

      this.logger.log(`VOD asset created: ${assetId}`);
      return assetId;
    } catch (error) {
      this.logger.error('Failed to create VOD asset:', error);
      throw error;
    }
  }

  /**
   * 处理VOD资产
   */
  async processVodAsset(assetId: string, config: StreamConfig): Promise<void> {
    const asset = this.assets.get(assetId);
    if (!asset) {
      throw new Error(`VOD asset not found: ${assetId}`);
    }

    try {
      this.logger.log(`Processing VOD asset: ${assetId}`);

      // 下载源文件到本地
      const sourceBuffer = await this.minioService.getObject(asset.source.bucketName, asset.source.objectName);
      const sourcePath = path.join(this.vodDir, assetId, 'source.mp4');
      await fs.writeFile(sourcePath, sourceBuffer);

      // 分析视频信息
      const videoInfo = await this.analyzeVideo(sourcePath);
      asset.duration = videoInfo.duration;
      asset.resolution = videoInfo.resolution;
      asset.bitrate = videoInfo.bitrate;
      asset.framerate = videoInfo.framerate;
      asset.videoCodec = videoInfo.videoCodec;
      asset.audioCodec = videoInfo.audioCodec;

      // 生成缩略图
      asset.thumbnails = await this.generateThumbnails(sourcePath, assetId);

      // 生成多码率输出
      asset.outputs = await this.generateMultipleOutputs(sourcePath, assetId, config.outputs);

      // 生成字幕（如果有）
      if (config.metadata?.subtitles) {
        asset.subtitles = await this.processSubtitles(assetId, config.metadata.subtitles);
      }

      // 生成章节（如果有）
      if (config.metadata?.chapters) {
        asset.chapters = await this.processChapters(assetId, config.metadata.chapters);
      }

      asset.status = 'ready';
      asset.updatedAt = new Date();

      // 清理临时文件
      await fs.unlink(sourcePath);

      this.logger.log(`VOD asset processing completed: ${assetId}`);
    } catch (error) {
      asset.status = 'error';
      asset.updatedAt = new Date();
      this.logger.error(`Failed to process VOD asset ${assetId}:`, error);
      throw error;
    }
  }

  /**
   * 开始播放会话
   */
  async startPlaybackSession(
    assetId: string,
    clientInfo: {
      ip: string;
      userAgent: string;
      userId?: string;
      quality?: string;
      metadata?: Record<string, any>;
    }
  ): Promise<string> {
    const asset = this.assets.get(assetId);
    if (!asset) {
      throw new Error(`VOD asset not found: ${assetId}`);
    }

    if (asset.status !== 'ready') {
      throw new Error(`VOD asset not ready: ${asset.status}`);
    }

    const sessionId = this.generateSessionId();
    const session: VodPlaybackSession = {
      sessionId,
      assetId,
      userId: clientInfo.userId,
      clientIp: clientInfo.ip,
      userAgent: clientInfo.userAgent,
      quality: clientInfo.quality || 'auto',
      startTime: new Date(),
      lastActivity: new Date(),
      currentPosition: 0,
      watchedDuration: 0,
      bufferingEvents: [],
      qualityChanges: [],
      completed: false,
      metadata: clientInfo.metadata,
    };

    this.playbackSessions.set(sessionId, session);

    // 更新资产分析数据
    asset.analytics.views++;
    asset.updatedAt = new Date();

    this.logger.log(`Playback session started: ${sessionId} for asset ${assetId}`);
    return sessionId;
  }

  /**
   * 更新播放进度
   */
  async updatePlaybackProgress(
    sessionId: string,
    position: number,
    quality?: string
  ): Promise<void> {
    const session = this.playbackSessions.get(sessionId);
    if (!session) {
      throw new Error(`Playback session not found: ${sessionId}`);
    }

    const previousPosition = session.currentPosition;
    session.currentPosition = position;
    session.lastActivity = new Date();

    // 计算观看时长
    if (position > previousPosition) {
      session.watchedDuration += (position - previousPosition);
    }

    // 记录质量变化
    if (quality && quality !== session.quality) {
      session.qualityChanges.push({
        timestamp: new Date(),
        fromQuality: session.quality,
        toQuality: quality,
        reason: 'user_selection',
      });
      session.quality = quality;
    }

    // 检查是否完成观看
    const asset = this.assets.get(session.assetId);
    if (asset && position >= asset.duration * 0.95) {
      session.completed = true;
    }
  }

  /**
   * 记录缓冲事件
   */
  async recordBufferingEvent(
    sessionId: string,
    duration: number,
    position: number
  ): Promise<void> {
    const session = this.playbackSessions.get(sessionId);
    if (!session) {
      return;
    }

    session.bufferingEvents.push({
      timestamp: new Date(),
      duration,
      position,
    });

    session.lastActivity = new Date();
  }

  /**
   * 结束播放会话
   */
  async endPlaybackSession(sessionId: string): Promise<void> {
    const session = this.playbackSessions.get(sessionId);
    if (!session) {
      return;
    }

    try {
      // 更新资产分析数据
      const asset = this.assets.get(session.assetId);
      if (asset) {
        asset.analytics.watchTime += session.watchedDuration;
        
        if (session.completed) {
          asset.analytics.completionRate = 
            (asset.analytics.completionRate * (asset.analytics.views - 1) + 1) / asset.analytics.views;
        }
        
        asset.analytics.averageViewDuration = 
          (asset.analytics.averageViewDuration * (asset.analytics.views - 1) + session.watchedDuration) / asset.analytics.views;
        
        asset.updatedAt = new Date();
      }

      this.playbackSessions.delete(sessionId);
      this.logger.log(`Playback session ended: ${sessionId}`);
    } catch (error) {
      this.logger.error(`Failed to end playback session ${sessionId}:`, error);
    }
  }

  /**
   * 获取VOD统计
   */
  async getStreamStats(streamId: string): Promise<StreamStats | null> {
    const asset = Array.from(this.assets.values()).find(a => a.streamId === streamId);
    if (!asset) {
      return null;
    }

    const activeSessions = Array.from(this.playbackSessions.values()).filter(s => s.assetId === asset.id);
    const currentViewers = activeSessions.length;

    return {
      streamId,
      status: asset.status,
      viewers: {
        current: currentViewers,
        peak: Math.max(currentViewers, asset.analytics.views),
        total: asset.analytics.views,
      },
      bandwidth: {
        input: 0,
        output: currentViewers * 2000, // 估算
        total: currentViewers * 2000,
      },
      quality: {
        bitrate: asset.bitrate,
        framerate: asset.framerate,
        resolution: asset.resolution,
        dropRate: 0,
      },
      duration: asset.duration,
      startTime: asset.createdAt,
      lastUpdate: new Date(),
    };
  }

  /**
   * 获取VOD资产
   */
  getVodAsset(assetId: string): VodAsset | null {
    return this.assets.get(assetId) || null;
  }

  /**
   * 获取播放会话
   */
  getPlaybackSession(sessionId: string): VodPlaybackSession | null {
    return this.playbackSessions.get(sessionId) || null;
  }

  /**
   * 清理流资源
   */
  async cleanupStream(streamId: string): Promise<void> {
    try {
      // 清理资产
      const assets = Array.from(this.assets.values()).filter(a => a.streamId === streamId);
      for (const asset of assets) {
        await this.deleteVodAsset(asset.id);
      }

      // 清理目录
      const streamDir = path.join(this.vodDir, streamId);
      try {
        await fs.rmdir(streamDir, { recursive: true });
      } catch (error) {
        this.logger.warn(`Failed to cleanup VOD directory for ${streamId}:`, error);
      }

      this.logger.log(`VOD stream cleaned up: ${streamId}`);
    } catch (error) {
      this.logger.error(`Failed to cleanup VOD stream ${streamId}:`, error);
    }
  }

  private async downloadSourceFile(url: string): Promise<Buffer> {
    // 实现文件下载逻辑
    // 这里可以支持HTTP URL、本地文件路径等
    return Buffer.from(''); // 临时实现
  }

  private async analyzeVideo(filePath: string): Promise<any> {
    return new Promise((resolve, reject) => {
      ffmpeg.ffprobe(filePath, (err, metadata) => {
        if (err) {
          reject(err);
          return;
        }

        const videoStream = metadata.streams.find(s => s.codec_type === 'video');
        const audioStream = metadata.streams.find(s => s.codec_type === 'audio');

        resolve({
          duration: metadata.format.duration || 0,
          resolution: videoStream ? `${videoStream.width}x${videoStream.height}` : '',
          bitrate: parseInt(metadata.format.bit_rate) || 0,
          framerate: videoStream ? eval(videoStream.r_frame_rate) : 0,
          videoCodec: videoStream?.codec_name || '',
          audioCodec: audioStream?.codec_name || '',
        });
      });
    });
  }

  private async generateThumbnails(sourcePath: string, assetId: string): Promise<string[]> {
    // 实现缩略图生成
    return [];
  }

  private async generateMultipleOutputs(sourcePath: string, assetId: string, outputConfigs: any[]): Promise<any[]> {
    // 实现多码率输出生成
    return [];
  }

  private async processSubtitles(assetId: string, subtitles: any[]): Promise<any[]> {
    // 实现字幕处理
    return [];
  }

  private async processChapters(assetId: string, chapters: any[]): Promise<any[]> {
    // 实现章节处理
    return [];
  }

  private async deleteVodAsset(assetId: string): Promise<void> {
    const asset = this.assets.get(assetId);
    if (asset) {
      // 删除Minio中的文件
      try {
        await this.minioService.removeObject(asset.source.bucketName, asset.source.objectName);
      } catch (error) {
        this.logger.warn(`Failed to delete source file for asset ${assetId}:`, error);
      }

      this.assets.delete(assetId);
    }
  }

  private async ensureVodDirectory(): Promise<void> {
    try {
      await fs.access(this.vodDir);
    } catch {
      await fs.mkdir(this.vodDir, { recursive: true });
    }
  }

  private generateAssetId(): string {
    return `asset_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
