import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@nestjs/typeorm'
import { ConfigModule } from '@nestjs/config'

import { Scene } from './entities/scene.entity'
import { SceneVersion } from './entities/scene-version.entity'
import { SceneTemplate } from './entities/scene-template.entity'
import { SceneComponent } from './entities/scene-component.entity'
import { SceneAsset } from './entities/scene-asset.entity'
import { SceneShare } from './entities/scene-share.entity'
import { SceneComment } from './entities/scene-comment.entity'
import { SceneBookmark } from './entities/scene-bookmark.entity'

import { ScenesController } from './controllers/scenes.controller'
import { SceneVersionsController } from './controllers/scene-versions.controller'
import { SceneTemplatesController } from './controllers/scene-templates.controller'
import { SceneComponentsController } from './controllers/scene-components.controller'
import { SceneAssetsController } from './controllers/scene-assets.controller'
import { SceneSharesController } from './controllers/scene-shares.controller'
import { SceneCommentsController } from './controllers/scene-comments.controller'
import { SceneBookmarksController } from './controllers/scene-bookmarks.controller'

import { ScenesService } from './services/scenes.service'
import { SceneVersionsService } from './services/scene-versions.service'
import { SceneTemplatesService } from './services/scene-templates.service'
import { SceneComponentsService } from './services/scene-components.service'
import { SceneAssetsService } from './services/scene-assets.service'
import { SceneSharesService } from './services/scene-shares.service'
import { SceneCommentsService } from './services/scene-comments.service'
import { SceneBookmarksService } from './services/scene-bookmarks.service'
import { SceneValidationService } from './services/scene-validation.service'
import { SceneRenderingService } from './services/scene-rendering.service'

import { ProjectsModule } from '../projects/projects.module'
import { UsersModule } from '../users/users.module'
import { AssetsModule } from '../assets/assets.module'
import { NotificationModule } from '../notifications/notification.module'
import { StorageModule } from '../storage/storage.module'

/**
 * 场景管理模块
 * 
 * 功能包括：
 * - 3D场景创建和管理
 * - 场景版本控制
 * - 场景模板管理
 * - 场景组件管理
 * - 场景资产管理
 * - 场景分享和协作
 * - 场景评论和收藏
 * - 场景渲染和预览
 */
@Module({
  imports: [
    ConfigModule,
    TypeOrmModule.forFeature([
      Scene,
      SceneVersion,
      SceneTemplate,
      SceneComponent,
      SceneAsset,
      SceneShare,
      SceneComment,
      SceneBookmark
    ]),
    ProjectsModule,
    UsersModule,
    AssetsModule,
    NotificationModule,
    StorageModule
  ],
  controllers: [
    ScenesController,
    SceneVersionsController,
    SceneTemplatesController,
    SceneComponentsController,
    SceneAssetsController,
    SceneSharesController,
    SceneCommentsController,
    SceneBookmarksController
  ],
  providers: [
    ScenesService,
    SceneVersionsService,
    SceneTemplatesService,
    SceneComponentsService,
    SceneAssetsService,
    SceneSharesService,
    SceneCommentsService,
    SceneBookmarksService,
    SceneValidationService,
    SceneRenderingService
  ],
  exports: [
    ScenesService,
    SceneVersionsService,
    SceneTemplatesService,
    SceneComponentsService,
    SceneAssetsService,
    SceneSharesService,
    SceneCommentsService,
    SceneBookmarksService,
    SceneRenderingService
  ]
})
export class ScenesModule {}
