import { NestFactory } from '@nestjs/core';
import { ValidationPipe, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { StorageModule } from './storage.module';

async function bootstrap() {
  const logger = new Logger('StorageService');
  
  try {
    const app = await NestFactory.create(StorageModule);
    const configService = app.get(ConfigService);

    // 全局验证管道
    app.useGlobalPipes(new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
    }));

    // CORS配置
    app.enableCors({
      origin: configService.get('CORS_ORIGIN', '*'),
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
      credentials: true,
    });

    // API文档
    const config = new DocumentBuilder()
      .setTitle('DL-Engine Storage Service')
      .setDescription('存储与AI智能服务API文档')
      .setVersion('1.0.0')
      .addTag('storage', '存储管理')
      .addTag('database', '数据库服务')
      .addTag('minio', '对象存储')
      .addTag('backup', '备份与迁移')
      .addTag('media', '媒体处理')
      .addBearerAuth()
      .build();

    const document = SwaggerModule.createDocument(app, config);
    SwaggerModule.setup('api/docs', app, document);

    // 启动服务
    const port = configService.get('STORAGE_PORT', 3001);
    await app.listen(port);

    logger.log(`Storage service is running on port ${port}`);
    logger.log(`API documentation available at http://localhost:${port}/api/docs`);
  } catch (error) {
    logger.error('Failed to start storage service:', error);
    process.exit(1);
  }
}

bootstrap();
