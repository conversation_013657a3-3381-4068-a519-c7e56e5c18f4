#!/bin/bash

# DL-Engine 第四批次服务部署脚本
# 部署存储与AI智能服务

set -e

echo "🚀 开始部署 DL-Engine 第四批次：存储与AI智能服务"
echo "=================================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker和Docker Compose
check_prerequisites() {
    log_info "检查部署环境..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    log_success "环境检查通过"
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录结构..."
    
    mkdir -p ./nginx/ssl
    mkdir -p ./scripts
    mkdir -p ./data/mysql
    mkdir -p ./data/redis
    mkdir -p ./data/postgresql
    mkdir -p ./data/minio
    mkdir -p ./data/ollama
    
    log_success "目录结构创建完成"
}

# 生成环境配置文件
generate_env_files() {
    log_info "生成环境配置文件..."
    
    # 存储服务环境配置
    cat > ./storage/.env << EOF
NODE_ENV=production
PORT=3001

# MySQL配置
MYSQL_HOST=mysql
MYSQL_PORT=3306
MYSQL_USERNAME=dl_engine
MYSQL_PASSWORD=dl_engine_password
MYSQL_DATABASE=dl_engine

# Redis配置
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=redis_password

# PostgreSQL配置
POSTGRESQL_HOST=postgresql
POSTGRESQL_PORT=5432
POSTGRESQL_USERNAME=postgres
POSTGRESQL_PASSWORD=postgres_password
POSTGRESQL_DATABASE=dl_engine_vectors

# Minio配置
MINIO_ENDPOINT=minio
MINIO_PORT=9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin
MINIO_USE_SSL=false

# 服务配置
STORAGE_PORT=3001
LOG_LEVEL=info
EOF

    # AI服务环境配置
    cat > ./ai/.env << EOF
NODE_ENV=production
PORT=3002

# 数据库配置
MYSQL_HOST=mysql
MYSQL_PORT=3306
MYSQL_USERNAME=dl_engine
MYSQL_PASSWORD=dl_engine_password
MYSQL_DATABASE=dl_engine_ai

REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=redis_password

POSTGRESQL_HOST=postgresql
POSTGRESQL_PORT=5432
POSTGRESQL_USERNAME=postgres
POSTGRESQL_PASSWORD=postgres_password
POSTGRESQL_DATABASE=dl_engine_vectors

# Ollama配置
OLLAMA_HOST=ollama
OLLAMA_PORT=11434
AI_DEFAULT_MODEL=llama2
AI_EMBEDDING_MODEL=mxbai-embed-large

# 服务配置
AI_PORT=3002
LOG_LEVEL=info
EOF

    # 媒体服务环境配置
    cat > ./media/.env << EOF
NODE_ENV=production
PORT=3003

# 数据库配置
MYSQL_HOST=mysql
MYSQL_PORT=3306
MYSQL_USERNAME=dl_engine
MYSQL_PASSWORD=dl_engine_password
MYSQL_DATABASE=dl_engine_media

REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=redis_password

# Minio配置
MINIO_ENDPOINT=minio
MINIO_PORT=9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin
MINIO_USE_SSL=false

# 媒体处理配置
FFMPEG_PATH=/usr/bin/ffmpeg
IMAGEMAGICK_PATH=/usr/bin/convert
MAX_FILE_SIZE=1073741824
SUPPORTED_IMAGE_FORMATS=jpg,jpeg,png,gif,webp,bmp,tiff
SUPPORTED_VIDEO_FORMATS=mp4,avi,mov,wmv,flv,webm,mkv
SUPPORTED_AUDIO_FORMATS=mp3,wav,aac,ogg,flac,m4a

# 服务配置
MEDIA_PORT=3003
LOG_LEVEL=info
EOF

    log_success "环境配置文件生成完成"
}

# 构建Docker镜像
build_images() {
    log_info "构建Docker镜像..."
    
    # 构建存储服务镜像
    log_info "构建存储服务镜像..."
    docker build -t dl-engine/storage:latest ./storage/
    
    # 构建AI服务镜像
    log_info "构建AI服务镜像..."
    docker build -t dl-engine/ai:latest ./ai/
    
    # 构建媒体服务镜像
    log_info "构建媒体服务镜像..."
    docker build -t dl-engine/media:latest ./media/
    
    log_success "Docker镜像构建完成"
}

# 启动基础设施服务
start_infrastructure() {
    log_info "启动基础设施服务..."
    
    # 启动数据库服务
    docker-compose up -d mysql redis postgresql minio ollama
    
    log_info "等待数据库服务启动..."
    sleep 30
    
    # 检查服务状态
    check_service_health "mysql" "3306"
    check_service_health "redis" "6379"
    check_service_health "postgresql" "5432"
    check_service_health "minio" "9000"
    check_service_health "ollama" "11434"
    
    log_success "基础设施服务启动完成"
}

# 检查服务健康状态
check_service_health() {
    local service_name=$1
    local port=$2
    local max_attempts=30
    local attempt=1
    
    log_info "检查 $service_name 服务状态..."
    
    while [ $attempt -le $max_attempts ]; do
        if nc -z localhost $port; then
            log_success "$service_name 服务已就绪"
            return 0
        fi
        
        log_info "等待 $service_name 服务启动... (尝试 $attempt/$max_attempts)"
        sleep 2
        ((attempt++))
    done
    
    log_error "$service_name 服务启动失败"
    return 1
}

# 启动应用服务
start_applications() {
    log_info "启动应用服务..."
    
    # 启动第四批次服务
    docker-compose up -d storage ai media
    
    log_info "等待应用服务启动..."
    sleep 20
    
    # 检查应用服务状态
    check_service_health "storage" "3001"
    check_service_health "ai" "3002"
    check_service_health "media" "3003"
    
    log_success "应用服务启动完成"
}

# 运行健康检查
run_health_checks() {
    log_info "运行服务健康检查..."
    
    # 检查存储服务
    if curl -f http://localhost:3001/storage/health > /dev/null 2>&1; then
        log_success "存储服务健康检查通过"
    else
        log_warning "存储服务健康检查失败"
    fi
    
    # 检查AI服务
    if curl -f http://localhost:3002/ai/health > /dev/null 2>&1; then
        log_success "AI服务健康检查通过"
    else
        log_warning "AI服务健康检查失败"
    fi
    
    # 检查媒体服务
    if curl -f http://localhost:3003/media/health > /dev/null 2>&1; then
        log_success "媒体服务健康检查通过"
    else
        log_warning "媒体服务健康检查失败"
    fi
}

# 显示部署信息
show_deployment_info() {
    echo ""
    echo "🎉 DL-Engine 第四批次部署完成！"
    echo "=================================================="
    echo ""
    echo "📋 服务访问信息："
    echo "  存储服务:     http://localhost:3001"
    echo "  AI智能服务:   http://localhost:3002"
    echo "  媒体处理服务: http://localhost:3003"
    echo ""
    echo "📋 基础设施服务："
    echo "  MySQL:        localhost:3306"
    echo "  Redis:        localhost:6379"
    echo "  PostgreSQL:   localhost:5432"
    echo "  Minio:        http://localhost:9000 (控制台: http://localhost:9001)"
    echo "  Ollama:       http://localhost:11434"
    echo ""
    echo "📋 API文档："
    echo "  存储服务:     http://localhost:3001/api/docs"
    echo "  AI智能服务:   http://localhost:3002/api/docs"
    echo "  媒体处理服务: http://localhost:3003/api/docs"
    echo ""
    echo "🔧 管理命令："
    echo "  查看日志:     docker-compose logs -f [service_name]"
    echo "  停止服务:     docker-compose down"
    echo "  重启服务:     docker-compose restart [service_name]"
    echo "  查看状态:     docker-compose ps"
    echo ""
    echo "🧪 运行功能测试："
    echo "  cd ../../ && node test-fourth-batch.js"
    echo ""
}

# 主部署流程
main() {
    log_info "开始部署流程..."
    
    check_prerequisites
    create_directories
    generate_env_files
    build_images
    start_infrastructure
    start_applications
    run_health_checks
    show_deployment_info
    
    log_success "部署完成！"
}

# 错误处理
trap 'log_error "部署过程中发生错误，正在清理..."; docker-compose down; exit 1' ERR

# 执行主流程
main "$@"
