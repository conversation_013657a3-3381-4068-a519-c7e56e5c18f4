import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@nestjs/typeorm'
import { ConfigModule } from '@nestjs/config'

import { Course } from './entities/course.entity'
import { Lesson } from './entities/lesson.entity'
import { Assignment } from './entities/assignment.entity'
import { Submission } from './entities/submission.entity'
import { Assessment } from './entities/assessment.entity'
import { Grade } from './entities/grade.entity'
import { LearningPath } from './entities/learning-path.entity'
import { LearningProgress } from './entities/learning-progress.entity'
import { Achievement } from './entities/achievement.entity'
import { Certificate } from './entities/certificate.entity'
import { Classroom } from './entities/classroom.entity'
import { Enrollment } from './entities/enrollment.entity'

import { CoursesController } from './controllers/courses.controller'
import { LessonsController } from './controllers/lessons.controller'
import { AssignmentsController } from './controllers/assignments.controller'
import { SubmissionsController } from './controllers/submissions.controller'
import { AssessmentsController } from './controllers/assessments.controller'
import { GradesController } from './controllers/grades.controller'
import { LearningPathsController } from './controllers/learning-paths.controller'
import { LearningProgressController } from './controllers/learning-progress.controller'
import { AchievementsController } from './controllers/achievements.controller'
import { CertificatesController } from './controllers/certificates.controller'
import { ClassroomsController } from './controllers/classrooms.controller'
import { EnrollmentsController } from './controllers/enrollments.controller'

import { CoursesService } from './services/courses.service'
import { LessonsService } from './services/lessons.service'
import { AssignmentsService } from './services/assignments.service'
import { SubmissionsService } from './services/submissions.service'
import { AssessmentsService } from './services/assessments.service'
import { GradesService } from './services/grades.service'
import { LearningPathsService } from './services/learning-paths.service'
import { LearningProgressService } from './services/learning-progress.service'
import { AchievementsService } from './services/achievements.service'
import { CertificatesService } from './services/certificates.service'
import { ClassroomsService } from './services/classrooms.service'
import { EnrollmentsService } from './services/enrollments.service'
import { EducationAnalyticsService } from './services/education-analytics.service'
import { EducationValidationService } from './services/education-validation.service'

import { UsersModule } from '../users/users.module'
import { ProjectsModule } from '../projects/projects.module'
import { ScenesModule } from '../scenes/scenes.module'
import { AssetsModule } from '../assets/assets.module'
import { NotificationModule } from '../notifications/notification.module'

/**
 * 教育功能模块
 * 
 * DL-Engine的核心特色功能，包括：
 * - 课程管理和学习路径
 * - 作业系统和提交管理
 * - 评估系统和成绩管理
 * - 学习进度跟踪
 * - 成就系统和证书颁发
 * - 教室管理和学员注册
 * - 教育数据分析
 */
@Module({
  imports: [
    ConfigModule,
    TypeOrmModule.forFeature([
      Course,
      Lesson,
      Assignment,
      Submission,
      Assessment,
      Grade,
      LearningPath,
      LearningProgress,
      Achievement,
      Certificate,
      Classroom,
      Enrollment
    ]),
    UsersModule,
    ProjectsModule,
    ScenesModule,
    AssetsModule,
    NotificationModule
  ],
  controllers: [
    CoursesController,
    LessonsController,
    AssignmentsController,
    SubmissionsController,
    AssessmentsController,
    GradesController,
    LearningPathsController,
    LearningProgressController,
    AchievementsController,
    CertificatesController,
    ClassroomsController,
    EnrollmentsController
  ],
  providers: [
    CoursesService,
    LessonsService,
    AssignmentsService,
    SubmissionsService,
    AssessmentsService,
    GradesService,
    LearningPathsService,
    LearningProgressService,
    AchievementsService,
    CertificatesService,
    ClassroomsService,
    EnrollmentsService,
    EducationAnalyticsService,
    EducationValidationService
  ],
  exports: [
    CoursesService,
    LessonsService,
    AssignmentsService,
    SubmissionsService,
    AssessmentsService,
    GradesService,
    LearningPathsService,
    LearningProgressService,
    AchievementsService,
    CertificatesService,
    ClassroomsService,
    EnrollmentsService,
    EducationAnalyticsService
  ]
})
export class EducationModule {}
