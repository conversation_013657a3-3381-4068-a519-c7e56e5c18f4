import { Injectable, Inject, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as crypto from 'crypto';

export interface ObjectInfo {
  name: string;
  size: number;
  etag: string;
  lastModified: Date;
  contentType?: string;
  metadata?: Record<string, string>;
}

export interface BucketInfo {
  name: string;
  creationDate: Date;
  region?: string;
}

export interface PresignedUrlOptions {
  expires?: number; // 过期时间（秒）
  reqParams?: Record<string, string>;
  requestDate?: Date;
}

@Injectable()
export class MinioService {
  private readonly logger = new Logger(MinioService.name);

  constructor(
    @Inject('MINIO_CLIENT') private readonly minioClient: any,
    private configService: ConfigService,
  ) {}

  // 存储桶操作
  async createBucket(bucketName: string, region?: string): Promise<void> {
    try {
      const exists = await this.bucketExists(bucketName);
      if (!exists) {
        await this.minioClient.makeBucket(bucketName, region || 'us-east-1');
        this.logger.log(`Bucket created: ${bucketName}`);
      } else {
        this.logger.debug(`Bucket already exists: ${bucketName}`);
      }
    } catch (error) {
      this.logger.error(`Failed to create bucket ${bucketName}:`, error);
      throw error;
    }
  }

  async deleteBucket(bucketName: string): Promise<void> {
    try {
      await this.minioClient.removeBucket(bucketName);
      this.logger.log(`Bucket deleted: ${bucketName}`);
    } catch (error) {
      this.logger.error(`Failed to delete bucket ${bucketName}:`, error);
      throw error;
    }
  }

  async bucketExists(bucketName: string): Promise<boolean> {
    try {
      return await this.minioClient.bucketExists(bucketName);
    } catch (error) {
      this.logger.error(`Failed to check bucket existence ${bucketName}:`, error);
      return false;
    }
  }

  async listBuckets(): Promise<BucketInfo[]> {
    try {
      const buckets = await this.minioClient.listBuckets();
      return buckets.map(bucket => ({
        name: bucket.name,
        creationDate: bucket.creationDate,
      }));
    } catch (error) {
      this.logger.error('Failed to list buckets:', error);
      throw error;
    }
  }

  // 对象操作
  async putObject(
    bucketName: string,
    objectName: string,
    data: Buffer | string,
    metadata?: Record<string, string>
  ): Promise<{ etag: string; versionId?: string }> {
    try {
      const result = await this.minioClient.putObject(bucketName, objectName, data, metadata);
      this.logger.debug(`Object uploaded: ${bucketName}/${objectName}`);
      return result;
    } catch (error) {
      this.logger.error(`Failed to put object ${bucketName}/${objectName}:`, error);
      throw error;
    }
  }

  async getObject(bucketName: string, objectName: string): Promise<Buffer> {
    try {
      const stream = await this.minioClient.getObject(bucketName, objectName);
      const chunks: Buffer[] = [];
      
      return new Promise((resolve, reject) => {
        stream.on('data', (chunk: Buffer) => chunks.push(chunk));
        stream.on('end', () => resolve(Buffer.concat(chunks)));
        stream.on('error', reject);
      });
    } catch (error) {
      this.logger.error(`Failed to get object ${bucketName}/${objectName}:`, error);
      throw error;
    }
  }

  async getObjectStream(bucketName: string, objectName: string): Promise<any> {
    try {
      return await this.minioClient.getObject(bucketName, objectName);
    } catch (error) {
      this.logger.error(`Failed to get object stream ${bucketName}/${objectName}:`, error);
      throw error;
    }
  }

  async removeObject(bucketName: string, objectName: string): Promise<void> {
    try {
      await this.minioClient.removeObject(bucketName, objectName);
      this.logger.debug(`Object removed: ${bucketName}/${objectName}`);
    } catch (error) {
      this.logger.error(`Failed to remove object ${bucketName}/${objectName}:`, error);
      throw error;
    }
  }

  async removeObjects(bucketName: string, objectNames: string[]): Promise<void> {
    try {
      await this.minioClient.removeObjects(bucketName, objectNames);
      this.logger.debug(`Objects removed: ${bucketName}/${objectNames.length} objects`);
    } catch (error) {
      this.logger.error(`Failed to remove objects from ${bucketName}:`, error);
      throw error;
    }
  }

  async copyObject(
    bucketName: string,
    objectName: string,
    sourceObject: string,
    conditions?: any,
    metadata?: Record<string, string>
  ): Promise<{ etag: string; lastModified: Date }> {
    try {
      const result = await this.minioClient.copyObject(
        bucketName,
        objectName,
        sourceObject,
        conditions,
        metadata
      );
      this.logger.debug(`Object copied: ${sourceObject} -> ${bucketName}/${objectName}`);
      return result;
    } catch (error) {
      this.logger.error(`Failed to copy object ${sourceObject} to ${bucketName}/${objectName}:`, error);
      throw error;
    }
  }

  async statObject(bucketName: string, objectName: string): Promise<ObjectInfo> {
    try {
      const stat = await this.minioClient.statObject(bucketName, objectName);
      return {
        name: objectName,
        size: stat.size,
        etag: stat.etag,
        lastModified: stat.lastModified,
        contentType: stat.metaData?.['content-type'],
        metadata: stat.metaData,
      };
    } catch (error) {
      this.logger.error(`Failed to stat object ${bucketName}/${objectName}:`, error);
      throw error;
    }
  }

  async listObjects(
    bucketName: string,
    prefix?: string,
    recursive: boolean = false
  ): Promise<ObjectInfo[]> {
    try {
      const objects: ObjectInfo[] = [];
      const stream = this.minioClient.listObjects(bucketName, prefix, recursive);
      
      return new Promise((resolve, reject) => {
        stream.on('data', (obj: any) => {
          objects.push({
            name: obj.name,
            size: obj.size,
            etag: obj.etag,
            lastModified: obj.lastModified,
          });
        });
        stream.on('end', () => resolve(objects));
        stream.on('error', reject);
      });
    } catch (error) {
      this.logger.error(`Failed to list objects in ${bucketName}:`, error);
      throw error;
    }
  }

  // 预签名URL
  async getPresignedUrl(
    method: 'GET' | 'PUT' | 'DELETE',
    bucketName: string,
    objectName: string,
    options: PresignedUrlOptions = {}
  ): Promise<string> {
    try {
      const { expires = 3600, reqParams, requestDate } = options;
      
      let url: string;
      
      switch (method) {
        case 'GET':
          url = await this.minioClient.presignedGetObject(bucketName, objectName, expires, reqParams, requestDate);
          break;
        case 'PUT':
          url = await this.minioClient.presignedPutObject(bucketName, objectName, expires);
          break;
        case 'DELETE':
          url = await this.minioClient.presignedUrl(method, bucketName, objectName, expires, reqParams, requestDate);
          break;
        default:
          throw new Error(`Unsupported method: ${method}`);
      }
      
      this.logger.debug(`Presigned URL generated: ${method} ${bucketName}/${objectName}`);
      return url;
    } catch (error) {
      this.logger.error(`Failed to generate presigned URL for ${bucketName}/${objectName}:`, error);
      throw error;
    }
  }

  async getPresignedPostPolicy(
    bucketName: string,
    objectName: string,
    expires: Date,
    conditions?: any
  ): Promise<{ postURL: string; formData: Record<string, string> }> {
    try {
      const policy = this.minioClient.newPostPolicy();
      policy.setBucket(bucketName);
      policy.setKey(objectName);
      policy.setExpires(expires);
      
      if (conditions) {
        Object.entries(conditions).forEach(([key, value]) => {
          policy.setContentLengthRange(value.min, value.max);
        });
      }
      
      const result = await this.minioClient.presignedPostPolicy(policy);
      this.logger.debug(`Presigned POST policy generated: ${bucketName}/${objectName}`);
      return result;
    } catch (error) {
      this.logger.error(`Failed to generate presigned POST policy for ${bucketName}/${objectName}:`, error);
      throw error;
    }
  }

  // 分片上传
  async initiateMultipartUpload(
    bucketName: string,
    objectName: string,
    metadata?: Record<string, string>
  ): Promise<string> {
    try {
      const uploadId = await this.minioClient.initiateNewMultipartUpload(bucketName, objectName, metadata);
      this.logger.debug(`Multipart upload initiated: ${bucketName}/${objectName} (${uploadId})`);
      return uploadId;
    } catch (error) {
      this.logger.error(`Failed to initiate multipart upload for ${bucketName}/${objectName}:`, error);
      throw error;
    }
  }

  async uploadPart(
    bucketName: string,
    objectName: string,
    uploadId: string,
    partNumber: number,
    data: Buffer
  ): Promise<{ etag: string; partNumber: number }> {
    try {
      const result = await this.minioClient.uploadPart(bucketName, objectName, uploadId, partNumber, data);
      this.logger.debug(`Part uploaded: ${bucketName}/${objectName} part ${partNumber}`);
      return { etag: result.etag, partNumber };
    } catch (error) {
      this.logger.error(`Failed to upload part ${partNumber} for ${bucketName}/${objectName}:`, error);
      throw error;
    }
  }

  async completeMultipartUpload(
    bucketName: string,
    objectName: string,
    uploadId: string,
    parts: Array<{ etag: string; partNumber: number }>
  ): Promise<{ etag: string; location: string }> {
    try {
      const result = await this.minioClient.completeMultipartUpload(bucketName, objectName, uploadId, parts);
      this.logger.log(`Multipart upload completed: ${bucketName}/${objectName}`);
      return result;
    } catch (error) {
      this.logger.error(`Failed to complete multipart upload for ${bucketName}/${objectName}:`, error);
      throw error;
    }
  }

  async abortMultipartUpload(bucketName: string, objectName: string, uploadId: string): Promise<void> {
    try {
      await this.minioClient.abortMultipartUpload(bucketName, objectName, uploadId);
      this.logger.debug(`Multipart upload aborted: ${bucketName}/${objectName} (${uploadId})`);
    } catch (error) {
      this.logger.error(`Failed to abort multipart upload for ${bucketName}/${objectName}:`, error);
      throw error;
    }
  }

  // 工具方法
  async generateObjectKey(prefix: string, filename: string): Promise<string> {
    const timestamp = Date.now();
    const random = crypto.randomBytes(8).toString('hex');
    const ext = filename.split('.').pop();
    return `${prefix}/${timestamp}_${random}.${ext}`;
  }

  async calculateMD5(data: Buffer): Promise<string> {
    return crypto.createHash('md5').update(data).digest('hex');
  }

  async validateObjectIntegrity(bucketName: string, objectName: string, expectedMD5: string): Promise<boolean> {
    try {
      const objectData = await this.getObject(bucketName, objectName);
      const actualMD5 = await this.calculateMD5(objectData);
      return actualMD5 === expectedMD5;
    } catch (error) {
      this.logger.error(`Failed to validate object integrity ${bucketName}/${objectName}:`, error);
      return false;
    }
  }

  // 健康检查
  async healthCheck(): Promise<{ status: string; details: any }> {
    try {
      const buckets = await this.listBuckets();
      return {
        status: 'healthy',
        details: {
          connection: 'active',
          bucketsCount: buckets.length,
          timestamp: new Date().toISOString(),
        },
      };
    } catch (error) {
      this.logger.error('Minio health check failed:', error);
      return {
        status: 'unhealthy',
        details: {
          error: error.message,
          timestamp: new Date().toISOString(),
        },
      };
    }
  }

  // 获取客户端
  getClient() {
    return this.minioClient;
  }
}
