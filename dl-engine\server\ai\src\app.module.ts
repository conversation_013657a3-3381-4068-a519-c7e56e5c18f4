import { Module } from '@nestjs/common'
import { ConfigModule } from '@nestjs/config'
import { TypeOrmModule } from '@nestjs/typeorm'
import { HttpModule } from '@nestjs/axios'

import { OllamaModule } from './ollama/ollama.module'
import { EmbeddingsModule } from './embeddings/embeddings.module'
import { RecommendationsModule } from './recommendations/recommendations.module'
import { AnalyticsModule } from './analytics/analytics.module'
import { NlpModule } from './nlp/nlp.module'
import { HealthModule } from './health/health.module'

import { AIModel } from './entities/ai-model.entity'
import { EmbeddingVector } from './entities/embedding-vector.entity'
import { RecommendationLog } from './entities/recommendation-log.entity'
import { AnalyticsData } from './entities/analytics-data.entity'

/**
 * DL-Engine AI智能服务主模块
 * 
 * 功能包括：
 * - Ollama集成服务 (模型管理、嵌入服务、推理服务)
 * - 智能功能 (内容推荐、学习分析、自然语言处理)
 * - 向量嵌入和相似度搜索
 * - 学习分析和个性化推荐
 */
@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env']
    }),

    HttpModule.register({
      timeout: 30000,
      maxRedirects: 5
    }),
    
    TypeOrmModule.forRoot({
      type: 'mysql',
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT || '3306'),
      username: process.env.DB_USERNAME || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_DATABASE || 'dl_engine_ai',
      entities: [AIModel, EmbeddingVector, RecommendationLog, AnalyticsData],
      synchronize: process.env.NODE_ENV === 'development',
      logging: process.env.NODE_ENV === 'development'
    }),

    // PostgreSQL 向量数据库
    TypeOrmModule.forRoot({
      name: 'vectors',
      type: 'postgres',
      host: process.env.POSTGRES_HOST || 'localhost',
      port: parseInt(process.env.POSTGRES_PORT || '5432'),
      username: process.env.POSTGRES_USERNAME || 'postgres',
      password: process.env.POSTGRES_PASSWORD || '',
      database: process.env.POSTGRES_DATABASE || 'dl_engine_vectors',
      entities: [EmbeddingVector],
      synchronize: process.env.NODE_ENV === 'development',
      logging: process.env.NODE_ENV === 'development'
    }),

    OllamaModule,
    EmbeddingsModule,
    RecommendationsModule,
    AnalyticsModule,
    NlpModule,
    HealthModule
  ]
})
export class AppModule {}
