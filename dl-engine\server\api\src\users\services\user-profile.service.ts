/**
 * DL-Engine 用户资料管理服务
 * 
 * 专门处理用户资料的高级功能：
 * - 头像上传和管理
 * - 资料完整度分析
 * - 资料隐私控制
 * - 资料验证和审核
 */

import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { Repository } from 'typeorm'
import { ConfigService } from '@nestjs/config'

import { User } from '../entities/user.entity'
import { UserProfile } from '../entities/user-profile.entity'
import { UserPrivacy } from '../entities/user-privacy.entity'

@Injectable()
export class UserProfileService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(UserProfile)
    private readonly profileRepository: Repository<UserProfile>,
    @InjectRepository(UserPrivacy)
    private readonly privacyRepository: Repository<UserPrivacy>,
    private readonly configService: ConfigService
  ) {}

  /**
   * 获取用户完整资料
   */
  async getFullProfile(userId: string): Promise<any> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['profile', 'privacy', 'educations']
    })

    if (!user) {
      throw new NotFoundException('用户不存在')
    }

    const completeness = await this.calculateProfileCompleteness(user)
    const verificationStatus = this.getVerificationStatus(user)

    return {
      user: {
        id: user.id,
        username: user.username,
        displayName: user.displayName,
        avatarUrl: user.avatarUrl,
        status: user.status,
        userType: user.userType,
        phoneVerified: user.phoneVerified,
        emailVerified: user.emailVerified,
        createdAt: user.createdAt
      },
      profile: user.profile,
      privacy: user.privacy,
      educations: user.educations,
      stats: {
        completeness,
        verificationStatus,
        profileViews: user.profile?.profileViews || 0,
        lastProfileUpdate: user.profile?.updatedAt
      }
    }
  }

  /**
   * 更新用户头像
   */
  async updateAvatar(userId: string, avatarFile: Express.Multer.File): Promise<string> {
    const user = await this.userRepository.findOne({ where: { id: userId } })
    
    if (!user) {
      throw new NotFoundException('用户不存在')
    }

    // 验证文件类型和大小
    this.validateAvatarFile(avatarFile)

    // 上传文件到存储服务
    const avatarUrl = await this.uploadAvatarFile(userId, avatarFile)

    // 更新用户头像URL
    user.avatarUrl = avatarUrl
    await this.userRepository.save(user)

    return avatarUrl
  }

  /**
   * 计算资料完整度
   */
  async calculateProfileCompleteness(user: User): Promise<{
    percentage: number
    missingFields: string[]
    suggestions: string[]
  }> {
    const requiredFields = [
      { field: 'displayName', weight: 15, name: '显示名称' },
      { field: 'avatarUrl', weight: 10, name: '头像' },
      { field: 'profile.realName', weight: 15, name: '真实姓名' },
      { field: 'profile.bio', weight: 10, name: '个人简介' },
      { field: 'profile.location', weight: 8, name: '所在地' },
      { field: 'profile.birthDate', weight: 7, name: '生日' },
      { field: 'profile.website', weight: 5, name: '个人网站' },
      { field: 'phoneVerified', weight: 15, name: '手机验证' },
      { field: 'emailVerified', weight: 15, name: '邮箱验证' }
    ]

    let totalWeight = 0
    let completedWeight = 0
    const missingFields: string[] = []
    const suggestions: string[] = []

    for (const field of requiredFields) {
      totalWeight += field.weight
      const value = this.getNestedValue(user, field.field)
      
      if (this.isFieldComplete(value)) {
        completedWeight += field.weight
      } else {
        missingFields.push(field.name)
        suggestions.push(`完善${field.name}可提升资料完整度`)
      }
    }

    const percentage = Math.round((completedWeight / totalWeight) * 100)

    return {
      percentage,
      missingFields,
      suggestions: suggestions.slice(0, 3) // 只返回前3个建议
    }
  }

  /**
   * 获取验证状态
   */
  private getVerificationStatus(user: User): {
    overall: string
    phone: boolean
    email: boolean
    identity: boolean
  } {
    const phone = user.phoneVerified
    const email = user.emailVerified
    const identity = user.profile?.identityVerified || false

    let overall = 'unverified'
    if (phone && email && identity) {
      overall = 'fully_verified'
    } else if (phone || email) {
      overall = 'partially_verified'
    }

    return { overall, phone, email, identity }
  }

  /**
   * 验证头像文件
   */
  private validateAvatarFile(file: Express.Multer.File): void {
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp']
    const maxSize = 5 * 1024 * 1024 // 5MB

    if (!allowedTypes.includes(file.mimetype)) {
      throw new BadRequestException('不支持的文件类型，请上传 JPEG、PNG 或 WebP 格式的图片')
    }

    if (file.size > maxSize) {
      throw new BadRequestException('文件大小不能超过 5MB')
    }
  }

  /**
   * 上传头像文件
   */
  private async uploadAvatarFile(userId: string, file: Express.Multer.File): Promise<string> {
    // 这里应该集成实际的文件存储服务（如 Minio、AWS S3 等）
    // 暂时返回一个模拟的 URL
    const timestamp = Date.now()
    const extension = file.originalname.split('.').pop()
    return `https://storage.dl-engine.com/avatars/${userId}/${timestamp}.${extension}`
  }

  /**
   * 获取嵌套对象的值
   */
  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj)
  }

  /**
   * 检查字段是否完整
   */
  private isFieldComplete(value: any): boolean {
    if (value === null || value === undefined) return false
    if (typeof value === 'string' && value.trim() === '') return false
    if (typeof value === 'boolean') return value
    return true
  }

  /**
   * 更新资料可见性设置
   */
  async updateProfileVisibility(userId: string, visibilitySettings: any): Promise<UserPrivacy> {
    const privacy = await this.privacyRepository.findOne({ where: { userId } })
    
    if (!privacy) {
      throw new NotFoundException('隐私设置不存在')
    }

    Object.assign(privacy, {
      ...visibilitySettings,
      updatedAt: new Date()
    })

    return await this.privacyRepository.save(privacy)
  }

  /**
   * 获取资料访问统计
   */
  async getProfileStats(userId: string): Promise<any> {
    const profile = await this.profileRepository.findOne({ where: { userId } })
    
    if (!profile) {
      throw new NotFoundException('用户资料不存在')
    }

    return {
      profileViews: profile.profileViews || 0,
      lastViewedAt: profile.lastViewedAt,
      profileUpdates: profile.profileUpdateCount || 0,
      lastUpdatedAt: profile.updatedAt,
      createdAt: profile.createdAt
    }
  }

  /**
   * 记录资料访问
   */
  async recordProfileView(userId: string, viewerId?: string): Promise<void> {
    // 不记录自己查看自己的资料
    if (userId === viewerId) return

    await this.profileRepository.update(userId, {
      profileViews: () => 'profileViews + 1',
      lastViewedAt: new Date()
    })
  }
}
