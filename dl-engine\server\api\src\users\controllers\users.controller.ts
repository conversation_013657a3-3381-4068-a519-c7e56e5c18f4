import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  UseInterceptors,
  HttpStatus,
  ParseUUIDPipe
} from '@nestjs/common'
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBearerAuth
} from '@nestjs/swagger'

import { UsersService } from '../services/users.service'
import { CreateUserDto } from '../dto/create-user.dto'
import { UpdateUserDto } from '../dto/update-user.dto'
import { UserQueryDto } from '../dto/user-query.dto'
import { UserResponseDto } from '../dto/user-response.dto'

import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard'
import { RolesGuard } from '../../auth/guards/roles.guard'
import { Roles } from '../../auth/decorators/roles.decorator'
import { CurrentUser } from '../../auth/decorators/current-user.decorator'
import { LoggingInterceptor } from '../../common/interceptors/logging.interceptor'
import { TransformInterceptor } from '../../common/interceptors/transform.interceptor'

/**
 * 用户管理控制器
 * 
 * 提供用户相关的HTTP API接口
 */
@ApiTags('用户管理')
@Controller('users')
@UseInterceptors(LoggingInterceptor, TransformInterceptor)
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  /**
   * 创建新用户
   */
  @Post()
  @ApiOperation({ summary: '创建新用户', description: '注册新用户账户' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: '用户创建成功',
    type: UserResponseDto
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: '请求参数错误'
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: '用户已存在'
  })
  async create(@Body() createUserDto: CreateUserDto): Promise<UserResponseDto> {
    return await this.usersService.create(createUserDto)
  }

  /**
   * 获取用户列表
   */
  @Get()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'teacher')
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取用户列表', description: '分页查询用户列表（需要管理员或教师权限）' })
  @ApiQuery({ name: 'page', required: false, description: '页码', example: 1 })
  @ApiQuery({ name: 'limit', required: false, description: '每页数量', example: 20 })
  @ApiQuery({ name: 'search', required: false, description: '搜索关键词' })
  @ApiQuery({ name: 'userType', required: false, description: '用户类型' })
  @ApiQuery({ name: 'status', required: false, description: '用户状态' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '获取用户列表成功'
  })
  async findMany(@Query() query: UserQueryDto) {
    return await this.usersService.findMany(query)
  }

  /**
   * 获取当前用户信息
   */
  @Get('me')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取当前用户信息', description: '获取当前登录用户的详细信息' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '获取用户信息成功',
    type: UserResponseDto
  })
  async getCurrentUser(@CurrentUser() user: any): Promise<UserResponseDto> {
    return await this.usersService.findById(user.id, true)
  }

  /**
   * 根据ID获取用户信息
   */
  @Get(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '根据ID获取用户信息', description: '获取指定用户的详细信息' })
  @ApiParam({ name: 'id', description: '用户ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '获取用户信息成功',
    type: UserResponseDto
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: '用户不存在'
  })
  async findById(
    @Param('id', ParseUUIDPipe) id: string,
    @Query('includeRelations') includeRelations?: boolean
  ): Promise<UserResponseDto> {
    return await this.usersService.findById(id, includeRelations)
  }

  /**
   * 根据手机号查找用户
   */
  @Get('phone/:phone')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  @ApiBearerAuth()
  @ApiOperation({ summary: '根据手机号查找用户', description: '通过手机号查找用户（需要管理员权限）' })
  @ApiParam({ name: 'phone', description: '手机号' })
  @ApiQuery({ name: 'countryCode', required: false, description: '国家代码', example: '+86' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '查找用户成功',
    type: UserResponseDto
  })
  async findByPhone(
    @Param('phone') phone: string,
    @Query('countryCode') countryCode?: string
  ): Promise<UserResponseDto | null> {
    return await this.usersService.findByPhone(phone, countryCode)
  }

  /**
   * 根据邮箱查找用户
   */
  @Get('email/:email')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  @ApiBearerAuth()
  @ApiOperation({ summary: '根据邮箱查找用户', description: '通过邮箱查找用户（需要管理员权限）' })
  @ApiParam({ name: 'email', description: '邮箱地址' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '查找用户成功',
    type: UserResponseDto
  })
  async findByEmail(@Param('email') email: string): Promise<UserResponseDto | null> {
    return await this.usersService.findByEmail(email)
  }

  /**
   * 更新用户信息
   */
  @Put(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '更新用户信息', description: '更新指定用户的信息' })
  @ApiParam({ name: 'id', description: '用户ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '用户信息更新成功',
    type: UserResponseDto
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: '用户不存在'
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: '无权限修改此用户'
  })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateUserDto: UpdateUserDto,
    @CurrentUser() currentUser: any
  ): Promise<UserResponseDto> {
    // 检查权限：只能修改自己的信息，或者管理员可以修改任何人的信息
    if (currentUser.id !== id && !currentUser.roles?.includes('admin')) {
      throw new Error('无权限修改此用户信息')
    }

    return await this.usersService.update(id, updateUserDto)
  }

  /**
   * 更新当前用户信息
   */
  @Put('me')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '更新当前用户信息', description: '更新当前登录用户的信息' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '用户信息更新成功',
    type: UserResponseDto
  })
  async updateCurrentUser(
    @Body() updateUserDto: UpdateUserDto,
    @CurrentUser() user: any
  ): Promise<UserResponseDto> {
    return await this.usersService.update(user.id, updateUserDto)
  }

  /**
   * 删除用户
   */
  @Delete(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  @ApiBearerAuth()
  @ApiOperation({ summary: '删除用户', description: '删除指定用户（需要管理员权限）' })
  @ApiParam({ name: 'id', description: '用户ID' })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: '用户删除成功'
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: '用户不存在'
  })
  async remove(@Param('id', ParseUUIDPipe) id: string): Promise<void> {
    await this.usersService.remove(id)
  }

  /**
   * 激活用户账户
   */
  @Post(':id/activate')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  @ApiBearerAuth()
  @ApiOperation({ summary: '激活用户账户', description: '激活指定用户账户（需要管理员权限）' })
  @ApiParam({ name: 'id', description: '用户ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '用户账户激活成功',
    type: UserResponseDto
  })
  async activate(@Param('id', ParseUUIDPipe) id: string): Promise<UserResponseDto> {
    return await this.usersService.activate(id)
  }

  /**
   * 验证手机号
   */
  @Post(':id/verify-phone')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '验证手机号', description: '标记用户手机号为已验证' })
  @ApiParam({ name: 'id', description: '用户ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '手机号验证成功',
    type: UserResponseDto
  })
  async verifyPhone(
    @Param('id', ParseUUIDPipe) id: string,
    @CurrentUser() currentUser: any
  ): Promise<UserResponseDto> {
    // 检查权限：只能验证自己的手机号，或者管理员可以验证任何人的
    if (currentUser.id !== id && !currentUser.roles?.includes('admin')) {
      throw new Error('无权限验证此用户的手机号')
    }

    return await this.usersService.verifyPhone(id)
  }

  /**
   * 验证邮箱
   */
  @Post(':id/verify-email')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '验证邮箱', description: '标记用户邮箱为已验证' })
  @ApiParam({ name: 'id', description: '用户ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '邮箱验证成功',
    type: UserResponseDto
  })
  async verifyEmail(
    @Param('id', ParseUUIDPipe) id: string,
    @CurrentUser() currentUser: any
  ): Promise<UserResponseDto> {
    // 检查权限：只能验证自己的邮箱，或者管理员可以验证任何人的
    if (currentUser.id !== id && !currentUser.roles?.includes('admin')) {
      throw new Error('无权限验证此用户的邮箱')
    }

    return await this.usersService.verifyEmail(id)
  }

  /**
   * 更新最后活跃时间
   */
  @Post('me/heartbeat')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '更新活跃状态', description: '更新当前用户的最后活跃时间' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '活跃状态更新成功'
  })
  async updateHeartbeat(@CurrentUser() user: any): Promise<{ success: boolean }> {
    await this.usersService.updateLastActive(user.id)
    return { success: true }
  }
}
