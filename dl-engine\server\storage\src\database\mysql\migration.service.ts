import { Injectable, Logger } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { TransactionService } from './transaction.service';

export interface MigrationStep {
  id: string;
  name: string;
  description: string;
  version: string;
  execute: (dataSource: DataSource) => Promise<void>;
  rollback: (dataSource: DataSource) => Promise<void>;
  validate?: (dataSource: DataSource) => Promise<boolean>;
}

export interface MigrationStatus {
  id: string;
  name: string;
  version: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'rolled_back';
  executedAt?: Date;
  duration?: number;
  error?: string;
}

@Injectable()
export class DataMigrationService {
  private readonly logger = new Logger(DataMigrationService.name);

  constructor(
    private dataSource: DataSource,
    private transactionService: TransactionService,
  ) {}

  /**
   * 初始化迁移表
   */
  async initializeMigrationTable(): Promise<void> {
    await this.dataSource.query(`
      CREATE TABLE IF NOT EXISTS dl_migrations (
        id VARCHAR(255) PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        version VARCHAR(50) NOT NULL,
        status ENUM('pending', 'running', 'completed', 'failed', 'rolled_back') DEFAULT 'pending',
        executed_at TIMESTAMP NULL,
        duration INT NULL,
        error TEXT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);
  }

  /**
   * 执行迁移
   */
  async executeMigration(migration: MigrationStep): Promise<void> {
    await this.initializeMigrationTable();

    // 检查迁移是否已执行
    const existing = await this.getMigrationStatus(migration.id);
    if (existing && existing.status === 'completed') {
      this.logger.log(`Migration ${migration.id} already completed, skipping`);
      return;
    }

    this.logger.log(`Starting migration: ${migration.name} (${migration.id})`);
    const startTime = Date.now();

    try {
      // 更新状态为运行中
      await this.updateMigrationStatus(migration.id, {
        name: migration.name,
        version: migration.version,
        status: 'running',
        executedAt: new Date(),
      });

      // 执行迁移
      await this.transactionService.executeTransaction(async () => {
        await migration.execute(this.dataSource);
      });

      // 验证迁移结果
      if (migration.validate) {
        const isValid = await migration.validate(this.dataSource);
        if (!isValid) {
          throw new Error('Migration validation failed');
        }
      }

      const duration = Date.now() - startTime;

      // 更新状态为完成
      await this.updateMigrationStatus(migration.id, {
        status: 'completed',
        duration,
      });

      this.logger.log(`Migration ${migration.id} completed in ${duration}ms`);

    } catch (error) {
      const duration = Date.now() - startTime;

      // 更新状态为失败
      await this.updateMigrationStatus(migration.id, {
        status: 'failed',
        duration,
        error: error.message,
      });

      this.logger.error(`Migration ${migration.id} failed:`, error);
      throw error;
    }
  }

  /**
   * 批量执行迁移
   */
  async executeMigrations(migrations: MigrationStep[]): Promise<void> {
    this.logger.log(`Starting batch migration of ${migrations.length} steps`);

    for (const migration of migrations) {
      await this.executeMigration(migration);
    }

    this.logger.log('Batch migration completed');
  }

  /**
   * 回滚迁移
   */
  async rollbackMigration(migrationId: string): Promise<void> {
    const status = await this.getMigrationStatus(migrationId);
    if (!status || status.status !== 'completed') {
      throw new Error(`Cannot rollback migration ${migrationId}: not in completed state`);
    }

    this.logger.log(`Rolling back migration: ${migrationId}`);
    const startTime = Date.now();

    try {
      // 这里需要从某个地方获取迁移定义，实际实现中可能需要迁移注册表
      // 为了示例，我们假设有一个方法来获取迁移定义
      const migration = await this.getMigrationDefinition(migrationId);
      
      await this.transactionService.executeTransaction(async () => {
        await migration.rollback(this.dataSource);
      });

      const duration = Date.now() - startTime;

      await this.updateMigrationStatus(migrationId, {
        status: 'rolled_back',
        duration,
      });

      this.logger.log(`Migration ${migrationId} rolled back in ${duration}ms`);

    } catch (error) {
      this.logger.error(`Failed to rollback migration ${migrationId}:`, error);
      throw error;
    }
  }

  /**
   * 获取迁移状态
   */
  async getMigrationStatus(migrationId: string): Promise<MigrationStatus | null> {
    try {
      const [result] = await this.dataSource.query(
        'SELECT * FROM dl_migrations WHERE id = ?',
        [migrationId]
      );

      if (!result) return null;

      return {
        id: result.id,
        name: result.name,
        version: result.version,
        status: result.status,
        executedAt: result.executed_at,
        duration: result.duration,
        error: result.error,
      };
    } catch (error) {
      this.logger.error(`Failed to get migration status for ${migrationId}:`, error);
      return null;
    }
  }

  /**
   * 获取所有迁移状态
   */
  async getAllMigrationStatuses(): Promise<MigrationStatus[]> {
    try {
      const results = await this.dataSource.query(
        'SELECT * FROM dl_migrations ORDER BY executed_at DESC'
      );

      return results.map(result => ({
        id: result.id,
        name: result.name,
        version: result.version,
        status: result.status,
        executedAt: result.executed_at,
        duration: result.duration,
        error: result.error,
      }));
    } catch (error) {
      this.logger.error('Failed to get migration statuses:', error);
      return [];
    }
  }

  /**
   * 数据一致性检查
   */
  async validateDataConsistency(): Promise<{
    isValid: boolean;
    issues: string[];
    details: any;
  }> {
    const issues: string[] = [];
    const details: any = {};

    try {
      // 检查外键约束
      const foreignKeyIssues = await this.checkForeignKeyConstraints();
      if (foreignKeyIssues.length > 0) {
        issues.push('Foreign key constraint violations found');
        details.foreignKeyIssues = foreignKeyIssues;
      }

      // 检查数据完整性
      const integrityIssues = await this.checkDataIntegrity();
      if (integrityIssues.length > 0) {
        issues.push('Data integrity issues found');
        details.integrityIssues = integrityIssues;
      }

      // 检查索引一致性
      const indexIssues = await this.checkIndexConsistency();
      if (indexIssues.length > 0) {
        issues.push('Index consistency issues found');
        details.indexIssues = indexIssues;
      }

      return {
        isValid: issues.length === 0,
        issues,
        details,
      };

    } catch (error) {
      this.logger.error('Data consistency validation failed:', error);
      return {
        isValid: false,
        issues: ['Validation process failed'],
        details: { error: error.message },
      };
    }
  }

  /**
   * 数据备份
   */
  async createDataBackup(tables?: string[]): Promise<string> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupId = `backup_${timestamp}`;

    try {
      const tablesToBackup = tables || await this.getAllTableNames();
      
      for (const table of tablesToBackup) {
        await this.dataSource.query(`
          CREATE TABLE ${backupId}_${table} AS SELECT * FROM ${table}
        `);
      }

      this.logger.log(`Data backup created: ${backupId}`);
      return backupId;

    } catch (error) {
      this.logger.error('Failed to create data backup:', error);
      throw error;
    }
  }

  /**
   * 恢复数据备份
   */
  async restoreDataBackup(backupId: string): Promise<void> {
    try {
      const backupTables = await this.getBackupTableNames(backupId);
      
      await this.transactionService.executeTransaction(async (context) => {
        for (const backupTable of backupTables) {
          const originalTable = backupTable.replace(`${backupId}_`, '');
          
          // 清空原表
          await context.queryRunner.query(`TRUNCATE TABLE ${originalTable}`);
          
          // 恢复数据
          await context.queryRunner.query(`
            INSERT INTO ${originalTable} SELECT * FROM ${backupTable}
          `);
        }
      });

      this.logger.log(`Data restored from backup: ${backupId}`);

    } catch (error) {
      this.logger.error('Failed to restore data backup:', error);
      throw error;
    }
  }

  private async updateMigrationStatus(
    migrationId: string,
    updates: Partial<MigrationStatus>
  ): Promise<void> {
    const fields = [];
    const values = [];

    if (updates.name) {
      fields.push('name = ?');
      values.push(updates.name);
    }
    if (updates.version) {
      fields.push('version = ?');
      values.push(updates.version);
    }
    if (updates.status) {
      fields.push('status = ?');
      values.push(updates.status);
    }
    if (updates.executedAt) {
      fields.push('executed_at = ?');
      values.push(updates.executedAt);
    }
    if (updates.duration !== undefined) {
      fields.push('duration = ?');
      values.push(updates.duration);
    }
    if (updates.error !== undefined) {
      fields.push('error = ?');
      values.push(updates.error);
    }

    values.push(migrationId);

    await this.dataSource.query(`
      INSERT INTO dl_migrations (id, ${fields.map(f => f.split(' = ')[0]).join(', ')})
      VALUES (?, ${fields.map(() => '?').join(', ')})
      ON DUPLICATE KEY UPDATE ${fields.join(', ')}
    `, values);
  }

  private async getMigrationDefinition(migrationId: string): Promise<MigrationStep> {
    // 这里应该从迁移注册表或文件系统中获取迁移定义
    // 为了示例，返回一个空的迁移定义
    throw new Error('Migration definition not found');
  }

  private async checkForeignKeyConstraints(): Promise<string[]> {
    // 实现外键约束检查逻辑
    return [];
  }

  private async checkDataIntegrity(): Promise<string[]> {
    // 实现数据完整性检查逻辑
    return [];
  }

  private async checkIndexConsistency(): Promise<string[]> {
    // 实现索引一致性检查逻辑
    return [];
  }

  private async getAllTableNames(): Promise<string[]> {
    const results = await this.dataSource.query(`
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_SCHEMA = DATABASE()
      AND TABLE_TYPE = 'BASE TABLE'
    `);
    
    return results.map(row => row.TABLE_NAME);
  }

  private async getBackupTableNames(backupId: string): Promise<string[]> {
    const results = await this.dataSource.query(`
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_SCHEMA = DATABASE()
      AND TABLE_NAME LIKE '${backupId}_%'
    `);
    
    return results.map(row => row.TABLE_NAME);
  }
}
