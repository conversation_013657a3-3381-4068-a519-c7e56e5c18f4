# DL-Engine 服务器端核心服务

数字化学习引擎 (Digital Learning Engine) 服务器端核心服务，包含实例管理和任务调度两大核心服务模块。

## 架构概览

### 实例服务 (Instance Services)
位于 `dl-engine/server/instance/` 目录，负责管理虚拟世界实例的生命周期。

#### 核心模块
- **实例管理器 (Instance Manager)**: 实例创建、销毁、状态管理和资源分配
- **网络同步 (Network Sync)**: 状态同步、事件广播和客户端管理  
- **物理同步 (Physics Sync)**: 物理状态同步、碰撞事件和约束同步
- **扩缩容管理 (Scaling Manager)**: 自动扩容、负载监控和实例迁移

### 任务服务 (Task Services)
位于 `dl-engine/server/task/` 目录，负责分布式任务调度和执行。

#### 核心模块
- **任务调度器 (Task Scheduler)**: 定时任务、优先级队列和任务分发
- **任务队列 (Task Queue)**: 消息队列、任务持久化和失败重试
- **工作进程 (Task Worker)**: 工作节点、任务执行和结果回调

## 功能特性

### 实例服务特性
- ✅ 实例生命周期管理 (创建、销毁、状态跟踪)
- ✅ 实时网络同步 (WebSocket、状态广播)
- ✅ 物理引擎同步 (Rapier3D 集成)
- ✅ 自动扩缩容 (基于负载的智能扩容)
- ✅ 资源管理 (CPU、内存、网络资源分配)
- ✅ 客户端管理 (连接管理、权限控制)
- ✅ 负载均衡 (实例间负载分配)

### 任务服务特性
- ✅ 多种任务类型 (立即、定时、循环、延迟、条件)
- ✅ 优先级队列 (5级优先级支持)
- ✅ 分布式队列 (Redis 支持)
- ✅ 多种工作节点 (线程、进程、集群)
- ✅ 失败重试 (指数退避、线性退避)
- ✅ 任务持久化 (数据库存储)
- ✅ 健康检查 (工作节点监控)

## 快速开始

### 1. 安装依赖

```bash
npm install
```

### 2. 配置服务

```typescript
import { createInstanceServices, createTaskServices } from 'dl-engine/server'

// 实例服务配置
const instanceConfig = {
  instanceManager: {
    enabled: true,
    defaultResourceConfig: {
      cpu: 2,
      memory: 2048,
      maxUsers: 100,
      priority: 5
    }
  },
  networkSync: {
    enabled: true,
    maxClientsPerInstance: 100,
    stateUpdateInterval: 50 // 20 FPS
  },
  physicsSync: {
    enabled: true,
    syncRate: 60, // 60 Hz
    compressionLevel: 3
  },
  scalingManager: {
    enabled: true,
    defaultStrategy: 'auto_hybrid',
    minInstances: 1,
    maxInstances: 10
  }
}

// 任务服务配置
const taskConfig = {
  scheduler: {
    enabled: true,
    maxConcurrentTasks: 20
  },
  queue: {
    enabled: true,
    redis: {
      host: 'localhost',
      port: 6379
    }
  },
  worker: {
    enabled: true,
    defaultWorkerCount: 8
  }
}
```

### 3. 初始化服务

```typescript
import { Application } from '@feathersjs/feathers'

// 在 FeathersJS 应用中注册服务
export default function(app: Application) {
  // 注册实例服务
  const instanceServices = createInstanceServices(app, instanceConfig)
  
  // 注册任务服务  
  const taskServices = createTaskServices(app, taskConfig)
  
  // 启动服务
  await instanceServices.start()
  await taskServices.start()
}
```

## API 使用示例

### 实例管理

```typescript
// 获取实例管理器
const instanceManager = app.get('instanceManager')

// 创建实例
const instance = await instanceManager.createInstance({
  locationId: 'location-123',
  resourceConfig: {
    cpu: 2,
    memory: 2048,
    maxUsers: 50,
    priority: 7
  }
})

// 销毁实例
await instanceManager.destroyInstance(instance.id)

// 获取实例状态
const state = await instanceManager.getInstanceState(instance.id)
```

### 网络同步

```typescript
// 获取网络同步服务
const networkSync = app.get('networkSync')

// 处理客户端连接
await networkSync.handleClientConnection(spark, userId, instanceId)

// 广播消息
await networkSync.broadcastToInstance(instanceId, {
  type: 'chat_message',
  message: 'Hello World!',
  userId: 'user-123'
})
```

### 任务调度

```typescript
// 获取任务服务
const taskServices = app.get('taskServices')

// 创建立即执行任务
const taskId = await taskServices.createTask({
  name: '数据清理任务',
  type: 'immediate',
  priority: 'high',
  handler: 'cleanup-expired-data',
  payload: { 
    table: 'sessions',
    expiredBefore: new Date()
  }
})

// 创建定时任务
const scheduledTaskId = await taskServices.createTask({
  name: '每日报告',
  type: 'recurring',
  priority: 'normal',
  handler: 'generate-daily-report',
  schedule: {
    cron: '0 9 * * *', // 每天上午9点
    timezone: 'Asia/Shanghai'
  },
  payload: { reportType: 'daily' }
})

// 取消任务
await taskServices.cancelTask(taskId)
```

### 扩缩容管理

```typescript
// 获取扩缩容管理器
const scalingManager = app.get('scalingManager')

// 设置扩缩容策略
await scalingManager.setScalingConfig('location-123', {
  strategy: 'auto_hybrid',
  minInstances: 2,
  maxInstances: 20,
  targetCpuUsage: 70,
  targetMemoryUsage: 80,
  scaleUpThreshold: 85,
  scaleDownThreshold: 30,
  cooldownPeriod: 300 // 5分钟
})

// 手动扩容
await scalingManager.scaleUp('location-123', 5)

// 获取负载统计
const stats = await scalingManager.getLocationLoadStats('location-123')
```

## 监控和运维

### 服务状态监控

```typescript
// 获取实例服务状态
const instanceStatus = instanceServices.getServicesStatus()
console.log('实例服务状态:', instanceStatus)

// 获取任务服务状态  
const taskStatus = taskServices.getServicesStatus()
console.log('任务服务状态:', taskStatus)

// 获取工作池统计
const workerStats = taskServices.getService('taskWorker').getPoolStats()
console.log('工作池统计:', workerStats)
```

### 性能指标

```typescript
// 实例性能指标
const instanceMetrics = await instanceManager.getInstanceResources(instanceId)
console.log('实例资源使用:', instanceMetrics)

// 队列统计
const queueStats = await taskQueue.getQueueStats('instance-tasks')
console.log('队列统计:', queueStats)
```

## 配置参考

### 环境变量

```bash
# Redis 配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your-password

# 实例配置
INSTANCE_MAX_USERS=100
INSTANCE_DEFAULT_CPU=2
INSTANCE_DEFAULT_MEMORY=2048

# 任务配置
TASK_MAX_CONCURRENT=20
TASK_WORKER_COUNT=8
TASK_QUEUE_SIZE=1000

# 扩缩容配置
SCALING_MIN_INSTANCES=1
SCALING_MAX_INSTANCES=50
SCALING_EVALUATION_PERIOD=30
```

### 日志配置

服务使用结构化日志，支持以下日志级别：
- `error`: 错误信息
- `warn`: 警告信息  
- `info`: 一般信息
- `debug`: 调试信息

## 故障排除

### 常见问题

1. **实例创建失败**
   - 检查资源配额是否充足
   - 验证位置ID是否有效
   - 查看实例管理器日志

2. **网络同步延迟**
   - 检查网络连接质量
   - 调整状态更新频率
   - 启用数据压缩

3. **任务执行失败**
   - 检查任务处理器是否注册
   - 验证任务参数格式
   - 查看工作进程日志

4. **扩缩容异常**
   - 检查负载指标是否正常
   - 验证扩缩容策略配置
   - 查看资源分配情况

### 性能优化

1. **实例性能优化**
   - 合理设置资源配额
   - 启用物理同步压缩
   - 优化网络更新频率

2. **任务性能优化**
   - 增加工作进程数量
   - 使用优先级队列
   - 启用任务批处理

3. **扩缩容优化**
   - 调整评估周期
   - 设置合理的阈值
   - 使用混合扩缩容策略

## 开发指南

### 自定义任务处理器

```typescript
import { TaskHandler, TaskContext } from 'dl-engine/server/task'

class CustomTaskHandler implements TaskHandler {
  async execute(payload: any, context: TaskContext): Promise<any> {
    // 实现自定义任务逻辑
    console.log('执行自定义任务:', payload)
    
    // 返回执行结果
    return { success: true, processedAt: new Date() }
  }
}

// 注册处理器
taskServices.registerTaskHandler('custom-task', new CustomTaskHandler())
```

### 扩展实例管理

```typescript
import { InstanceManagerService } from 'dl-engine/server/instance'

class CustomInstanceManager extends InstanceManagerService {
  async createInstance(params: any): Promise<any> {
    // 添加自定义逻辑
    console.log('创建自定义实例:', params)
    
    // 调用父类方法
    return super.createInstance(params)
  }
}
```

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

## 许可证

本项目采用 CPAL-1.0 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

- 项目主页: https://github.com/ir-engine/ir-engine
- 文档: https://docs.ir-engine.io
- 社区: https://discord.gg/ir-engine
